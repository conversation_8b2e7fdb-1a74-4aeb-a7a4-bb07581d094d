.idea/
__pycache__/
module/Classification/BertClassifier/语料优化建议.csv
data/faq/中电智恒1125.csv
module/Classification/BertClassifier/语料优化建议_zhongdian.csv
pretrain_model/chinese-roberta-wwm-ext/
pretrain_model/roberta_chinese_clue_base/
module/TextPairSim/TrainTextPairSim/
data/对练话术/baike_qa_train.json
data/对练话术/baike_qa_valid.json
module/BertTextPairSimPoint/model/baike_v3_100000_transv3_eda/
module/BertTextPairSimPoint/model/baike_v3_100000_transv3_eda_neg10/
module/BertTextPairSimPoint/model/baike_v3_100000_transv3_eda_simlength_removelastsymbol_shortsample/
module/TextPairSim/BertTextPairSimPoint/text2eda_100000_remove_space.json
module/TextPairSim/BertTextPairSimPoint/text2eda_855530.json
module/TextPairSim/BertTextPairSimPoint/text2eda_855530_remove_space.json
module/TextPairSim/BertTextPairSimPoint/text2eda_100000.json
module/TextPairSim/BertTextPairSimPoint/text2subtext_trans_855530_trans_v3.json
module/TextPairSim/BertTextPairSimPoint/text2subtext_trans_855530_trans_v4.json
module/TextPairSim/BertTextPairSimPoint/text2subtext_trans_100000_trans_v3.json
module/TextPairSim/BertTextPairSimPoint/model/baike_v3_100000_transv3_eda_simlength_removelastsymbol_shortsample_0720/
pretrain_model/pretrain_arcsimcse_ori/
pretrain_model/pretrain_tiny_webank_1393_arccse_arcTrue_triTrue_batch64_epoch5_t0.7/
pretrain_model/pretrain_arcsimcse/
pretrain_model/pretrain_arc_128_e1/
pretrain_model/ArcCSEPreRankPretrain/
data/百度知道问答/
data/语义匹配/
module/PreRank/ArcCSEPreRank/model/pretrain_tiny_arccse2n_arcTrue_tri(replace)False_esimcse_shuffle(b_t)_batch64_epoch1_allsts/
TRAIN.py
data/中行/train_data_0203.json
ARC_ACC.py
ARC_PREDICT.py
DISTILL.py
data/中行/
data/中行_cp/
PREDICT.py
module/PreRank/TT/
module/PreRank/TT_cls/
module/PreRank/TT_len/
TRAIN_CLS.py
database/REDIS_send.py
database/REDIS_listen.py
demo/
data/813/
data/webank_hard/
data/CLUE/ZeroCLUE/
data/test_data_multi_data_type/
/data/faq_test_data/
/data/语义匹配/英文数据/
/data/语义匹配/英文数据/multi_nli/MNLI/MNLI/dev_matched.tsv
data/cluster/
data/faq/
data/sentiment/
data/对练话术/
data/webankcar/
data/澳门/
data/顺丰/
pretrain_model/roberta_chinese_clue_tiny.rar
pretrain_model/roberta_chinese_clue_tiny_anto.zip
module/PreRank/FileChat/trained_model/
pretrain_model/chinese-roberta-wwm-ext-large/
pretrain_model/roberta_intent_tupu_antoyue_num_en_trassim_0904/
module/PreRank/FileChat/test_data/
module/PreRank/FileChat/train_data/
data/webank/make_redis_data.py
data/webank/活期+与plus购买意图.xlsx
module/PreRank/ArcCSEPreRank/webank_huoqi_suggest.json
data/wiki/
pretrain_model/roberta_intent_tupu_antoyue_num_en_trassim_1030.zip
model/YesOrNoModel/model/NOUSE_231218_m1_our_gpt_log_qa_replace_unrelate_answer_norepeat_nodirty_sim_e2/
model/YesOrNoModel/model/NOUSE_YesOrNo_231213_our_qa_sim_e2/
data/语义匹配业务数据/
log/.__nlp_log.lock
log/nlp_log
script/data/
script/model/log/nlp_log
script/model/log/.__nlp_log.lock
log/
pretrain_model/jina_emb_v2_base_241204/tf_model.onnx
