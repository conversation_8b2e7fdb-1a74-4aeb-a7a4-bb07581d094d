stages:
  - package-push-image
  - deploy
  - auto_test


after_script:
  - echo ${CI_BUILD_STAGE}|grep 'deploy' && /usr/local/bin/wechat_v2 ${CI_PROJECT_NAME} ${CI_COMMIT_REF_NAME} ${GITLAB_USER_EMAIL} ${CI_PIPELINE_URL} ${CI_JOB_STATUS} 打包步骤 || /usr/local/bin/wechat_v2 ${CI_PROJECT_NAME} ${CI_COMMIT_REF_NAME} ${GITLAB_USER_EMAIL} ${CI_JOB_URL} ${CI_JOB_STATUS} 打包步骤

package-push-image:
  stage: package-push-image
  only:
    - skm_merge
  script:
    - git submodule sync --recursive
    - git submodule update --init --recursive
    - IMAGE_NAME="registry.cn-zhangjiakou.aliyuncs.com/qnzs/test:aicc-nlp-nlu--${CI_COMMIT_REF_NAME}_$(date +%Y%m%d)_${CI_COMMIT_SHA}"
    - docker build -t ${IMAGE_NAME} ./ && docker push ${IMAGE_NAME}

deploy-239-gpu:
  stage: deploy
  only:
    - skm_merge
  script:
    - ssh 239-gpu "/root/script/update_nlp.sh aicc-nlp-nlu--${CI_COMMIT_REF_NAME}_$(date +%Y%m%d)_${CI_COMMIT_SHA}"
  after_script:
    - /usr/local/bin/wechat_notifiy ${CI_PROJECT_NAME} ${CI_COMMIT_REF_NAME} ${GITLAB_USER_EMAIL} ${CI_PIPELINE_URL} ${CI_JOB_STATUS} *************-gpu
  when: manual

auto_test:
  stage: auto_test
  when: manual
  only:
    - skm_merge
  script:
    - python3.9 auto_test/test_smartqc.py
    - python3.9 auto_test/test_nlu_textbot_callout.py



##########################################################
deploy-aicc-dev:
  stage: deploy
  only:
    - skm_merge
  script:
    - ssh aicc-dev "/root/script/update_nlp.sh aicc-nlp-nlu--${CI_COMMIT_REF_NAME}_$(date +%Y%m%d)_${CI_COMMIT_SHA}"
  after_script:
    - /usr/local/bin/wechat_notifiy ${CI_PROJECT_NAME} ${CI_COMMIT_REF_NAME} ${GITLAB_USER_EMAIL} ${CI_PIPELINE_URL} ${CI_JOB_STATUS} https://aicc-dev.qnzsai.com/
  when: manual

deploy-aicc:
  stage: deploy
  only:
    - skm_merge
  script:
    - ssh aicc "/root/script/update_nlp.sh aicc-nlp-nlu--${CI_COMMIT_REF_NAME}_$(date +%Y%m%d)_${CI_COMMIT_SHA}"
  after_script:
    - /usr/local/bin/wechat_notifiy ${CI_PROJECT_NAME} ${CI_COMMIT_REF_NAME} ${GITLAB_USER_EMAIL} ${CI_PIPELINE_URL} ${CI_JOB_STATUS} aicc.qnzsai.com
  when: manual
