# -*- coding: utf-8 -*-
import json
import time
from concurrent.futures import ThreadPoolExecutor

from flask import Flask, request, abort

import setting
from database.REDIS import REDIS
from main.ClassifySearchMethodMain import ClassifyMain
from main.RegularMain import RegularMain
from setting import logger
from utils.ModelManager import ModelManager
from utils.Prometheus import Prometheus
from utils.my_utils import MyEncoder, json_message

app = Flask(__name__)

callout = True

redis = REDIS()
regular_main = RegularMain(callout)
classify_main = ClassifyMain(callout)

prometheus_key2msg = {
    "regular_request_count": "Total count of regular request",
    "sim_score_point_request_count": "Total count of point similarity score request"
}
prometheus = Prometheus(prometheus_key2msg, setting.USE_PORMETHEUS)

executor = ThreadPoolExecutor(2)
def info(msg, *args):
    executor.submit(logger.info, msg, *args)

if callout:
    model_manager = ModelManager(name="FAQ", main_list=[regular_main, classify_main])

search_time = 0
search_n = 0
classify_time = 0
classify_n = 0

@app.route("/metrics", methods=["GET"])
def metrics():
    info_data = {"msg": "收到metrics请求"}
    logger.info(json.dumps(info_data, cls=MyEncoder, ensure_ascii=False))
    try:
        return prometheus.get_metric()
    except Exception as e:
        logger.error(f"prometheus metrics 接口报错:{e}")
        result = {'code': 1, 'msg': f"prometheus metrics 接口报错:{e}"}
        return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/train", methods=["POST"])
def train():
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "Train", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        old_model_id = data.get('old_model_id', '')
        sync = int(data.get('sync', '0'))
        data_key = data.get('data_key', '')

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91001"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif data_key == '':
            logger.error("请求参数错误: data_key 为空")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91002"
            result["error_code"] = "NLU91002"
            result["error_type"] = 1
        else:
            logger.debug('[{}] 开始模型训练, data_key:{}'.format(model_id, data_key))
            is_finish = model_manager.append_train_queue(model_id=model_id, data_key=data_key, old_model_id=old_model_id, sync=sync)
            if old_model_id:
                assert is_finish==True
                result['msg'] = "外呼同步正则训练成功!"

    except Exception as e:
        logger.error(f"训练接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"NLP模型训练出错了，请联系服务商~错误代码：NLU91005"
        result["error_code"] = "NLU91005"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "Train", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/switch_model", methods=["POST"])
def switch_model():
    """
    模型上下线
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SwitchModel", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        flag = int(data.get('flag', 1))
        logger.debug(f"从字典 get 数据: {model_id}, {type(model_id)}, {flag}, {type(flag)}")

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91001"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            error_dict = model_manager.switch_model(model_id=model_id, flag=flag)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"上下线接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"NLP模型发布出错了，请联系服务商~错误代码：NLU91006"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "SwitchModel", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/copy_online_model", methods=["POST"])
def copy_online_model():
    """
    预测端模型上线,训练使用同一个model_id,预测端上线前需要拷贝一份模型文件,Callout可以不用这个接口
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "CopyOnlineModel", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        if model_id == '':
            logger.error("请求参数错误:model_id为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:model_id为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            error_dict = model_manager.copy_online_model(model_id=model_id)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"拷贝上线模型报错,错误:{e}")
        result["code"] = 1
        result["msg"] = f"拷贝上线模型报错"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "CopyOnlineModel", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/download_model_redis", methods=["POST"])
def download_model_redis():
    """
    模型下载(Redis)
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "DownloadModelRedis", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        all_dirs = data.get('all_dirs', [])
        dir_to_keys = data.get('dir_to_keys', {})

        if model_id == '':
            logger.error("请求参数错误:model_id为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:model_id为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif len(all_dirs) == 0:
            logger.error("请求参数错误:all_dirs为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:all_dirs为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif len(dir_to_keys) == 0:
            logger.error("请求参数错误:dir_to_keys为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:dir_to_keys为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            error_dict = model_manager.download_model_redis(model_id=model_id, all_dirs=all_dirs, dir_to_keys=dir_to_keys)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"Redis下载模型接口调用错误,报错:{e}")
        result["code"] = 1
        result["msg"] = f"Redis下载模型接口调用错误"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "DownloadModelRedis", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/train_states", methods=["POST"])
def train_states():
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "msg": "api succeed", "state": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "TrainStates", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        result["model_id"] = model_id

        if model_id == "":
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            res = model_manager.check_train_state(model_id=model_id)
            result['state'] = res
            train_queue = result.get('train_queue', [model_id])
            result['msg'] = '前面还有{}个模型等待训练'.format(len(train_queue[:train_queue.index(model_id)]))
    except Exception as e:
        logger.error(f"状态查询接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"状态查询接口调用错误，报错:{e}"
        result["error_code"] = "NLU91007"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "TrainStates", "out": result, "time": end-start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/re_matching/predict", methods=["POST"])
def re_predict():
    """
    正则匹配
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "api succeed", "match": 1, "data": []}
    return_code = 200

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "RegularPredict", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        detail = int(data.get('detail', 1))
        enterprise_id = data.get('enterprise_id', '')
        result["query"] = query
        result["model_id"] = model_id
        labelIds = data.get('label_ids', [])

        if callout:
            #超过上线模型数量则返回500
            if model_id not in regular_main.callout_load_model_dict:
                if len(regular_main.callout_load_model_dict) >= setting.CALLOUT_MAX_LOAD_MODEL_NUMS:
                    return_code = 500
                    return abort(return_code)

        if isinstance(labelIds, str):
            labelIds = json.loads(labelIds)
        if model_id == "":
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            predict_result, error_dict = regular_main.predict(model_id, query, labelIds, detail, enterprise_id=enterprise_id,full_match=True)
            result['data'] = predict_result
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"正则预测接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"正则预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91008"
        result["error_type"] = 0

    if return_code != 200:
        return abort(return_code)

    prometheus.add_count("regular_request_count")
    info_out_data = {"service": "RegularPredict", "out": result, "time": time.time()-start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/re_matching/test", methods=["POST"])
def re_test():
    """
    正则匹配
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "query": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "RegularTest", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        query = data.get('query', '')
        regexs = data.get('regexs', [])
        ners = data.get('ners', [])
        result["query"] = query

        if query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            predict_result, error_dict = regular_main.test(query, regexs, ners)
            result['data'] = predict_result
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"正则测试接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"正则测试接口调用错误，报错:{e}"
        result["error_code"] = "NLU91008"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "RegularTest", "out": result, "time": end-start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

counter = 0
@app.route("/nlp/classify/predict", methods=["POST"])
def classify_predict():
    global counter
    """
    意图
    """
    start = time.time()
    #服务运算数
    if counter+1> setting.MAX_CONCURRENT_NUM:
        return_code = 404
        return abort(return_code)
    else:
        counter+=1
        # logger.debug(f'并发数{counter}')

    global classify_time, classify_n
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": None}
    return_code = 200
    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "ClassifyPredict", "from": data}
        info(str(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False)))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        labelIds = data.get('label_ids', [])
        if callout:
            #超过上线模型数量则返回500
            if model_id not in classify_main.callout_load_model_dict:
                if len(classify_main.callout_load_model_dict) >= setting.CALLOUT_MAX_LOAD_MODEL_NUMS:
                    return_code = 500
                    return abort(return_code)

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            # if callout:
            # enterprise_id = data.get('enterprise_id', '')
            # query, error_msg = regular_main.sim_word_replace_model.predict(query=query, enterprise_id=enterprise_id)
            # if error_msg:
            #     logger.warning(f"正则预测相似词替换出错, query:{query}, enterprise_id:{enterprise_id}, {error_msg}")
            result = classify_main.predict(model_id, query, labelIds=labelIds, return_search_result=False, topk=0)

            result["sn"] = data.get("sn", "")
    except Exception as e:
        if return_code != 200:
            return abort(return_code)
        logger.error(f"分类预测接口调用错误，报错:{e}", exc_info=True)
        result["code"] = 1
        result["msg"] = f"分类预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91010"
        result["error_type"] = 0

    end = time.time()
    classify_time += (end-start)
    classify_n += 1
    info_out_data = {"service": "ClassifyPredict", "out": result, "time": end-start, "avg_time": (classify_time/classify_n)}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    result['consume_time'] = end-start
    counter-=1
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route('/nlp/classify/labeling', methods=['POST'])
def classify_labeling():
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}
    try:
        logger.debug("接到标注请求")

        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SearchClassifyLabeling", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        logger.debug(f"标注请求参数: {data}")

        result["sn"] = data.get("sn", "")
        model_id = data.get("model_id", '')
        data_key = data.get("data_key", '')
        data_key = data_key.strip()

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif data_key == '':
            logger.error("请求参数错误: data_key 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: data_key 为空"
            result["error_code"] = "NLU91002"
            result["error_type"] = 1
        else:
            logger.debug(f"[{model_id}] 开始标注")
            args = {"model_id": model_id, "data_key": data_key}
            executor.submit(lambda p: classify_main.labeling(**p), args)
    except Exception as e:
        logger.error(f"分类标注接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"分类标注接口调用错误，报错:{e}"
        result["error_code"] = "NLU91013"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "SearchClassifyLabeling", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=7779, debug=False)

# gunicorn -w 1 -b 0.0.0.0:9888 FAQHttpServer:app
# gunicorn -w 3 -b 0.0.0.0:9878 FAQHttpServer:app
