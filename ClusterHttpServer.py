# -*- coding: utf-8 -*-
import json
import time
from concurrent.futures import Thread<PERSON>oolExecutor

from flask import <PERSON>lask, request

from main.ClusterMain import ClusterMain
from setting import logger
from utils.my_utils import MyEncoder, json_message

app = Flask(__name__)

cluster_main = ClusterMain()
executor = ThreadPoolExecutor(1)


@app.route("/nlp/cluster/predict", methods=["POST"])
def predict():
    """
    聚类预测
    """
    start = time.time()
    result = {"sn": "", "code": 0, "data_key": "", "msg": "api succeed"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "ClusterPredict", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        data_key = data.get('data_key', '')
        result["sn"] = data.get("sn", "")
        result["data_key"] = data_key

        if data_key == "":
            logger.error("请求参数错误: data_key 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: data_key 为空"
            result["error_code"] = "NLU91002"
            result["error_type"] = 1
        else:
            args = {"data_key": data_key}
            executor.submit(lambda p:cluster_main.predict(**p), args)
    except Exception as e:
        logger.error(f"聚类预测接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"聚类预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91022"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "ClusterPredict", "out": result, "time": end-start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9890, debug=False)

# gunicorn -w 1 -b 0.0.0.0:9890 ClusterHttpServer:app
