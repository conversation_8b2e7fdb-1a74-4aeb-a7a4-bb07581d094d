FROM centos:7
#FROM registry.cn-zhangjiakou.aliyuncs.com/volvo-exam/test:nlp-tf2
WORKDIR /app
ENV RUN_CMD="/bin/bash ./bin/train.sh"
COPY bin/ /app/bin/
COPY pretrain_model/ /app/pretrain_model/
COPY module/ /app/module/
COPY model/ /app/model/
COPY database/ /app/database/
COPY data/ /app/data/
COPY main/ /app/main/
COPY route/ /app/route/
COPY conf/ /app/conf/
COPY utils/ /app/utils/
COPY submodule_utils/ /app/submodule_utils/
COPY *.py /app/
RUN echo "$RUN_CMD"
CMD $RUN_CMD
