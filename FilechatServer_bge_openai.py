import time,json,os
import random
import math
from concurrent.futures import ThreadPoolExecutor
from utils.my_utils import get_port_from_pid

from submodule_utils.nacos_register import NacosHelper
nacos_r = NacosHelper("nlp-nlu-filechat")
nacos_r.nacos_fun()

port = get_port_from_pid()
nacos_r.service_port = port

import setting
from utils.logHelper import log_initialize

severname = 'filechat_nlu'  # nlu_predict
logger = log_initialize(setting.logPath, severname)
setting.logger = logger
from setting import logger



setting.WAITING_QUEUE = 'filechat_waiting_queue'


from utils.my_utils import MyEncoder, json_message, lang_symbol_to_lang_string
from model.LanguageJudge.LanguageJudgeRegular import LanguageJudgeRegular


import uvicorn
from fastapi import FastAPI, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware

from pydantic import BaseModel, <PERSON>
from typing import List, Literal, Optional, Union


from sse_starlette.sse import EventSourceResponse
# Set up limit request time
EventSourceResponse.DEFAULT_PING_INTERVAL = 1000


app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# file_chat = Blueprint('file_chat', __name__)



executor = ThreadPoolExecutor(max_workers=10)
# model_manager = current_app.config['model_manager']
language_judge_regular = LanguageJudgeRegular()


from main.RetrievalMain_bge import RetrievalMain_bge
from main.FileChatMain import FileChatMain

train_retrievalmain_bge = RetrievalMain_bge(client='train')
predict_retrievalmain_bge = RetrievalMain_bge(client='predict')



import threading
from main.RetrievalMain_bge import filechat_train_record
train_thread = threading.Thread(target=filechat_train_record.check_train_record)
train_thread.start()


filechatmain = FileChatMain()

from utils.ModelManager import ModelManager
model_manager = ModelManager('FileChat',[train_retrievalmain_bge])

class UploadData(BaseModel):
    sn : Optional[str] = ''
    model_id : Optional[str] = ''
    filenames: Optional[Union[str,list]] = ''
    base64s: Optional[Union[str,list]] = ''
    fileids: Optional[Union[str,list]] = ''
    callback_url: Optional[str] = ''
    cut_len: Optional[list] = [1000]
    window_size: Optional[list] = [500]
    parse_result_redis_key: Optional[str] = ''
    es_index : Optional[str] = ''
    knowledge_detail_id : Optional[str] = ''
    use_embedding:Optional[bool] = True


@app.post("/nlp/filechat/fileupload")
def trainer(request: UploadData):
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0,  "query": "", "msg": "", "data": []}

    try:
        data = request.dict()

        info_in_data = {"service": "/nlp/filechat/fileupload", "from": {i:j for i,j in data.items() if i!='base64s'}}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        fileids = data.get('fileids', [])
        base64s = data.get('base64s', None)
        filenames = data.get('filenames', [])
        callback_url = data.get('callback_url','')
        cut_len = data.get('cut_len', [1000])
        window_size = data.get('window_size',[500])


        from hashlib import md5
        data_key = md5(str(data).encode()).hexdigest()
        # language: chs、cht、yue、en
        # source_langs = data.get("source_langs", "chs")
        source_langs = "chs"
        target_langs = "chs"

        if isinstance(filenames, str):
            filenames = [filenames]
        if isinstance(source_langs, str):
            source_langs = [source_langs] * len(filenames)
        if isinstance(target_langs, str):
            target_langs = [target_langs] * len(filenames)
        source_langs = [lang_symbol_to_lang_string(s) for s in source_langs]
        target_langs = [lang_symbol_to_lang_string(s) for s in target_langs]
        cut_lens = cut_len
        window_sizes = window_size

        # logger.debug(f"接收到的base64s长度:{len(base64s)},{[len(b) for b in base64s]}")

        if fileids == []:
            logger.warning("预测错误: empty fileids")
            return json_message('{"code":1, "msg":"empty fileids"}')
        if len(filenames) == 0:
            logger.warning("预测错误: empty filenames")
            return json_message('{"code":1, "msg":"empty filenames"}')

        model_id = fileids[0]
        parse_result_redis_key = data.get('parse_result_redis_key','')
        upload_time = int(time.time())

        if len(filenames) and len(filenames) == len(source_langs):
            if base64s and type(base64s) == list:
                data_for_model_manager = [{'filename': filename, 'base64': base64_, 'source_lang': source_lang_,"target_langs": target_langs,'cut_len':cut_len,
                                           'parse_result_redis_key':parse_result_redis_key,'upload_time':upload_time}
                                          for filename, base64_, source_lang_,cut_len in zip(filenames, base64s, source_langs,cut_lens)]
            else:
                data_for_model_manager = [{'filename': filename, 'source_lang': source_lang_, "target_langs": target_langs,'cut_len':cut_len,
                                           'parse_result_redis_key':parse_result_redis_key,'upload_time':upload_time,'window_size':window_size}
                                          for filename, source_lang_,cut_len,window_size in zip(filenames, source_langs,cut_lens,window_sizes)]

            # retrieval_main.train(model_id,data=data_for_model_manager)
            # model_manager.append_train_queue(model_id,data=data_for_model_manager)
            # executor.submit(current_app.config['model_manager'].append_train_queue, model_id, data=data_for_model_manager, model_list=[retrievalmain_bge],data_key=data_key)
            executor.submit(model_manager.append_train_queue, model_id, data=data_for_model_manager, model_list=[train_retrievalmain_bge],
                            data_key=data_key,scenario='filechat',callback_url=callback_url,**{"es_index":data['es_index'],
                                                                                               "knowledge_detail_id":data['knowledge_detail_id'],
                                                                                               "use_embedding":data['use_embedding']})
            result["msg"] = '上传成功!'
            result["model_id"] = model_id
            result["filenames"] = filenames
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            # logger.error("请求数据出错,data:{}".format(data))
            logger.error("请求数据出错,data:{}",exc_info=True)
    except Exception as e:
        result["code"] = 1
        result["msg"] = '上传文件失败,报错:{}'.format(e)
        logger.error('上传文件失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "/nlp/filechat/fileupload", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result
    # return json.dumps(result, cls=MyEncoder, ensure_ascii=False)


class TrainData(BaseModel):
    sn : Optional[str] = ''
    model_id : Optional[str] = ''
    #fileids支持传空
    fileids: Optional[Union[str,list]] = []
    callback_url : Optional[str] = ''
    rag_config : Optional[list] = [{"provider":"zhisheng"}]

translate_excutor = ThreadPoolExecutor(max_workers=2)
@app.post("/nlp/filechat/train")
def traintrain(request:TrainData):
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "", "data": []}

    try:
        data = request.dict()

        info_in_data = {"service": "/nlp/filechat/train", "from": {i:j for i,j in data.items() if i!='base64s'}}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', "")
        fileids = data.get('fileids', [])
        callback_url = data.get('callback_url', '')
        rag_config = data['rag_config']
        if rag_config==[{}]:
            rag_config = [{"provider":"zhisheng"}]
        result.update(data)

        if len(model_id) == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":1, "msg":"empty model_id"}')
        elif model_id:
            translate_excutor.submit(train_retrievalmain_bge.offline_train_from_fileids, model_id, fileids,rag_config,callback_url,data)
            # code = train_retrievalmain_bge.train_from_fileids(model_id, fileids,rag_config)
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            # logger.error("请求数据出错,data:{}".format(data))
            logger.error("请求数据出错,data:{}",exc_info=True)
    except Exception as e:
        result["code"] = 1
        result["msg"] = '文件绑定失败,报错:{}'.format(e)
        logger.error('文件绑定失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "/nlp/filechat/train", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result


class DeleteData(BaseModel):
    sn : Optional[str] = ''
    fileids: Optional[list] = ''
@app.post("/nlp/filechat/filedelete")
def filedelete(request:DeleteData):
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0,  "query": "", "msg": "", "data": []}

    try:
        data = request.dict()

        info_in_data = {"service": "/nlp/filechat/filedelete", "from": {i:j for i,j in data.items() if i!='base64s'}}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        fileids = data.get('fileids', [])


        if fileids == []:
            logger.warning("预测错误: empty fileids")
            return json_message('{"code":1, "msg":"empty fileids"}')

        elif len(fileids) > 0:
            train_retrievalmain_bge.file_delete(fileids)
            result["msg"] = '删除成功!'
            result["fileids"] = fileids
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            # logger.error("请求数据出错,data:{}".format(data))
            logger.error("请求数据出错,data:{}",exc_info=True)
    except Exception as e:
        result["code"] = 1
        result["msg"] = '删除文件失败,报错:{}'.format(e)
        logger.error('删除文件失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "/nlp/filechat/filedelete", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result




class UsageInfo(BaseModel):
    prompt_tokens: int = 0
    total_tokens: int = 0
    completion_tokens: Optional[int] = 0
    encode_time: Optional[float] = 0
    total_time: Optional[float] = 0




class RetrievalData(BaseModel):
    sn : Optional[str] = ''
    model_id : Optional[str] = ''
    query : str
    topk: Optional[int] = 5
    one_file_only:Optional[bool] = False
    provider: Optional[str] = 'zs'
    rewrite: Optional[bool] = False
    history: Optional[List] = []
    text_len: Optional[int] = None
    embedding_model:Optional[str] = ''


@app.post("/nlp/train/filechat/retrieval")
def retrieval(request: RetrievalData):
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}
    service_name = '/nlp/train/filechat/retrieval'
    try:
        data = request.dict()
        result.update(data)
        info_in_data = {"service": service_name, "from": {i:j for i,j in data.items() if i!='base64s'}}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        topk = data.get('topk', 5)
        provider = data.get('provider', 'zs')
        one_file_only = data.get('one_file_only', False)
        rewrite = data.get('rewrite', False)
        history = data.get('history', [])

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":1, "msg":"empty model_id"}')

        if query:
            result = train_retrievalmain_bge.predict(**data)
            result.update(data)
            # result['retrival_content']['match_text'][0] = '\n------------------\n'.join(result['retrival_content']['match_text'])
            result["msg"] = '检索成功!'
            result["model_id"] = model_id
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            # logger.error("请求数据出错,data:{}".format(data))
            logger.error("请求数据出错,data:{}",exc_info=True)
    except Exception as e:
        result["code"] = 1
        result["msg"] = '检索失败,报错:{}'.format(e)
        logger.error('检索失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    random_score = []
    for i in result.get("retrival_content", {}).get("score", []):
        if isinstance(i, float) and math.isnan(i) or i == 0:
            logger.warning(f"score :{i}")
            random_score.append(random.uniform(0.7, 0.9))
        else:
            random_score.append(i)
    if random_score:
        result.get("retrival_content", {})["score"] = random_score



    info_out_data = {"service": service_name, "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result



class RewriteData(BaseModel):
    query : str
    history: Optional[List] = []

@app.post("/nlp/train/filechat/rewrite")
def retrieval(request: RewriteData):
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}
    service_name = 'REWRITE'
    try:
        data = request.dict()
        result.update(data)
        info_in_data = {"service": service_name, "from": {i:j for i,j in data.items() if i!='base64s'}}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        history = data.get('history', [])


        if query:
            info = train_retrievalmain_bge.rewrite_history(query, history)
            result.update(info)
            # result['retrival_content']['match_text'][0] = '\n------------------\n'.join(result['retrival_content']['match_text'])
            result["msg"] = 'rewrite成功!'
            result["model_id"] = model_id
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            # logger.error("请求数据出错,data:{}".format(data))
            logger.error("请求数据出错,data:{}",exc_info=True)
    except Exception as e:
        result["code"] = 1
        result["msg"] = 'rewrite失败,报错:{}'.format(e)
        logger.error('rewrite失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": service_name, "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result

@app.post("/nlp/filechat/retrieval")
def retrieval(request: RetrievalData):
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}
    service_name = '/nlp/filechat/retrieval'

    try:
        data = request.dict()
        result.update(data)
        info_in_data = {"service": service_name, "from": {i:j for i,j in data.items() if i!='base64s'}}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        topk = data.get('topk', 5)
        provider = data.get('provider', 'zs')
        one_file_only = data.get('one_file_only', False)
        history = data.get('history', [])



        model_id_client = model_id + "_switch"
        if provider=='volcengine':
            model_id_client = model_id

        data['model_id'] = model_id_client
        logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":1, "msg":"empty model_id"}')

        if query:
            result = predict_retrievalmain_bge.predict(**data)
            result.update(data)
            # result['retrival_content']['match_text'][0] = '\n------------------\n'.join(result['retrival_content']['match_text'])
            result["msg"] = '检索成功!'
            result["model_id"] = model_id
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            # logger.error("请求数据出错,data:{}".format(data))
            logger.error("请求数据出错,data:{}",exc_info=True)
    except Exception as e:
        result["code"] = 1
        result["msg"] = '检索失败,报错:{}'.format(e)
        logger.error('检索失败,错误：{}'.format(e), exc_info=True)

    end = time.time()

    random_score = []
    for i in result.get("retrival_content", {}).get("score", []):
        if isinstance(i, float) and math.isnan(i) or i == 0:
            logger.warning(f"score :{i}")
            random_score.append(random.uniform(0.7, 0.9))
        else:
            random_score.append(i)
    if random_score:

        result.get("retrival_content", {})["score"] = random_score

    info_out_data = {"service": service_name, "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result



class SwtichData1(BaseModel):
    sn: Optional[str] = None
    model_id: Optional[str] = None
    flag: Optional[int] = None
    imm : Optional[int] = None
    model_suffix: Optional[int] = None

class SwtichData2(BaseModel):
    sn: Optional[str] = None
    model_id: Optional[str] = None
    flag: Optional[int] = None
    imm : Optional[int] = None
    model_suffix: Optional[int] = None


@app.post("/nlp/predict/switch_model")
def switch_model(request:SwtichData1):
    """
    模型上下线
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        data = request.dict()

        info_in_data = {"service": "SwitchModel", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        if '_switch' not in model_id:
            model_id = model_id + '_switch'
        flag = int(data.get('flag', 1))
        logger.debug(f"从字典 get 数据: {model_id}, {type(model_id)}, {flag}, {type(flag)}")

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91001"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            code = predict_retrievalmain_bge.switch_model(model_id, flag)
            result['code'] = code
            logger.debug(f"已提交上下线请求")

    except Exception as e:
        logger.error(f"上下线接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"NLP模型发布出错了，请联系服务商~错误代码：NLU91006"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0
    end = time.time()
    info_out_data = {"service": "SwitchModel", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result




@app.post("/nlp/copy_online_model")
def copy_online_model(request:SwtichData2):
    """
    预测端模型上线，训练使用同一个model_id，预测端上线前需要拷贝一份模型文件
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        data = request.dict()

        info_in_data = {"service": "CopyOnlineModel", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        if model_id == '':
            logger.error("请求参数错误:model_id为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:model_id为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            error_dict = {"error_code":0}
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"拷贝上线模型报错,错误:{e}")
        result["code"] = 1
        result["msg"] = f"拷贝上线模型报错"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "CopyOnlineModel", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result


class RedisDelete(BaseModel):
    sn : Optional[str] = ''
    redis_key : Optional[str] = ''


@app.post("/nlp/filechat/redis_key_delete")
def traintrain(request:RedisDelete):
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "", "data": []}

    try:
        data = request.dict()

        info_in_data = {"service": "/nlp/filechat/redis_key_delete", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        redis_key = data.get('redis_key', "")
        delete_result = train_retrievalmain_bge.redis.delete(redis_key)

        if delete_result!=None:
            code = 0
        else:
            code = 1
        result['code'] = code
    except Exception as e:
        result["code"] = 1
        result["msg"] = 'redis删除失败,报错:{}'.format(e)
        logger.error('redis删除失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "/nlp/filechat/redis_key_delete", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result


class ModelDelete(BaseModel):
    sn : Optional[str] = ''
    model_id : Optional[str] = ''
    provider : Optional[list] = ['zhisheng']
    embedding_models: Optional[list] = []


@app.post("/nlp/filechat/modeldelete")
def modeldelete(request:ModelDelete):
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "", "data": []}

    try:
        data = request.dict()

        info_in_data = {"service": "/nlp/filechat/modeldelete", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', "")
        providers = data.get('provider', [])
        embedding_models = data.get('embedding_models',[])
        for provider in providers:
            if provider=='zhisheng':
                delete_result = train_retrievalmain_bge.model_delete(model_id, provider)
                delete_result = predict_retrievalmain_bge.model_delete(model_id + '_switch', provider)
                predict_retrievalmain_bge.switch_model(model_id + '_switch',1)
            else:
                delete_result = train_retrievalmain_bge.model_delete(model_id, provider,embedding_models)

        if delete_result!=None:
            code = 0
        else:
            code = 1
        result['code'] = code
    except Exception as e:
        result["code"] = 1
        result["msg"] = 'redis删除失败,报错:{}'.format(e)
        logger.error('redis删除失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "/nlp/filechat/modeldelete", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return result





if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=9899, workers=1)
