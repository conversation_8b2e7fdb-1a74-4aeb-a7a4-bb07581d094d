# -*- coding: utf-8 -*-
import json
import os
import time
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor
from utils.logHelper import log_initialize
from utils.my_utils import get_port_from_pid
import setting

severname = 'predict_nlu'  # nlu_predict
logger = log_initialize(setting.logPath, severname)
setting.logger = logger
setting.CLIENT = 'predict'

# nacos注册
from submodule_utils.nacos_register import NacosHelper
nacos_r = NacosHelper("nlp-nlu-" + setting.CLIENT)
nacos_r.get_config()

port = get_port_from_pid()
nacos_r.service_port = port

from flask import Flask, request, abort

os.environ['MKL_NUM_THREADS'] = str(setting.NUM_THREADS)
from database.REDIS import REDIS
from main.BertTextPairSimPointMain import BertTextPairSimPointMain
from main.ClassifyMain import ClassifyMain
from main.RegularMain import RegularMain
from main.SearchMainDataType import SearchMain
from main.SpellCheckMain import SpellCheckMain
# from main.PretrainSimilarityScoreMain import PretrainSimilarityScoreMain
from main.TfidfSimilarityScoreMain import TfidfSimilarityScoreMain
from model.CorpusRecommend.CorpusRecommend import CorpusRecommend
from model.LanguageJudge.LanguageJudgeRegular import LanguageJudgeRegular
from model.YesOrNoModel.YesOrNoModel import YesOrNoModel
from main.ClusterMain import ClusterMain
from setting import logger
from utils.ModelManager import ModelManager
from utils.Prometheus import Prometheus
from utils.my_utils import MyEncoder, json_message, get_client_model_id, lang_symbol_to_lang_string


is_loading = False
app = Flask(__name__)

redis = REDIS()
corpus_recommend = CorpusRecommend()
cluster_main = ClusterMain()
language_judge_regular = LanguageJudgeRegular()
yes_or_no_model = YesOrNoModel()

train_main_mapper = {
    'smartspeech': ['regular_main', 'search_main'],
    'smartchat': ['regular_main', 'tfidf_sim_score_main', 'tfidf_sim_score_point_main', 'bert_sim_score_point_main',
                  'spell_check_main', 'search_main'],
    'smartanalysis': ['regular_main', 'classify_main', 'search_main'],
    "smartcoach": ['bert_sim_score_point_main', 'regular_main', "classify_main"],
    "smartllm": ['exam_main', 'speech_text_main'],
}


all_main_list = []
for USE_SCENARIO in setting.USE_SCENARIOS:
    for main in train_main_mapper[USE_SCENARIO]:
        # exec(f"{main} = {smallmain_to_bigmain[main]}()")
        all_main_list.append(main)
all_main_list = list(set(all_main_list))
logger.debug(f'服务初始化main_list:{all_main_list}')

if 'regular_main' in all_main_list:
    regular_main = RegularMain()
    if "smartanalysis" in setting.USE_SCENARIOS:
        regular_main.model.search_switch = True
if 'search_main' in all_main_list:
    from main.TranslateMain import TranslateMain
    from main.TranslateMain import translate_recode
    train_thread = threading.Thread(target=translate_recode.check_train_record)
    train_thread.start()
    search_main = SearchMain()
    translate_main = TranslateMain()
if 'classify_main' in all_main_list:
    classify_main = ClassifyMain()
if 'tfidf_sim_score_main' in all_main_list:
    if 'search_main' in globals():
        tfidf_sim_score_main = search_main
    else:
        tfidf_sim_score_main = SearchMain()
if 'tfidf_sim_score_point_main' in all_main_list:
    tfidf_sim_score_point_main = TfidfSimilarityScoreMain()
if 'bert_sim_score_point_main' in all_main_list:
    bert_sim_score_point_main = BertTextPairSimPointMain()
if 'spell_check_main' in all_main_list:
    spell_check_main = SpellCheckMain()
if 'exam_main' in all_main_list:
    from main.ExamMain import ExamMain
    exam_main = ExamMain()
if 'speech_text_main' in all_main_list:
    from main.SpeechTextMain import SpeechTextMain
    speech_text_main = SpeechTextMain()

all_main_list = [eval(i) for i in all_main_list]
model_manager = ModelManager(name="FAQ_predict", main_list=all_main_list)
# model_manager.info_record()

prometheus_key2msg = {
    "regular_request_count": "Total count of regular request",
    "sim_score_point_request_count": "Total count of point similarity score request"
}
prometheus = Prometheus(prometheus_key2msg, setting.USE_PORMETHEUS)
if setting.USE_PORMETHEUS:
    prometheus.redis.persistence_db = prometheus.redis.get_redis()
executor = ThreadPoolExecutor(10)
if setting.CLIENT == 'train':
    executor.submit(corpus_recommend.period_train)

search_time = 0
search_n = 0
classify_time = 0
classify_n = 0

"""
新版训练统一用一个model_id,因此当模型发布时预测端需要拷贝一次model_id对应的模型(拷贝后是model_id_switch),因此和预测端预测相关的预测请求需要调用model_id_switch。
下面的接口无需用到model_id_switch,直接用model_id即可:
metrics
/nlp/train
/nlp/switch_model
/nlp/copy_online_model
/nlp/download_model_redis
/nlp/train_states
/nlp/re_matching/test
/similarquerysearch/predict
下面的接口需要判断是训练端还是预测端,预测端使用model_id_switch:
/nlp/re_matching/predict
/nlp/search/predict
/nlp/classify/predict
/nlp/similarity_score/predict
/nlp/similarity_score_point/predict
/nlp/search/labeling
/nlp/classify/labeling
/nlp/spellcheck/predict
"""

nacos_r.nacos_fun()
@app.route("/nlp/predict/metrics", methods=["GET"])
def metrics():
    info_data = {"msg": "收到metrics请求"}
    logger.info(json.dumps(info_data, cls=MyEncoder, ensure_ascii=False))
    try:
        return prometheus.get_metric()
    except Exception as e:
        logger.error(f"prometheus metrics 接口报错:{e}")
        result = {'code': 1, 'msg': f"prometheus metrics 接口报错:{e}"}
        return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/copy_online_model", methods=["POST"])
def copy_online_model():
    """
    预测端模型上线，训练使用同一个model_id，预测端上线前需要拷贝一份模型文件
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "CopyOnlineModel", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        train_record = data.get("train_record", '')
        if model_id == '':
            logger.error("请求参数错误:model_id为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:model_id为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            error_dict = model_manager.copy_online_model(model_id, train_record)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"拷贝上线模型报错,错误:{e}")
        result["code"] = 1
        result["msg"] = f"拷贝上线模型报错"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "CopyOnlineModel", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/download_model_redis", methods=["POST"])
def download_model_redis():
    """
    模型下载(Redis)
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "DownloadModelRedis", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        all_dirs = data.get('all_dirs', [])
        dir_to_keys = data.get('dir_to_keys', {})

        if model_id == '':
            logger.error("请求参数错误:model_id为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:model_id为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif len(all_dirs) == 0:
            logger.error("请求参数错误:all_dirs为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:all_dirs为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif len(dir_to_keys) == 0:
            logger.error("请求参数错误:dir_to_keys为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:dir_to_keys为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            error_dict = model_manager.download_model_redis(model_id=model_id, all_dirs=all_dirs,
                                                            dir_to_keys=dir_to_keys)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"Redis下载模型接口调用错误,报错:{e}")
        result["code"] = 1
        result["msg"] = f"Redis下载模型接口调用错误"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "DownloadModelRedis", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


# 正则预测
@app.route("/nlp/re_matching/predict", methods=["POST"])
def re_predict():
    """
    正则匹配
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "api succeed", "match": 1, "data": []}
    return_code = 200

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "RegularPredict", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        model_suffix = int(data.get('model_suffix', 0))
        detail = int(data.get('detail', 1))
        enterprise_id = data.get('enterprise_id', '')
        result["query"] = query
        result["model_id"] = model_id
        labelIds = data.get('labelIds', [])

        if isinstance(labelIds, str):
            labelIds = json.loads(labelIds)
        if model_id == "":
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            if model_suffix:
                model_id_client = get_client_model_id(model_id)
            else:
                model_id_client = model_id
            logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
            # model_manager.auto_online(regular_main, model_id_client)
            predict_result, error_dict = regular_main.predict(model_id_client, query, labelIds, detail,
                                                              enterprise_id=enterprise_id)
            result['data'] = predict_result
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
            if result["code"] == 0:
                model_manager.model_user_time[model_id_client] = time.time()
    except Exception as e:
        logger.error(f"正则预测接口调用错误，报错:{e}", exc_info=True)
        result["code"] = 1
        result["msg"] = f"正则预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91008"
        result["error_type"] = 0

    if return_code != 200:
        return abort(return_code)

    prometheus.add_count("regular_request_count")
    result.update({"api_time": time.time()-start})
    info_out_data = {"service": "RegularPredict", "out": result, "time": time.time() - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

# def logger_is_loading():
#     global is_loading
#     while True:
#         print('is_loading',is_loading)
#         time.sleep(0.1)
#
# threading.Thread(target=logger_is_loading).start()

@app.route("/nlp/search/predict", methods=["POST"])
def search_predict():
    """
    意图
    """
    start = time.time()
    global search_time, search_n,is_loading
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "intent_result": {}, "faq_result": {},
              "chat_result": {}, "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SearchPredict", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")

        if not is_loading:
            model_id = data.get('model_id', '')
            query = data.get('query', '')
            return_num = int(data.get('return_num', 5))
            context_input_process = data.get('context_input_process', '')
            enterprise_id = data.get('enterprise_id', '')
            remove_stop_words_flag = True
            robot_model_type = data.get('robotModelType', '')

            if model_id == '':
                logger.error("请求参数错误: model_id 为空")
                result["code"] = 1
                result["msg"] = "请求参数错误: model_id 为空"
                result["error_code"] = "NLU91001"
                result["error_type"] = 1
            elif query == "":
                logger.error("请求参数错误: query 为空")
                result["code"] = 1
                result["msg"] = "请求参数错误: query 为空"
                result["error_code"] = "NLU91003"
                result["error_type"] = 1
            else:
                model_id_client = get_client_model_id(model_id)
                logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
                # model_manager.auto_online(search_main, model_id_client)
                result = search_main.predict(model_id_client, query, return_num=return_num,
                                             context_input_process=context_input_process, enterprise_id=enterprise_id,
                                             remove_stop_words_flag=remove_stop_words_flag,robot_model_type=robot_model_type)
                if result["code"] == 0:
                    model_manager.model_user_time[model_id_client] = time.time()
                result["model_id"] = model_id
                result["sn"] = data.get("sn", "")
        else:
            result["code"] = 404
            result["msg"] = f"模型正在加载中:{nacos_r.service_ip}:{nacos_r.service_port}"


    except Exception as e:
        logger.error(f"检索预测接口调用错误，报错:{e}", exc_info=True)
        result["code"] = 1
        result["msg"] = f"检索预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91009"
        result["error_type"] = 0

    prometheus.add_count("search_request_count")
    end = time.time()
    search_time += (end - start)
    search_n += 1
    info_out_data = {"service": "SearchPredict", "out": result, "time": end - start,
                     "avg_time": (search_time / search_n)}
    result.update({"api_time": end-start})
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))



concurrent_num_counter = []


@app.route("/nlp/classify/predict", methods=["POST"])
def classify_predict():
    global concurrent_num_counter
    """
    意图
    """
    start = time.time()
    one_seconed_later = start - 1
    concurrent_num_counter = concurrent_num_counter[-10000:] + [start]

    global classify_time, classify_n
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": None}
    return_code = 200
    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "ClassifyPredict", "from": data}
        logger.info(str(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False)))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        labelIds = data.get('label_ids', [])

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            # model_manager.auto_online(classify_main, model_id)
            logger.debug(f"获取服务对应的client_model_id:{model_id},model_id:{model_id}")
            result = classify_main.predict(model_id, query, labelIds=labelIds, return_search_result=False,
                                           topk=1)
            if result["code"] == 0:
                model_manager.model_user_time[model_id] = time.time()
            result["model_id"] = model_id
            result["sn"] = data.get("sn", "")
    except Exception as e:
        if return_code != 200:
            return abort(return_code)
        logger.error(f"分类预测接口调用错误，报错:{e}", exc_info=True)
        result["code"] = 1
        result["msg"] = f"分类预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91010"
        result["error_type"] = 0

    prometheus.add_count("classify_request_count")
    end = time.time()
    classify_time += (end - start)
    classify_n += 1
    info_out_data = {"service": "ClassifyPredict", "out": result, "time": end - start,
                     "avg_time": (classify_time / classify_n)}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    result['consume_time'] = end - start
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route('/nlp/similarity_score_point/predict', methods=['POST'])
def sim_score_point_predict():
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SimScorePointPredict", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get("model_id", "pretrain")
        query = data.get("query", "")
        texts = query.split("||")
        if query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91003"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        elif len(texts) < 2:
            logger.error("请求参数错误: query 格式错误，query 应该包含两段文本，用 \"||\" 隔开")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91004"
            result["error_code"] = "NLU91004"
            result["error_type"] = 1
        else:
            query = texts[0].strip()
            point_list = [t.strip() for t in texts[1:]]
            # result = tfidf_sim_score_point_main.predict(model_id=model_id, query=query, point_list=point_list)
            model_id_client = get_client_model_id(model_id)
            logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
            # model_manager.auto_online(bert_sim_score_point_main, model_id)
            result = bert_sim_score_point_main.predict(model_id=model_id_client, query=query, point_list=point_list)
            if result["code"] == 0:
                model_manager.model_user_time[model_id] = time.time()
            result["model_id"] = model_id
            result["sn"] = data.get("sn", "")
    except Exception as e:
        logger.error(f"相似度预测接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"NLP预测服务暂不可用，请联系服务商~错误代码：NLU91011"
        result["error_code"] = "NLU91011"
        result["error_type"] = 0

    prometheus.add_count("sim_score_point_request_count")
    result.update({"api_time": time.time()-start})
    info_out_data = {"service": "SimScorePointPredict", "out": result, "time": time.time() - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/pos_neg_classify/predict", methods=["POST"])
def pos_neg_classify_predict():
    start = time.time()
    global search_time, search_n
    label_str_dict = {0: "肯定", 1: "否定", 2: "中性"}
    result = {"sn": "", "code": 0, "msg": "", "label": 2, "label_str": "中性", "score": 0}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "PosNegClassify", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        question = data.get('question', '是不是？')
        if question == '':
            question = '是不是？'
        answer = data.get("answer", "")

        if question == '':
            logger.error("请求参数错误:question为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:question为空"
            result["label"] = 2
            result["label_str"] = label_str_dict[result["label"]]
            result["score"] = 0
        elif answer == "":
            logger.error("请求参数错误:answer为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:answer为空"
            result["label"] = 2
            result["label_str"] = label_str_dict[result["label"]]
            result["score"] = 0
        else:
            pred_result = yes_or_no_model.predict(question=question, answer=answer)
            result["score"] = float(pred_result[0][0]["score"])
            if result["score"] < 0.5:
                result["label"] = 2
            else:
                result["label"] = int(pred_result[0][0]["label_id"])
            result["label_str"] = label_str_dict[result["label"]]
            result["sn"] = data.get("sn", "")
    except Exception as e:
        logger.error(f"二分类预测接口调用错误,报错:{e}")
        result["code"] = 1
        result["msg"] = f"二分类预测接口调用错误,报错:{e}"
        result["label"] = 2
        result["label_str"] = label_str_dict[result["label"]]
        result["score"] = 0

    end = time.time()
    result.update({"api_time": end-start})
    info_out_data = {"service": "PosNegClassify", "out": result, "time": end-start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/spellcheck/predict", methods=["POST"])
def predict():
    """
    意图
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SpellCheckPredict", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('text', '')

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":400, "msg":"empty model_id"}')

        if model_id and query:
            model_id_client = get_client_model_id(model_id)
            logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
            # model_manager.auto_online(spell_check_main, model_id)
            result = spell_check_main.predict(model_id_client, query)
            if result["code"] == 0:
                model_manager.model_user_time[model_id] = time.time()
            result["model_id"] = model_id
            result["sn"] = data.get("sn", "")
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            logger.error("请求数据出错,data:{}".format(data))
    except Exception as e:
        result["code"] = 1
        result["msg"] = '预测失败,报错:{}'.format(e)
        logger.error('预测失败,错误：{}'.format(e))

    prometheus.add_count("spellcheck_request_count")
    end = time.time()
    result.update({"api_time": end-start})
    info_out_data = {"service": "SpellCheckPredict", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/filechat/language_judge", methods=["POST"])
def language_judge():
    """
    判断文本是否是给定语言的
    """
    start = time.time()
    result = {"sn": "", "code": 0, "query": "", "msg": "", "is_lang": 0}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "language_judge", "from": str(data)[0:100]}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        query = data.get('query', '')
        lang = data.get('lang', 'chs')
        list_flag = True
        if isinstance(lang, list):
            lang_list = lang
        else:
            lang_list = lang
            list_flag = False
        result["query"] = query
        # lang: chs、cht、yue、en

        if query == '':
            logger.warning("预测错误: empty query")
            return json_message('{"code":1, "msg":"empty query"}')
        is_lang_list = []
        for lang in lang_list:
            lang = lang_symbol_to_lang_string(lang)
            is_lang = language_judge_regular.language_judge(text=query, lang=lang)
            is_lang_list.append(1 if is_lang else 0)
        result["is_lang"] = is_lang_list if list_flag else is_lang_list[0]
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'language_judge错误,报错:{e}'
        logger.error(f'language_judge错误,报错:{e}', exc_info=True)

    prometheus.add_count("language_judge_request_count")
    end = time.time()
    info_out_data = {"service": "language_judge", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

translate_excutor = ThreadPoolExecutor(max_workers=2)
@app.route("/nlp/translate/predict", methods=["POST"])
def translate():
    """
    正则匹配
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "query": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "Translate", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        source_lang = data.get('source_lang', '')
        target_lang = data.get('target_lang', '')
        text = data.get('text', '')
        result["text"] = text
        callback_url = data.get('callback_url','')

        if callback_url:
            result["callback_url"] = callback_url
            data_url = data.get('data_url','')
            translate_excutor.submit(translate_main.offline_predict, data_url, callback_url,data)
            result['msg'] = "离线翻译提交成功"
        else:#实时
            if text == "":
                logger.error("请求参数错误: text 为空")
                result["code"] = 1
                result["msg"] = "请求参数错误: text 为空"
                result["error_code"] = "NLU91003"
                result["error_type"] = 1
            else:
                if type(text)==str:
                    text = [text]
                if type(target_lang)==str:
                    target_lang = [target_lang]
                predict_result, error_dict = translate_main.predict(text, source_lang, target_lang)
                result = predict_result
                if error_dict["error_code"] != 0:
                    result["code"] = 1
                    result["msg"] = error_dict["error_msg"]
                    result["error_code"] = error_dict["error_code"]
                    result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"翻译接口调用错误，报错:{e}",exc_info=True)
        result["code"] = 1
        result["msg"] = f"翻译接口调用错误，报错:{e}"
        result["error_code"] = "NLU91008"
        result["error_type"] = 0

    end = time.time()
    result.update({"api_time": end-start})
    info_out_data = {"service": "Translate", "out": result, "time": end-start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))



# def loop_get_start_name_num():
#     global start_name_num
#     while True:
#         start_name_num = nacos_r.get_list_num()
#         time.sleep(1)
# threading.Thread(target=loop_get_start_name_num).start()


@app.route("/nlp/is_loading_on", methods=["POST"])
def is_loading_on_function():
    global is_loading
    start_name_num = nacos_r.get_list_num()
    can_stop = start_name_num > 1
    if can_stop:
        is_loading = True
        logger.info(f'{nacos_r.service_port},is_loading_on')
    return json_message(json.dumps({"code":0,"msg":"is_loading_on"}, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/is_loading_off", methods=["POST"])
def is_loading_off_function():
    global is_loading
    is_loading = False
    logger.info(f'{nacos_r.service_port},is_loading_off')
    return json_message(json.dumps({"code":0,"msg":"is_loading_off"}, cls=MyEncoder, ensure_ascii=False))



@app.route("/nlp/predict/switch_model", methods=["POST"])
def switch_model():
    """
    模型上下线
    """
    global is_loading
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SwitchModel", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        flag = int(data.get('flag', 1))
        immediately_flag = int(data.get('imm', 1))  # 是否关闭延迟下线
        model_suffix = int(data.get('model_suffix', 0))  # 外呼模型id会加后缀
        logger.debug(f"从字典 get 数据: {model_id}, {type(model_id)}, {flag}, {type(flag)}")

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91001"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            args = {"model_id": model_id, "flag": flag, "immediately_flag": immediately_flag,
                    "model_suffix": model_suffix}
            # 获取当前时间
            start_name_num = nacos_r.get_list_num()
            can_stop = start_name_num>1
            if can_stop:
                # is_loading = True
                nacos_r.nacos_stop()
            nacos_start = datetime.now()
            nacos_start = nacos_start.strftime('%Y-%m-%d %H:%M:%S.%f')
            error_dict = model_manager.switch_model(**args)
            if can_stop:
                # is_loading = False
                nacos_r.nacos_fun()
            nacos_end = datetime.now()
            nacos_end = nacos_end.strftime('%Y-%m-%d %H:%M:%S.%f')
            error_dict["nacos_start"] = nacos_start
            error_dict["nacos_end"] = nacos_end
            result['main_info'] = error_dict
            result['start_name_num'] = start_name_num
            result['end_name_num'] = nacos_r.get_list_num()
            result['code'] = error_dict['code']

            # executor.submit(lambda p: model_manager.switch_model(**p), args)
            logger.debug(f"已提交上下线请求")

            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]

    except Exception as e:
        logger.error(f"上下线接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"NLP模型发布出错了，请联系服务商~错误代码：NLU91006"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0
    end = time.time()
    result.update({"api_time": end-start})
    info_out_data = {"service": "SwitchModel", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9911, debug=False)

# gunicorn -w 1 -b 0.0.0.0:9888 FAQHttpServer:app
# gunicorn -w 3 -b 0.0.0.0:9878 FAQHttpServer:app
