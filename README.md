## 1. 代码结构

* data: 数据集
* database: 数据库代码，目前只有 Redis
* model: 模型代码，例如 SentenceBert、SimCSE 等
* module: 模块代码，完成某一算法功能的模块，例如粗排、精排、正则匹配、文本相似度、分类等。每个模块都需要提供下面的接口:
    * train: 训练模型
    * load_model: 上线模型
    * offline_model: 下线模型
    * predict: 预测
    * get_data_generator: 根据批量输入，整理模型需要的数据格式
* main: 业务代码，通过使用、整合 module (一个或多个) 实现业务功能，例如检索需要整合粗排和精排的代码。 main 类需要提供下面的接口:
    * train: 训练
    * switch_model: 上下线模型
    * predict: 预测
    * init_model: 刚启动时检查已上线模型的记录，并上线对应模型
* pretrain_model: 预训练模型
* utils: 工具库
    * ModelManager.py: 模型管理代码，支持训练模型、发送模型 (Scp，Redis) 和下载模型 (Redis)
    * my_utils.py: 工具函数，工具类
    * logHelper.py: 日志
* setting: 配置
* XXXXX_HttpServer: 具体业务的 http 接口

## 2. 系统架构

![架构图](./doc/图片/项目结构.png)

从整体上看，项目由低到高分为四个层级 model、module、main 和 http server。 model 模型层，包含模型的实现代码；module 模块层，包含具体算法功能的实现代码、训练代码、上下线代码；main 业务层，整合 module 实现业务功能，业务逻辑； http server，主要是接收 http 请求，判断数据合法性。

ModelManager 类，里面包含训练队列管理，训练完模型发送，上下线模型消息转发等逻辑。

项目部署的时候分为训练端和预测端，由 setting.py 里面的 CLIENT 字段控制。通常训练端只有一个，而预测端有多个。训练端主要负责训练模型、接收上下线请求；预测端主要负责预测。区别如下:

* 训练端:
  * 训练模型: 训练完发送模型到预测端
  * 接收上下线请求: 收到上下线请求，把请求发到 redis 消息队列中，预测端会监听 redis 消息。
  * 测试: 和预测端的预测功能相同，但是训练端调测试是可以自动加载模型的。
* 预测端:
  * 监听 redis 上下线模型消息: 收到上下线消息就上下线对应的模型。
  * 预测: 进行预测，预测前要先上线模型。
  
### 2.1 model

model 模型层主要包括具体模型的实现代码，包括: 模型结构、损失函数、评估指标。

目前包含的模型:

* SimCSE
* RDrop
* SentenceBert
* TextRepresentationPretrainBert

### 2.2 module

module 模块层主要是具体算法功能的实现代码，包括：模型创建、数据加载、模型训练、上线模型、下线模型、预测。

module 中每一次训练的模型都有一个独一无二的 model_id， 即训练、上线、下线、预测都需要传入 model_id 用于区分。

module 需要提供的接口:

* train: 训练模型，从初始化模型 - 加载训练数据 - 训练模型的代码。
* load_model: 上线模型
* offline_model: 下线模型
* predict: 预测
* get_data_generator: module 内部使用的函数，主要是加载训练数据。

### 2.3 main

main 业务层主要是整合 module 实现业务功能，包括：module 初始化，调用 module 训练接口，监听上下线消息，调用 module 上下线接口，调用 module 预测接口。

main 中包含一个或多个 module，main 每一次训练都会调用它包含的所有 module 的训练函数，main 用 model_id 标志每一次训练，即所有 module 也是用该 model_id 标志。

main 需要提供的接口:
* train: 训练，主要是调用 module 的训练接口，并整合训练结果返回。
* switch_model: 上下线模型，当监听到上下线消息时就调用 module 的上下线接口
* predict: 预测，主要是调用 module 的预测接口，并整合预测结果。
* init_model: 刚启动时检查已上线模型的记录，并上线对应模型

### 2.4 http server

http server 主要是接收 http 请求，并检查请求数据合法性，是整个项目最早接到请求的地方。

* train: 训练请求，收到请求后会发送到 ModelManager，ModelManager 统一管理训练队列。
* switch_model: 上下线请求，收到请求后会发送到 ModelManager，ModelManager 再统一发上下线消息。
* predict: 预测，预测请求直接发送到 main 函数，不经过 ModelManager。

### 2.5 ModelManager

ModelManager 包含一个或多个 main 类，主要有以下功能:

* 训练管理
  * 接收 http server 的训练请求 (每个训练请求都有一个 model_id)，读取训练数据，根据数据确定要训练的 main 类，生成训练任务。
  * 训练队列管理: 每次只能执行一个训练任务 (每个训练任务对应一个 model_id)，要把训练任务加到队列中，按顺序执行。 
  * 训练速度快的任务不用排队，直接训练。例如只训练正则，就不用排队。
  * 异常重启。需要记录还没训练完成的任务，异常重启后要发送训练失败的消息。
* 上下线模型
  * 接收 http server 的上下线请求，并往 redis 发送消息通知预测端上下线模型。
* 发送模型 (针对训练端)
  * 训练结束后把模型发送给预测端
* 下载模型 (针对预测端)
  * 训练端发送模型之后，预测端用 ModelManager 下载模型。
  
## 3. 流程图

### 3.1 启动流程

![启动流程图](./doc/图片/启动流程.png)

因为项目可能会出现异常重启，重启后训练任务队列和已加载到内存的模型都丢失了，所以训练端启动后需要检查是否有未完成的训练记录，如果有要发送训练失败的消息；预测端重启要检查之前上线的模型记录，重新上线模型。

### 3.2 训练流程

![训练流程图](./doc/图片/训练流程.png)

训练请求的参数: 
* model_id: 每一次训练模型的唯一标识。
* data_key: 训练数据在 redis 里访问的 key 值。

训练端接收训练请求，训练结束后会把模型文件写入 redis，并通过 redis 消息队列告知预测端下载模型文件。

**训练端**训练流程具体步骤:
* http server 把训练请求发到 ModelManager。
* ModelManager 根据训练请求中的训练数据，判断要调用哪些 main 类的训练方法，并生成训练任务。
* 判断训练任务的训练速度:
  * 如果训练速度快，直接进入训练逻辑 (训练 - 发送模型文件到 redis - 发送下载模型的消息到 redis 消息队列 - 发送模型训练记录到 redis)。
  * 如果训练速度慢，加入训练队列，有一个线程管理训练队列，即依次读取训练任务，进入训练逻辑 (训练 - 发送模型文件到 redis - 发送下载模型的消息到 redis 消息队列 - 发送模型训练记录到 redis)。

**预测端**训练流程具体步骤:
* 启动后，预测端的 ModelManager 会监听 redis 消息队列里的下载模型消息。
* 监听到下载模型消息
* 从 redis 下载模型文件

### 3.3 上下线流程

![上下线流程图](./doc/图片/上下线流程.png)

上下线请求的参数:
* model_id: 每一次训练模型的唯一标识。
* flag: 0 表示下线模型，1 表示上线模型。

redis 消息队列的消息根据 channel (渠道) 区分，写消息是写入到具体的 channel 里，每一个 main 类的上下线消息都有对应的渠道，在 setting.py 的 SERVICE_CHANNEL_DICT 里定义。

上下线模型的请求是发送到**训练端**的，训练端接到上下线请求自己并不会进行上下线，而是往 redis 消息队列中发送上下线模型的消息，然后**预测端**监听到上下线消息进行模型的上下或者下线。

训练端接到上下线请求后，会根据 model_id 获取训练记录，得到需要上下线的 main 类，并往该 main 类对应的上下线消息渠道发送消息。

### 3.4 预测流程

![预测流程图](./doc/图片/预测流程.png)

训练端和预测端都可以执行预测功能，但是两个端的预测流程有一些区别。

训练端主要是供内部人员测试效果用的，因此预测前无需上线模型，训练端接到请求后会自动上线。同时训练端会记录自动上线的模型数量，数量超过一定的数值就把先前加载的模型下线，防止内存占用过多。

预测端是用于正式请求的，预测前需要先上线模型 (即调用上下线模型接口)。

## 4. API

### 4.1 正则 api

* 地址：http://39.99.199.142:9894
* 正则训练（/nlp/re_matching/train）
```json
    请求方式：POST
    数据类型：JSON
    请求参数： {   
                  "model_id":"test-2021-05-19",
                  "data_key":"test-2021-05-19"
              }
    返回参数：
              {"code": 0,"msg": "api succeed","data":{}}
```

* 正则匹配（/nlp/re_matching/predict）
```json
    请求方式：POST
    数据类型：JSON
    请求参数： {
                "model_id":"test-2021-05-19",
                "query":"哪位",
                "detail":"1",
                "labelIds":["5f34ad90"]
              }
    返回参数：
              {
                "code": 0,
                "model_id": "model1",
                "query": "请问余额理财赎回到账时间？",
                "msg": "成功",
                "match": 1,
                "data": 
                        [
                            {"match_labelId": "122323",
                            "match_re": ["余额.*赎回"，"到账.*时间"]},
                            {"match_labelId": "23456",
                             "match_re": ["余额.*理财"，"赎回.*时间"]}
                        ]
              }
```

* 正则上下线（/nlp/re_matching/switchmodel）
```json
    请求方式：POST
    数据类型：JSON
    请求参数： {   
                  "model_id":"test-2021-05-19",
                  "flag":1
              }
    返回参数：
              {"code": 0,"msg": "api succeed","data":{}}
```

### 4.2 相似度打分 api

* 地址：http://192.168.1.104:9887
* 训练（/nlp/similarity_score/train）
```json
    请求方式：POST
    数据类型：JSON
    请求参数： {   
                  "model_id":"model2",
                  "data_key":"faq_model2_all"
              }
    返回参数：
              {"code": 0,"msg": "api succeed"}
```

* 预测打分（/nlp/similarity_score/predict）
```json
    请求方式：POST
    数据类型：JSON
    请求参数： {
                "model_id":"model2",
                "query":"想问一下宾治2是多少颜色||请问缤智20的油耗以及颜色" // 两段文本用 || 分隔
              }
    返回参数：
              {
    			"code": 0,
    			"model_id": "pretrain",
    			"text1": "想问一下宾治2是多少颜色",
    			"text2": "请问缤智20的油耗以及颜色",
    			"score": 0.9448133111000061,
    			"msg": "预测成功"
			  }
```

* 上下线（/nlp/similarity_score/switchmodel）
```json
    请求方式：POST
    数据类型：JSON
    请求参数： {   
                  "model_id":"model2",
                  "flag":1 // 1 表示上线 0 表示下线
              }
    返回参数：
              {"code": 0,"msg": "api succeed"}
```