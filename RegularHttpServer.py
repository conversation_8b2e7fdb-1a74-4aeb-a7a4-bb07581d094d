# -*- coding: utf-8 -*-
import json
import re
import threading
import time
import traceback
from threading import Thread

from flask import Flask, request

import setting
import os
from main.RegularMain import RegularMain
from main.ClassifyMain import ClassifyMain
from setting import logger
from utils.my_utils import MyE<PERSON><PERSON>, json_message
from utils.ModelManager import ModelManager
# from module.HotWord import hot_word

app = Flask(__name__)
regular_main = RegularMain()
classify_main = ClassifyMain()
classify_time = 0
classify_n = 0
# 正则全文匹配开关
search_switch = True
regular_main.model.search_switch = search_switch

if setting.CLIENT == "train":
    model_manager = ModelManager(name="Regular", main_list=[regular_main, classify_main])
else:
    model_manager = ModelManager(name="Regular", main_list=[regular_main, classify_main])
train_main_mapper = {
    # 'callout':[search_main],
    # 'textbot':[regular_main, tfidf_sim_score_main, tfidf_sim_score_point_main, bert_sim_score_point_main, spell_check_main, search_main],
    'qualitycontrol': [regular_main,classify_main]
}


# 正则训练
@app.route("/nlp/re_matching/train", methods=["POST"])
def re_train():
    """
    正则训练
    """
    start = time.time()
    result = {"model_id": "", "sn": "", "code": 0, "msg": "api succeed"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "RegularTrain", "from": data}

        result["sn"] = data.get("sn", "")
        result["model_id"] = data.get("model_id", "")
        setting.request_id_var.set(result["sn"])
        logger.info(setting.request_id_var.get() + json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))
        model_id = data.get('model_id', '')
        data_key = data.get('data_key', '')
        if model_id and data_key:
            logger.debug(setting.request_id_var.get() + '[{}] 开始正则训练,data_key:{}'.format(model_id, data_key))
            model_manager.append_train_queue(model_id=model_id, data_key=data_key, model_list=[regular_main,classify_main])
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            logger.error(setting.request_id_var.get() + "请求数据出错,data:{}".format(data))
    except Exception as e:
        result["code"] = 1
        result["msg"] = '正则训练失败,报错:{}'.format(e)
        logger.error(setting.request_id_var.get() + '正则训练失败,错误：{}'.format(e))

    end = time.time()
    info_out_data = {"service": "RegularTrain", "out": result, "time": end - start}
    logger.info(setting.request_id_var.get() + json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


# 正则预测
@app.route("/nlp/re_matching/predict", methods=["POST"])
def re_predict():
    """
    正则预测
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "match": 1, "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "RegularPredict", "from": data}

        result["sn"] = data.get("sn", "")
        setting.request_id_var.set(result["sn"])

        logger.info(setting.request_id_var.get() + json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        detail = data.get('detail', 1)
        labelIds = data.get('label_ids', [])

        if model_id and query:
            res, msg = regular_main.predict(model_id, query, labelIds, detail)
            result['msg'] = msg
            result['data'] = res
            result["query"] = query
            result["model_id"] = model_id
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            logger.error(setting.request_id_var.get() + "请求数据出错,data:{}".format(data))
    except Exception as e:
        result["code"] = 1
        result["msg"] = '正则预测失败,报错:{}'.format(e)
        logger.error(setting.request_id_var.get() + '正则预测失败,错误：{}'.format(e))
        logger.error(traceback.print_exc())

    end = time.time()
    info_out_data = {"service": "RegularPredict", "out": result, "time": end - start}
    logger.info(setting.request_id_var.get() + json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


# 正则规则检测
@app.route("/nlp/re_matching/test", methods=["POST"])
def re_test():
    """
    正则规则检测
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "query": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "RegularTest", "from": data}

        result["sn"] = data.get("sn", "")
        setting.request_id_var.set(result["sn"])
        logger.info(setting.request_id_var.get() + json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))
        query = data.get('query', '')
        regexs = data.get('regexs', [])
        ners = data.get('ners', [])
        result["query"] = query

        if query == "":
            logger.error(setting.request_id_var.get() + "请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            predict_result, error_dict = regular_main.test(query, regexs, ners)
            result['data'] = predict_result
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(setting.request_id_var.get() + f"正则测试接口调用错误，报错:{e}", exc_info=True)
        result["code"] = 1
        result["msg"] = f"正则测试接口调用错误，报错:{e}"
        result["error_code"] = "NLU91008"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "RegularTest", "out": result, "time": end - start}
    logger.info(setting.request_id_var.get() + json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


# 模型上下线
@app.route("/nlp/re_matching/switch_model", methods=["POST"])
def re_switch_model():
    """
    模型上下线
    """
    start = time.time()
    result = {"model_id": "", "sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SwitchModel", "from": data}

        result["sn"] = data.get("sn", "")
        result["model_id"] = data.get("model_id", "")
        setting.request_id_var.set(result["sn"])
        logger.info(setting.request_id_var.get() + json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))
        model_id = data.get('model_id', '')
        flag = int(data.get('flag', 1))
        immediately_flag = int(data.get('imm', 1))  # 是否关闭延迟下线
        logger.debug(setting.request_id_var.get() + f"从字典 get 数据: {model_id}, {type(model_id)}, {flag}, {type(flag)}")

        if model_id == '':
            logger.error(setting.request_id_var.get() + "请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91001"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            error_dict = model_manager.switch_model(model_id=model_id, flag=flag, immediately_flag=immediately_flag)
            # 下线历史模型
            version_n = re.findall("_v(\d+)", model_id)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]

            elif version_n and flag:
                logger.debug(version_n)
                for n in range(int(version_n[0])):
                    history_model_id = re.sub(r"_v\d+", "_v" + str(n), model_id)
                    logger.debug("----------下线历史模型------------")
                    logger.debug(history_model_id)
                    thread_task = threading.Thread(target=model_manager.switch_model, args=(history_model_id, 0, 0))
                    thread_task.start()
    except Exception as e:
        logger.error(setting.request_id_var.get() + f"上下线接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"NLP模型发布出错了，请联系服务商~错误代码：NLU91006"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "SwitchModel", "out": result, "time": end - start}
    logger.info(setting.request_id_var.get() + json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


# 分类预测
@app.route("/nlp/classify/predict", methods=["POST"])
def classify_predict():
    """
    意图
    """
    start = time.time()
    global classify_time, classify_n
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SearchClassifyPredict", "from": data}

        result["sn"] = data.get("sn", "")
        setting.request_id_var.set(result["sn"])
        logger.info(setting.request_id_var.get() + json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))
        model_id = data.get('model_id', '')
        query = data.get('query', '')

        if model_id == '':
            logger.error(setting.request_id_var.get() + "请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif query == "":
            logger.error(setting.request_id_var.get() + "请求参数错误: query 为空")
            result["code"] = 1

            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            result = classify_main.predict(model_id, query, labelIds=None, return_search_result=False,topk=1)
            result["sn"] = data.get("sn", "")
    except Exception as e:
        logger.error(setting.request_id_var.get() + f"分类预测接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"分类预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91010"
        result["error_type"] = 0

    end = time.time()
    classify_time += (end-start)
    classify_n += 1
    info_out_data = {"service": "SearchClassifyPredict", "out": result, "time": end-start, "avg_time": (classify_time/classify_n)}
    logger.info(setting.request_id_var.get() + json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


# 状态查看
@app.route("/nlp/train_states", methods=["POST"])
def train_states():
    """
    状态查看
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "msg": "api succeed", "state": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "TrainStates", "from": data}

        result["sn"] = data.get("sn", "")
        setting.request_id_var.set(result["sn"])
        logger.info(setting.request_id_var.get() + json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))
        model_id = data.get('model_id', '')
        result["model_id"] = model_id

        if model_id == "":
            logger.error(setting.request_id_var.get() + "请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            res = model_manager.check_train_state(model_id=model_id)
            result['state'] = res
    except Exception as e:
        logger.error(setting.request_id_var.get() + f"状态查询接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"状态查询接口调用错误，报错:{e}"
        result["error_code"] = "NLU91007"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "TrainStates", "out": result, "time": end - start}
    logger.info(setting.request_id_var.get() + json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/model/delete", methods=["POST"])
def model_delete():
    """
    模型删除，有个坑先挖着，目前没有同步预测端的模型删除
    """
    start = time.time()
    result = {"sn": "", "code": 1, "model_id": "", "msg": "模型删除成功"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "ModelDelete", "from": data}

        result["sn"] = data.get("sn", "")
        setting.request_id_var.set(result["sn"])
        logger.info(setting.request_id_var.get() + json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))
        model_id = data.get('model_id', '')

        if model_id == '':
            logger.warning("模型删除出错: empty model_id")
            return json_message('{"code": -1, "model_id": "", "msg":"model_id为空"}')
        else:
            result = model_manager.model_delete_local(model_id)

    except Exception as e:
        result["code"] = -1
        result["msg"] = '模型删除出错'
        logger.error(setting.request_id_var.get() + '模型删除出错,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "ModelDelete", "out": result, "time": end - start}
    logger.info(setting.request_id_var.get() + json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route('/hot_word_count', methods=["POST"])
def hot_word_count():
    try:
        data = request.get_data(as_text=True)
        data = json.loads(data)
    except:
        data = request.form
    return hot_word.make_call_data(data)


@app.route("/nlp/regular/heartbeat", methods=["POST"])
def heartbeat():
    result = {"code": 200}
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9880, debug=False)
