# -*- coding: UTF-8 -*-

import json
import os
import time

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = "1"
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from database.REDIS import REDIS
from module.PreRank.ArcCSEPreRankReduceDim.ArcCSEPreRankPretrain import ArcCSEPreRankPretrain

if __name__ == "__main__":
    # 初始化
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    model = ArcCSEPreRankPretrain()
    R = REDIS()
    # data_test = R.get_data(f'faq_model1_all')[:]
    data_test = R.get_data(f'webank_train_data_1393_0902')[:]
    # data_test = R.get_data(f'faq_webank_hard_data')[:]
    # data_test = R.get_data(f'faq_813_1123_data')[:]
    # data_test = R.get_data(f'tencent_all_data_en')[:]

    # with open("/data/cbk/NLP_Platform/data/语义匹配/all_sts_pretrain_data_with_intent_anto_en_0712_7432760.json", "r", encoding="utf-8") as f:
    #     data_list = json.load(f)
    data_list = data_test[:10]

    # 预训练模型
    start = time.time()
    model_id = "230714_pretrain_tiny_emb_len_nocls_arccseN_arcTrue_triFalse_dclFalse_esimcse_shuffleb_batch64_epoch1_allsts_intent_anto_num_en_dim16"
    # model_id = "test_base_text2vec_base_chinese"
    model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{model.__class__.__name__}/")
    score = model.train(model_id=model_id, data=data_list, data_test=data_test, save_dir=model_save_dir, just_test=True, epoch=1, final_dim=16)
    train_time = time.time()-start
    print(f"训练得分: {score}, 耗时: {train_time}")
    print("wait")

    # 加载模型
    model.load_model(model_id=model_id, save_dir=model_save_dir)

    # 测试准确率
    start = time.time()
    # model.test_acc(model_id=model_id, data=data_test, topk=5, model_save_dir=model_save_dir, result_name="tencent_boc")
    model.test_acc(model_id=model_id, data=data_test, topk=5, model_save_dir=model_save_dir, result_name="webank_1393")
    # model.test_acc(model_id=model_id, data=data_test, topk=5, model_save_dir=model_save_dir, result_name="webank_hard")
    # model.test_acc(model_id=model_id, data=data_test, topk=5, model_save_dir=model_save_dir, result_name="813_1123")
    # model.test_acc(model_id=model_id, data=data_test, topk=5, model_save_dir=model_save_dir, result_name="tencent_boc_en")
    print(f"测试准确率耗时: {time.time()-start}, 训练耗时:{train_time}")
    print("wait")
