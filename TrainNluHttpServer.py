# -*- coding: utf-8 -*-
import json
import os
import time
import re
import threading
from hashlib import md5

from concurrent.futures import Thread<PERSON>oolExecutor
from utils.logHelper import log_initialize
from utils.my_utils import get_port_from_pid
import setting

severname = 'train_nlu'  # nlu_predict
logger = log_initialize(setting.logPath, severname)
setting.logger = logger
setting.CLIENT = 'train'
# nacos注册
from submodule_utils.nacos_register import NacosHelper
nacos_r = NacosHelper("nlp-nlu-" + setting.CLIENT)
nacos_r.get_config()

port = get_port_from_pid()
nacos_r.service_port = port

from flask import Flask, request, abort

os.environ['MKL_NUM_THREADS'] = str(setting.NUM_THREADS)
from database.REDIS import REDIS
from main.BertTextPairSimPointMain import BertTextPairSimPointMain
from main.ClassifyMain import ClassifyMain
from main.RegularMain import RegularMain
from main.SearchMainDataType import SearchMain
from main.SpellCheckMain import SpellCheckMain
# from main.PretrainSimilarityScoreMain import PretrainSimilarityScoreMain
from main.TfidfSimilarityScoreMain import TfidfSimilarityScoreMain
from model.CorpusRecommend.CorpusRecommend import CorpusRecommend
from model.LanguageJudge.LanguageJudgeRegular import LanguageJudgeRegular
from model.YesOrNoModel.YesOrNoModel import YesOrNoModel
from module.HotWord import hot_word
from main.ClusterMain import ClusterMain
from setting import logger
from utils.ModelManager import ModelManager
from utils.Prometheus import Prometheus
from utils.my_utils import MyEncoder, json_message, get_client_model_id, lang_symbol_to_lang_string
from utils.callback_utils import RECODER


rrr = RECODER()
rrr.check_train_record()

app = Flask(__name__)

redis = REDIS()
corpus_recommend = CorpusRecommend()
if setting.SQL_SETTING.get('db_type','mysql') == 'mysql':
    cluster_main = ClusterMain()
    from main.ClusterMain import cluster_recode
    train_thread = threading.Thread(target=cluster_recode.check_train_record)
    train_thread.start()
else:
    from main.ClusterMain_DM import ClusterMain_DM
    cluster_main = ClusterMain_DM()
language_judge_regular = LanguageJudgeRegular()
yes_or_no_model = YesOrNoModel()


train_main_mapper = {
    'smartspeech': ['regular_main', 'search_main'],
    'smartchat': ['regular_main', 'tfidf_sim_score_main', 'tfidf_sim_score_point_main', 'bert_sim_score_point_main',
                'spell_check_main', 'search_main'],
    'smartanalysis': ['search_main', 'regular_main', 'classify_main'],
    "smartcoach": ['bert_sim_score_point_main', 'regular_main', 'classify_main'],
    "smartllm": ['exam_main', 'speech_text_main'],
}


all_main_list = []
for USE_SCENARIO in setting.USE_SCENARIOS:
    if USE_SCENARIO == "smartllm":
        from route.ExamHttpServer import ppt_app
        app.register_blueprint(ppt_app)  # 注册蓝图
    if USE_SCENARIO == "filechat":
        from route.FilechatServer import file_chat
        app.register_blueprint(file_chat)  # 注册蓝图

    for main in train_main_mapper[USE_SCENARIO]:
        # exec(f"{main} = {smallmain_to_bigmain[main]}()")
        all_main_list.append(main)
all_main_list = list(set(all_main_list))
if "search_main" in all_main_list and "tfidf_sim_score_main" in all_main_list:
    all_main_list.remove("tfidf_sim_score_main")
logger.debug(f'服务初始化main_list:{all_main_list}')

if 'regular_main' in all_main_list:
    regular_main = RegularMain()
    if "smartanalysis" in setting.USE_SCENARIOS:
        regular_main.model.search_switch = True
if 'search_main' in all_main_list:
    from main import CorpusRecommendMain
    search_main = SearchMain()
    tfidf_sim_score_main = search_main
if 'classify_main' in all_main_list:
    classify_main = ClassifyMain()
if 'tfidf_sim_score_point_main' in all_main_list:
    tfidf_sim_score_point_main = TfidfSimilarityScoreMain()
if 'bert_sim_score_point_main' in all_main_list:
    bert_sim_score_point_main = BertTextPairSimPointMain()
if 'spell_check_main' in all_main_list:
    spell_check_main = SpellCheckMain()
if 'exam_main' in all_main_list:
    from main.ExamMain import ExamMain
    exam_main = ExamMain()
if 'speech_text_main' in all_main_list:
    from main.SpeechTextMain import SpeechTextMain
    speech_text_main = SpeechTextMain()


all_main_list = [eval(i) for i in all_main_list]
model_manager = ModelManager(name="FAQ", main_list=all_main_list)

# model_manager.info_record()
app.config['model_manager'] = model_manager

prometheus_key2msg = {
    "regular_request_count": "Total count of regular request",
    "sim_score_point_request_count": "Total count of point similarity score request"
}
prometheus = Prometheus(prometheus_key2msg, setting.USE_PORMETHEUS)
if setting.USE_PORMETHEUS:
    prometheus.redis.persistence_db = prometheus.redis.get_redis()
executor = ThreadPoolExecutor(10)
if setting.CLIENT == 'train':
    executor.submit(corpus_recommend.period_train)

search_time = 0
search_n = 0
classify_time = 0
classify_n = 0

"""
新版训练统一用一个model_id,因此当模型发布时预测端需要拷贝一次model_id对应的模型(拷贝后是model_id_switch),因此和预测端预测相关的预测请求需要调用model_id_switch。
下面的接口无需用到model_id_switch,直接用model_id即可:
metrics
/nlp/train
/nlp/switch_model
/nlp/copy_online_model
/nlp/download_model_redis
/nlp/train_states
/nlp/re_matching/test
/similarquerysearch/predict
下面的接口需要判断是训练端还是预测端,预测端使用model_id_switch:
/nlp/re_matching/predict
/nlp/search/predict
/nlp/classify/predict
/nlp/similarity_score/predict
/nlp/similarity_score_point/predict
/nlp/search/labeling
/nlp/classify/labeling
/nlp/spellcheck/predict
"""

nacos_r.nacos_fun()
@app.route("/nlp/train/metrics", methods=["GET"])
def metrics():
    info_data = {"msg": "收到metrics请求"}
    logger.info(json.dumps(info_data, cls=MyEncoder, ensure_ascii=False))
    try:
        return prometheus.get_metric()
    except Exception as e:
        logger.error(f"prometheus metrics 接口报错:{e}")
        result = {'code': 1, 'msg': f"prometheus metrics 接口报错:{e}"}
        return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/train", methods=["POST"])
def train():
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "Train", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        data_key = data.get('data_key', '')
        if not data_key:
            data_key = "nlp_train_data_" + md5(str(data).encode()).hexdigest()
        is_inc = int(data.get('is_inc', 0))
        is_inc = True if is_inc == 1 else False
        data_url = data.get('data_url', '')
        raw_data = data.get('data', '')
        scenario = data.get('scenario', '')
        callback_url = data.get('callback_url', '')

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "全量训练报错,model_id为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif data_url == '' and raw_data == '' and data_key == '':
            logger.error("请求参数错误: 训练数据来源为空")
            result["code"] = 1
            result["msg"] = "全量训练报错,训练数据来源为空"
            result["error_code"] = "NLU91002"
            result["error_type"] = 1
        elif scenario == '':
            logger.error("请求参数错误: scenario 为空")
            result["code"] = 1
            result["msg"] = "全量训练报错,scenario 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            logger.debug(f"开始训练{'增量' if is_inc else '全量'}模型,model_id:{model_id},data_key:{data_key}")
            model_list = train_main_mapper[scenario]
            model_list = [eval(f'{main}') for main in model_list]
            model_manager.append_train_queue(model_id=model_id, data_url=data_url, data=raw_data, data_key=data_key,
                                             is_inc=is_inc, model_list=model_list, callback_url=callback_url, scenario=scenario, sn=result.get("sn", ""))
    except Exception as e:
        logger.error(f"训练接口调用错误，报错:{e}", exc_info=True)
        result["code"] = 1
        result["msg"] = f"全量训练接口报错"
        result["error_code"] = "NLU91005"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "Train", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/train_cancel", methods=["POST"])
def train_cancel():
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "TrainCancel", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')

        if model_id == '':
            logger.error("取消训练任务,报错model_id为空")
            result["code"] = 1
            result["msg"] = "取消训练报错,model_id为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            logger.debug(f"取消训练任务,model_id:{model_id}")
            error_dict = model_manager.train_cancel(model_id=model_id)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"取消训练任务,报错:{e}")
        result["code"] = 1
        result["msg"] = f"取消训练报错"
        result["error_code"] = "NLU91005"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "TrainCancel", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))



switch_thread = ThreadPoolExecutor(1)
def one_thread_switch(**kwargs):
    task = switch_thread.submit(model_manager.switch_model, **kwargs)
    return task.result()

@app.route("/nlp/switch_model", methods=["POST"])
def switch_model():
    """
    模型上下线
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SwitchModel", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        flag = int(data.get('flag', 1))
        immediately_flag = int(data.get('imm', 1))  # 是否关闭延迟下线
        model_suffix = int(data.get('model_suffix', 0))  # 外呼模型id会加后缀
        logger.debug(f"从字典 get 数据: {model_id}, {type(model_id)}, {flag}, {type(flag)}")

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91001"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            args = {"model_id": model_id, "flag": flag, "immediately_flag": immediately_flag, "model_suffix": model_suffix}
            info_dict = one_thread_switch(**args)
            result.update(info_dict)
            result['code'] = 0
            result['model_id'] = model_id

            # executor.submit(lambda p: model_manager.switch_model(**p), args)
            logger.debug(f"已提交上下线请求")

            # 下线历史模型
            # version_n = re.findall("_v(\d+)", model_id)
            # # if error_dict["error_code"] != 0:
            # #     result["code"] = 1
            # #     result["msg"] = error_dict["error_msg"]
            # #     result["error_code"] = error_dict["error_code"]
            # #     result["error_type"] = error_dict["error_type"]
            #
            # if version_n and flag:
            #     logger.debug(version_n)
            #     for n in range(int(version_n[0])):
            #         history_model_id = re.sub(r"_v\d+", "_v" + str(n), model_id)
            #         logger.debug("----------下线历史模型------------")
            #         logger.debug(history_model_id)
            #         # 下线数量未知，数量上去了线程池不好使
            #         thread_task = threading.Thread(target=model_manager.switch_model, args=(history_model_id, 0, 0))
            #         thread_task.start()
    except Exception as e:
        logger.error(f"上下线接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"NLP模型发布出错了，请联系服务商~错误代码：NLU91006"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "SwitchModel", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/delete_model", methods=["POST"])
def delete_model():
    """
    删除模型
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "DeleteModel", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id_list = data.get('model_id', [])
        if not isinstance(model_id_list, list):
            model_id_list = [model_id_list]
        if model_id_list[0] +'_switch' not in model_id_list:
            model_id_list.append(model_id_list[0] + '_switch')
        logger.debug(f"从字典get数据:{model_id_list},{type(model_id_list)}")

        if model_id_list == '':
            logger.error("请求参数错误:model_id为空")
            result["code"] = 1
            result["msg"] = "NLP删除模型接口调用出错了,请联系服务商~错误代码：NLU91001"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            error_dict = model_manager.delete_model(model_id_list=model_id_list)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
            logger.debug(f"已提交删除模型请求")
    except Exception as e:
        logger.error(f"删除模型接口调用错误,报错:{e}")
        result["code"] = 1
        result["msg"] = f"NLP删除模型出错了,请联系服务商~错误代码：NLU91006"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "DeleteModel", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/train_states", methods=["POST"])
def train_states():
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "msg": "api succeed", "state": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "TrainStates", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        other_model_type = data.get('other_model_type', '')
        result["model_id"] = model_id

        if model_id == "":
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            res = model_manager.check_train_state(model_id=model_id, other_model_type=other_model_type)
            result['state'] = res
            train_queue = result.get('train_queue', [model_id])
            result['msg'] = '前面还有{}个模型等待训练'.format(len(train_queue[:train_queue.index(model_id)]))
    except Exception as e:
        logger.error(f"状态查询接口调用错误，报错:{e}",exc_info=True)
        result["code"] = 1
        result["msg"] = f"状态查询接口调用错误，报错:{e}"
        result["error_code"] = "NLU91007"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "TrainStates", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/train/search/predict", methods=["POST"])
def search_predict():
    """
    意图
    """
    start = time.time()
    global search_time, search_n
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "intent_result": {}, "faq_result": {}, "chat_result": {}, "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SearchPredict", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        return_num = int(data.get('return_num', 5))
        context_input_process = data.get('context_input_process', '')
        enterprise_id = data.get('enterprise_id', '')
        remove_stop_words_flag = True
        robot_model_type = data.get('robotModelType', '')
        history = data.get('history', [])


        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            model_id_client = get_client_model_id(model_id)
            logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
            result = search_main.predict(model_id_client, query, return_num=return_num, context_input_process=context_input_process,
                                         enterprise_id=enterprise_id, remove_stop_words_flag=remove_stop_words_flag,robot_model_type=robot_model_type,history=history)
            result["model_id"] = model_id
            result["sn"] = data.get("sn", "")
    except Exception as e:
        logger.error(f"检索预测接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"检索预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91009"
        result["error_type"] = 0

    end = time.time()
    search_time += (end-start)
    search_n += 1
    result.update({"api_time": end-start})
    info_out_data = {"service": "SearchPredict", "out": result, "time": end-start, "avg_time": (search_time/search_n)}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/train/re_matching/predict", methods=["POST"])
def re_predict():
    """
    正则交互测试
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "api succeed", "match": 1, "data": []}
    return_code = 200

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "RegularPredict", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        detail = int(data.get('detail', 1))
        enterprise_id = data.get('enterprise_id', '')
        result["query"] = query
        result["model_id"] = model_id
        labelIds = data.get('labelIds', [])

        if isinstance(labelIds, str):
            labelIds = json.loads(labelIds)
        if model_id == "":
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            model_id_client = get_client_model_id(model_id)
            logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
            predict_result, error_dict = regular_main.predict(model_id_client, query, labelIds, detail,
                                                              enterprise_id=enterprise_id)
            result['data'] = predict_result
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"正则预测接口调用错误，报错:{e}", exc_info=True)
        result["code"] = 1
        result["msg"] = f"正则预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91008"
        result["error_type"] = 0

    if return_code != 200:
        return abort(return_code)

    prometheus.add_count("regular_request_count")
    result.update({"api_time": time.time()-start})
    info_out_data = {"service": "RegularPredict", "out": result, "time": time.time() - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


# 语法合规检测
@app.route("/nlp/re_matching/test", methods=["POST"])
def re_test():
    """
    正则匹配
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "query": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "RegularTest", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        query = data.get('query', '')
        regexs = data.get('regexs', [])
        ners = data.get('ners', [])
        result["query"] = query

        if query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            predict_result, error_dict = regular_main.test(query, regexs, ners)
            result['data'] = predict_result
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"正则测试接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"正则测试接口调用错误，报错:{e}"
        result["error_code"] = "NLU91008"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "RegularTest", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route('/nlp/hot_word_count', methods=["POST"])
def hot_word_count():
    try:
        data = request.get_data(as_text=True)
        data = json.loads(data)
    except:
        data = request.form
    return hot_word.make_call_data(data)


@app.route("/nlp/heartbeat", methods=["POST"])
def heartbeat():
    result = {"code": 200}
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))



concurrent_num_counter = []


@app.route("/nlp/train/classify/predict", methods=["POST"])
def classify_predict():
    global concurrent_num_counter
    """
    意图交互测试
    """
    start = time.time()
    one_seconed_later = start - 1
    concurrent_num_counter = concurrent_num_counter[-10000:] + [start]

    global classify_time, classify_n
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": None}
    return_code = 200
    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "ClassifyPredict", "from": data}
        logger.info(str(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False)))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')
        labelIds = data.get('label_ids', [])

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: query 为空"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        else:
            model_id_client = get_client_model_id(model_id)
            logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
            result = classify_main.predict(model_id_client, query, labelIds=labelIds, return_search_result=False,
                                           topk=1)
            result["model_id"] = model_id
            result["sn"] = data.get("sn", "")
    except Exception as e:
        if return_code != 200:
            return abort(return_code)
        logger.error(f"分类预测接口调用错误，报错:{e}", exc_info=True)
        result["code"] = 1
        result["msg"] = f"分类预测接口调用错误，报错:{e}"
        result["error_code"] = "NLU91010"
        result["error_type"] = 0

    end = time.time()
    classify_time += (end - start)
    classify_n += 1
    result.update({"api_time": end-start})
    info_out_data = {"service": "ClassifyPredict", "out": result, "time": end - start,
                     "avg_time": (classify_time / classify_n)}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    result['consume_time'] = end - start
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route('/nlp/train/similarity_score_point/predict', methods=['POST'])
def sim_score_point_predict():
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SimScorePointPredict", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get("model_id", "pretrain")
        query = data.get("query", "")
        texts = query.split("||")
        if query == "":
            logger.error("请求参数错误: query 为空")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91003"
            result["error_code"] = "NLU91003"
            result["error_type"] = 1
        elif len(texts) < 2:
            logger.error("请求参数错误: query 格式错误，query 应该包含两段文本，用 \"||\" 隔开")
            result["code"] = 1
            result["msg"] = "NLP模型调用出错了，请联系服务商~错误代码：NLU91004"
            result["error_code"] = "NLU91004"
            result["error_type"] = 1
        else:
            query = texts[0].strip()
            point_list = [t.strip() for t in texts[1:]]
            # result = tfidf_sim_score_point_main.predict(model_id=model_id, query=query, point_list=point_list)
            model_id_client = get_client_model_id(model_id)
            logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
            result = bert_sim_score_point_main.predict(model_id=model_id_client, query=query, point_list=point_list)
            result["model_id"] = model_id
            result["sn"] = data.get("sn", "")
    except Exception as e:
        logger.error(f"相似度预测接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"NLP预测服务暂不可用，请联系服务商~错误代码：NLU91011"
        result["error_code"] = "NLU91011"
        result["error_type"] = 0

    prometheus.add_count("sim_score_point_request_count")
    result.update({"api_time": time.time()-start})
    info_out_data = {"service": "SimScorePointPredict", "out": result, "time": time.time() - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/train/pos_neg_classify/predict", methods=["POST"])
def pos_neg_classify_predict():
    start = time.time()
    global search_time, search_n
    label_str_dict = {0: "肯定", 1: "否定", 2: "中性"}
    result = {"sn": "", "code": 0, "msg": "", "label": 2, "label_str": "中性", "score": 0}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "PosNegClassify", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        question = data.get('question', '是不是？')
        if question == '':
            question = '是不是？'
        answer = data.get("answer", "")

        if question == '':
            logger.error("请求参数错误:question为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:question为空"
            result["label"] = 2
            result["label_str"] = label_str_dict[result["label"]]
            result["score"] = 0
        elif answer == "":
            logger.error("请求参数错误:answer为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:answer为空"
            result["label"] = 2
            result["label_str"] = label_str_dict[result["label"]]
            result["score"] = 0
        else:
            pred_result = yes_or_no_model.predict(question=question, answer=answer)
            result["score"] = float(pred_result[0][0]["score"])
            if result["score"] < 0.5:
                result["label"] = 2
            else:
                result["label"] = int(pred_result[0][0]["label_id"])
            result["label_str"] = label_str_dict[result["label"]]
            result["sn"] = data.get("sn", "")
    except Exception as e:
        logger.error(f"二分类预测接口调用错误,报错:{e}")
        result["code"] = 1
        result["msg"] = f"二分类预测接口调用错误,报错:{e}"
        result["label"] = 2
        result["label_str"] = label_str_dict[result["label"]]
        result["score"] = 0

    end = time.time()
    result.update({"api_time": end-start})
    info_out_data = {"service": "PosNegClassify", "out": result, "time": end-start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route('/nlp/search/labeling', methods=['POST'])
def search_labeling():
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}
    try:
        logger.debug("接到标注请求")

        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SearchLabeling", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        logger.debug(f"标注请求参数: {data}")

        result["sn"] = data.get("sn", "")
        model_id = data.get("model_id", '')
        data_key = data.get("data_key", '')
        data_key = data_key.strip()

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif data_key == '':
            logger.error("请求参数错误: data_key 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: data_key 为空"
            result["error_code"] = "NLU91002"
            result["error_type"] = 1
        else:
            logger.debug(f"[{model_id}] 开始标注")
            model_id_client = get_client_model_id(model_id)
            logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
            args = {"model_id": model_id_client, "data_key": data_key}
            executor.submit(lambda p: search_main.labeling(**p), args)
    except Exception as e:
        logger.error(f"检索标注接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"检索标注接口调用错误，报错:{e}"
        result["error_code"] = "NLU91012"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "SearchLabeling", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route('/nlp/classify/labeling', methods=['POST'])
def classify_labeling():
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}
    try:
        logger.debug("接到标注请求")

        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SearchClassifyLabeling", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        logger.debug(f"标注请求参数: {data}")

        result["sn"] = data.get("sn", "")
        model_id = data.get("model_id", '')
        data_key = data.get("data_key", '')
        data_key = data_key.strip()

        if model_id == '':
            logger.error("请求参数错误: model_id 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: model_id 为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif data_key == '':
            logger.error("请求参数错误: data_key 为空")
            result["code"] = 1
            result["msg"] = "请求参数错误: data_key 为空"
            result["error_code"] = "NLU91002"
            result["error_type"] = 1
        else:
            logger.debug(f"[{model_id}] 开始标注")
            model_id_client = get_client_model_id(model_id)
            logger.debug(f"获取服务对应的client_model_id:{model_id_client},model_id:{model_id}")
            args = {"model_id": model_id_client, "data_key": data_key}
            executor.submit(lambda p: classify_main.labeling(**p), args)
    except Exception as e:
        logger.error(f"分类标注接口调用错误，报错:{e}")
        result["code"] = 1
        result["msg"] = f"分类标注接口调用错误，报错:{e}"
        result["error_code"] = "NLU91013"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "SearchClassifyLabeling", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/similarquerysearch/predict", methods=["POST"])
def similarquerysearch_predict():
    start = time.time()
    result = {'code': 0, 'msg': '预测成功'}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "SimilarQuerySearch", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        query = data.get('query', '')
        n = int(data.get('topn', 300))
        knowledge_id = data.get("knowledge_id", "")
        if not query:
            result['code'] = 1
            result['msg'] = 'query 为空'
        else:
            similar_query = corpus_recommend.predict(query, n, knowledge_id=knowledge_id)
            result = {'code': 0, "similar_query": similar_query, "raw_query": query,
                      "time_consume": time.time() - start}
    except Exception as e:
        logger.error(f"相似问搜索报错:{e}")
        result['code'] = 1
        result['msg'] = f'相似问搜索报错:{e}'
        result["msg"] = result["msg"][:100]

    end = time.time()
    result.update({"api_time": end-start})
    info_out_data = {"service": "SimilarQuerySearch", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/cluster/predict", methods=["POST"])
def cluster_predict():
    start = time.time()
    result = {'code': 0, 'msg': '聚类预测成功'}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "Cluster", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        data_key = data.get('data_key', '')
        executor.submit(cluster_main.predict, data_key)

    except Exception as e:
        logger.error(f"聚类报错:{e}")
        result['code'] = 1
        result['msg'] = f'聚类报错:{e}'
        result["msg"] = result["msg"][:100]

    end = time.time()
    info_out_data = {"service": "Cluster", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

@app.route("/nlp/conversation_cluster/predict", methods=["POST"])
def conversation_cluster_predict():
    start = time.time()
    result = {'code': 0, 'msg': '聚类预测成功'}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "Cluster", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        executor.submit(cluster_main.conversation_predict, data)

    except Exception as e:
        logger.error(f"聚类报错:{e}")
        result['code'] = 1
        result['msg'] = f'聚类报错:{e}'
        result["msg"] = result["msg"][:100]

    end = time.time()
    info_out_data = {"service": "Cluster", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/gpt/corpusrecommend", methods=["POST"])
def corpusrecommend():
    start = time.time()
    result = {'code': 0, 'msg': '聚类预测成功'}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "CorpusRecommend", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        theme_describe = data.get('theme_describe', '')
        redis_key = data.get('redis_key', '')
        callback_url = data.get('callback_url', '')
        one_data = data.get('data', [])

        if redis_key:
            executor.submit(CorpusRecommendMain.offline, redis_key, theme_describe, callback_url, data)
        else:
            model_id = 'corpusrecommend_' + model_id
            input_data = one_data[0]
            title = input_data['title']
            labelData = input_data.get('labelData', '')
            answer = input_data.get('answer', '')
            output = CorpusRecommendMain.realtime(title=title, labelData=labelData, answer=answer, theme=theme_describe,
                                                  model_id=model_id)
            one_data[0].update({'recommend': '||'.join(output)})
            result['result'] = one_data

    except Exception as e:
        logger.error(f"语料推荐报错:{e}", exc_info=True)
        result['code'] = 1
        result['msg'] = f'语料推荐报错:{e}'
        result["msg"] = result["msg"][:100]

    end = time.time()
    info_out_data = {"service": "CorpusRecommend", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/filechat/train/language_judge", methods=["POST"])
def language_judge():
    """
    判断文本是否是给定语言的
    """
    start = time.time()
    result = {"sn": "", "code": 0, "query": "", "msg": "", "is_lang": 0}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "language_judge", "from": str(data)[0:100]}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        query = data.get('query', '')
        lang = data.get('lang', 'chs')
        list_flag = True
        if isinstance(lang, list):
            lang_list = lang
        else:
            lang_list = lang
            list_flag = False
        result["query"] = query
        # lang: chs、cht、yue、en

        if query == '':
            logger.warning("预测错误: empty query")
            return json_message('{"code":1, "msg":"empty query"}')
        is_lang_list = []
        for lang in lang_list:
            lang = lang_symbol_to_lang_string(lang)
            is_lang = language_judge_regular.language_judge(text=query, lang=lang)
            is_lang_list.append(1 if is_lang else 0)
        result["is_lang"] = is_lang_list if list_flag else is_lang_list[0]
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'language_judge错误,报错:{e}'
        logger.error(f'language_judge错误,报错:{e}', exc_info=True)

    end = time.time()
    info_out_data = {"service": "language_judge", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/gpt/avalible", methods=["POST"])
def avalible():
    is_ok = CorpusRecommendMain.is_llm_available()
    if is_ok:
        return json_message(json.dumps({'code': 0}, cls=MyEncoder, ensure_ascii=False))
    else:
        return json_message(json.dumps({'code': 1}, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/task_check", methods=["POST"])
def task_check():
    info_in_data = {"service": "TaskCheck", "from": ""}
    logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))
    task_list = model_manager.check_task_list()
    info_out_data = {"service": "TaskCheck", "out": ""}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    if task_list:
        return json_message(json.dumps({'code': 0, "task_list": task_list}, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps({'code': 1, "task_list": []}, cls=MyEncoder, ensure_ascii=False))


@app.route("/nlp/download_model_redis", methods=["POST"])
def download_model_redis():
    """
    模型下载(Redis)
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "DownloadModelRedis", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        all_dirs = data.get('all_dirs', [])
        dir_to_keys = data.get('dir_to_keys', {})

        if model_id == '':
            logger.error("请求参数错误:model_id为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:model_id为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif len(all_dirs) == 0:
            logger.error("请求参数错误:all_dirs为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:all_dirs为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        elif len(dir_to_keys) == 0:
            logger.error("请求参数错误:dir_to_keys为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:dir_to_keys为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            error_dict = model_manager.download_model_redis(model_id=model_id, all_dirs=all_dirs,
                                                            dir_to_keys=dir_to_keys)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
    except Exception as e:
        logger.error(f"Redis下载模型接口调用错误,报错:{e}")
        result["code"] = 1
        result["msg"] = f"Redis下载模型接口调用错误"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "DownloadModelRedis", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

from module.ContextInput.NGram.NGramContextInput import NGramContextInput
cut_text = NGramContextInput()
@app.route("/nlp/tokenize", methods=["POST"])
def tokenize():
    """
    模型下载(Redis)
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": "api succeed", "data": {}}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "tokenize", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result.update(data)
        text = data.get('text', )

        if text == '':
            logger.error("请求参数错误:text为空")
            result["code"] = 1
            result["msg"] = "请求参数错误:text为空"
            result["error_code"] = "NLU91001"
            result["error_type"] = 1
        else:
            result['data'] = cut_text.jieba_cut(text)
    except Exception as e:
        logger.error(f"tokenize接口调用错误,报错:{e}")
        result["code"] = 1
        result["msg"] = f"tokenize接口调用错误"
        result["error_code"] = "NLU91006"
        result["error_type"] = 0

    end = time.time()
    info_out_data = {"service": "tokenize", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))



if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9432, debug=False)

# gunicorn -w 1 -b 0.0.0.0:9888 FAQHttpServer:app
# gunicorn -w 3 -b 0.0.0.0:9878 FAQHttpServer:app
