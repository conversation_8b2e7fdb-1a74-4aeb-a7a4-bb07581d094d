import time

import pandas as pd
import requests
from multiprocessing.pool import Thread<PERSON>ool as Pool # from multiprocessing import Pool

# url = 'http://*************:8080//nlp/classify/predict'
# url = 'http://*************:8080/nlp/classify/predict'
# url = 'http://127.0.0.1:2000/nlp/classify/predict'

url = 'http://*************:7778/nlp/classify/predict'


# callout_test_3,callout_test_2
import json
result = []
def poster(url, data):
    try:
        start = time.time()
        output = requests.post(url, json.dumps(data),timeout=2)
        if output.status_code==200 and output!= None:
            output.encoding = 'utf-8'
            output = output.json()
            try:
                output['transform_time'] = time.time() - start - output['consume_time']
            except:
                pass
            # wb_data.encoding = 'gbk'
            result.append(output)
        else:
            out = output.status_code if output!=None else None
            result.append({'code':out})
        # if output['code']!=0:
    except:
        import traceback
        print(output)
        print(traceback.print_exc())
        # result.append({'code': None})
        data['code'] = output.status_code
        result.append(data)
    return output

def thread_post( url, data_list, pool_size):
    pool = Pool(pool_size)
    for num, data in enumerate(data_list):
        pool.apply_async(poster, (url, data,))
    pool.close()
    pool.join()

# data_list = [
#                 {'model_id':'callout_test','query':'你好'},
#         {'model_id':'callout_test_3','query':'你好'},
#              {'model_id':'callout_test_1','query':'你好'},
#              {'model_id':'callout_test_4','query':'你好'}
#              ]*1

data_list = []
for i in range(3):
    data_list.append({'model_id':f'callout_test_{i}','query':'你好'})
thread_post(url,data_list,1)


data = []
for i in result:
    data.append(i)
df = pd.DataFrame(data)

##########
# data_list = [{'model_id':'callout_test_4','query':'你好'}]*20
# thread_post(url,data_list,5)
# data = []
# for i in result:
#     data.append(i)
# df = pd.DataFrame(data)


