import json
import os
print(os.getcwd())
print(os.listdir())
import sys

# sys.path.insert(0,'/data/sammy/project/aicc-nlu')

import setting

model_id = 'pytest_nlu_1_textbot'
#训练端初始化耗时
@profile
def train_server_memories_consume():
    # -*- coding: utf-8 -*-
    import json
    import os
    import time
    import re
    import threading
    from concurrent.futures import ThreadPoolExecutor
    from utils.logHelper import log_initialize
    import setting

    severname = 'train_nlu'  # nlu_predict
    logger = log_initialize(setting.logPath, severname)
    setting.logger = logger
    setting.CLIENT = 'train'
    from flask import Flask, request, abort

    os.environ['MKL_NUM_THREADS'] = str(setting.NUM_THREADS)
    from database.REDIS import REDIS
    from main.BertTextPairSimPointMain import BertTextPairSimPointMain
    from main.ClassifyMain import ClassifyMain
    from main.RegularMain import RegularMain
    from main.SearchMainDataType import SearchMain
    from main.SpellCheckMain import Spell<PERSON><PERSON>ck<PERSON>ain
    # from main.PretrainSimilarityScoreMain import PretrainSimilarityScoreMain
    from main.TfidfSimilarityScoreMain import TfidfSimilarityScoreMain
    from main.TfidfSimilarityScorePointMain import TfidfSimilarityScorePointMain
    from model.CorpusRecommend.CorpusRecommend import CorpusRecommend
    from model.LanguageJudge.LanguageJudgeRegular import LanguageJudgeRegular
    from module.HotWord import hot_word
    from main.ClusterMain import ClusterMain
    from main import CorpusRecommendMain
    from setting import logger
    from utils.ModelManager import ModelManager
    from utils.Prometheus import Prometheus
    from utils.my_utils import MyEncoder, json_message, get_client_model_id, lang_symbol_to_lang_string
    from utils.callback_utils import RECODER

    rrr = RECODER()
    rrr.check_train_record()

    app = Flask(__name__)

    redis = REDIS()
    corpus_recommend = CorpusRecommend()
    cluster_main = ClusterMain()
    language_judge_regular = LanguageJudgeRegular()

    train_main_mapper = {
        'callout': ['regular_main', 'search_main'],
        'textbot': ['regular_main', 'tfidf_sim_score_main', 'tfidf_sim_score_point_main', 'bert_sim_score_point_main',
                    'spell_check_main', 'search_main'],
        'smartqc': ['regular_main', 'classify_main', 'search_main'],
        "kaopei": ['bert_sim_score_point_main', 'regular_main']
    }
    # nacos注册

    all_main_list = []
    for USE_SCENARIO in setting.USE_SCENARIOS:
        for main in train_main_mapper[USE_SCENARIO]:
            # exec(f"{main} = {smallmain_to_bigmain[main]}()")
            all_main_list.append(main)
    all_main_list = list(set(all_main_list))
    logger.debug(f'服务初始化main_list:{all_main_list}')

    if 'regular_main' in all_main_list:
        regular_main = RegularMain()
        if "smartqc" in setting.USE_SCENARIOS:
            regular_main.model.search_switch = True
    if 'search_main' in all_main_list:
        search_main = SearchMain()
    if 'classify_main' in all_main_list:
        classify_main = ClassifyMain()
    if 'tfidf_sim_score_main' in all_main_list:
        tfidf_sim_score_main = SearchMain()
    if 'tfidf_sim_score_point_main' in all_main_list:
        tfidf_sim_score_point_main = TfidfSimilarityScoreMain()
    if 'bert_sim_score_point_main' in all_main_list:
        bert_sim_score_point_main = BertTextPairSimPointMain()
    if 'spell_check_main' in all_main_list:
        spell_check_main = SpellCheckMain()
    local = locals()
    print(local,'locallocallocal')
    all_main_list = [eval(i,{},local) for i in all_main_list]
    model_manager = ModelManager(name="FAQ", main_list=all_main_list)

    # model_manager.info_record()

    prometheus_key2msg = {
        "regular_request_count": "Total count of regular request",
        "sim_score_point_request_count": "Total count of point similarity score request"
    }
    prometheus = Prometheus(prometheus_key2msg, setting.USE_PORMETHEUS)
    if setting.USE_PORMETHEUS:
        prometheus.redis.persistence_db = prometheus.redis.get_redis()
    executor = ThreadPoolExecutor(10)
    if setting.CLIENT == 'train':
        executor.submit(corpus_recommend.period_train)

# @profile
# def textbox_memories_consume():
#     from main.SearchMainDataType import SearchMain
#     search_main = SearchMain()
#     search_main._load_model(model_id)

# if __name__=='__main__':
train_server_memories_consume()
# textbox_memories_consume()