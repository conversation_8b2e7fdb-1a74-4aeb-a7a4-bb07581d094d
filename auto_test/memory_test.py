

@profile
def haha():
    try:
        # -*- coding: utf-8 -*-
        import json
        import os
        import time
        import re
        import threading
        from concurrent.futures import ThreadPoolExecutor
        from utils.logHelper import log_initialize
        import setting

        severname = 'train_nlu'  # nlu_predict
        logger = log_initialize(setting.logPath, severname)
        setting.logger = logger
        setting.CLIENT = 'train'
        # nacos注册
        # from utils.nacos_register import NacosHelper
        # nacos_r = NacosHelper("nlp-nlu-" + setting.CLIENT)
        # nacos_r.nacos_fun()

        from flask import Flask, request, abort, g

        os.environ['MKL_NUM_THREADS'] = str(setting.NUM_THREADS)
        from database.REDIS import REDIS
        from main.BertTextPairSimPointMain import BertTextPairSimPointMain
        from main.ClassifyMain import ClassifyMain
        from main.RegularMain import RegularMain
        from main.SearchMainDataType import SearchMain
        # from main.SpellCheckMain import Spell<PERSON>heckMain
        from main.RetrievalMain import RetrievalMain
        # from main.PretrainSimilarityScoreMain import PretrainSimilarityScoreMain
        from main.TfidfSimilarityScoreMain import TfidfSimilarityScoreMain
        from main.TfidfSimilarityScorePointMain import TfidfSimilarityScorePointMain
        from model.CorpusRecommend.CorpusRecommend import CorpusRecommend
        from model.LanguageJudge.LanguageJudgeRegular import LanguageJudgeRegular
        from module.HotWord import hot_word
        from main.ClusterMain import ClusterMain
        from main import CorpusRecommendMain
        from main.ExamMain import ExamMain
        from main.SpeechTextMain import SpeechTextMain
        from setting import logger
        from utils.ModelManager import ModelManager
        from utils.Prometheus import Prometheus
        from utils.my_utils import MyEncoder, json_message, get_client_model_id, lang_symbol_to_lang_string, random_id
        from utils.callback_utils import RECODER
        from route.ExamHttpServer import ppt_app
        from route.FilechatServer import file_chat
        rrr = RECODER()
        rrr.check_train_record()

        app = Flask(__name__)

        redis = REDIS()
        corpus_recommend = CorpusRecommend()
        cluster_main = ClusterMain()
        language_judge_regular = LanguageJudgeRegular()

        train_main_mapper = {
            'smartspeech': ['regular_main', 'search_main'],
            'smartchat': ['search_main'],
            'smartanalysis': ['search_main', 'regular_main', 'classify_main'],
            "smartcoach": ['bert_sim_score_point_main', 'regular_main', 'exam_main', 'speech_text_main'],
            "smartllm": ['exam_main', 'speech_text_main'],
            "filechat": ['retrieval_main']
        }


        all_main_list = []
        for USE_SCENARIO in setting.USE_SCENARIOS:
            if USE_SCENARIO == "smartllm":
                app.register_blueprint(ppt_app)  # 注册蓝图
            if USE_SCENARIO == "filechat":
                app.register_blueprint(file_chat)  # 注册蓝图

            for main in train_main_mapper[USE_SCENARIO]:
                # exec(f"{main} = {smallmain_to_bigmain[main]}()")
                all_main_list.append(main)
        all_main_list = list(set(all_main_list))
        logger.debug(f'服务初始化main_list:{all_main_list}')
        ALL_MAIN_LIST = []
        if 'regular_main' in all_main_list:
            regular_main = RegularMain()
            if "smartanalysis" in setting.USE_SCENARIOS:
                regular_main.model.search_switch = True
            ALL_MAIN_LIST.append(regular_main)
        if 'search_main' in all_main_list:
            search_main = SearchMain()
            search_main._load_model('1742078468327391232_switch')  # 30M
            search_main._load_model('1668526485380337666_switch')  #368M
            search_main._load_model('519467131_switch')            #276M
            ALL_MAIN_LIST.append(search_main)

        if 'classify_main' in all_main_list:
            classify_main = ClassifyMain()
            ALL_MAIN_LIST.append(classify_main)

        if 'tfidf_sim_score_main' in all_main_list:
            tfidf_sim_score_main = SearchMain()
            ALL_MAIN_LIST.append(tfidf_sim_score_main)

        if 'tfidf_sim_score_point_main' in all_main_list:
            tfidf_sim_score_point_main = TfidfSimilarityScoreMain()
            ALL_MAIN_LIST.append(tfidf_sim_score_point_main)

        if 'bert_sim_score_point_main' in all_main_list:
            bert_sim_score_point_main = BertTextPairSimPointMain()
            ALL_MAIN_LIST.append(bert_sim_score_point_main)

        # if 'spell_check_main' in all_main_list:
            # spell_check_main = SpellCheckMain()
            # ALL_MAIN_LIST.append(spell_check_main)

        if 'exam_main' in all_main_list:
            exam_main = ExamMain()
            ALL_MAIN_LIST.append(exam_main)

        if 'speech_text_main' in all_main_list:
            speech_text_main = SpeechTextMain()
            ALL_MAIN_LIST.append(speech_text_main)

        if 'exam_main' in all_main_list:
            exam_main = ExamMain()
            ALL_MAIN_LIST.append(exam_main)

        if 'speech_text_main' in all_main_list:
            speech_text_main = SpeechTextMain()
            ALL_MAIN_LIST.append(speech_text_main)

        if 'retrieval_main' in all_main_list:
            retrieval_main = RetrievalMain()
            ALL_MAIN_LIST.append(retrieval_main)



        all_main_list = ALL_MAIN_LIST
        # model_manager = ModelManager(name="FAQ", main_list=all_main_list)

        # model_manager.info_record()
        # app.config['model_manager'] = model_manager

        prometheus_key2msg = {
            "regular_request_count": "Total count of regular request",
            "sim_score_point_request_count": "Total count of point similarity score request"
        }
        prometheus = Prometheus(prometheus_key2msg, setting.USE_PORMETHEUS)
        if setting.USE_PORMETHEUS:
            prometheus.redis.persistence_db = prometheus.redis.get_redis()
        executor = ThreadPoolExecutor(10)
        if setting.CLIENT == 'train':
            executor.submit(corpus_recommend.period_train)
    except:
        logger.error('cuowu',exc_info=True)
haha()