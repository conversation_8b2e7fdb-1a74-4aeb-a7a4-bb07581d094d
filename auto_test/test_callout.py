import os.path

import pandas as pd

from database.REDIS import REDIS
import json
import time
import setting
import requests

########测试请修改setting.ini成如下配置#############
# callout_max_load_model_nums = 3
# callout_model_exist_hour = 0.01
# client = predict
###################################################
TRAIN_IP = "127.0.0.1"
NLP_TRAIN_PORT = "9888"
TRAIN_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/train"
SWITCH_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/switch_model"
STATUS_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/train_states"

NLP_PREDICT_PORT = "9889"
CLASS_PREDICT_URL = f"http://{TRAIN_IP}:{NLP_PREDICT_PORT}/nlp/search_classify/predict"
REG_PREDICT_URL = f"http://{TRAIN_IP}:{NLP_PREDICT_PORT}/nlp/re_matching/predict"

class TestClassCallout:
    def __init__(self):
        self.result = None

    def test_callout(self):
        self.model_ids = self.train()
        #模型
        # self.check_auto_loadmodel(CLASS_PREDICT_URL)
        # self.check_404_return(CLASS_PREDICT_URL)
        self.check_auto_online_train_model(CLASS_PREDICT_URL)

        #正则
        # self.check_auto_loadmodel(REG_PREDICT_URL)
        # self.check_404_return(REG_PREDICT_URL)
        # self.check_auto_online_train_model(REG_PREDICT_URL)

    def check_auto_loadmodel(self,url):
        print('='*100)
        print('测试模型自动上线!')
        # 并发请求一个模型
        self.result = []
        post_time = 500
        self.thread_post(url, [{"model_id": self.model_ids[0], "query": "你好!"}] * post_time, 100)
        code_count = pd.Series([i.status_code for i in self.result]).value_counts()
        assert len(code_count)==1 and code_count.tolist()[0]==post_time
        print('测试通过!!!')

    def check_404_return(self,url):
        print('='*100)
        print('测试模型达到上限后返回404!')
        all_post_data = []
        post_time = 10
        for model_id in self.model_ids:
            all_post_data += [{"model_id": model_id, "query": "你好!"}] * post_time
        self.result = []
        self.thread_post(url,all_post_data,10)
        code_count = pd.Series([i.status_code for i in self.result]).value_counts()
        print(code_count,'code_countcode_count')
        assert len(code_count)==2 and code_count.loc[404]==post_time
        print('测试通过!!!')

    def check_auto_online_train_model(self,url):
        print('='*100)
        print('测试上线模型训练后自动上线')
        model_id = self.model_ids[0]
        old_model_pred = self.poster(url,{'model_id':model_id,'query':'天气不错'}).json()

        # #测试自动更新新训练的上线模型
        with open("./nlu_data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        data += [{
            "labelId": "4",
            "title": "天气不错",
            "keywords": "天气.*||天气不错",
            "labelData": "天气很好||天气不错||天气很棒||天气非常棒||这个天气很不错"
        }]
        self.redis.set_data(key="pytest_nlu_train_data", msg=data)

        tag_path = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'download_finish',"download_finish.tag")
        old_tag_time = os.path.getmtime(tag_path)
        requests.post(TRAIN_URL, {'model_id': model_id, "data_key": 'pytest_nlu_train_data'})
        # 等待训练
        while True:
            print(os.path.getmtime(tag_path), old_tag_time, 'aaaaa')
            if old_tag_time != os.path.getmtime(tag_path):
                break
            else:
                time.sleep(1)
        time.sleep(10)
        new_model_pred = self.poster(url,{'model_id':model_id,'query':'天气不错'}).json()
        print(new_model_pred,'new_model_prednew_model_pred')
        print(old_model_pred,'old_model_predold_model_pred')
        assert new_model_pred!=old_model_pred
        print('测试通过!!!')


    def train(self):
        model_id = 'callout_test'
        #########训练###########
        self.redis = REDIS()
        with open("./nlu_data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        self.redis.set_data(key="pytest_nlu_train_data", msg=data)
        data = {'model_id':model_id,"data_key":'pytest_nlu_train_data'}
        tag_path = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'download_finish',"download_finish.tag")
        if os.path.exists(tag_path):os.remove(tag_path)

        r = requests.post(TRAIN_URL, data)
        print(r.json())

        #等待训练
        while True:
            if os.path.exists(tag_path):
                break
            else:
                time.sleep(1)
        ########训练###########

        #复制训练模型
        model_dir = os.path.join(setting.SAVE_MODEL_DIR,model_id)
        cp_model_list = [f'{model_dir}_{i}' for i in range(setting.CALLOUT_MAX_LOAD_MODEL_NUMS)]
        for i in cp_model_list:
            try:
                import shutil
                shutil.copytree(model_dir,i)
            except:
                pass
        model_ids = [model_id] + [os.path.split(i)[-1] for i in cp_model_list]
        print('待使用模型训练完成!')
        return model_ids

    def thread_post(self,url,data_list,pool_size):
        from multiprocessing.pool import ThreadPool as Pool
        pool = Pool(pool_size)
        for num, data in enumerate(data_list):
            pool.apply_async(self.poster, (url,data,))
        pool.close()
        pool.join()

    def poster(self,url,data):
        output = requests.post(url,data)
        if self.result!=None:
            self.result.append(output)
        # if output['code']!=0:
        return output


if __name__ == "__main__":
    t  = TestClassCallout()
    t.test_callout()
    # SWITCH_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/switch_model"
    # requests.post(SWITCH_URL,{'model_id':'callout_test','flag':0}).json()
    # requests.post(PREDICT_URL,{'model_id':'callout_test_1','query':'你好'}).json()




