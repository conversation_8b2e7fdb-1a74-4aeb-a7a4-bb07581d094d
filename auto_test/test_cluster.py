from database.REDIS import REDIS
import json
import time
import requests

class TestClassCluster:
    IP = "************"
    PORT = "9890"

    def test_nlu(self):
        # 读取聚类数据,写入redis
        self.redis = REDIS()
        with open("./nlu_data_cluster.json", "r", encoding="utf-8") as f:
            data = json.load(f)

        # 测试训练接口
        self.check_cluster(data)
        self.check_cluster(data[:1])
        self.check_cluster(data[:2])

    def wait_for_key(self, key):
        while True:
            keys = self.redis.get_keys(key)
            if len(keys):
                break
            else:
                time.sleep(10)

    def check_cluster(self, data):
        self.redis.get_redis().delete(f"nlp_cluster_predict_pytest_nlu_cluster_data")
        self.redis.set_data(key=f"pytest_nlu_cluster_data", msg=data)
        data_get = self.redis.get_data(key="pytest_nlu_cluster_data")
        assert len(data) == len(data_get)
        for i in range(len(data)):
            assert data[i] == data_get[i]

        URL = f"http://{self.IP}:{self.PORT}/nlp/cluster/predict"
        request_data = {"data_key":"pytest_nlu_cluster_data"}
        r = requests.post(URL, request_data)
        r = json.loads(r.text)
        assert r["code"] == 0

        self.wait_for_key(f"nlp_cluster_predict_pytest_nlu_cluster_data*")

        result = self.redis.get_data(key="nlp_cluster_predict_pytest_nlu_cluster_data")
        assert result["code"] == 0
        count = 0
        for l in result["result"]:
            count += len(l)
        assert count == len(data)

# if __name__ == "__main__":
#     A = TestClassCluster()
#     A.test_nlu()