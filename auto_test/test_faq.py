from database.REDIS import REDIS
import json
import time
import requests

class TestClassNLU:
    TRAIN_IP = "*************"
    PREDICT_IP = "*************"
    NLP_TRAIN_PORT = "9882"
    NLP_PREDICT_PORT = "9883"

    TRAIN_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/train"
    TRAIN_STATE_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/train_states"
    SWITCH_MODEL_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/switch_model"
    CORPUS_RECOMMEND_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/similarquerysearch/predict"

    def test_faq(self):
        # 模型id
        model_id1 = "pytest_nlu_1"
        model_id2 = "pytest_nlu_2"
        model_id_error = "pytest_error"

        # 读取训练数据,写入redis
        self.redis = REDIS()
        with open("./nlu_data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        self.redis.set_data(key=f"pytest_nlu_train_data", msg=data)
        data_get = self.redis.get_data(key="pytest_nlu_train_data")
        assert len(data) == len(data_get)
        for i in range(len(data)):
            for key, value in data[i].items():
                assert key in data_get[i].keys()
                assert data[i][key] == data_get[i][key]

        # 测试训练接口
        self.redis.get_redis().delete(f"nlp_train_{model_id1}")
        self.redis.get_redis().delete(f"nlp_train_{model_id2}")
        self.redis.get_redis().delete(f"nlp_train_{model_id_error}")
        self.redis.get_redis().delete(f"train_state_{model_id1}")
        self.redis.get_redis().delete(f"train_state_{model_id2}")
        self.redis.get_redis().delete(f"train_state_{model_id_error}")
        data = {'model_id': model_id1, 'data_key': 'pytest_nlu_train_data'}
        r = requests.post(self.TRAIN_URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        data = {'model_id': model_id2, 'data_key': 'pytest_nlu_train_data'}
        r = requests.post(self.TRAIN_URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0

        # 测试状态查询接口
        time.sleep(5)
        data = {'model_id': model_id1}
        r = requests.post(self.TRAIN_STATE_URL, data)
        r = json.loads(r.text)
        assert r["model_id"] == model_id1
        assert r["state"]["state"] == 0
        data = {'model_id': model_id2}
        r = requests.post(self.TRAIN_STATE_URL, data)
        r = json.loads(r.text)
        assert r["model_id"] == model_id2
        assert r["state"]["state"] == 1
        data = {'model_id': model_id_error}
        r = requests.post(self.TRAIN_STATE_URL, data)
        r = json.loads(r.text)
        assert r["model_id"] == model_id_error
        assert r["state"]["state"] == 3

        # 等待训练结果
        self.wait_for_key(key=f"nlp_train_{model_id2}")
        result = self.redis.get_data(key=f"nlp_train_{model_id1}")
        assert result["model_id"] == model_id1
        assert result["code"] == 0
        result = self.redis.get_data(key=f"nlp_train_{model_id2}")
        assert result["model_id"] == model_id2
        assert result["code"] == 0
        time.sleep(60)

        # 测试训练端测试接口
        self.check_switch_model(model_id=model_id1, flag=0)
        self.check_switch_model(model_id=model_id2, flag=0)
        time.sleep(10)
        self.check_re_matching(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True)
        self.check_re_matching(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True)
        self.check_re_matching(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=False)
        self.check_re_matching(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=False)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=False)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=False)

        # 测试上线模型接口
        self.check_switch_model(model_id=model_id1, flag=1)
        self.check_switch_model(model_id=model_id2, flag=1)
        time.sleep(10)
        self.check_re_matching(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True)
        self.check_re_matching(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True)
        self.check_re_matching(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=True)
        self.check_re_matching(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)

        # 测试标注接口
        self.check_switch_model(model_id=model_id1, flag=0)
        self.check_switch_model(model_id=model_id2, flag=0)
        self.check_search_labeling(ip=self.TRAIN_IP, port=self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True)
        self.check_search_labeling(ip=self.TRAIN_IP, port=self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True)
        self.check_search_labeling(ip=self.PREDICT_IP, port=self.NLP_PREDICT_PORT, model_id=model_id1, is_load=False)
        self.check_search_labeling(ip=self.PREDICT_IP, port=self.NLP_PREDICT_PORT, model_id=model_id2, is_load=False)

        # 测试语料推荐接口(需要设置SQL配置,下面的query可以调整，换成SQL里面有的语料)
        query = "人工"
        self.check_corpus_recommend(ip=self.TRAIN_IP, port=self.NLP_TRAIN_PORT, knowledge_id="", query=query)
        self.check_corpus_recommend(ip=self.TRAIN_IP, port=self.NLP_TRAIN_PORT, knowledge_id="123", query=query)

    def wait_for_key(self, key):
        while True:
            keys = self.redis.get_keys(key)
            if len(keys):
                break
            else:
                time.sleep(10)

    def check_switch_model(self, model_id, flag):
        data = {'model_id': model_id, 'flag': flag}
        r = requests.post(self.SWITCH_MODEL_URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0

    @staticmethod
    def check_re_matching(ip, port, model_id, is_load=True):
        url = f"http://{ip}:{port}/nlp/re_matching/predict"
        data = {"model_id": model_id, "query": "你好,您好"}
        r = requests.post(url, data)
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0
            assert r["data"][0]["match_labelId"] == "1"
            assert r["data"][0]["match_re"][0] == "(.*你好.*)" and r["data"][0]["match_re"][1] == "(.*您好.*)"
            assert r["data"][0]["match_keyword"][0] == "你好" and r["data"][0]["match_keyword"][1] == "您好"
            assert r["data"][0]["match_num"] == 2
            assert r["data"][0]["all_num"] == 3
        else:
            assert r["code"] == 1
            assert r["error_code"] == "NLU91024"
            assert "模型未加载" in r["msg"]

    @staticmethod
    def check_search_predict(ip, port, model_id, is_load=True):
        url = f"http://{ip}:{port}/nlp/search/predict"
        data = {"model_id": model_id, "query": "我听不清，麻烦说多一次？"}
        r = requests.post(url, data)
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0
        else:
            assert r["code"] == 1
            assert r["error_code"] == "NLU91024"
            assert "模型未加载" in r["msg"]

    @staticmethod
    def check_search_classify_predict(ip, port, model_id, is_load=True):
        url = f"http://{ip}:{port}/nlp/search_classify/predict"
        data = {"model_id": model_id, "query": "我听不清，麻烦说多一次？"}
        r = requests.post(url, data)
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0
        else:
            assert r["code"] == 1
            assert r["error_code"] == "NLU91024"
            assert "模型未加载" in r["msg"]

    @staticmethod
    def check_similarity_score_predict(ip, port, model_id):
        url = f"http://{ip}:{port}/nlp/similarity_score/predict"
        data = {"model_id": model_id, "query": "etc. ||ETC"}
        r = requests.post(url, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        assert r["score"] == 1.0
        assert len(r["all_score"]) == 1
        assert r["all_score"][0] == 1.0
        data = {"query": "etc. ||ETC||什么"}
        r = requests.post(url, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        assert r["score"] == 1.0
        assert len(r["all_score"]) == 2
        assert r["all_score"][0] == 1.0 and r["all_score"][1] < 1.0
        data = {"query": "ETC||什么"}
        r = requests.post(url, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        assert r["score"] < 1.0
        assert len(r["all_score"]) == 1
        assert r["all_score"][0] < 1.0

    def check_search_labeling(self, ip, port, model_id, is_load):
        data_key = "pytest_nlu_labeling_data"
        with open("./nlu_labeling_data.json", "r", encoding="utf-8") as f:
            redis_data = json.load(f)
        self.redis.set_data(key=data_key, msg=redis_data)

        self.redis.get_redis().delete(f"faq_labeling_{data_key}")
        url = f"http://{ip}:{port}/nlp/search/labeling"
        data = {"model_id": model_id, "data_key": data_key}
        r = requests.post(url, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        self.wait_for_key(f"faq_labeling_{data_key}*")
        if is_load:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 0
            assert len(result["label_result"]) == len(redis_data)
        else:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 1
            assert result["error_code"] == "NLU91021"

    def check_search_classify_labeling(self, ip, port, model_id, is_load):
        data_key = "pytest_nlu_labeling_data"
        with open("./nlu_labeling_data.json", "r", encoding="utf-8") as f:
            redis_data = json.load(f)
        self.redis.set_data(key=data_key, msg=redis_data)

        self.redis.get_redis().delete(f"faq_labeling_{data_key}")
        url = f"http://{ip}:{port}/nlp/search_classify/labeling"
        data = {"model_id": model_id, "data_key": data_key}
        r = requests.post(url, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        self.wait_for_key(f"faq_labeling_{data_key}*")
        if is_load:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 0
            assert len(result["label_result"]) == len(redis_data)
        else:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 1
            assert result["error_code"] == "NLU91021"

    @staticmethod
    def check_corpus_recommend(ip, port, knowledge_id, query):
        url = f"http://{ip}:{port}/similarquerysearch/predict"
        data = {'query': query, 'knowledge_id': knowledge_id}
        r = requests.post(url, data)
        r = json.loads(r.text)
        assert r["code"] == 0

if __name__ == "__main__":
    A = TestClassNLU()
    A.test_faq()
