# from database.REDIS import REDIS
import json
import time
import requests
import redis
import os
import sys

absolute_path = os.path.realpath(__file__)
sys.path.append(os.path.dirname(os.path.dirname(absolute_path)))
class REDIS:
    def __init__(self):
        host = "*************"
        port = "6379"
        password = "Qnzs_NLP_Test_Redis_2024"
        pool = redis.ConnectionPool(host=host, port=port, decode_responses=True, password=password)
        self.redisConn = redis.StrictRedis(connection_pool=pool)

    def get_data(self, key, need_json=True):
        print('[{}] 开始读取redis数据'.format(key))
        try:
            data = self.redisConn.get(key)
            # 如果读取数据
            if "{" in data[:10]:
                if isinstance(data, str) and data.startswith('"') and data.endswith('"'):
                    data = data.replace('\\', "")
                    data = data[1:-1]

            if need_json:
                data = json.loads(data)
            print('[{}] 读取redis数据成功'.format(key))

            return data
        except TypeError as e:
            data = str({})
            print("该key'{}'在redis中不存在,重新将返回值设定为'str({})'".format(key, ""))
            return data
        except Exception as e:
            print('[{}] 读取redis数据失败, 报错:{}'.format(key, e))
            raise Exception("读取redis数据失败")

    def set_data(self, key, msg, need_json=True, expire_time=None):
        try:
            if need_json:
                msg = json.dumps(msg, ensure_ascii=False, indent=2).encode('utf8')
            self.redisConn.set(key, msg)
            if expire_time is not None:
                self.redisConn.expire(key, expire_time)
        except Exception as e:
            print(f'写入redis报错, key:{key}, 错误信息: {e}')

    def get_keys(self, pattern):
        print(f'准备获取keys, pattern:{pattern}')
        try:
            return self.redisConn.keys(pattern=pattern)
        except Exception as e:
            print(f'准备获取keys报错, pattern:{pattern}, 错误信息: {e}')
            raise Exception(f"准备获取keys报错, pattern:{pattern}, 错误信息: {e}")

class TestClassNLU:
    TRAIN_IP = "*************"
    PREDICT_IP = "*************"
    NLP_TRAIN_PORT = "7880"
    NLP_PREDICT_PORT = "7881"

    TRAIN_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/train"
    TRAIN_STATE_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/train_states"
    SWITCH_MODEL_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/switch_model"

    def test_nlu(self):
        # 读取训练数据,写入redis
        self.redis = REDIS()
        with open(os.path.join(os.path.dirname(absolute_path), "nlu_data.json"), "r", encoding="utf-8") as f:
            data = json.load(f)
        self.redis.set_data(key=f"pytest_nlu_train_data", msg=data)
        data_get = self.redis.get_data(key="pytest_nlu_train_data")
        assert len(data) == len(data_get)
        for i in range(len(data)):
            for key, value in data[i].items():
                assert key in data_get[i].keys()
                assert data[i][key] == data_get[i][key]

        # 测试训练接口
        model_id1 = "pytest_nlu_1"
        model_id2 = "pytest_nlu_2"
        model_id_error = "pytest_error"
        # self.redis.get_redis().delete(f"nlp_train_{model_id1}")
        # self.redis.get_redis().delete(f"nlp_train_{model_id2}")
        for scenario in ['smartchat','smartspeech']:
            data = {'model_id':model_id1 + '_' + scenario, 'data_key':'pytest_nlu_train_data','scenario':scenario}
            r = requests.post(self.TRAIN_URL, data)
            r = json.loads(r.text)
            assert r["code"] == 0,r
            data = {'model_id':model_id2 + '_' + scenario, 'data_key':'pytest_nlu_train_data','scenario':scenario}
            r = requests.post(self.TRAIN_URL, data)
            r = json.loads(r.text)
            assert r["code"] == 0,r
            break
        # #
        scenario = 'smartchat'
        model_id1 = model_id1 + '_'+ scenario
        model_id2 = model_id2 + '_'+ scenario

        #训练取消
        time.sleep(2)
        self.check_train_cancel(ip=self.TRAIN_IP, port=self.NLP_TRAIN_PORT, model_id=model_id2)
        time.sleep(2)
        data = {'model_id':model_id2}
        r = requests.post(self.TRAIN_STATE_URL, data)
        r = json.loads(r.text)
        assert r["model_id"] == model_id2
        assert r["state"]["state"] == 3,r

        #再次训练
        data = {'model_id': model_id2, 'data_key': 'pytest_nlu_train_data', 'scenario': scenario}
        r = requests.post(self.TRAIN_URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0, r

        # 测试状态查询接口
        time.sleep(3)
        data = {'model_id':model_id1}
        r = requests.post(self.TRAIN_STATE_URL, data)
        r = json.loads(r.text)
        assert r["model_id"] == model_id1
        assert r["state"]["state"] == 0 or 2,r
        # assert len(r["state"]["train_queue"]) == 2,r
        data = {'model_id':model_id2}
        r = requests.post(self.TRAIN_STATE_URL, data)
        r = json.loads(r.text)
        assert r["model_id"] == model_id2
        assert r["state"]["state"] == 1,r
        # assert len(r["state"]["train_queue"]) == 2,r
        data = {'model_id':model_id_error}
        r = requests.post(self.TRAIN_STATE_URL, data)
        r = json.loads(r.text)
        assert r["model_id"] == model_id_error,r
        assert r["state"]["state"] == 3,r
        # assert len(r["state"]["train_queue"]) == 0,r

        ## 等待训练结果
        self.wait_for_key(key=f"nlp_train_{model_id2}*")
        result = self.redis.get_data(key=f"nlp_train_{model_id1}")
        assert result["model_id"] == model_id1
        assert result["code"] == 0,result
        result = self.redis.get_data(key=f"nlp_train_{model_id2}")
        assert result["model_id"] == model_id2
        assert result["code"] == 1,result
        time.sleep(120)

        # 测试训练端测试接口
        self.check_switch_model(model_id=model_id1, flag=0)
        self.check_switch_model(model_id=model_id2, flag=0)
        time.sleep(10)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True,is_train=True)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True,is_train=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=False)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=False)
        self.check_similarity_score_predict(model_id1,self.TRAIN_IP, self.NLP_TRAIN_PORT,is_train=True)




        # 测试上线模型接口
        self.check_switch_model(model_id=model_id1, flag=1)
        self.check_switch_model(model_id=model_id2, flag=1)
        time.sleep(10)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True,is_train=True)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True,is_train=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)
        self.check_similarity_score_predict(model_id1,self.TRAIN_IP, self.NLP_TRAIN_PORT,is_train=True)
        self.check_similarity_score_predict(model_id1,self.PREDICT_IP, self.NLP_PREDICT_PORT)


        #测试其他功能
        # /similarquerysearch/predict
        # URL = f"http://{A.TRAIN_IP}:{A.NLP_TRAIN_PORT}" + "/nlp/similarquerysearch/predict"
        # data = {"topn": 300, "query": "我听不清，麻烦说多一次？"}
        # r = requests.post(URL, data).json()
        # print(r)
        # assert len(r['similar_query']) > 0, r

        # /nlp/gpt/corpusrecommend
        # URL = f"http://{A.TRAIN_IP}:{A.NLP_TRAIN_PORT}" + "/nlp/gpt/corpusrecommend"
        # with open(os.path.join(os.path.dirname(absolute_path), "nlu_data.json"), "r", encoding="utf-8") as f:
        #     data = json.load(f)
        # data[0]['answer'] = '您好,在的,请问有什么可以帮助您'
        # data = {'data': data}
        # r = requests.post(URL, json.dumps(data)).json()
        # assert len(r['result'][0]['recommend'].split('||')) == 50, r

        # /filechat/train/language_judge
        URL = f"http://{A.TRAIN_IP}:{A.NLP_TRAIN_PORT}" + "/nlp/filechat/train/language_judge"
        data = {'query': 'how are you', 'lang': ['en', 'yue']}
        r = requests.post(URL, json.dumps(data)).json()
        assert r['is_lang'] == [1, 0], r

        # 测试标注接口
        # self.check_switch_model(model_id=model_id1, flag=0)
        # self.check_switch_model(model_id=model_id2, flag=0)
        # self.check_search_labeling(ip=self.TRAIN_IP, port=self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True)
        # self.check_search_labeling(ip=self.TRAIN_IP, port=self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True)
        # self.check_search_labeling(ip=self.PREDICT_IP, port=self.NLP_PREDICT_PORT, model_id=model_id1, is_load=False)
        # self.check_search_labeling(ip=self.PREDICT_IP, port=self.NLP_PREDICT_PORT, model_id=model_id2, is_load=False)
        print("测试通过")




    def wait_for_key(self, key):
        while True:
            keys = self.redis.get_keys(key)
            if len(keys):
                break
            else:
                time.sleep(10)

    def check_switch_model(self, model_id, flag):
        data = {'model_id':model_id, 'flag':flag, "model_suffix": 1}
        r = requests.post(self.SWITCH_MODEL_URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0

    def check_re_matching(self, ip, port, model_id, is_load=True,is_train=False):
        if is_train:
            URL = f"http://{ip}:{port}/nlp/train/re_matching/predict"
        else:
            URL = f"http://{ip}:{port}/nlp/re_matching/predict"
        data = {"model_id": model_id, "query":"你好,您好"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0
            assert r["data"]['faq'][0]["match_labelId"] == "1",r
            assert r["data"]['faq'][0]["match_re"][0] == ".*你好.*" and r["data"]['faq'][0]["match_re"][1] == ".*您好.*",r
            assert r["data"]['faq'][0]["match_keyword"][0] == "你好" and r["data"]['faq'][0]["match_keyword"][1] == "您好",r
            assert r["data"]['faq'][0]["match_num"] == 2
            assert r["data"]['faq'][0]["all_num"] == 3
        else:
            assert r["code"] == 1
            assert r["error_code"] == "NLU91024"
            assert "模型未加载" in r["msg"]

    def check_search_predict(self, ip, port, model_id, is_load=True,is_train=False):
        if is_train:
            URL = f"http://{ip}:{port}/nlp/train/search/predict"
        else:
            URL = f"http://{ip}:{port}/nlp/search/predict"
        data = {"model_id": model_id, "query":"我听不清，麻烦说多一次？"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0, r
        else:
            assert r["code"] == 1
            assert r["error_code"] in ["NLU91025","NLU91024"],r
            assert "模型未加载" in r["msg"],r

    def check_classify_predict(self, ip, port, model_id, is_load=True,is_train=False):
        if is_train:
            URL = f"http://{ip}:{port}/nlp/train/classify/predict"
        else:
            URL = f"http://{ip}:{port}/nlp/classify/predict"
        data = {"model_id": model_id, "query":"我听不清，麻烦说多一次？"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0
        else:
            assert r["code"] == 1
            assert r["error_code"] == "NLU91024"
            assert "模型未加载" in r["msg"]

    def check_similarity_score_predict(self,model_id, ip, port,is_train=False):
        if is_train:
            URL = f"http://{ip}:{port}/nlp/train/similarity_score_point/predict"
        else:
            URL = f"http://{ip}:{port}/nlp/similarity_score_point/predict"
        data = {'model_id':model_id,"query":"etc. ||ETC"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        assert r["score"] > 0.99,r
        assert len(r["all_score"]) == 1
        # assert r["all_score"][0] == 1.0,
        data = {'model_id':model_id,"query":"etc. ||ETC||什么"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        assert r["score"] > 0.99
        assert len(r["all_score"]) == 2
        assert r["all_score"][0] > 0.99 and r["all_score"][1] < 0.99
        data = {'model_id':model_id,"query":"ETC||什么"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        assert r["score"] < 1.0
        assert len(r["all_score"]) == 1
        assert r["all_score"][0] < 1.0

    def check_search_labeling(self, ip, port, model_id, is_load):
        data_key = "pytest_nlu_labeling_data"
        with open("./nlu_labeling_data.json", "r", encoding="utf-8") as f:
            redis_data = json.load(f)
        self.redis.set_data(key=data_key, msg=redis_data)

        self.redis.get_redis().delete(f"faq_labeling_{data_key}")
        URL = f"http://{ip}:{port}/nlp/search/labeling"
        data = {"model_id":model_id, "data_key":data_key}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        self.wait_for_key(f"faq_labeling_{data_key}*")
        if is_load:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 0
            assert len(result["label_result"]) == len(redis_data)
        else:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 1
            assert result["error_code"] == "NLU91021"

    def check_classify_labeling(self, ip, port, model_id, is_load):
        data_key = "pytest_nlu_labeling_data"
        with open("./nlu_labeling_data.json", "r", encoding="utf-8") as f:
            redis_data = json.load(f)
        self.redis.set_data(key=data_key, msg=redis_data)

        self.redis.get_redis().delete(f"faq_labeling_{data_key}")
        URL = f"http://{ip}:{port}/nlp/classify/labeling"
        data = {"model_id":model_id, "data_key":data_key}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        self.wait_for_key(f"faq_labeling_{data_key}*")
        if is_load:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 0
            assert len(result["label_result"]) == len(redis_data)
        else:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 1
            assert result["error_code"] == "NLU91021"


    def check_train_cancel(self, ip, port, model_id):
        URL = f"http://{ip}:{port}/nlp/train_cancel"
        data = {"model_id": model_id}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0





if __name__ == "__main__":
    A = TestClassNLU()
    A.test_nlu()

