import json
import time
import requests
import sys
import os
import redis


absolute_path = os.path.realpath(__file__)
sys.path.append(os.path.dirname(os.path.dirname(absolute_path)))


# from database.REDIS import REDIS

class REDIS:
    def __init__(self):
        host = "*************"
        port = "6379"
        password = "Qnzs_NLP_Test_Redis_2024"
        pool = redis.ConnectionPool(host=host, port=port, decode_responses=True, password=password)
        self.redisConn = redis.StrictRedis(connection_pool=pool)

    def get_data(self, key, need_json=True):
        print('[{}] 开始读取redis数据'.format(key))
        try:
            data = self.redisConn.get(key)
            # 如果读取数据
            if "{" in data[:10]:
                if isinstance(data, str) and data.startswith('"') and data.endswith('"'):
                    data = data.replace('\\', "")
                    data = data[1:-1]

            if need_json:
                data = json.loads(data)
            print('[{}] 读取redis数据成功'.format(key))

            return data
        except TypeError as e:
            data = str({})
            print("该key'{}'在redis中不存在,重新将返回值设定为'str({})'".format(key, ""))
            return data
        except Exception as e:
            print('[{}] 读取redis数据失败, 报错:{}'.format(key, e))
            raise Exception("读取redis数据失败")

    def set_data(self, key, msg, need_json=True, expire_time=None):
        try:
            if need_json:
                msg = json.dumps(msg, ensure_ascii=False, indent=2).encode('utf8')
            self.redisConn.set(key, msg)
            if expire_time is not None:
                self.redisConn.expire(key, expire_time)
        except Exception as e:
            print(f'写入redis报错, key:{key}, 错误信息: {e}')

class TestClassNLU:
    TRAIN_IP = "*************"
    PREDICT_IP = "*************"
    NLP_TRAIN_PORT = "9880"
    NLP_PREDICT_PORT = "9881"

    TRAIN_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/train"
    TRAIN_STATE_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/train_states"
    SWITCH_MODEL_URL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/switch_model"
    TRAIN_CANCEL = f"http://{TRAIN_IP}:{NLP_TRAIN_PORT}/nlp/train_cancel"

    def test_nlu(self):
        # 取消历史任务

        # 读取训练数据,写入redis
        self.redis = REDIS()
        with open(os.path.join(os.path.dirname(absolute_path), "nlu_data.json"), "r", encoding="utf-8") as f:
            data = json.load(f)
        print(data)
        self.redis.set_data(key=f"pytest_nlu_train_data", msg=data)
        data_get = self.redis.get_data(key="pytest_nlu_train_data")
        assert len(data) == len(data_get)
        for i in range(len(data)):
            for key, value in data[i].items():
                assert key in data_get[i].keys()
                assert data[i][key] == data_get[i][key]

        # 测试训练接口
        model_id1 = "pytest_smartqc_1"
        model_id2 = "pytest_smartqc_2"
        requests.post(self.TRAIN_CANCEL, data={"model_id":model_id1})
        requests.post(self.TRAIN_CANCEL, data={"model_id":model_id2})

        model_id_error = "pytest_error"
        self.redis.redisConn.delete(f"nlp_train_{model_id1}")
        self.redis.redisConn.delete(f"nlp_train_{model_id2}")
        time.sleep(20)
        data = {'model_id':model_id1, 'data_key':'pytest_nlu_train_data', "scenario": "smartanalysis"}
        r = requests.post(self.TRAIN_URL, data)
        print(f"模型1训练响应：{r.text}")
        r = json.loads(r.text)
        assert r["code"] == 0
        data = {'model_id':model_id2, 'data_key':'pytest_nlu_train_data', "scenario": "smartanalysis"}
        r = requests.post(self.TRAIN_URL, data)
        print(f"模型2训练响应：{r.text}")
        r = json.loads(r.text)
        assert r["code"] == 0

        # 测试状态查询接口
        time.sleep(10)
        data = {'model_id': model_id1}
        r = requests.post(self.TRAIN_STATE_URL, data)
        print(f"模型1训练状态：{r.text}")
        r = json.loads(r.text)
        assert r["model_id"] == model_id1
        assert r["state"]["state"] == 0
        data = {'model_id': model_id2}
        r = requests.post(self.TRAIN_STATE_URL, data)
        print(f"模型2训练状态：{r.text}")
        r = json.loads(r.text)
        assert r["model_id"] == model_id2
        assert r["state"]["state"] == 1
        data = {'model_id': model_id_error}
        r = requests.post(self.TRAIN_STATE_URL, data)
        print(f"模型3训练状态：{r.text}")
        r = json.loads(r.text)
        assert r["model_id"] == model_id_error
        assert r["state"]["state"] == 3

        ## 等待训练结果
        self.wait_for_key(key=f"train_state_{model_id2}")
        result = self.redis.get_data(key=f"nlp_train_{model_id1}")
        assert result["model_id"] == model_id1
        assert result["code"] == 0
        result = self.redis.get_data(key=f"nlp_train_{model_id2}")
        assert result["model_id"] == model_id2
        assert result["code"] == 0

        print("模型上线测试")
        # 测试上线模型接口
        self.check_switch_model(model_id=model_id1, flag=1)
        time.sleep(5)
        self.check_switch_model(model_id=model_id2, flag=1)
        time.sleep(5)
        self.check_switch_model(model_id=model_id1, flag=1, model_suffix=1)
        time.sleep(5)
        self.check_switch_model(model_id=model_id2, flag=1, model_suffix=1)
        time.sleep(10)
        self.check_re_matching(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True, is_train=True)
        self.check_re_matching(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True, is_train=True)
        self.check_re_matching(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=True)
        self.check_re_matching(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)

        self.check_class_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True, is_train=True)
        self.check_class_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True, is_train=True)
        self.check_class_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)
        self.check_class_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)

        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True, is_train=True)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True, is_train=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)

        print("模型下线测试")
        # 测试训练端测试接口
        self.check_switch_model(model_id=model_id1, flag=0)
        time.sleep(5)
        self.check_switch_model(model_id=model_id2, flag=0)
        time.sleep(5)
        self.check_switch_model(model_id=model_id1, flag=0, model_suffix=1)
        time.sleep(5)
        self.check_switch_model(model_id=model_id2, flag=0, model_suffix=1)
        time.sleep(10)
        self.check_re_matching(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True, is_train=True)
        self.check_re_matching(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True, is_train=True)
        self.check_re_matching(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=True)
        self.check_re_matching(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)

        self.check_class_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True, is_train=True)
        self.check_class_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True, is_train=True)
        self.check_class_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)
        self.check_class_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)

        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id1, is_load=True, is_train=True)
        self.check_search_predict(self.TRAIN_IP, self.NLP_TRAIN_PORT, model_id=model_id2, is_load=True, is_train=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id1, is_load=True)
        self.check_search_predict(self.PREDICT_IP, self.NLP_PREDICT_PORT, model_id=model_id2, is_load=True)

        print("测试通过")

    def wait_for_key(self, key):
        time.sleep(10)
        count = 0
        while True:
            keys = eval(self.redis.get_data(key=key, need_json=False))
            if keys["state"] != 2:
                time.sleep(10)
                count += 1
                continue
            if keys["msg"] == '训练成功' or count >= 50:
                break
            else:
                time.sleep(10)
                count += 1

    def check_switch_model(self, model_id, flag, model_suffix=0):
        data = {'model_id': model_id, 'flag': flag, "model_suffix": model_suffix}
        r = requests.post(self.SWITCH_MODEL_URL, data)
        print(f"模型上下线：{data}, {r.text}")
        r = json.loads(r.text)
        assert r["code"] == 0

    def check_re_matching(self, ip, port, model_id, is_load=True, is_train=False):
        if is_train:
            URL = f"http://{ip}:{port}/nlp/train/re_matching/predict"
        else:
            URL = f"http://{ip}:{port}/nlp/re_matching/predict"
        data = {"model_id": model_id, "query": "你好,您好"}
        r = requests.post(URL, data)
        print(f"预测结果：{r.text}")
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0
            assert r["data"]["faq"][0]["match_labelId"] == "1"
            assert r["data"]["faq"][0]["match_re"][0] == ".*(.*你好.*).*" and r["data"]["faq"][0]["match_re"][1] == ".*(.*您好.*).*"
            assert r["data"]["faq"][0]["match_keyword"][0] == "你好" and r["data"]["faq"][0]["match_keyword"][1] == "您好"
            assert r["data"]["faq"][0]["match_num"] == 2
            assert r["data"]["faq"][0]["all_num"] == 3
        else:
            assert r["code"] == 1
            assert r["error_code"] == "NLU91024"
            assert "模型未加载" in r["msg"]

    def check_class_predict(self, ip, port, model_id, is_load=True, is_train=False):
        if is_train:
            URL = f"http://{ip}:{port}/nlp/train/classify/predict"
        else:
            URL = f"http://{ip}:{port}/nlp/classify/predict"
        data = {"model_id": model_id, "query": "我这边听不清"}
        r = requests.post(URL, data)
        print(f"预测结果：{r.text}")
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0
            assert r["data"][0]["score"] == 1
            # assert r["data"]["faq"][0]["match_re"][0] == ".*(.*你好.*).*" and r["data"]["faq"][0]["match_re"][
            #     1] == ".*(.*您好.*).*"
            # assert r["data"]["faq"][0]["match_keyword"][0] == "你好" and r["data"]["faq"][0]["match_keyword"][
            #     1] == "您好"
            # assert r["data"]["faq"][0]["match_num"] == 2
            # assert r["data"]["faq"][0]["all_num"] == 3
        else:
            assert r["code"] == 1
            assert r["error_code"] == "NLU91024"
            assert "模型未加载" in r["msg"]

    def check_search_predict(self, ip, port, model_id, is_load=True, is_train=False):
        if is_train:
            URL = f"http://{ip}:{port}/nlp/train/search/predict"
        else:
            URL = f"http://{ip}:{port}/nlp/search/predict"
        data = {"model_id": model_id, "query": "我听不清，麻烦说多一次？"}
        r = requests.post(URL, data)
        print(f"预测结果：{r.text}")
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0
        else:
            assert r["code"] == 1
            assert r["error_code"] == "NLU91024"
            assert "模型未加载" in r["msg"]

    def check_search_classify_predict(self, ip, port, model_id, is_load=True):
        URL = f"http://{ip}:{port}/nlp/search_classify/predict"
        data = {"model_id": model_id, "query":"我听不清，麻烦说多一次？"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        if is_load:
            assert r["code"] == 0
        else:
            assert r["code"] == 1
            assert r["error_code"] == "NLU91024"
            assert "模型未加载" in r["msg"]

    def check_similarity_score_predict(self, ip, port):
        URL = f"http://{ip}:{port}/nlp/similarity_score/predict"
        data = {"query":"etc. ||ETC"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        assert r["score"] == 1.0
        assert len(r["all_score"]) == 1
        assert r["all_score"][0] == 1.0
        data = {"query":"etc. ||ETC||什么"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        assert r["score"] == 1.0
        assert len(r["all_score"]) == 2
        assert r["all_score"][0] == 1.0 and r["all_score"][1] < 1.0
        data = {"query":"ETC||什么"}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        assert r["score"] < 1.0
        assert len(r["all_score"]) == 1
        assert r["all_score"][0] < 1.0

    def check_search_labeling(self, ip, port, model_id, is_load):
        data_key = "pytest_nlu_labeling_data"
        with open("./nlu_labeling_data.json", "r", encoding="utf-8") as f:
            redis_data = json.load(f)
        self.redis.set_data(key=data_key, msg=redis_data)

        self.redis.get_redis().delete(f"faq_labeling_{data_key}")
        URL = f"http://{ip}:{port}/nlp/search/labeling"
        data = {"model_id":model_id, "data_key":data_key}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        self.wait_for_key(f"faq_labeling_{data_key}*")
        if is_load:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 0
            assert len(result["label_result"]) == len(redis_data)
        else:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 1
            assert result["error_code"] == "NLU91021"

    def check_search_classify_labeling(self, ip, port, model_id, is_load):
        data_key = "pytest_nlu_labeling_data"
        with open("./nlu_labeling_data.json", "r", encoding="utf-8") as f:
            redis_data = json.load(f)
        self.redis.set_data(key=data_key, msg=redis_data)

        self.redis.get_redis().delete(f"faq_labeling_{data_key}")
        URL = f"http://{ip}:{port}/nlp/search_classify/labeling"
        data = {"model_id":model_id, "data_key":data_key}
        r = requests.post(URL, data)
        r = json.loads(r.text)
        assert r["code"] == 0
        self.wait_for_key(f"faq_labeling_{data_key}*")
        if is_load:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 0
            assert len(result["label_result"]) == len(redis_data)
        else:
            result = self.redis.get_data(key=f"faq_labeling_{data_key}")
            assert result["code"] == 1
            assert result["error_code"] == "NLU91021"

if __name__ == "__main__":
    A = TestClassNLU()
    A.test_nlu()
    # redis = REDIS()

    # a = redis.set_data(key=f"pytest_nlu_train_data", msg={"asdf":1})
    # a = redis.get_data(key=f"pytest_nlu_train_data")
    # print(a)