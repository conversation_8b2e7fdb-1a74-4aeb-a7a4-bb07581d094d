#!/bin/sh

worker=1
app=FilechatServer_bge_openai
port=9899
while getopts ":a:p:w:" opt; do
  case $opt in
    a) app="$OPTARG";;
    p) port="$OPTARG";;
    w) worker="$OPTARG";;
  esac
done

# Set the PORT environment variable
export NLP_PORT=$port

echo "app $app"
echo "port $port"
echo "worker $worker"
ID=$(ps aux | grep $app | grep $port | grep -v start.sh | grep -v grep | awk '{print $2}')
for aid in ${ID};do
  echo "kill $aid"
  kill -9 $aid
  echo "kill $aid"
done
/data/aicc-nlp/tf2/bin/python -u  /data/aicc-nlp/tf2/bin/gunicorn -w $worker -b 0.0.0.0:$port $app:app -t 1000 -k uvicorn.workers.UvicornWorker
