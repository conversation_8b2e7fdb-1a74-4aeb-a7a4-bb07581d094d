#!/bin/sh

if [ -z "$WORKER_NUM" ]; then
    worker=1
else
    worker=$WORKER_NUM
fi

app=PredictNluHttpServer
port_start=9881
processes=3
while getopts ":a:s:p:w:" opt; do
  case $opt in
    a) app="$OPTARG";;
    s) port_start="$OPTARG";;
    p) processes="$OPTARG";;
    w) worker="$OPTARG";;
  esac
done

# Kill existing processes
ps -ef | grep "$app:app \-t 1234" | grep -v grep | awk '{print $2}' | xargs kill -9

for i in $(seq 1 $processes); do
    port=$(expr $port_start + $i - 1)
    echo "Starting $app on port $port"
    #预测端必须启动两个线程，上线过程中，一个线程处理上线接口，一个返回404。
    /data/tf2/bin/python -u /data/tf2/bin/gunicorn -k gthread --threads 2 -b 0.0.0.0:$port $app:app -t 1234 &
done
echo "$app 启动完毕,启动个数:$processes"

# Wait for all background processes to prevent script from exiting immediately
wait
