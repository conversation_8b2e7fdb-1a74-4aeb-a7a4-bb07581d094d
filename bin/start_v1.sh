#!/bin/sh
if [ $# != 3 ] ; then
echo "启动脚本start.sh参数数量有误，需要三个参数"
exit 1;
fi
echo "参数1 $1"
echo "参数2 $2"
echo "参数3 $3"

# Set the PORT environment variable
export NLP_PORT=$2

ID=$(ps aux | grep $1 | grep $2 | grep -v start_v1.sh | grep -v grep | awk '{print $2}')
for aid in ${ID};do
  echo "kill $aid"
  kill -9 $aid
  echo "kill $aid"
done
/home/<USER>/anaconda3/envs/tf2/bin/python -u  /home/<USER>/anaconda3/envs/tf2/bin/gunicorn -w $3 -b 0.0.0.0:$2 $1:app -t 1000
