[program:nlp-nlu-train]
directory = /data/79/aicc-nlp-nlu_RegularHttpServer_merge
command = bash ./bin/train.sh   ; default 9880,
stopsignal = TERM
autostart = false
autorestart = true
startsecs = 5
user = root
redirect_stderr = true
stdout_logfile = /data/logs/supervisord/nlp-nlu-train.log
stdout_logfile_maxbytes=20MB   ; max # logfile bytes b4 rotation (default 50MB)
stdout_logfile_backups=50     ; # of stdout logfile backups (0 means none, default 10)
stderr_logfile_maxbytes=20MB   ; max # logfile bytes b4 rotation (default 50MB)
stderr_logfile_backups=50     ; # of stderr logfile backups (0 means none, default 10)

[program:nlp-nlu-predict]
directory = /data/79/aicc-nlp-nlu_RegularHttpServer_merge
command = bash ./bin/predict.sh   ; default 9881,
stopsignal = TERM
autostart = false
autorestart = true
startsecs = 5
user = root
redirect_stderr = true
stdout_logfile = /data/logs/supervisord/nlp-nlu-predict.log
stdout_logfile_maxbytes=20MB   ; max # logfile bytes b4 rotation (default 50MB)
stdout_logfile_backups=50     ; # of stdout logfile backups (0 means none, default 10)
stderr_logfile_maxbytes=20MB   ; max # logfile bytes b4 rotation (default 50MB)
stderr_logfile_backups=50     ; # of stderr logfile backups (0 means none, default 10)

