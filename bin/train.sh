#!/bin/sh

if [ -z "$WORKER_NUM" ]; then
    worker=1
else
    worker=$WORKER_NUM
fi

app=TrainNluHttpServer
port_start=9880
processes=1
while getopts ":a:s:p:w:" opt; do
  case $opt in
    a) app="$OPTARG";;
    s) port_start="$OPTARG";;
    p) processes="$OPTARG";;
    w) worker="$OPTARG";;
  esac
done

# Kill existing processes
ps -ef | grep "$app:app \-t 1234" | grep -v grep | awk '{print $2}' | xargs kill -9

for i in $(seq 1 $processes); do
    port=$(expr $port_start + $i - 1)
    echo "Starting $app on port $port"
    # Start Gunicorn in the background
    /data/tf2/bin/python -u /data/tf2/bin/gunicorn -b 0.0.0.0:$port $app:app -t 1234 &
done

# Wait for all background processes to prevent script from exiting immediately
wait

#结束进程
#ps -ef | grep "TrainNluHttpServer.app -t 1234" | grep -v grep | awk '{print $2}' | xargs kill -9
#ps -ef | grep "PredictNluHttpServer:app -t 1234" | grep -v grep | awk '{print $2}' | xargs kill -9
