; 通用配置
[common]
; 如果不用GPU，填-1。如果用GPU，填GPU的编号(从0开始)
gpu = -1
num_threads = 10
; 是否使用prometheus统计请求数量
; 如果服务都不使用prometheus，则train和predict的use_prometheus都设为False
; 如果服务要使用prometheus，通常client为train就把use_prometheus设为False，client为predict就把use_prometheus设为True
use_prometheus = False
; 服务名，每次启动服务都要有一个独一无二的服务名，否则训练队列会混淆
service_name = skm_merge_test_asd
; 使用的业务类型 smartspeech:外呼， smartchat：文本机器人, smartanalysis：质检
;              smartcoach：智能培训, smartllm: 考培ppt, filechat:大模型文档问答
use_scenarios = smartspeech,smartchat,smartanalysis,smartcoach,smartllm

# 自动上下线配置
auto_online_model = True
#预测端最大加载个数
lru_model_load_num = 50
# 日志文件名，可默认
ServerLogName = nlp_log
# 日志存放路径，可默认
ServerLogPath = ./log
# 模型存放路径，可默认
ModelPath = ../NLP_Model

[SEARCH]
arccse_pre_rank_max_len = 64

[LLM]
other_llm_url =  https://aicc-test.qnzsai.com/aicc-api/aicc-gptproxy/gpt/test
image_to_text_url =  http://**************:9000/v1/chat/completions
; image_to_text_url =  http://10.218.56.46:19000/v1/chat/completions
image_to_text_url_opanai = http://**************:9000/v1/chat/completions
; image_to_text_url_opanai = http://10.218.56.46:19000/v1/chat/completions
embedding_url = http://**************:6012/embeddings
image_embedding_url =
;embedding_url = http://192.168.1.238:6012/embeddings
;embedding_url = http://*************:6012/embeddings
rerank_url = http://**************:5001/reranker_scores
;rerank_url = http://*************:6011/reranker_scores


[EXAM]
; 考培LLM,抽题和PPT讲稿
; 模型,支持 gpt4,gpt3.5,our
exam_model = our
; 当exam_model=our的时候需要填写,our_model_url
our_model_url = https://dashscope.aliyuncs.com/compatible-mode/v1
; PPT图片内容识别服务url地址
image_to_text_url = http://region-3.seetacloud.com:46181/image_to_text

[FILECHAT]
; gpt4,gpt3.5,tx,our
filechat_model = our
filechat_lang = en
filechat_model_url = http://region-3.seetacloud.com:46181/v1/
filechat_embedding_model_name = xiaobu-57w-ck976-mix-ori_8_2
filechat_ocr=True
filechat_pptx2ima_url = https://aicc-test.qnzsai.com/aicc-api/filetrans/toImages
filechat_table_sql = False
filechat_cut_merge = False
filechat_rerank = 20


[TRANSLATE]
translate_model = our
translate_model_url = http://**************:1688/translate
yue_translate_model_url = https://dashscope.aliyuncs.com/compatible-mode/v1


; SEND_MODEL
[SEND_MODEL]
; 同步模型的方式，可以采用 [Redis、Rsync、Nas]。
; 如果用Nas的话就不会发送模型，必须保证所有服务器(训练端和预测端)的NLP_Model文件夹都是在NAS上。
send_model_mode = Nas
; rsync 发送速度，单位 KB/s
rsync_send_speed = 409600

[IPS]
; 训练端和预测端模型文件夹的路径 (填宿主机路径，需要映射到docker)
; 这个参数在使用Rsync发送模型时需要填写
service_model_path_dict = {
                                "train": "/data/236/NLP_Model",
                                "predict": "/home/<USER>/NLP_Model"
                          }
; 所有服务的IP和端口(包括本服务),训练端service=train,预测端service=predict;service_port是nlu服务端口;ssh_port是访问服务器的端口;user是访问服务器的用户
; 这个参数在使用Rsync发送模型时需要填写
ssh_port = 22
user = root
;server_ip_list = [
;                    {"ip": "*************", "service_port": "9881", "service": "train", "ssh_port": 22, "user": "root"},
;                    {"ip": "*************", "service_port": "9880", "service": "predict", "ssh_port": 22, "user": "root"}
;                 ]

; KENLM
[KENLM]
; 文本机器人使用
; KENLM安装路径
kenlm_path = "/data/sammy/app/kenlm/build/bin"

[SERVER]
; java提供的上传文件服务
upload_file_url = https://aicc.qnzsai.com/crm-api/sss-boot/exam/file/common/uploadPptx
; 考培不用管这个配置
llm_embedding_ip_port = http://**************:9000/


[NACOS]
; 不填则不注册
; 填了nacos不用再填下面数据库
; server_addresses为空走环境变量
server_addresses =
nacos_username = nacos
nacos_password = nacos
nacos_namespace =
nacos_config_name = yaml-base-comfig


; REDIS
[REDIS]
; 单点redis还是集群redis
use_single_redis = True
; 是否加密password
encrypt = False
; 单点redis
redis_setting = {"host":"*********",
                "port":"6379",
                "password":"QNzs@.root_1347908642",
                "dbname":0}
; 集群redis
redis_cluster_list = [{'host':'**************','port':6378},
                     {'host':'**************','port':6380},
                     {'host':'**************','port':6381},
                     {'host':'**************','port':6382},
                     {'host':'**************','port':6383},
                     {'host':'**************','port':6384},
                     {'host':'**************','port':6385},
                     ]
redis_cluster_password = "fxTJyCj4cSZ00QL9mleW9by2GqIOHds5Rk5wfLPWKa0="

; ES
[ES]
es_host = ***********:9200
es_username = elastic
es_password = Qnzs&2023

[MINIO]
minio_url = *********:9000
minio_name = qnzs
minio_pass = Qnzs2023
bucketName = nlpforjava

; RabbitMQ
[RabbitMQ]
; 是否使用RabbitMQ
use_rabbit_mq = False
; RabbitMQ
rabbit_mq_setting = {"user":"admin", "password":"admin", "host": "*************", "port": 5672}


[SQL]
; SQL 配置
sql_setting = {'host': '*************',
        'port': 3306,
        'user': 'root',
        'passwd': 'Qnzs123',
        'db':'aicc'}
