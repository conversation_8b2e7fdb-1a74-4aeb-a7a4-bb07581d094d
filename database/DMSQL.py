from urllib.parse import quote_plus as urlquote

import pandas as pd
from sqlalchemy import create_engine
import time
from setting import logger
import dmPython

class DMSQL:
    def __init__(self, host, port, user, passwd, db):
        self.host = host
        self.port = port
        self.user = user
        self.passwd = passwd
        self.db = db


    def creat_engine(self, db):
        for i in range(2):
            try:
                # return create_engine(f'dm://{self.user}:{urlquote(self.passwd)}@{self.host}:{self.port}/{db}', connect_args={'connect_timeout': 5})
                return create_engine(f'dm://{self.user}:{urlquote(self.passwd)}@{self.host}:{self.port}/{db}', connect_args={'connect_timeout': 5})
                # return create_engine(f'dm+dmPython://{self.user}:{self.passwd}@{self.host}:{self.port}/{db}', connect_args={'connect_timeout': 5})
                # return create_engine(f'mysql+pymysql://{self.user}:{urlquote(self.passwd)}@{self.host}:{self.port}/{db}', connect_args={'connect_timeout': 5})
            except:
                logger.error('报错:[数据库]连接失败,再次重连.',exc_info=True)
        logger.error('报错:[数据库]连接失败,重连无效', exc_info=True)

    def read_sql_query(self, db, sql):
        try:
            df = pd.read_sql_query(sql, con=self.creat_engine(db))
            return df
        except:
            logger.error('报错:[数据库]连接失败', exc_info=True)

    def to_sql(self, df, db, table, if_exists='fail',**args):
        try:
            for i in range(len(df)):
                sec = df.iloc[i]
                values = sec.values
                columns_str = str(tuple(df.columns.tolist())).replace('\'','')
                sql = f"""INSERT INTO {db}.{table} {columns_str} VALUES {tuple(values)};"""
                print(sql,'asdasdsaasd')
                self.execute_sql(db,sql)
            logger.debug(f'[数据库]写入成功,写入{len(df)}条')
        except:
            logger.error('报错:[数据库]写入sql失败', exc_info=True)

    def execute_sql(self, db, sql):
        for i in range(5):
            try:
                conn = dmPython.connect(host=self.host, port=self.port, user=self.user, password=self.passwd)
                cur = conn.cursor()
                print('python: conn success!')

                cur.execute(sql)
                #显示执行结果
                # print(cur.fetchall())
                conn.commit()
                conn.close()
                logger.info('db:{},sql:{},execute succeed'.format(db, sql))
                return True
            except:
                logger.error('报错{},{},DEBUG execute failed! re-executing...'.format(db, sql),exc_info=True)
                time.sleep(1)
        return False

if __name__ == '__main__':
    print('asdasdasd')
    mysql_config = {
        'host': '***********',
        'port': 5236,
        'user': 'SYSDBA',
        'passwd': 'SYSDBA001',
        'db':'AICC'}
    dbsql = DMSQL(**mysql_config)
    dbsql.execute_sql('AICC',"SELECT DISTINCT object_name FROM ALL_OBJECTS WHERE OBJECT_TYPE = 'SCH'")
    db = 'AICC'
    table = 't_robot_none_match_nlp'
    # values = []
    # sql = """"INSERT INTO AICC.t_robot_none_match_nlp VALUES ('0e6d459732051294aac63b1c494f8215', '做这些瓶装的水。', 1, '', '', 1, '2024-06-28 14:23:43', '3536', '做这些瓶装的水。', '161165294', '2024-09-14 14:07:33')"""
    # dbsql.execute_sql('AICC',sql)

    dbsql.execute_sql('AICC','select * from AICC.t_robot_none_match_nlp')

