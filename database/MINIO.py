from setting import logger
from minio import Minio
import os
import setting
# from setting import MINI<PERSON>_URL,MINIO_NAME,MINIO_PASS,BUCKETNAME

# minio_url = '192.168.1.211:9000'
# minio_name = 'qnzs'
# minio_pass = 'Qnzs2022'
# bucketName = 'filetrans'
#
# MINIO_URL,MINIO_NAME,MINIO_PASS,BUCKETNAME = minio_url,minio_name,minio_pass,bucketName

class MINIO:
    def __init__(self):
        self.MINIO_URL = setting.MINIO_URL
        self.BUCKETNAME = setting.BUCKETNAME
        self.client = Minio(
            setting.MINIO_URL,
            access_key=setting.MINIO_NAME,
            secret_key=setting.MINIO_PASS, secure=False
        )

    def upload_file(self,filename,filepath,delete=False):
        # Create a client with the MinIO server playground, its access key
        # and secret key.
        # Make 'asiatrip' bucket if not exist.
        try:
            found = self.client.bucket_exists(self.BUCKETNAME)
            if not found:
                logger.info(f'bucketName:{self.BUCKETNAME}不存在,开始创建')
                self.client.make_bucket(self.BUCKETNAME)
                logger.info('新建桶{}成功！'.format(self.BUCKETNAME))
            # Upload '/home/<USER>/Photos/asiaphotos.zip' as object name
            # 'asiaphotos-2015.zip' to bucket 'asiatrip'.
            self.client.fput_object(
                self.BUCKETNAME, filename, filepath
            )
            logger.info(f'上传成功:{filename}，文件路径：{filepath}')
            if delete:
                os.remove(filepath)
            return True
        except:
            logger.error(f'上传错误:{filename}',exc_info=True)
            return False



    def download_file(self,filename,output_path,bucketname = None):
        if bucketname==None:
            bucketname = self.BUCKETNAME
        logger.info(f'{bucketname,filename,output_path},开始下载')
        output_filename = filename.split('/')[-1]
        save_path = os.path.join(output_path,output_filename)
        if os.path.exists(save_path):
            return save_path
        data = self.client.get_object(bucketname, filename)
        with open(save_path, "wb") as fp:
            for d in data.stream(1024):
                fp.write(d)
        return save_path

    def get_download_url(self,filename):
        return 'http://' +  f'{self.MINIO_URL}/{self.BUCKETNAME}/{filename}'

    def get_download_url_for_java(self, filename):
        # 考培PPT增加备注的，java那边需要这种url
        return f'/minio/{self.BUCKETNAME}/{filename}'

    def delete_file(self,filename):
        try:
            self.client.remove_object(self.BUCKETNAME, filename)
            return True
        except:
            logger.error(f'删除文件错误:{filename}',exc_info=True)
            return False

if __name__ == "__main__":
    bucketName = setting.BUCKETNAME
    filename = 'NLP 部署文档.pdf'
    filepath = r"/data/cbk/NLP_Platform/doc/NLP 部署文档.pdf"
    m = MINIO()
    m.upload_file(filename,filepath)
    # url = url.split('/')
    # url = [i for i in url if i]

    # m.download_file(BUCKETNAME, filename, r"")
    url = m.get_download_url(filename)
    # m.download_file('aiccssb','audioFile/在硬件工艺方面除了搭载瑞芯微旗舰八核高性_xiaoqin_female_301033_不切片_1696906754828.wav',r"C:\Users\<USER>\Desktop")
