from urllib.parse import quote_plus as urlquote

import pandas as pd
from sqlalchemy import create_engine
import pymysql
import time
from setting import logger


class MYSQL:
    def __init__(self, host, port, user, passwd, db):
        self.host = host
        self.port = port
        self.user = user
        self.passwd = passwd
        self.db = db

    def creat_engine(self, db):
        for i in range(2):
            try:
                return create_engine(f'mysql+pymysql://{self.user}:{urlquote(self.passwd)}@{self.host}:{self.port}/{db}', connect_args={'connect_timeout': 5})
            except:
                logger.error('报错:[数据库]连接失败,再次重连.')
        logger.error('报错:[数据库]连接失败,重连无效', exc_info=True)

    def read_sql_query(self, db, sql):
        try:
            df = pd.read_sql_query(sql, con=self.creat_engine(db))
            return df
        except:
            logger.error('报错:[数据库]连接失败', exc_info=True)

    def to_sql(self, df, db, table, if_exists='fail',**args):
        try:
            df.to_sql(table, con=self.creat_engine(db), if_exists=if_exists, index=False, **args)
            logger.debug(f'[数据库]写入成功,写入{len(df)}条')
        except:
            logger.error('报错:[数据库]写入sql失败', exc_info=True)

    def execute_sql(self, db, sql):
        for i in range(5):
            try:
                conn = pymysql.connect(host=self.host, port=self.port, user=self.user, passwd=self.passwd, database=db)
                cur = conn.cursor()
                cur.execute(sql)
                conn.commit()
                conn.close()
                logger.info('db:{},sql:{},execute succeed'.format(db, sql))
                return True
            except:
                logger.error('报错{},{},DEBUG execute failed! re-executing...'.format(db, sql),exc_info=True)
                time.sleep(1)
        return False

if __name__ == '__main__':
    mysql_config = {
        'host': '**************',
        'port': 3308,
        'user': 'root',
        'passwd': 'Qnzs123',
        'db': 'zhongyin'
    }
    db = MYSQL(**mysql_config)
