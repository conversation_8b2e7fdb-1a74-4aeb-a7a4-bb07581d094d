import json
import time
import socket
import uuid
import numpy as np

import os
import requests
import redis
from rediscluster import RedisCluster

from setting import logger
import setting
# from utils.my_utils import MyEncoder
_redis_cluster = None  # 全局变量保存单例

class MyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return super(MyEncoder, self).default(obj)


class REDIS:
    def __init__(self):
        self.subscribe_channels = []
        self.pubsub = None
        self.now_task = None
        # self.persistence_db = self.get_redis()

    def _get_db(self):
        if setting.USE_SINGLE_REDIS:
            db = self.connect_redis_singel()
        else:
            db = self.connect_redis_cluster()
        db.ping()  # 要调用 ping 之后才正真连接
        return db

    def get_redis(self):
        start = time.time()
        fail_times = 0
        while True:
            try:
                db = self._get_db()
                return db
            except Exception as e:
                fail_times += 1
                if fail_times >= 3 and (time.time()-start) >= 60:
                    raise e
                else:
                    logger.error('redis 连接失败, 错误:[{}:{}], 耗时:{}'.format(e.__class__.__name__, e, time.time()-start))
                    time.sleep(10)

    def get_pubsub(self):
        logger.debug(f"准备获取pubsub")
        start = time.time()
        fail_times = 0
        while True:
            try:
                if self.pubsub is not None:
                    logger.debug(f"需要关闭旧的pubsub")
                    self.pubsub.close()
                    self.pubsub = None
                    logger.debug(f"成功关闭旧的pubsub")
                db = self._get_db()
                self.pubsub = db.pubsub(ignore_subscribe_messages=True)
                for channel in self.subscribe_channels:
                    self.pubsub.subscribe(channel)
                logger.debug(f"成功获取pubsub")
                return
            except Exception as e:
                self.pubsub = None
                fail_times += 1
                if fail_times <= 30:
                    logger.error('获取 pubsub 失败, 错误:[{}:{}], 耗时:{}'.format(e.__class__.__name__, e, time.time()-start))
                time.sleep(30)

    # @staticmethod
    # def connect_redis_cluster():
    #     db = RedisCluster(startup_nodes=setting.REDIS_CLUSTER_LIST, decode_responses=True, password=setting.REDIS_CLUSTER_PASSWORD, health_check_interval=30,
    #                       socket_keepalive=True, socket_keepalive_options={socket.TCP_KEEPIDLE: 60, socket.TCP_KEEPINTVL: 30, socket.TCP_KEEPCNT: 3})
    #     return db

    @staticmethod
    def connect_redis_cluster():
        global _redis_cluster
        if _redis_cluster is None:
            _redis_cluster = RedisCluster(
                startup_nodes=setting.REDIS_CLUSTER_LIST,
                decode_responses=True,
                password=setting.REDIS_CLUSTER_PASSWORD,
                health_check_interval=30,
                socket_keepalive=True,
                skip_full_coverage_check=True,
                socket_keepalive_options={
                    socket.TCP_KEEPIDLE: 60,
                    socket.TCP_KEEPINTVL: 30,
                    socket.TCP_KEEPCNT: 3
                }
            )
        return _redis_cluster

    @staticmethod
    def connect_redis_singel():
        pool = redis.ConnectionPool(host=setting.REDIS_SETTING['host'], port=setting.REDIS_SETTING['port'],
                                    password=setting.REDIS_SETTING['password'], db=setting.REDIS_SETTING['dbname'],
                                    decode_responses=True)
        db = redis.Redis(connection_pool=pool, socket_keepalive=True, health_check_interval=30,
                         socket_keepalive_options={socket.TCP_KEEPIDLE: 60, socket.TCP_KEEPINTVL: 30, socket.TCP_KEEPCNT: 3})
        return db

    def get_data(self, key, need_json=True, show_log=True):
        if show_log:
            logger.debug('[{}] 开始读取redis数据'.format(key))
        try:
            data = self.get_redis().get(key)
            # 如果读取数据
            if "{" in data[:10]:
                if isinstance(data, str) and data.startswith('"') and data.endswith('"'):
                    data = data.replace('\\', "")
                    data = data[1:-1]
            if data.startswith('http'):
                data = self.url_to_json(key=key, url=data)
                data = json.dumps(data,ensure_ascii=False)
            if need_json:
                data = json.loads(data)
            if show_log:
                logger.debug('[{}] 读取redis数据成功'.format(key))

            return data
        except TypeError as e:
            data = str({})
            if show_log:
                logger.warning("该key'{}'在redis中不存在,重新将返回值设定为'str({})'".format(key, ""))
            return data
        except Exception as e:
            logger.error('[{}] 读取redis数据失败, 报错:{}'.format(key, e))
            raise Exception("读取redis数据失败")

    def set_data(self, key, msg, need_json=True, expire_time=None):
        logger.debug(f'准备写入redis, key:{key}')
        try:
            if need_json:
                msg = json.dumps(msg, cls=MyEncoder, ensure_ascii=False, indent=2).encode('utf8')
            self.get_redis().set(key, msg)
            if expire_time is not None:
                self.get_redis().expire(key, expire_time)
            logger.debug(f'成功写入redis, key:{key}')
        except Exception as e:
            logger.error(f'写入redis报错, key:{key}, 错误信息: {e}')

    def push_key(self, list_key, msg):
        logger.debug('开始push[{}]数据到redis列表[{}]'.format(msg, list_key))
        try:
            self.get_redis().rpush(list_key, msg)
            logger.debug('push数据[{}]到redis列表[{}]成功'.format(msg, list_key))
        except Exception as e:
            logger.error('push数据[{}]到redis列表[{}]失败, 报错:{}'.format(msg, list_key, e))
            raise Exception("push数据到redis列表失败")

    def pop_key(self, key):
        logger.debug('开始从redis列表[{}] 弹出'.format(key))
        try:
            task = self.get_redis().lpop(key)
            self.now_task = task
            return task
        except Exception as e:
            logger.error('从redis列表[{}] 弹出失败, 报错:{}'.format(key, e))
            raise Exception("从redis列表弹出失败")

    def key_exist(self, key):
        logger.debug('查看redis里是否存在key:{}'.format(key))
        try:
            return self.get_redis().exists(key) > 0
        except Exception as e:
            logger.error('查看redis里是否存在key:{}, 报错:{}'.format(key, e))
            raise Exception("查看redis里是否存在key失败")

    def get_keys(self, pattern):
        logger.debug(f'准备获取keys, pattern:{pattern}')
        try:
            return self.get_redis().keys(pattern=pattern)
        except Exception as e:
            logger.error(f'准备获取keys报错, pattern:{pattern}, 错误信息: {e}')
            raise Exception(f"准备获取keys报错, pattern:{pattern}, 错误信息: {e}")

    def get_list(self, list_key, start=0, end=-1):
        logger.debug('准备获取列表[{}]'.format(list_key))
        try:
            waiting_task_list = self.get_redis().lrange(list_key, start, end)
            if waiting_task_list:
                logger.debug('从redis获取列表[{}]成功'.format(list_key))
                return waiting_task_list
            else:
                return False
        except Exception as e:
            logger.error('从redis获取列表[{}] 失败, 报错:{}'.format(list, e))
            raise Exception("从redis获取列表失败")

    def hexists(self, hash_name, key):
        logger.debug(f'检查hash表是否有key:{key}')
        try:
            return self.get_redis().hexists(hash_name, key)
        except Exception as e:
            logger.error(f'检查hash表是否有key报错, key:{key}, 错误信息: {e}')

    def hset(self, hash_name, key, value):
        logger.debug(f'准备放到hash表, key:{key}')
        try:
            self.get_redis().hset(hash_name, key, value)
            logger.debug(f'成功放到hash表, key:{key}')
        except Exception as e:
            logger.error(f'放到hash表报错, key:{key}, 错误信息: {e}')

    def hget(self, hash_name, key):
        logger.debug(f'准备读取hash表, key:{key}')
        try:
            res = self.get_redis().hget(hash_name, key)
            logger.debug(f'成功读取hash表, key:{key},res:{res}')
            return res
        except Exception as e:
            logger.error(f'读取hash表报错, key:{key}, 错误信息: {e}')

    def hdel(self, hash_name, key):
        logger.debug(f'准备删除hash表key, key:{key}')
        try:
            self.get_redis().hdel(hash_name, key)
            logger.debug(f'成功删除hash表key, key:{key}')
        except Exception as e:
            logger.error(f'删除hash表key报错, key:{key}, 错误信息: {e}')

    def delete(self, hash_name):
        logger.debug(f'准备删除key, key_name:{hash_name}')
        try:
            res = self.get_redis().delete(hash_name)
            logger.debug(f'准备删除key, key_name:{hash_name},res:{res}')
            return res
        except Exception as e:
            logger.error(f'删除key报错, hash_name:{hash_name}, 错误信息: {e}')

    def lrem(self, list_name, count, value):
        logger.debug(f'准备删除list中的值, list_name:{list_name}, value:{value}')
        try:
            res = self.get_redis().lrem(list_name, count, value)
            logger.debug(f'删除list中的值, list_name:{list_name}, value:{value},res:{res}')
            return res
        except Exception as e:
            logger.error(f'删除list中的值报错, list_name:{list_name}, value:{value}, 错误信息: {e}')

    def publish_msg(self, channel, message):
        logger.debug(f'redis 开始发送信息, key:{channel}, msg:{message}')
        try:
            res = self.get_redis().publish(channel=channel, message=message)
            logger.debug(f'redis 发送信息成功, key:{channel}, msg:{message}, 监听者: {res}')
        except Exception as e:
            logger.debug(f'redis 发送信息失败, key:{channel}, msg:{message}, 错误信息:{e}')
            res = -1
        return res

    def subscribe(self, channel):
        self.subscribe_channels.append(channel)

    def listen_msg(self, callback_dict):
        self.get_pubsub()
        while True:
            try:
                item = None
                timeout = 60
                stop_time = time.time() + timeout
                health_result = self.pubsub.check_health()  # self.pubsub.reset() reset后需要重新订阅的
                if health_result is not None:
                    logger.debug(f"self.pubsub.check_health() 返回:{health_result}")
                while time.time() < stop_time:
                    item = self.pubsub.get_message(timeout=stop_time - time.time())
                    if item:
                        break
                if item is not None:
                    logger.debug(f'redis接收到监听消息, msg:{item}')
                    if item['type'] == 'message':
                        channel = item['channel']
                        if channel in callback_dict:
                            recv_data = json.loads(item["data"])
                            callback_dict[channel](**recv_data)
                else:
                    logger.debug(f"redis没监听到消息")
            except Exception as e:
                logger.error(f"redis监听消息报错,重新获取pubsub:{e}")
                self.get_pubsub()

    def count_add(self, count_key, amount):
        fail_times = 0
        while fail_times <= 1:
            try:
                self.persistence_db.incr(name=count_key, amount=amount)
                return
            except:
                logger.warning(f"redis计数器增加失败, count_key: {count_key}, amount: {amount}")
                self.persistence_db = self.get_redis()
                fail_times += 1

    def get_count(self, count_key):
        fail_times = 0
        while fail_times <= 1:
            try:
                data = self.persistence_db.get(name=count_key)
                return int(data)
            except:
                logger.warning(f"redis计数器增加失败, count_key: {count_key}")
                self.persistence_db = self.get_redis()
                fail_times += 1
        return None

    # 加锁
    def acquire_lock(self, lock_name="nlp", acquire_timeout=4, lock_timeout=7):
        """
        param lock_name: 锁名称
        param acquire_timeout: 客户端获取锁的超时时间
        param lock_timeout: 锁过期时间, 超过这个时间锁自动释放
        """
        redis_cli = self._get_db()
        identifier = str(uuid.uuid4())
        end_time = time.time() + acquire_timeout  # 客户端获取锁的结束时间
        while time.time() <= end_time:
            # setnx(key, value) 只有 key 不存在情况下将 key 设置为 value 返回 True
            # 若 key 存在则不做任何动作,返回 False
            if redis_cli.setnx(lock_name, identifier):
                redis_cli.expire(lock_name, lock_timeout)  # 设置锁的过期时间，防止线程获取锁后崩溃导致死锁
                return identifier  # 返回锁唯一标识
            elif redis_cli.ttl(lock_name) == -1:  # 当锁未被设置过期时间时，重新设置其过期时间
                redis_cli.expire(lock_name, lock_timeout)
            time.sleep(0.001)
        return False  # 获取超时返回 False

    # 释放锁
    def release_lock(self, identifier,lock_name="nlp"):
        """
        !! 集群模式不可用
        param lock_name:   锁名称
        param identifier:  锁标识
        """
        redis_cli = self._get_db()
        # 解锁操作需要在一个 redis 事务中进行，python 中 redis 事务通过 pipeline 封装实现
        with redis_cli.pipeline() as pipe:
            while True:
                try:
                    # 使用 WATCH 监听锁，如果删除过程中锁自动失效又被其他客户端拿到，即锁标识被其他客户端修改
                    # 此时设置了 WATCH 事务就不会再执行，这样就不会出现删除了其他客户端锁的情况
                    pipe.watch(lock_name)
                    id = pipe.get(lock_name)
                    if id and id == identifier:  # 判断解锁与加锁线程是否一致
                        pipe.multi()
                        pipe.delete(lock_name)  # 标识相同，在事务中删除锁
                        pipe.execute()  # 执行EXEC命令后自动执行UNWATCH
                        return True
                    pipe.unwatch()
                    break
                except redis.WatchError:
                    logger.error("redis-look error")
            return False

    def url_to_json(self,key,url):
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, 'TRAIN_DATA')
        os.makedirs(save_dir,exist_ok=True)
        save_filename = url.split('/')[-1]
        file_path = os.path.join(save_dir, f'{key}_{save_filename}')
        try:
            logger.info(f'正在请求下载链接{url}')
            data = requests.get(url)
            with open(file_path, 'wb') as f:
                f.write(data.content)
                f.close()
            data = json.loads(open(file_path,'r').read())
            logger.info(f'{url}下载成功')
            return data
        except Exception as e:
            logger.error(f'文件下载失败:{url}',exc_info=True)

if __name__ == "__main__":
    import threading
    import setting
    r = REDIS()
    r.set_data("test_data", {"TEST": 123})
    data = r.get_data("test_data")

    r.key_exist("test_data_123")
    print(data)

    # CALL_BACK_ID = -1
    # def call_back(text):
    #     global CALL_BACK_ID
    #     if CALL_BACK_ID != text-1:
    #         print("ERROR")
    #     CALL_BACK_ID = text
    #     print(type(text))
    #     print(f"call_back收到:{text}")
    # r.subscribe("nlp_test")
    # callback_dict = {
    #     "nlp_test": call_back
    # }
    # t1 = threading.Thread(target=r.listen_msg, args=(callback_dict,))
    # t1.start()

    res_0 = r.hget(setting.REDIS_TRAIN_CANCEL_HASH_KEY, key="ABC")
    r.hset(setting.REDIS_TRAIN_CANCEL_HASH_KEY, key="ABC", value=1)
    res_1 = r.hget(setting.REDIS_TRAIN_CANCEL_HASH_KEY, key="ABC")
    r.hset(setting.REDIS_TRAIN_CANCEL_HASH_KEY, key="ABC", value=int(res_1)+1)
    res_2 = r.hget(setting.REDIS_TRAIN_CANCEL_HASH_KEY, key="ABC")
    r.hdel(hash_name=setting.REDIS_TRAIN_CANCEL_HASH_KEY, key="ABC")
