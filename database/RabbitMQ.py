import pika
import time
from setting import logger, SERVICE_CHANNEL_DICT, USE_RABBIT_MQ, RABBIT_MQ_SETTING


class RabbitMQ:
    def __init__(self):
        logger.debug(f'rabbit_mq准备连接')
        self.credentials = pika.PlainCredentials(RABBIT_MQ_SETTING['user'], RABBIT_MQ_SETTING['password'])  # mq用户名和密码
        self.connection = pika.BlockingConnection(pika.ConnectionParameters(host=RABBIT_MQ_SETTING['host'], port=RABBIT_MQ_SETTING['port'], virtual_host='/', credentials=self.credentials))  # 虚拟队列需要指定参数 virtual_host，如果是默认的可以不填。
        logger.debug(f'rabbit_mq成功连接')

        self.channel = self.connection.channel()
        logger.debug(f'rabbit_mq获取channel')
        self.result = self.channel.queue_declare('', exclusive=True)
        logger.debug(f'rabbit_mq获取result')

        self.channel.exchange_declare(exchange='nlp_exchange', durable=True, exchange_type='direct')
        logger.debug(f'rabbit_mq声明exchange')

        # 绑定exchange和队列 exchange 使我们能够确切地指定消息应该到哪个队列去
        for service_channel in SERVICE_CHANNEL_DICT.values():
            self.channel.queue_bind(exchange='nlp_exchange', queue=self.result.method.queue, routing_key=service_channel["switch_model"])

    def get_channel(self):
        logger.debug(f'rabbit_mq准备连接')
        self.credentials = pika.PlainCredentials(RABBIT_MQ_SETTING['user'], RABBIT_MQ_SETTING['password'])  # mq用户名和密码
        self.connection = pika.BlockingConnection(pika.ConnectionParameters(host=RABBIT_MQ_SETTING['host'], port=RABBIT_MQ_SETTING['port'], virtual_host='/', credentials=self.credentials))  # 虚拟队列需要指定参数 virtual_host，如果是默认的可以不填。
        logger.debug(f'rabbit_mq成功连接')

        self.channel = self.connection.channel()
        logger.debug(f'rabbit_mq获取channel')
        self.result = self.channel.queue_declare('', exclusive=True)
        logger.debug(f'rabbit_mq获取result')

        self.channel.exchange_declare(exchange='nlp_exchange', durable=True, exchange_type='direct')
        logger.debug(f'rabbit_mq声明exchange')

        # 绑定exchange和队列 exchange 使我们能够确切地指定消息应该到哪个队列去
        for service_channel in SERVICE_CHANNEL_DICT.values():
            self.channel.queue_bind(exchange='nlp_exchange', queue=self.result.method.queue, routing_key=service_channel["switch_model"])

    def publish_msg(self, channel, message):
        logger.debug(f'rabbit_mq开始发送信息,key:{channel},msg:{message}')
        for _ in range(2):
            try:
                self.channel.basic_publish(exchange='nlp_exchange', routing_key=channel, body=message, properties=pika.BasicProperties(delivery_mode=2))  # delivery_mode = 2 声明消息在队列中持久化，delivery_mod = 1 消息非持久化
                logger.debug(f'rabbit_mq发送信息成功,key:{channel},msg:{message}')
                return 0
            except Exception as e:
                logger.debug(f'rabbit_mq发送信息失败,准备重新连接,错误信息:{e}')
                self.get_channel()
                logger.debug(f'rabbit_mq发送信息失败,key:{channel},msg:{message},错误信息:{e}')
        return -1

    def listen_msg(self, callback):
        while True:
            try:
                logger.debug(f'rabbit_mq开始监听')
                self.channel.basic_consume(self.result.method.queue, callback, auto_ack=False)
                self.channel.start_consuming()
                logger.debug(f'rabbit_mq接收到监听消息')
            except Exception as e:
                logger.error(f"rabbit_mq监听报错:{e}")
                time.sleep(10)


if __name__ == "__main__":
    import json
    import threading

    test_channel = SERVICE_CHANNEL_DICT["ClassifyMain"]["switch_model"]


    def callback(ch, method, properties, body):
        if method.routing_key == "":
            pass
        print(f"ch: {ch}")
        print(f"method: {method}")
        print(f"properties: {properties}")
        print(f"body: {body}")


    rabbit_send = RabbitMQ()
    rabbit_listen = RabbitMQ()

    # a_thread = threading.Thread(target=rabbit_listen.listen_msg, args=(callback,))
    # a_thread.start()

    transfer_data = json.dumps({"model_id": "test555", "flag": 1})
    res = rabbit_send.publish_msg(test_channel, transfer_data)
    print("wait")
