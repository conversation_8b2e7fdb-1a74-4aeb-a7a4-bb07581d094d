import json
import time
import numpy as np
import redis
from rediscluster import RedisCluster

USE_SINGLE_REDIS = True
REDIS_SETTING = {"host":"127.0.0.1",
                 "port":"6379",
                 "password":"Qnsoft#2021",
                 "dbname":0}
REDIS_CLUSTER_LIST = [{'host':'**************','port':6376},
                      {'host':'**************','port':6377},
                      {'host':'**************','port':6378},
                      {'host':'**************','port':6380},
                      {'host':'**************','port':6381},
                      {'host':'**************','port':6382}
                      ]
REDIS_CLUSTER_PASSWORD = "QNzs@.root_1347908642"

class MyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return super(MyEncoder, self).default(obj)

class REDIS:
    def __init__(self):
        self.subscribe_channels = []
        self.pubsub = None

    def _get_db(self):
        if USE_SINGLE_REDIS:
            db = self.connect_redis_singel()
        else:
            db = self.connect_redis_cluster()
        db.ping() # 要调用 ping 之后才正真连接
        return db

    def get_redis(self):
        start = time.time()
        fail_times = 0
        while True:
            try:
                db = self._get_db()
                return db
            except Exception as e:
                fail_times += 1
                if fail_times >= 3 and (time.time()-start) >= 60:
                    raise e
                else:
                    print('redis 连接失败, 错误:[{}:{}], 耗时:{}'.format(e.__class__.__name__, e, time.time()-start))
                    time.sleep(10)

    def get_pubsub(self):
        start = time.time()
        fail_times = 0
        while True:
            try:
                db = self._get_db()
                self.pubsub = db.pubsub()
                for channel in self.subscribe_channels:
                    self.pubsub.subscribe(channel)
                return
            except Exception as e:
                fail_times += 1
                if fail_times <= 30:
                    print('获取 pubsub 失败, 错误:[{}:{}], 耗时:{}'.format(e.__class__.__name__, e, time.time()-start))
                time.sleep(30)

    @staticmethod
    def connect_redis_cluster():
        db = RedisCluster(startup_nodes=REDIS_CLUSTER_LIST, decode_responses=True, password=REDIS_CLUSTER_PASSWORD)
        return db

    @staticmethod
    def connect_redis_singel():
        pool = redis.ConnectionPool(host=REDIS_SETTING['host'], port=REDIS_SETTING['port'],
                                    password=REDIS_SETTING['password'], db=REDIS_SETTING['dbname'],
                                    decode_responses=True)
        db = redis.Redis(connection_pool=pool)
        return db

    def get_data(self, key, need_json=True):
        print('[{}] 开始读取redis数据'.format(key))
        try:
            data = self.get_redis().get(key)
            if isinstance(data, str) and data.startswith('"') and data.endswith('"'):
                data = data.replace('\\', "")
                data = data[1:-1]
            if need_json:
                data = json.loads(data)
            print('[{}] 读取redis数据成功'.format(key))
            return data
        except Exception as e:
            print('[{}] 读取redis数据失败, 报错:{}'.format(key, e))
            raise Exception("读取redis数据失败")

    def set_data(self, key, msg, need_json=True, expire_time=None):
        print(f'准备写入redis, key:{key}')
        try:
            if need_json:
                msg = json.dumps(msg, cls=MyEncoder, ensure_ascii=False, indent=2).encode('utf8')
            self.get_redis().set(key, msg)
            if expire_time is not None:
                self.get_redis().expire(key, expire_time)
            print(f'成功写入redis, key:{key}')
        except Exception as e:
            print(f'写入redis报错, key:{key}, 错误信息: {e}')

    def get_keys(self, pattern):
        print(f'准备获取keys, pattern:{pattern}')
        try:
            return self.get_redis().keys(pattern=pattern)
        except Exception as e:
            print(f'准备获取keys报错, pattern:{pattern}, 错误信息: {e}')
            raise Exception(f"准备获取keys报错, pattern:{pattern}, 错误信息: {e}")

    def publish_msg(self, channel, message):
        print(f'redis 开始发送信息, key:{channel}, msg:{message}')
        try:
            res = self.get_redis().publish(channel=channel, message=message)
            print(f'redis 发送信息成功, key:{channel}, msg:{message}, 监听者: {res}')
        except Exception as e:
            print(f'redis 发送信息失败, key:{channel}, msg:{message}, 错误信息:{e}')
            res = -1
        return res

    def subscribe(self, channel):
        self.subscribe_channels.append(channel)

    def listen_msg(self, callback_dict):
        while True:
            self.get_pubsub()
            try:
                for item in self.pubsub.listen():  # 监听状态：有消息发布了就拿过来
                    print(f'redis 接收到监听消息, msg:{item}')
                    if item['type'] == 'message':
                        channel = item['channel']
                        if channel in callback_dict:
                            data = json.loads(item["data"])
                            callback_dict[channel](**data)
            except Exception as e:
                print(f"listen 报错:{e}")


if __name__ == "__main__":
    r = REDIS()
    msg = {"code": 1, "model_id": "1422160910931230721", "task": "train", "msg": "训练失败", "score": 0}
    r.set_data("nlp_train_1422160910931230721", msg=msg)
