## aicc-nlu文本机器人部署文档

### 一、项目地址

aicc-nlp-nlu: http://192.168.1.234:20080/nlp/aicc-nlp-nlu/-/tree/SKM_4.5.0_2

### 二、启动服务
服务：FAQDataTypeHttpServer.py

#### 1. 启动方式

* 用gunicorn启动服务，端口号可更改，w参数表示进程数，t表示超时时间 (因启动时加载模型容易超时，尽量设置大一些)，启动命令:
  * gunicorn -w 1 -b 0.0.0.0:9888 FAQDataTypeHttpServer:app -t 1000
* 用start.sh脚本
  * bash ./bin/start.sh -a FAQDataTypeHttpServer -p 9889 -w 1
  * 需要修改./bin/start.sh内部python的路径

#### 2. 训练端和预测端

* 需要分别部署训练端和预测端（训练端和预测端通过./conf/setting.ini client字段区分）
* 训练端：训练模型、通知模型上下线、交互测试
* 预测端：预测

### 三、setting.ini配置

部署需要修改setting.ini配置,具体配置说明见setting.ini