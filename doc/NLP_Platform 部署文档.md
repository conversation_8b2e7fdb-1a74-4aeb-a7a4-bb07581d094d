## NLP_Platform 部署文档

### 一、项目地址

Git: http://192.168.1.199:3000/chenbk/NLP_Platform.git

包含的服务：

* 知识库 FAQ
* 分类
* 正则匹配
* 相似度打分
* 聚类

### 二、setting.py 配置

* Redis 相关配置
	* USE_SINGLE_REDIS：True (不使用 Redis 集群)，False (使用 Redis集群)
	* REDIS_SETTING：Redis 配置，非集群
	* REDIS_CLUSTER_LIST：集群 Redis 配置
	* REDIS_CLUSTER_PASSWORD：集群 Redis 的密码
* CLIENT："train" (训练端)，"predict" (预测端)
* NUM_THREADS：CPU 占用数量 
* 同步模型 (发送模型) 相关配置 (**一般不改，保持 Redis 发送模型即可**)
	* SEND_MODEL_MODE：发送模型的方式，可选 "Redis" 和 "Scp"，建议使用 "Redis"
	* PREDICT_SERVER_IPS：如果 SEND_MODEL_MODE 选择 "Redis" 保持为空列表即可 (即[])；如果 SEND_MODEL_MODE 选择 "Scp" 则需要填写预测端的 IP, 例如 ['*************', '*************']

### 三、启动服务

用 gunicorn 启动服务，9888 是端口号 (可更改)，w 参数表示进程数，t 表示超时时间 (因启动时加载模型容易超时，尽量设置大一些)，启动要区分训练端和预测端。

服务文件：

* FAQHttpServer 知识库
* ClusterHttpServer 聚类服务

启动命令:

* gunicorn -w 1 -b 0.0.0.0:9888 FAQHttpServer:app -t 1000
* gunicorn -w 1 -b 0.0.0.0:9890 ClusterHttpServer:app -t 1000

### 四、单台服务器同时启动训练和预测端

* 启动命令
	* 训练端启动:  gunicorn -w 1 -b 0.0.0.0:9888 FAQHttpServer:app -t 1000
	* 预测端启动:  gunicorn -w 1 -b 0.0.0.0:9878 FAQHttpServer:app -t 1000
	* 聚类服务: gunicorn -w 1 -b 0.0.0.0:9890 ClusterHttpServer:app -t 1000
* 参数说明
	* w: 进程数，训练端要设置为 1，预测端可以设置为大于 1
	* t: 超时时间，启动时要加载模型，比较耗时，因此尽量设置大一点的值，如 1000 
	* 端口号: 0.0.0.0: 后面的数字，**注意在单台服务器同时启动训练和预测端时要设置不同的端口号**

### 五、多台服务器分别启动训练和预测端

* 启动命令
	* 训练端启动:  gunicorn -w 1 -b 0.0.0.0:9888 FAQHttpServer:app -t 1000
	* 预测端启动:  gunicorn -w 1 -b 0.0.0.0:9888 FAQHttpServer:app -t 1000
	* 聚类服务: gunicorn -w 1 -b 0.0.0.0:9890 ClusterHttpServer:app -t 1000
* 参数说明
	* w: 进程数，训练端要设置为 1，预测端可以设置为大于 1
	* t: 超时时间，启动时要加载模型，比较耗时，因此尽量设置大一点的值，如 1000
	* 端口号: 0.0.0.0: 后面的数字，**在多台服务器分别启动训练和预测端时可以设置相同的端口号**

### 六、从 github 拉代码部署 NLP_Platform 要保留的文件

* /NLP_Platform/pretrain_model 下的所有文件