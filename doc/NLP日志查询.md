## 预测失败查询

#### 1. 判断是训练端失败还是预测端失败

* 方法一: 交互测试时训练端，外呼是预测端。
* 方法二: 直接在日志搜用户的输入和 model_id，看哪台服务器收到请求并返回错误结果 (关键词：用户的输入和model_id)。

#### 2.训练端预测失败

* 检查训练端的日志，看是哪个服务返回错误结果，一共有三个服务:
	* 知识库 (关键词：SearchPredict、model_id、用户输入)
	* 正则 (关键词：RegularPredict、model_id、用户输入)
	* 相似度打分 (关键词：SimScorePredict 和用户输入)
* 如果是相似度打分预测错误，则直接联系开发，如果是知识库和正则预测错误，就继续下面的步骤。
* 检查训练端训练日志，检查训练数据 (关键词：model_id 和 "训练数据")，看是否训练成功 (训练成功关键词："Redis 发送成功" 和 model_id)。
* 如果没有训练成功，看具体原因，可能是输入数据的问题，也可能是服务器资源被占用的问题。
* 如果训练成功了，检查训练数据和请求的参数是否有问题 (例如正则匹配要看下数据里面是否有正确能匹配上的正则，以及请求参数中的 labelIDs 是否对应)。
* 如果数据和参数都没问题，则进入训练端的 NLP_Model/model_id 文件夹，查看具体服务的模型文件是否存在  (正则模型文件在 Regular 里，知识库模型文件在 SimCSEPreRank 里)。
* 如果这些都没问题，联系开发。

#### 3. 预测端预测失败

* 先检查是哪台预测端服务器，哪个服务预测出错，检查方法参考上面训练端的方法。
* 进入预测端服务器 NLP_Model/model_id 文件夹，看对应服务的模型文件是否存在 (正则模型文件在 Regular 里，知识库模型文件在 SimCSEPreRank 里)。
4. 如果预测端模型文件不存在，要判断训练端是否有发送 model_id 的模型文件。如果训练端发送了，检查预测端是否有收到下载模型的 redis 消息 (关键词: nlp_download_model_channel 和 model_id)。
5. 如果预测端有对应的模型文件，检查训练端是否有发送模型上线请求 (关键词：switch_model_channel 和 model_id)，如果训练端没发送说明还未上线。如果训练端发送了，但是预测端收到上线请求  (关键词：switch_model_channel 和 model_id)，就要检查 redis 配置或者看服务在该时间段是否重启了。