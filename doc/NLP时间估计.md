## 服务配置

* CPU: 16 核
* EPOCH: 2，EPOCH 代表训练的轮数，每一轮训练会将所有训练数据过一遍。**训练时间和 EPOCH 基本成正比，如果 2 个 EPOCH 要 10 分钟，那 4 个 EPOCH 要 20 分钟。**
* BATCH: 64，一轮训练里会把数据分为很多批 (batch)，BATCH 是指每一批数据的量，例如 64，就是每一批数据量为 64。

## 预估时间

* 正则训练速度很快，预估 20 秒。
* 纠错模型训练速度也很快，预估也是 20 秒。
* 检索模型 (用于知识库检索)：
	* BATCH 参数配置项：SIMCSE_PRE_RANK_BATCH_SIZE
	* EPOCH 参数配置项：SIMCSE_PRE_RANK_EPOCHS
	* 训练时间 = 文本总量//BATCH✖EPOCH✖5.8 (//表示整除)
	* 如果训练数据总量是20000，EPOCH是2，BATCH是64，那么训练时间预估=20000//64✖2✖5.8=3619 秒。
* 分类模型 (用于返回语料优化建议)：
	* BATCH 参数配置项：BERT_CLASSIFICATION_BATCH_SIZE
	* EPOCH 参数配置项：BERT_CLASSIFICATION_EPOCHS
	* 训练时间=文本总量✖EPOCH✖0.03121488325633662。
	* 如果数据总量是20000，EPOCH是2，那么训练时间预估=20000✖2✖0.03121488325633662=1248 秒。

* 我们返回的总训练时间是上面四个模型预估时间的总和，即 20+20+3619+1248=4907 秒。

## 建议配置及预估时间

* 检索模型 (用于知识库检索)：
	* BATCH 建议设置为 64，即 SIMCSE_PRE_RANK_BATCH_SIZE=64
	* EPOCH 建议设置为 10，即 SIMCSE_PRE_RANK_EPOCHS=10
	* 训练时间 = 文本总量//64✖10✖5.8
	* 如果文本数量是 20000，预估时间=20000//64✖10✖5.8=18096 秒
* 分类模型 (用于返回语料优化建议)：
	* BATCH 建议设置为 64，即 BERT_CLASSIFICATION_BATCH_SIZE=64
	* EPOCH 建议设置为 30：即 BERT_CLASSIFICATION_EPOCHS=30
	* 训练时间=文本总量✖30✖0.03121488325633662。
	* 如果数据总量是20000，预估时间=20000✖30✖0.03121488325633662=18728 秒
* 总时间预估为 20+20+18096+18728=36864 秒。

