* 训练数据
    * 共5069个数据，正例853，负例4216
* baike_v1
    * 第一次实验，在训练中随机选1%的数据互译，但只训练了4个epoch就停了。
    * acc:0.8792661274413099
    * precision:0.5825908156271419
    * recall:0.9964830011723329
    * f1:0.7352941176470588
* baike_v2_100000
    * 只使用baike问答数据里面的100000个答案训练，没进行互译、EDA、难样本采样。
    * acc:0.9686328664430854
    * precision:0.8449304174950298
    * recall:0.9964830011723329
    * f1:0.914470145239376
* baike_v2_100000_transv1
    * 只使用baike问答数据里面的100000个答案训练，使用了互译(transv1)，没进行EDA、难样本采样。
    * acc:0.9443677253896232
    * precision:0.7528786536758193
    * recall:0.9964830011723329
    * f1:0.8577194752774976
* baike_v2_100000_transv3
    * 只使用baike问答数据里面的100000个答案训练，使用了互译(transv3)，没进行EDA、难样本采样。
    * acc:0.9854014598540146
    * precision:0.9275521405049396
    * recall:0.9906213364595545
    * f1:0.9580498866213153
```
R7的电机技术获国家科学技术创新奖，该奖项是国家技术创新最高奖项: 0.49408531188964844
我们拥有全国最好的技术，过去也获得了大量国家奖项: 0.0427815206348896
您好我们的技术是国家领先的，曾经获得过多个国家奖励: 0.9996975660324097
小明写的作文获国家文学创新奖，该奖项是文学领域最高奖项: 0.00013604696141555905
R7的电机技术获国家科学技术创新奖，V2X就是车子跟万物互联，可以提前预知道路情况，并计算出最优的驾驶操作: 0.018351580947637558
可以提前预知道路情况，并计算出最优的驾驶操作: 9.41168109420687e-05
```
* baike_v3_100000_transv3_eda
    * 只使用baike问答数据里面的100000个答案训练，使用了互译(transv3)和EDA,未难样本采样。
    * acc:0.9366739001775498
    * precision:0.7281303602058319
    * recall:0.9953106682297772
    * f1:0.8410104011887072
```
R7的电机技术获国家科学技术创新奖，该奖项是国家技术创新最高奖项: 0.8616976141929626
我们拥有全国最好的技术，过去也获得了大量国家奖项: 0.9956314563751221
您好我们的技术是国家领先的，曾经获得过多个国家奖励: 0.9999544620513916
小明写的作文获国家文学创新奖，该奖项是文学领域最高奖项: 0.6343377232551575
R7的电机技术获国家科学技术创新奖，V2X就是车子跟万物互联，可以提前预知道路情况，并计算出最优的驾驶操作: 0.4928446412086487
可以提前预知道路情况，并计算出最优的驾驶操作: 4.6001670853002e-05
```
* baike_v3_100000_transv3_eda_simneg
    * acc:0.9854014598540146
    * precision:0.9266155531215772
    * recall:0.9917936694021102
    * f1:0.958097395243488
```
R7的电机技术获国家科学技术创新奖，该奖项是国家技术创新最高奖项: 0.09838808327913284
我们拥有全国最好的技术，过去也获得了大量国家奖项: 0.47063785791397095
您好我们的技术是国家领先的，曾经获得过多个国家奖励: 0.9995670914649963
小明写的作文获国家文学创新奖，该奖项是文学领域最高奖项: 0.0003473618708085269
R7的电机技术获国家科学技术创新奖，V2X就是车子跟万物互联，可以提前预知道路情况，并计算出最优的驾驶操作: 0.43441492319107056
可以提前预知道路情况，并计算出最优的驾驶操作: 0.00017499446403235197
```

* baike_v3_855530_transv3_eda
    * acc:0.9356875123298481
    * precision:0.7254063301967494
    * recall:0.9941383352872216
    * f1:0.8387734915924828
```
R7的电机技术获国家科学技术创新奖，该奖项是国家技术创新最高奖项: 0.49531930685043335
我们拥有全国最好的技术，过去也获得了大量国家奖项: 0.39780882000923157
您好我们的技术是国家领先的，曾经获得过多个国家奖励: 0.9997791647911072
小明写的作文获国家文学创新奖，该奖项是文学领域最高奖项: 0.002229626290500164
R7的电机技术获国家科学技术创新奖，V2X就是车子跟万物互联，可以提前预知道路情况，并计算出最优的驾驶操作: 0.49380728602409363
可以提前预知道路情况，并计算出最优的驾驶操作: 0.0002532601938582957
```