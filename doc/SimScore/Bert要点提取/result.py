#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:result.py
@time:2022/04/29
"""

from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import pandas as pd

data = pd.read_csv("./baike_v3_855530_transv3_eda.csv", encoding="utf-8-sig")
label = data["label"].tolist()
predict_label = data["predict_label"].tolist()
print(f"acc:{accuracy_score(y_true=label, y_pred=predict_label)}")
print(f"precision:{precision_score(y_true=label, y_pred=predict_label)}")
print(f"recall:{recall_score(y_true=label, y_pred=predict_label)}")
print(f"f1:{f1_score(y_true=label, y_pred=predict_label)}")
print("wait")
