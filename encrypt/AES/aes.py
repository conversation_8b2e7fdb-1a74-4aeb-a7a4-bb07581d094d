#!/usr/bin/python
# -*- coding: utf-8 -*-
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

key = "Qnzs@2019-123456"

# 加密详情：
# 1. 密钥：Qnzs@2019-123456
# 2. 加密方式：AES
# 3. 填充方式：PKCS7
# 4. 偏移量：无
# 5. 模式：ECB
# 6. 输出：base64


def getKey():
    return key


def pad_zeros(data, block_size):
    padding_length = block_size - len(data) % block_size
    return data + b'\0' * padding_length


def unpad_zeros(data):
    return data.rstrip(b'\0')


def encrypt(source, key):
    try:
        source_bytes = source.encode('utf-8')
        key_bytes = key.encode('utf-8')
        cipher = AES.new(key_bytes, AES.MODE_ECB)
        # Zeros 填充方式
        # padded_data = pad_zeros(source_bytes, AES.block_size)
        # encrypted = cipher.encrypt(padded_data)
        # PKCS7 填充方式
        encrypted = cipher.encrypt(pad(source_bytes, AES.block_size))
        return base64.b64encode(encrypted).decode('utf-8')
    except Exception as e:
        print(e)
        return None


def decrypt(encryptStr, key):
    try:
        sourceBytes = base64.b64decode(encryptStr)
        keyBytes = key.encode('utf-8')
        cipher = AES.new(keyBytes, AES.MODE_ECB)
        decrypted = cipher.decrypt(sourceBytes)
        # 使用 unpad 函数去除填充
        unpadded = unpad(decrypted, AES.block_size)
        return unpadded.decode('utf-8')
    except Exception as e:
        print(e)
        return None


# 加密方法
def encrypt_oracle(text):
    key = getKey()
    encrypted_text = encrypt(text, key)
    return encrypted_text


# 解密方法
def decrypt_oralce(encrypted_text):
    key = getKey()
    decrypted_text = decrypt(encrypted_text, key)
    return decrypted_text

if __name__ == "__main__":
    tips= "Qnzs#123"  # 需要加密的key
    key = getKey()
    secret = encrypt(tips, key)
    print('*******对key加密解密******')
    print("原始输入:{},加密后:{}".format(tips, secret))
    plaintxt = decrypt(secret, key)
    print("原始输入:{},解密后:{}".format(secret, plaintxt))
    print(plaintxt == "Qnzs_NLP_Test_Redis_2024")

    print('*******对文本加密解密******')
    text = 'Qnzs_NLP_Test_Redis_2024'  # 待加密文本
    en_text = encrypt_oracle(text)
    print('加密输入:{},输出:{}'.format(text, en_text))
    decrypted_text = decrypt_oralce(en_text)
    print('解密输入:{},输出:{}'.format(en_text, decrypted_text))
