# -*- coding: utf-8 -*-

import fcntl
import json
import os
import threading
import time

import setting
from database.RabbitMQ import RabbitM<PERSON>
from database.REDIS import REDIS
from module.TextPairSim.BertTextPairSimPoint.BertTextPairSimPointPredict import BertTextPairSimPointPredict
from setting import logger

service_name = "BertTextPairSimPoint"
model_record_file = setting.MODEL_RECORD_FILE + f".{service_name}"
switch_model_channel = setting.SERVICE_CHANNEL_DICT["BertTextPairSimPointMain"]["switch_model"]

class BertTextPairSimPointMain:
    def __init__(self):
        self.model = BertTextPairSimPointPredict()
        self.Redis = REDIS()

        if setting.USE_RABBIT_MQ:
            self.rabbit_listen = RabbitMQ()
        else:
            self.rabbit_listen = None

        if setting.CLIENT != "train":
            if setting.USE_RABBIT_MQ:
                self.switch_thread = threading.Thread(target=self.rabbit_listen.listen_msg, args=(self.switch_callback,))
                self.switch_thread.start()
            else:
                self.Redis.subscribe(switch_model_channel)
                callback_dict = {
                    switch_model_channel: self.switch_model
                }
                t1 = threading.Thread(target=self.Redis.listen_msg, args=(callback_dict,))
                t1.start()

        # 加载已上线模型
        self.model_record = {}
        if setting.CLIENT == 'predict':
            self.init_model()

        # 测试加载的模型
        self.train_client_load_model_list = []

    def switch_callback(self, ch, method, properties, body):
        if method.routing_key == switch_model_channel:
            logger.debug(f'rabbit_mq接收到监听消息:{switch_model_channel},msg:{body}')
            logger.debug(f"ch:{ch}")
            logger.debug(f"method:{method}")
            logger.debug(f"properties:{properties}")
            logger.debug(f"body:{body}")
            body_dict = json.loads(body)
            self.switch_model(**body_dict)

    def init_model(self):
        # 加载已上线模型
        model_ids = []
        if os.path.exists(model_record_file):
            with open(model_record_file, 'r', encoding='utf-8') as f:
                try:
                    self.model_record = json.load(f)
                    model_ids = list(self.model_record.keys()) # 防止 model_record 发生变化而报错
                except Exception as e:
                    logger.warning(f"加载历史上线模型记录文件失败: {e}")
        try:
            for model_id in model_ids:
                if self._load_model(model_id=model_id): # 加载失败的要在 model_record 里面移除
                    self.model_record.pop(model_id, 0)
                logger.debug('[{}] 已加载历史上线模型'.format(model_id))
            self.dump_record()
        except Exception as e:
            logger.warning(f"加载历史上线模型失败: {e}")

    def dump_record(self):
        """记录已上线模型"""
        with open(model_record_file, 'w', encoding='utf-8') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.model_record, f, ensure_ascii=False)
        logger.debug('保存已上线模型记录')

    def train(self, model_id, data_key, sendredis=False,**kwargs):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "train",
            "msg": "训练成功",
            "threshold": 0.1
        }
        try:
            start = time.time()
            data = self.Redis.get_data(key=data_key)
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.model.__class__.__name__}/")
            self.model.train(model_id=model_id, data=data, save_dir=model_save_dir)
            logger.debug(f"{self.model.__class__.__name__} 训练成功 - model_id:{model_id},耗时:{time.time() - start}s")
        except Exception as e:
            logger.error(f"训练失败, model_id:{model_id}, 错误:{e}")
            result["code"] = 1
            result["msg"] = f'训练错误:{e}'
            result["error_code"] = "NLU91020"
            result["error_type"] = 0

        if sendredis:
            self.Redis.set_data(key=f"nlp_train_{model_id}", msg=result)
        if model_id in self.train_client_load_model_list:
            self._load_model(model_id=model_id)
        return result

    def switch_model(self, model_id, flag):
        # BertTextPairSimPointMain无需进行上下线
        return 0

    def _load_model(self, model_id):
        # BertTextPairSimPointMain无需进行上下线
        pass

    def _offline_model(self, model_id):
        # BertTextPairSimPointMain无需进行上下线
        pass

    # @staticmethod
    def train_time(self, data):
        return 300

    def predict(self, model_id, query, point_list):
        result = {
            "code": 0,
            "model_id": model_id,
            "query": query,
            "answers": point_list,
            "threshold": 0.82,
            "score": 0,
            "msg": "预测成功",
        }

        try:
            start = time.time()
            scores, threshold = self.model.predict(model_id=model_id, query=query, point_list=point_list)
            logger.debug(f"预测完成 - model_id: {model_id}, threshold: {threshold}, query: {query}, point_list: {point_list}, scores: {scores}, 耗时: {time.time()-start}")
            result["all_score"] = scores
            result["threshold"] = threshold
            result["score"] = max(scores)
        except Exception as e:
            logger.error(f"预测失败 - model_id: {model_id}, query: {query}, point_list: {point_list}, 错误: {e}")
            raise e
        return result

if __name__ == "__main__":
    model = BertTextPairSimPointMain()
    model_id = f"keypoint"
    data_key = "score_test_data_keypoint"

    # ==================== predict ====================
    point_list = ["我们的技术是国家领先的，曾经取得多个国家大奖",
                  "R7的电机技术获国家科学技术创新奖，该奖项是国家技术创新最高奖项",
                  "小明写的作文获国家文学创新奖，该奖项是文学领域最高奖项",
                  "R7的电机技术获国家科学技术创新奖，V2X就是车子跟万物互联，可以提前预知道路情况，并计算出最优的驾驶操作",
                  "可以提前预知道路情况，并计算出最优的驾驶操作",
                  "最厉害的就是我们的电机"]
    query = "您好我们的技术是国家领先的，曾经获得过多个国家奖励，最厉害的就是我们的电机，效率高达百分之九十七，今年新出的电车比之前的燃油车厉害了好几个量级"
    result = model.predict(model_id=model_id, query=query, point_list=point_list)
    print("wait")
