# -*- coding: utf-8 -*-

import fcntl
import json
import os
import threading
import time
import re
import pandas as pd
import setting
from setting import logger
from utils import my_utils

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)
# 限制CPU 占用，避免挤占其他服务
tf.config.threading.set_inter_op_parallelism_threads(setting.NUM_THREADS)
tf.config.threading.set_intra_op_parallelism_threads(setting.NUM_THREADS)
mutex = threading.Lock()
from database.REDIS import REDIS
from database.RabbitMQ import RabbitMQ
from module.Classification.BertClassifier.BertClassifier import BertClassifier

service_name = "Classify"
model_record_file = setting.MODEL_RECORD_FILE + f".{service_name}"
switch_model_channel = setting.SERVICE_CHANNEL_DICT["ClassifyMain"]["switch_model"]

class ClassifyMain:
    def __init__(self, callout=False):
        self.CALLOUR_SERIVE = callout
        os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
        self.redis = REDIS()
        self.load_model_dict = {}
        self.lastest_switch_time = {}

        if setting.USE_RABBIT_MQ:
            self.rabbit_listen = RabbitMQ()
        else:
            self.rabbit_listen = None

        # 粗排
        self.class_model = BertClassifier()

        # Redis
        if setting.CLIENT != "train":
            if setting.USE_RABBIT_MQ:
                self.switch_thread = threading.Thread(target=self.rabbit_listen.listen_msg, args=(self.switch_callback,))
                self.switch_thread.start()
            else:
                self.redis.subscribe(switch_model_channel)
                callback_dict = {
                    switch_model_channel: self.switch_model
                }
                t1 = threading.Thread(target=self.redis.listen_msg, args=(callback_dict,))
                t1.start()

        # 加载已上线模型
        self.model_record = {}
        if setting.CLIENT == 'predict' and (not setting.AUTO_ONLINE_MODEL or setting.LRU_MODEL_LOAD_NUM):
            self.init_model()

        # 测试加载的模型
        self.train_client_load_model_list = []

        #外呼自动上下线逻辑
        if self.CALLOUR_SERIVE:
            #外呼加载模型列表
            self.callout_load_model_dict = {}
            # 外呼自动下线模型
            self.last_predict_model_record = {}
            t2 = threading.Thread(target=self.auto_offline)
            t2.start()
            #模型自动上线更新过的模型
            self.last_train_time_record = {}
            t3 = threading.Thread(target=self.auto_online_trained_model)
            t3.start()
        if setting.AUTO_ONLINE_MODEL and setting.CLIENT == 'predict':
            t4 = threading.Thread(target=my_utils.auto_offline, args=(self, self.load_model_dict))
            t4.start()
        if setting.CLIENT == 'train' or setting.AUTO_ONLINE_MODEL:
            t5 = threading.Thread(target=my_utils.get_model_record_time, args=(self, self.lastest_switch_time,))
            t5.start()

    def switch_callback(self, ch, method, properties, body):
        if method.routing_key == switch_model_channel:
            logger.debug(f'rabbit_mq接收到监听消息:{switch_model_channel},msg:{body}')
            logger.debug(f"ch:{ch}")
            logger.debug(f"method:{method}")
            logger.debug(f"properties:{properties}")
            logger.debug(f"body:{body}")
            body_dict = json.loads(body)
            self.switch_model(**body_dict)

    def init_model(self):
        # 加载已上线模型
        model_ids = []
        if os.path.exists(model_record_file):
            with open(model_record_file, 'r', encoding='utf-8') as f:
                try:
                    self.model_record = json.load(f)
                    self.model_record = dict(sorted(self.model_record.items(), key=lambda x: x[1])) #从小到大
                    model_ids = list(self.model_record.keys())[-setting.LRU_MODEL_LOAD_NUM:] #只加载最近发布的模型
                    logger.info(f'{self.__class__.__name__}待加载列表:{model_ids}')
                except Exception as e:
                    logger.warning(f"加载历史上线模型记录文件失败: {e}")
        try:
            for model_id in model_ids:
                if self._load_model(model_id=model_id):  # 加载失败的要在 model_record 里面移除
                    self.model_record.pop(model_id, 0)
                logger.debug('[{}] 已加载历史上线模型'.format(model_id))
            self.dump_record()
        except Exception as e:
            logger.warning(f"加载历史上线模型失败: {e}")

    def dump_record(self):
        if setting.SEND_MODEL_MODE == "Nas":
            return
        with open(model_record_file, 'w', encoding='utf-8') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.model_record, f, ensure_ascii=False)
        logger.debug('保存已上线模型记录')

    def train(self, model_id, data_key='', sendredis=True,**kwargs):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "train",
            "msg": "训练成功",
            "score": 0,
        }
        try:
            data = self.redis.get_data(key=data_key)

            # 开始训练分类模型
            start = time.time()
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.class_model.__class__.__name__}/")
            score, mixed = self.class_model.train(model_id=model_id, data=data, save_dir=model_save_dir)
            logger.debug(f"{self.class_model.__class__.__name__} 训练成功 - model_id:{model_id},耗时:{time.time() - start}s")
            result["score"] = float(score)
            result["suggest"] = mixed
            logger.debug(f"语料优化建议:{mixed}")
        except Exception as e:
            logger.error(f"训练失败 - model_id: {model_id}, 错误: {e}", exc_info=True)
            result["code"] = 1
            result["msg"] = f'训练错误:{e}'
            result["error_code"] = "NLU91020"
            result["error_type"] = 0

        if sendredis:
            self.redis.set_data(key=f"classify_train_{model_id}", msg=result)
        if model_id in self.train_client_load_model_list:
            self._load_model(model_id=model_id)
        return result

    def switch_model(self, model_id, flag):
        code = 0
        if flag == 1:
            code = self._load_model(model_id)
            if code == 0:
                self.model_record[model_id] = time.time()
                self.load_model_dict[model_id] = time.time()
            else:
                self.model_record.pop(model_id, 0)
                code = 1
        else:
            self._offline_model(model_id)
            self.model_record.pop(model_id, 0)
            if model_id in self.load_model_dict:
                self.load_model_dict.pop(model_id)
        self.dump_record()
        return code

    def _load_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "load_model",
            "msg": "加载模型成功"
        }

        logger.debug(f"开始加载模型 - model_id: {model_id}, 进程: {os.getpid()}")
        # 加载模型
        try:
            save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.class_model.__class__.__name__}/")
            self.class_model.load_model(model_id=model_id, save_dir=save_dir)

            logger.debug(f"加载模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
            self.load_model_dict[model_id] = time.time()
            # 先进先出
            for model_id in list(self.load_model_dict)[:-setting.LRU_MODEL_LOAD_NUM]:
                try:
                    self._offline_model(model_id)
                except:
                    logger.error(f'lru模型下线失败,model_id:{model_id}', exc_info=True)
            return 0
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 400
            result["msg"] = f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}"
            return 400

    def _offline_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "offline_model",
            "msg": "下线模型成功"
        }

        logger.debug(f"开始下线模型 - model_id: {model_id}, 进程: {os.getpid()}")
        try:
            self.class_model.offline_model(model_id=model_id)
            logger.debug(f"下线模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
            if model_id in self.load_model_dict:
                self.load_model_dict.pop(model_id)
        except Exception as e:
            logger.error(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 400
            result["msg"] = str(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
        return result

    def predict(self, model_id, text, labelIds=None, return_search_result=False, topk=1):
        result = {
            "code": 0,
            "model_id": model_id,
            "text": text,
            "label_ids": labelIds,
            "labelScores": [],
            "data": None,
            "msg": "预测成功",
        }
        if setting.CLIENT == "train" and model_id not in self.train_client_load_model_list:
            load_result = self._load_model(model_id=model_id)
            if load_result == 0:
                self.train_client_load_model_list.append(model_id)
            if len(self.train_client_load_model_list) > setting.MAX_MODELS_TRAIN_CLIENT:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]
            self.lastest_switch_time[model_id] = int(time.time())


        if self.CALLOUR_SERIVE == True and setting.CLIENT == "predict":
            # 外呼预测自动上线逻辑
            self.auto_online(model_id, 1)
            #自动下线逻辑
            self.last_predict_model_record[model_id] = time.time()

        if setting.AUTO_ONLINE_MODEL and setting.CLIENT == "predict":
            my_utils.auto_online(self, self.load_model_dict, model_id,self.lastest_switch_time)

        try:
            start = time.time()
            if return_search_result:
                class_res_list, error_dict = self.class_model.predict(model_id=model_id, query=text, labelIds=None, topk=setting.RANK_RETURN_NUM)
            else:
                class_res_list, error_dict = self.class_model.predict(model_id=model_id, query=text, labelIds=labelIds, topk=topk)

            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
            else:
                logger.debug(f"预测完成 - model_id: {model_id}, text: {text}, res: {class_res_list}, 耗时: {time.time()-start}")
                if return_search_result:
                    if len(class_res_list) == 1:
                        class_result = self._get_searcg_result(class_res_list)
                        result["match_type"] = "complete"
                        result["msg"] = "返回精确匹配结果"
                        result["data"] = class_result
                    else:
                        class_result = self._get_searcg_result(class_res_list)
                        result["match_type"] = "model"
                        result["msg"] = "返回模型匹配结果"
                        result["data"] = class_result
                else:
                    if topk==1: #质检要的结构
                        result["data"] = class_res_list
                    elif topk==0:#外呼要的结构
                        result["data"] = class_res_list[0]
                        result['labelScores'] = class_res_list

        except Exception as e:
            logger.error(f"预测失败 - model_id: {model_id}, text: {text}, 错误: {e}")
            raise e
        return result

    def labeling(self, model_id, data_key):
        start = time.time()
        result = {
            "code": 0,
            "model_id": model_id,
            "data_key": data_key,
            "msg": "标注成功",
            "label_result": []
        }
        if setting.CLIENT == "train" and model_id not in self.train_client_load_model_list:
            if len(self.train_client_load_model_list) == setting.MAX_MODELS_TRAIN_CLIENT:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]
            self._load_model(model_id=model_id)
            self.train_client_load_model_list.append(model_id)

        try:
            data = self.redis.get_data(key=data_key)
            texts = []
            text_ids = []
            for data_dict in data:
                texts.append(data_dict["text"])
                text_ids.append(data_dict["id"])
            label_result = self.class_model.labeling(model_id=model_id, texts=texts, text_ids=text_ids)
            result["label_result"] = label_result
            logger.debug(f"标注成功, model_id:{model_id}, 耗时:{time.time()-start}s")
        except Exception as e:
            logger.error(f"标注失败, model_id:{model_id}, 错误信息:{e}")
            result["code"] = 1
            result["msg"] = f'标注错误:{e}'
            result["error_code"] = "NLU91021"
            result["error_type"] = 0

        self.redis.set_data(key=f"faq_labeling_{data_key}", msg=result)

    @staticmethod
    def _get_searcg_result(res_list):
        search_result = {
            "sim_query": [],
            "norm_query": [],
            "norm_query_id": [],
            "score": [],
            "threshold": 0.03,
            "low_threshold": 0
        }
        for res in res_list:
            search_result["sim_query"].append(res["label_title"])
            search_result["norm_query"].append(res["label_title"])
            search_result["norm_query_id"].append(res["label_id"])
            search_result["score"].append(res["score"])
        return search_result

    def train_time(self, data):
        return self.class_model.train_time(data)

    def regex_verification(self,reg):
        try:
            re.compile(reg)
            return True
        except:
            return False

    def node_train_data_checker(self,node):
        """
        处理每层的数据：
        level:
                [{'node_id':'4564564','reg':'.*不在||.*不在||我不是.*||我不是.*','labels':'我不是本人||他不在'},
                     {'node_id':'4868488','reg':'.*死.*||.*滚.*||我不是.*','labels':'他不在||你去死||滚吧'}]
        result:
                {'duplicates': [{'node_id': '4564564', 'reg': '.*不在||我不是.*', 'labels': '他不在'}, {'node_id': '4868488', 'reg': '我不是.*', 'labels': '他不在'}],
                     'small': [{'node_id': '4564564'}, {'node_id': '4868488'}],
                     'reg_check':[{'node_id': '4564564', 'reg': '.*不在||我不是.*'}, {'node_id': '4868488', 'reg': '我不是.*'}]}

        """
        level_df = pd.DataFrame(node)
        level_df = level_df.set_index('node_id')
        # 语料统计
        label_count = level_df['labels'].apply(lambda x: len(x.split('||')))
        smaller_index = label_count[label_count < 20].index.tolist()
        smaller = [{'node_id': i} for i in smaller_index]

        # 正则语料重复检测
        merge = []
        for key in ['reg', 'labels']:
            sec_level_df = level_df[key].str.split('\|\|', expand=True).stack().reset_index()
            df = sec_level_df[['node_id', 0]].copy()
            df.columns = ['node_id', key]
            if key == 'reg':
                ##正则合法性检测
                df['reg_verification'] = df[key].apply(lambda x: self.regex_verification(x))
                reg_verification_result = df[df['reg_verification'] == False].copy().drop('reg_verification',
                                                                                          1).drop_duplicates()
                reg_verification_result = reg_verification_result.groupby('node_id')[key].apply(
                    lambda x: x.str.cat(sep='||')).reset_index()
                reg_check = []
                for i in reg_verification_result.index:
                    i = reg_verification_result.loc[[i], :].dropna(axis=1).T.to_dict()
                    i = list(i.values())[0]
                    reg_check.append(i)
                df = df.drop('reg_verification', 1)

            count = df[key].value_counts()
            du_value = count[count >= 2].index
            df = df[df[key].isin(du_value)].drop_duplicates()
            df = df.groupby('node_id')[key].apply(lambda x: x.str.cat(sep='||')).reset_index()
            merge.append(df)
        m = pd.merge(merge[0], merge[1], on='node_id', how='outer')
        # m = merge[0]
        duplicates = []
        for i in m.index:
            i = m.loc[[i], :].dropna(axis=1).T.to_dict()
            i = list(i.values())[0]
            duplicates.append(i)

        result = {'duplicates': duplicates, 'small': smaller, 'reg_check': reg_check}
        return result

    def train_data_checker(self,levels: list):
        result = {
            "code": 0,
            "data": None,
            "msg": "预测成功",
        }
        pred = {'duplicates': {},
                'small': {},
                'reg_check': {}}
        for index, level in enumerate(levels):
            level_pred = self.node_train_data_checker(level)
            pred['duplicates'][index] = level_pred['duplicates']
            pred['small'][index] = level_pred['small']
            pred['reg_check'][index] = level_pred['reg_check']
        result['data'] = pred
        return result

    def auto_online(self,model_id,flag):
        if model_id not in self.callout_load_model_dict:   #每个模型id分配一个信号量(需要预先知道有哪些模型id,显然不成立),加锁,这样不会妨碍其他其他模型id io
            mutex.acquire()
            if self.callout_load_model_dict.get(model_id,None)==None:
                self.callout_load_model_dict[model_id] = 1  # 加锁
            else:
                flag = 0
            mutex.release()
            if flag==1:
                logger.debug(f'模型清单{self.callout_load_model_dict},正在加载模型{model_id}')
                load_result = self._load_model(model_id=model_id)  # 上线失败的异常
                if load_result == 0:
                    self.callout_load_model_dict[model_id] = 2  # 上线成功,解锁
                else:
                    self.callout_load_model_dict.pop(model_id)  # 模型加载失败
            else:logger.debug('有奇怪的东西跑进来了!!!!!!')
        while self.callout_load_model_dict[model_id] == 1:
            time.sleep(0.00001)
            pass

    def auto_offline(self):
        while True:
            model_list = list(self.last_predict_model_record.keys())
            for model_id in model_list:
                if time.time() - self.last_predict_model_record[model_id] > setting.CALLOUT_MODEL_EXIST_HOUR * 3600:
                    try:
                        logger.debug(f'启动自动下线分类模型:{model_id}')
                        self._offline_model(model_id)
                        self.callout_load_model_dict.pop(model_id)
                        self.last_predict_model_record.pop(model_id)
                    except:
                        logger.error(f'自动下线分类模型失败:{model_id}',exc_info=True)
            time.sleep(60)

    def auto_online_trained_model(self):
        while True:
            model_list = list(self.callout_load_model_dict.keys())
            for model_id in model_list:
                train_log_path = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'download_finish', 'download_finish.tag')
                if os.path.exists(train_log_path):
                    finish_tag_time = os.path.getmtime(train_log_path)
                else:
                    finish_tag_time = os.path.getmtime(os.path.join(setting.SAVE_MODEL_DIR, model_id))
                last_finish_time = self.last_train_time_record.get(model_id,finish_tag_time)
                if finish_tag_time-last_finish_time<3:#训练完,发送完会修改两次文件,所以会重新上线两次,10秒内修改的就不重新上线了
                    pass
                else:
                    try:
                        self.callout_load_model_dict[model_id] = 1    #加锁
                        load_result = self._load_model(model_id=model_id)
                        if load_result==0:
                            self.callout_load_model_dict[model_id] = 2
                            logger.debug(f"分类模型自动上线新训练的模型成功 - model_id: {model_id}")
                        else:
                            self.callout_load_model_dict.pop(model_id)
                            logger.debug(f"分类模型自动上线失败 - model_id: {model_id}")
                    except Exception as e:
                        self.callout_load_model_dict.pop(model_id)
                        logger.error(f"分类模型自动上线失败 - model_id: {model_id}, 错误: {e}",exc_info=True)
                self.last_train_time_record[model_id] = finish_tag_time
            time.sleep(0.2)

if __name__ == "__main__":
    model = ClassifyMain()

    model_id = "first"
    data_key = "outcall_test_data"

    # ==================== train ====================
    model.train(model_id=model_id, data_key=data_key)

    # ==================== load model ====================
    model.switch_model(model_id=model_id, flag=1)
    time.sleep(5)

    # ==================== predict ====================
    texts = ['你能告诉我什么时候能查到我快速赎回的余额理财到账的钱吗',
             '快速赎回的余额理财我什么时候能收到钱',
             'IE浏览器登录个人网银显示空白页如何处理',
             '数字牛熊证介绍',
             'ATM扫码取款支持怎样的账户啊',
             "请问余额理财赎回到账时间"]
    for text in texts:
        print(f"\n{text}")
        print(model.predict(model_id=model_id, text=text, labelIds=[]))