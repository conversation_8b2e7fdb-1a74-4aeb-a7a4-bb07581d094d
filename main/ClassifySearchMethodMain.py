# -*- coding: utf-8 -*-
import copy
import fcntl
import json
import os
import threading
import time
import re
import pandas as pd
import setting
from setting import logger

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)
# 限制CPU 占用，避免挤占其他服务
tf.config.threading.set_inter_op_parallelism_threads(setting.NUM_THREADS)
tf.config.threading.set_intra_op_parallelism_threads(setting.NUM_THREADS)
mutex = threading.Lock()
from database.REDIS import REDIS
from module.PreRank.ArcCSEPreRank.SimCSEPreRank_for_classification import SimCSEPreRank
from utils.my_utils import RankReturnObject
from typing import List

service_name = "Classify"
model_record_file = setting.MODEL_RECORD_FILE + f".{service_name}"
switch_model_channel = setting.SERVICE_CHANNEL_DICT["ClassifyMain"]["switch_model"]

class ClassifyMain:
    def __init__(self, callout=False):
        self.CALLOUR_SERIVE = callout
        os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
        self.redis = REDIS()

        # 粗排
        self.class_model = SimCSEPreRank()

        # Redis
        if setting.CLIENT != "train":
            self.redis.subscribe(switch_model_channel)
            callback_dict = {
                switch_model_channel: self.switch_model
            }
            t1 = threading.Thread(target=self.redis.listen_msg, args=(callback_dict,))
            t1.start()

        # 加载已上线模型
        self.model_record = {}
        if setting.CLIENT == 'predict' and self.CALLOUR_SERIVE == False:
            self.init_model()

        # 测试加载的模型
        self.train_client_load_model_list = []

        #外呼自动上下线逻辑
        if self.CALLOUR_SERIVE:
            #外呼加载模型列表
            self.callout_load_model_dict = {}
            # 外呼自动下线模型
            self.last_predict_model_record = {}
            t2 = threading.Thread(target=self.auto_offline)
            t2.start()
            #模型自动上线更新过的模型
            self.last_train_time_record = {}
            t3 = threading.Thread(target=self.auto_online_trained_model)
            t3.start()

    def init_model(self):
        # 加载已上线模型
        model_ids = []
        if os.path.exists(model_record_file):
            with open(model_record_file, 'r', encoding='utf-8') as f:
                try:
                    self.model_record = json.load(f)
                    model_ids = list(self.model_record.keys()) # 防止 model_record 发生变化而报错
                except Exception as e:
                    logger.warning(f"加载历史上线模型记录文件失败: {e}")
        try:
            for model_id in model_ids:
                if self._load_model(model_id=model_id): # 加载失败的要在 model_record 里面移除
                    self.model_record.pop(model_id, 0)
                logger.debug('[{}] 已加载历史上线模型'.format(model_id))
            self.dump_record()
        except Exception as e:
            logger.warning(f"加载历史上线模型失败: {e}")

    def dump_record(self):
        with open(model_record_file, 'w', encoding='utf-8') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.model_record, f, ensure_ascii=False)
        logger.debug('保存已上线模型记录')

    def train(self, model_id, data_key='', sendredis=True):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "train",
            "msg": "训练成功",
            "score": 0,
        }
        try:
            data = self.redis.get_data(key=data_key)
            # 开始训练分类模型
            start = time.time()
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.class_model.__class__.__name__}/")
            score, mixed = self.class_model.train(model_id=model_id, data=data, save_dir=model_save_dir)
            logger.debug(f"{self.class_model.__class__.__name__} 训练成功 - model_id:{model_id},耗时:{time.time() - start}s")
            result["score"] = float(score)
            result["suggest"] = mixed
            logger.debug(f"语料优化建议:{mixed}")
            if sendredis:
                self.redis.set_data(key=f"classify_train_{model_id}", msg=result)
            if model_id in self.train_client_load_model_list:
                self._load_model(model_id=model_id)
        except Exception as e:
            logger.error(f"训练失败 - model_id: {model_id}, 错误: {e}", exc_info=True)
            result["code"] = 1
            result["msg"] = f'训练错误:{e}'
            result["error_code"] = "NLU91020"
            result["error_type"] = 0

        if sendredis:
            self.redis.set_data(key=f"classify_train_{model_id}", msg=result)
        if model_id in self.train_client_load_model_list:
            self._load_model(model_id=model_id)
        return result

    def switch_model(self, model_id, flag):
        if flag == 1:
            code = self._load_model(model_id)
            if code == 0:
                self.model_record[model_id] = 1
            else:
                self.model_record.pop(model_id, 0)
        else:
            self._offline_model(model_id)
            self.model_record.pop(model_id, 0)
        self.dump_record()

    def _load_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "load_model",
            "msg": "加载模型成功"
        }

        logger.debug(f"开始加载模型 - model_id: {model_id}, 进程: {os.getpid()}")
        # 加载模型
        try:
            save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.class_model.__class__.__name__}/")
            self.class_model.load_model(model_id=model_id, save_dir=save_dir)

            logger.debug(f"加载模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
            return 0
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}",exc_info=True)
            result["code"] = 400
            result["msg"] = f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}"
            return 400

    def _offline_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "offline_model",
            "msg": "下线模型成功"
        }

        logger.debug(f"开始下线模型 - model_id: {model_id}, 进程: {os.getpid()}")
        try:
            self.class_model.offline_model(model_id=model_id)
            logger.debug(f"下线模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
        except Exception as e:
            logger.error(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 400
            result["msg"] = str(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")

    def predict(self, model_id, text, labelIds=None, return_search_result=False, topk=200):

        result = {
            "code": 0,
            "model_id": model_id,
            "text": text,
            "label_ids": labelIds,
            "labelScores": [],
            "data": None,
            "msg": "预测成功",
        }
        if setting.CLIENT == "train" and model_id not in self.train_client_load_model_list:
            load_result = self._load_model(model_id=model_id)
            if load_result==400:return result
            if load_result == 0:
                self.train_client_load_model_list.append(model_id)
            if len(self.train_client_load_model_list) > setting.MAX_MODELS_TRAIN_CLIENT:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]

        if self.CALLOUR_SERIVE == True and setting.CLIENT == "predict":
            # 外呼预测自动上线逻辑
            self.auto_online(model_id, 1)
            #自动下线逻辑
            self.last_predict_model_record[model_id] = time.time()


        try:
            start = time.time()
            from utils.my_utils import remove_symbol
            remove_symbol_text = remove_symbol(text)
            pre_rank_model_return = self.class_model.predict(model_id=model_id, query=remove_symbol_text,topk=topk)

            text_item_list = pre_rank_model_return[0]
            error_dict = pre_rank_model_return[1]
            threshold_dict = pre_rank_model_return[2] if len(pre_rank_model_return) == 3 else {}
            recall_info = pre_rank_model_return[-1]
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]
            else:
                # logger.debug(f"{self.class_model.__class__.__name__} 预测完成,model_id{model_id},数据量:{len(text_item_list)},耗时:{time.time() - start}")
                search_result = self._get_search_result(text_item_list, threshold_dict, len(text))
                recall_info = self._get_search_result(recall_info, threshold_dict, len(text))
                logger.debug(f'recall_info:{recall_info}')
                import pandas as pd
                df = pd.DataFrame(search_result)
                recall_info = pd.DataFrame(recall_info)
                df = df.drop_duplicates('norm_query_id')
                df = df[['norm_query_id', 'norm_query', 'score','sim_query']]
                df.columns = ['label_id', 'label_title', 'score','sim_query']
                if labelIds is None or len(labelIds) == 0:
                    pass
                else:
                    df = df[df['label_id'].isin(labelIds)]
                if len(df)>0:
                    class_res_list = [j for i, j in df.T.to_dict().items()]
                    result['labelScores'] = class_res_list
                    data = copy.deepcopy(class_res_list[0])
                    recall_info = recall_info[recall_info['norm_query_id']==data['label_id']]
                    data['sim_queries'] = recall_info['sim_query'].tolist()[:5]
                    data['scores'] = recall_info['score'].tolist()[:5]
                    result["data"] = data
                else:
                    result['labelScores'] = []
                    result["data"] = {}
        except Exception as e:
            logger.error(f"预测失败 - model_id: {model_id}, text: {text}, 错误: {e}")
            raise e
        return result

    def labeling(self, model_id, data_key):
        start = time.time()
        result = {
            "code": 0,
            "model_id": model_id,
            "data_key": data_key,
            "msg": "标注成功",
            "label_result": []
        }
        if setting.CLIENT == "train" and model_id not in self.train_client_load_model_list:
            if len(self.train_client_load_model_list) == setting.MAX_MODELS_TRAIN_CLIENT:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]
            self._load_model(model_id=model_id)
            self.train_client_load_model_list.append(model_id)

        try:
            data = self.redis.get_data(key=data_key)
            texts = []
            text_ids = []
            for data_dict in data:
                texts.append(data_dict["text"])
                text_ids.append(data_dict["id"])
            label_result = self.class_model.labeling(model_id=model_id, texts=texts, text_ids=text_ids)
            result["label_result"] = label_result
            logger.debug(f"标注成功, model_id:{model_id}, 耗时:{time.time()-start}s")
        except Exception as e:
            logger.error(f"标注失败, model_id:{model_id}, 错误信息:{e}")
            result["code"] = 1
            result["msg"] = f'标注错误:{e}'
            result["error_code"] = "NLU91021"
            result["error_type"] = 0

        self.redis.set_data(key=f"faq_labeling_{data_key}", msg=result)

    @staticmethod
    def _get_searcg_result(res_list):
        search_result = {
            "sim_query": [],
            "norm_query": [],
            "norm_query_id": [],
            "score": [],
            "threshold": 0.03,
            "low_threshold": 0
        }
        for res in res_list:
            search_result["sim_query"].append(res["label_title"])
            search_result["norm_query"].append(res["label_title"])
            search_result["norm_query_id"].append(res["label_id"])
            search_result["score"].append(res["score"])
        return search_result

    def train_time(self, data):
        return self.class_model.train_time(data)

    def regex_verification(self,reg):
        try:
            re.compile(reg)
            return True
        except:
            return False

    def node_train_data_checker(self,node):
        """
        处理每层的数据：
        level:
                [{'node_id':'4564564','reg':'.*不在||.*不在||我不是.*||我不是.*','labels':'我不是本人||他不在'},
                     {'node_id':'4868488','reg':'.*死.*||.*滚.*||我不是.*','labels':'他不在||你去死||滚吧'}]
        result:
                {'duplicates': [{'node_id': '4564564', 'reg': '.*不在||我不是.*', 'labels': '他不在'}, {'node_id': '4868488', 'reg': '我不是.*', 'labels': '他不在'}],
                     'small': [{'node_id': '4564564'}, {'node_id': '4868488'}],
                     'reg_check':[{'node_id': '4564564', 'reg': '.*不在||我不是.*'}, {'node_id': '4868488', 'reg': '我不是.*'}]}

        """
        level_df = pd.DataFrame(node)
        level_df = level_df.set_index('node_id')
        # 语料统计
        label_count = level_df['labels'].apply(lambda x: len(x.split('||')))
        smaller_index = label_count[label_count < 20].index.tolist()
        smaller = [{'node_id': i} for i in smaller_index]

        # 正则语料重复检测
        merge = []
        for key in ['reg', 'labels']:
            sec_level_df = level_df[key].str.split('\|\|', expand=True).stack().reset_index()
            df = sec_level_df[['node_id', 0]].copy()
            df.columns = ['node_id', key]
            if key == 'reg':
                ##正则合法性检测
                df['reg_verification'] = df[key].apply(lambda x: self.regex_verification(x))
                reg_verification_result = df[df['reg_verification'] == False].copy().drop('reg_verification',
                                                                                          1).drop_duplicates()
                reg_verification_result = reg_verification_result.groupby('node_id')[key].apply(
                    lambda x: x.str.cat(sep='||')).reset_index()
                reg_check = []
                for i in reg_verification_result.index:
                    i = reg_verification_result.loc[[i], :].dropna(axis=1).T.to_dict()
                    i = list(i.values())[0]
                    reg_check.append(i)
                df = df.drop('reg_verification', 1)

            count = df[key].value_counts()
            du_value = count[count >= 2].index
            df = df[df[key].isin(du_value)].drop_duplicates()
            df = df.groupby('node_id')[key].apply(lambda x: x.str.cat(sep='||')).reset_index()
            merge.append(df)
        m = pd.merge(merge[0], merge[1], on='node_id', how='outer')
        # m = merge[0]
        duplicates = []
        for i in m.index:
            i = m.loc[[i], :].dropna(axis=1).T.to_dict()
            i = list(i.values())[0]
            duplicates.append(i)

        result = {'duplicates': duplicates, 'small': smaller, 'reg_check': reg_check}
        return result

    def train_data_checker(self,levels: list):
        result = {
            "code": 0,
            "data": None,
            "msg": "预测成功",
        }
        pred = {'duplicates': {},
                'small': {},
                'reg_check': {}}
        for index, level in enumerate(levels):
            level_pred = self.node_train_data_checker(level)
            pred['duplicates'][index] = level_pred['duplicates']
            pred['small'][index] = level_pred['small']
            pred['reg_check'][index] = level_pred['reg_check']
        result['data'] = pred
        return result

    def auto_online(self,model_id,flag):
        if model_id not in self.callout_load_model_dict:   #每个模型id分配一个信号量(需要预先知道有哪些模型id,显然不成立),加锁,这样不会妨碍其他其他模型id io
            mutex.acquire()
            if self.callout_load_model_dict.get(model_id,None)==None:
                self.callout_load_model_dict[model_id] = 1  # 加锁
            else:
                flag = 0
            mutex.release()
            if flag==1:
                logger.debug(f'模型清单{self.callout_load_model_dict},正在加载模型{model_id}')
                load_result = self._load_model(model_id=model_id)  # 上线失败的异常
                if load_result == 0:
                    self.callout_load_model_dict[model_id] = 2  # 上线成功,解锁
                else:
                    self.callout_load_model_dict.pop(model_id)  # 模型加载失败
            else:logger.debug('有奇怪的东西跑进来了!!!!!!')
        while self.callout_load_model_dict[model_id] == 1:
            time.sleep(0.00001)
            pass

    def auto_offline(self):
        while True:
            model_list = list(self.last_predict_model_record.keys())
            for model_id in model_list:
                if time.time() - self.last_predict_model_record[model_id] > setting.CALLOUT_MODEL_EXIST_HOUR * 3600:
                    try:
                        logger.debug(f'启动自动下线分类模型:{model_id}')
                        self._offline_model(model_id)
                        self.callout_load_model_dict.pop(model_id)
                        self.last_predict_model_record.pop(model_id)
                    except:
                        logger.error(f'自动下线分类模型失败:{model_id}',exc_info=True)
            time.sleep(60)

    def auto_online_trained_model(self):
        while True:
            model_list = list(self.callout_load_model_dict.keys())
            for model_id in model_list:
                train_log_path = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'download_finish', 'download_finish.tag')
                if os.path.exists(train_log_path):
                    finish_tag_time = os.path.getmtime(train_log_path)
                else:
                    finish_tag_time = os.path.getmtime(os.path.join(setting.SAVE_MODEL_DIR, model_id))
                last_finish_time = self.last_train_time_record.get(model_id,finish_tag_time)
                if finish_tag_time-last_finish_time<3:#训练完,发送完会修改两次文件,所以会重新上线两次,10秒内修改的就不重新上线了
                    pass
                else:
                    try:
                        self.callout_load_model_dict[model_id] = 1    #加锁
                        load_result = self._load_model(model_id=model_id)
                        if load_result==0:
                            self.callout_load_model_dict[model_id] = 2
                            logger.debug(f"分类模型自动上线新训练的模型成功 - model_id: {model_id}")
                        else:
                            self.callout_load_model_dict.pop(model_id)
                            logger.debug(f"分类模型自动上线失败 - model_id: {model_id}")
                    except Exception as e:
                        self.callout_load_model_dict.pop(model_id)
                        logger.error(f"分类模型自动上线失败 - model_id: {model_id}, 错误: {e}",exc_info=True)
                self.last_train_time_record[model_id] = finish_tag_time
            time.sleep(0.2)


    @staticmethod
    def _get_search_result(items: List[RankReturnObject], threshold_dict=None, query_length=8):
        """
        :param items: list of RankReturnObject
        :return: search_result, dict
        """
        search_result = {
            "sim_query": [],
            "norm_query": [],
            "norm_query_id": [],
            "score": [],
        }
        if threshold_dict is not None and "complete_score" in threshold_dict:
            search_result["complete_score"] = threshold_dict["complete_score"]
            search_result["threshold"] = threshold_dict["threshold"]
            search_result["low_threshold"] = threshold_dict["low_threshold"]

        short_query_threshold = 0.791
        long_query_threshold = 0.75
        short_length = 8
        long_length = 20
        if query_length <= short_length:
            search_result["complete_score"] = short_query_threshold
            search_result["threshold"] = short_query_threshold
        elif query_length >= long_length:
            search_result["complete_score"] = long_query_threshold
            search_result["threshold"] = long_query_threshold
        else:
            temp_threshold = (long_query_threshold - short_query_threshold) / (long_length - short_length) * (query_length - short_length) + short_query_threshold
            search_result["complete_score"] = temp_threshold
            search_result["threshold"] = temp_threshold

        for item in items:
            search_result["sim_query"].append(item.sim_query)
            search_result["norm_query"].append(item.norm_query)
            search_result["norm_query_id"].append(item.norm_query_id)
            search_result["score"].append(item.score)
        return search_result




if __name__ == "__main__":
    model = ClassifyMain()

    model_id = "test"
    data_key = "outcall_test_data"

    # ==================== train ====================
    model.train(model_id=model_id, data_key=data_key)

    # ==================== load model ====================
    # model.switch_model(model_id=model_id, flag=1)
    # time.sleep(5)

    # ==================== predict ====================
    start = time.time()
    label_ids = ["22"]
    pre_rank_model_return = model.predict(model_id=model_id,
                                          text="11",labelIds=label_ids)
    # print(pre_rank_model_return)

    # text_item_list = []
    # if len(text_item_list) == 0:
    #     text_item_list = pre_rank_model_return[0]
    # error_dict = pre_rank_model_return[1]
    # threshold_dict = pre_rank_model_return[2] if len(pre_rank_model_return) == 3 else {}
    #
    #
    # result = {}
    # if error_dict["error_code"] != 0:
    #     result["code"] = 1
    #     result["msg"] = error_dict["error_msg"]
    #     result["error_code"] = error_dict["error_code"]
    #     result["error_type"] = error_dict["error_type"]
    #
    # logger.debug(f"{model.class_model.__class__.__name__} 预测完成,model_id{model_id},数据量:{len(text_item_list)},耗时:{time.time() - start}")
    # if len(text_item_list) == 1 and text_item_list[0].score == 1:
    #     search_result = model._get_search_result(text_item_list, threshold_dict, len(texts[0]))
    #     result["match_type"] = "complete"
    #     result["msg"] = "返回精确匹配结果"
    #     result["data"] = search_result
    # else:
    #     search_result = model._get_search_result(text_item_list, threshold_dict, len(texts[0]))
    #     result["match_type"] = "model"
    #     result["msg"] = "返回模型匹配结果"
    #     result["data"] = search_result
    #
    # output = search_result
    # import pandas as pd
    # df = pd.DataFrame(output)
    # df = df.drop_duplicates('norm_query_id')
    # df = df[['norm_query_id','norm_query','score']]
    # df.columns = ['label_id','label_title','score']
    # class_res_list = [j for i,j in df.T.to_dict().items()]
    # print(class_res_list)
    #==========================================
    m = model.class_model.models[model_id]['model']
    from sklearn.metrics.pairwise import cosine_similarity
    # emb1 = model.class_model.encode_sentences(['那个包裹里面是什么呀'], m)
    # emb2 = model.class_model.encode_sentences(['那里边是什么呀'], m)
    # print(cosine_similarity(emb1.reshape(1, -1), emb2.reshape(1, -1)))
    # [[0.37754762]]
    emb1 = model.class_model.encode_sentences(['简单说说'], m)
    emb1 = emb1[0][0][0]

    text1 = '简单说说'
    text2 = '简单点'
    bert_dir = f'{setting.PRETRAIN_BERT_DIR}'
    bert_vocab = f'{setting.PRETRAIN_BERT_DIR}/vocab.txt'

    from transformers import BertTokenizer
    tokenizer = BertTokenizer.from_pretrained(bert_vocab)
    feature1 = tokenizer.encode_plus(text1, return_tensors='tf')
    fit = m.bert(**feature1)[0][0][0]

    emb2 = model.class_model.encode_sentences(['简单点'], m)
    print(cosine_similarity(emb1.reshape(1, -1), emb2.reshape(1, -1)))
    # [[0.62082344]]
    # emb1 = model.class_model.encode_sentences(['简单说说'], m)
    # emb2 = model.class_model.encode_sentences(['你就给我简单点讲'], m)
    # print(cosine_similarity(emb1.reshape(1, -1), emb2.reshape(1, -1)))
    # [[0.5782972]]
    # emb1 = model.class_model.encode_sentences(['简单点说说'], m)
    # emb2 = model.class_model.encode_sentences(['简单讲'], m)
    # print(cosine_similarity(emb1.reshape(1, -1), emb2.reshape(1, -1)))
    # [[0.737783]]
    # emb1 = model.class_model.encode_sentences(['简单点说下'], m)
    # emb2 = model.class_model.encode_sentences(['简单说'], m)


    # emb = model.class_model.encode_sentences(['简单点说说','简单说'],m)
    # print(cosine_similarity(emb[0].reshape(1,-1), emb[1].reshape(1,-1)))



    # from transformers import BertTokenizer, BertConfig, TFBertModel
    # model_path =setting.PRETRAIN_BERT_DIR
    # config = BertConfig.from_pretrained(model_path)
    # config.emb_layer = [1, -1]
    # config.max_len = 128
    # tokenizer = BertTokenizer.from_pretrained(model_path)
    # bert = TFBertModel.from_pretrained(model_path, from_pt=False, config=config)
    #
    # one = tokenizer.encode('简单点说说。',return_tensors='tf')
    # two = tokenizer.encode('不知道',return_tensors='tf')
    # cls1 = bert(one)[0][:,:,:]
    # cls1 = tf.reduce_mean(cls1, axis=1)
    #
    # cls2 = bert(two)[0][:,:,:]
    # cls2 = tf.reduce_mean(cls2, axis=1)
    # print(cosine_similarity(cls2, cls1))
