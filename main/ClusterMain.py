# -*- coding: utf-8 -*-
import copy
import os
import time

import jieba

import setting
from setting import logger

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)
# 限制CPU 占用，避免挤占其他服务
tf.config.threading.set_inter_op_parallelism_threads(setting.NUM_THREADS)
tf.config.threading.set_intra_op_parallelism_threads(setting.NUM_THREADS)

from database.REDIS import REDIS
from database.MYSQL import MYSQL
from module.Cluster.SinglePassCluster.SinglePassCluster_ancient import SinglePassCluster
from module.Cluster.KmeansCluster.KmeansCluster import KmeansCluster
from model.KeyWordExtract.TFIDFModel import TFIDFModel

import pandas as pd
import json
from datetime import datetime
import hashlib
import requests
import jieba.analyse


from database.MINIO import MINIO
minio = MINIO()

from submodule_utils.nacos_register import NacosHelper
nacos_r = NacosHelper("tmp")



from utils.callback_utils import callback_for_http
from utils.callback_utils import RECODER
cluster_recode = RECODER('cluster')


class ClusterMain:
    def __init__(self):
        os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
        self.redis = REDIS()
        self.mysql = MYSQL(**setting.SQL_SETTING)

        # 粗排
        self.cluster_model = SinglePassCluster()
        self.kmeans = KmeansCluster()

    def predict(self, data_key):
        result = {
            "code": 0,
            "data_key": data_key,
            "msg": "预测成功",
        }

        try:
            start = time.time()
            logger.debug(f'SinglePassCluster开始聚类data_key:{data_key}')
            data = self.redis.get_data(data_key)
            df = pd.DataFrame(data)

            # df1 = pd.read_csv(r"C:\Users\<USER>\Desktop\fack_cluster_data1.csv")
            # df2 = pd.read_csv(r"C:\Users\<USER>\Desktop\fack_cluster_data.csv").drop('sim_query',1)
            # df = pd.concat([df1,df2])
            #
            # df = df[df['score']<0.75]
            # df['session_time'] = pd.to_datetime(df['session_time'], unit='s').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
            # df['text_id'] = range(21978,21978+len(df))
            #
            # df_for_db = df.drop(['Unnamed: 0'],1).rename(columns={'text':'query_content',
            #                                                              'text_id':"class_id",
            #                                                              'relation_type':'relation_knowledge_type'})
            # df_for_db['updated_at'] = df_for_db['session_time']
            # df_for_db['call_type'] = 1
            # df_for_db['id'] = range(21978,21978+len(df))
            # df_for_db['robot_id'] = 1717094802336321537
            # df_for_db['robot_name'] = '测试萨达速度阿萨'
            # df_for_db['round'] = 3
            # df_for_db['round_count'] = 5
            # df_for_db['relation_knowledge_type'] = df_for_db['relation_knowledge_type'].map({'FAQ': 1, '闲聊': 2, '意图': 3})
            # self.mysql.to_sql(df_for_db, 'aicc', 't_robot_none_match', if_exists='append')

            df['tenant_id'] = data_key.split('_')[1]
            df['session_time'] = pd.to_datetime(df['session_time'],unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
            #按阈值在0.45以上的直接归为一类,剩下的走聚类模型
            good = df[df['score']>=0.75]
            bad = df[df['score']<0.75]
            intent_class = []
            for relation_intention_id in good['relation_intention_id'].unique():
                intent_class.append(good[good['relation_intention_id']==relation_intention_id]['text'].tolist())

            texts = bad['text'].drop_duplicates().tolist()


            # from model.LanguageJudge.LanguageJudgeRegular import LanguageJudgeRegular
            # language_judge_regular = LanguageJudgeRegular()
            # is_sim =  [language_judge_regular.language_judge(text=i, lang='简体中文') for i in texts]
            # is_eng =  [language_judge_regular.language_judge(text=i, lang='英语') for i in texts]
            #
            # sims = [texts[index] for index,i in enumerate(zip(is_sim,is_eng)) if i[0]==True and i[1]==False]#是简体和不是英文就是简体
            # tras = [texts[index] for index,i in enumerate(zip(is_sim,is_eng)) if i[0]==False and i[1]==False]
            # engs = [texts[index] for index,i in enumerate(is_eng) if i==True]
            #
            #
            # sims_cluster_text_list, cluster_ids, _ = self.cluster_model.predict(sims,lang='sim')
            # tras_cluster_text_list, cluster_ids, _ = self.cluster_model.predict(tras,lang='tra')
            # engs_cluster_text_list, cluster_ids, _ = self.cluster_model.predict(engs,lang='tra')


            # cluster_text_list = intent_class + sims_cluster_text_list + tras_cluster_text_list + engs_cluster_text_list
            text = df['text'].drop_duplicates().tolist()

            cluster_text_list, cluster_ids, _ = self.cluster_model.predict(text)
            cluster_text_list = [i for i in cluster_text_list if i]
            final_output = []
            mapper = {'FAQ': 1, '闲聊': 2, '意图': 3}
            for index, i in enumerate(cluster_text_list):
                class_id = hashlib.md5(''.join(i).encode(encoding='UTF-8')).hexdigest()
                sec = df[df['text'].isin(i)]
                sec = sec.fillna('')
                item = {}
                # item['id'] = index
                item['class_id'] = class_id
                item['query_content'] = i[0]
                item['relation_knowledge_type'] = mapper.get(sec['relation_type'].value_counts().index[0],0)
                item['relation_intention_id'] = sec['relation_intention_id'].value_counts().index[0]
                item['relation_intention'] = sec['relation_intention'].value_counts().index[0]
                item['query_count'] = sec['session_id'].unique().shape[0]
                item['session_time'] = sec['session_time'].max()
                item['text_id'] = json.dumps(sec['text_id'].tolist(), ensure_ascii=False)
                item['detail'] = json.dumps(sec['text'].tolist(), ensure_ascii=False)
                item['tenant_id'] = data_key.split('_')[1]
                item['create_time'] = datetime.now()
                final_output.append(item)
                # 反写问句详情页

                text_id = str(tuple(sec['text_id'].tolist()))
                text_id = text_id[:-2] + text_id[-2:].replace(',', '')
                self.mysql.execute_sql('aicc', f"update t_robot_none_match set class_id='{class_id}' where id in {text_id}")

            final_df = pd.concat([pd.DataFrame(i, index=[0]) for i in final_output])
            self.mysql.execute_sql('aicc', f"delete from t_robot_none_match_nlp where tenant_id = {data_key.split('_')[1]}")
            self.mysql.to_sql(final_df, 'aicc', 't_robot_none_match_nlp', if_exists='append')

            result['耗时'] = time.time()-start
            logger.debug(f"聚类预测完成 - 耗时:{time.time()-start}")
        except Exception as e:
            logger.error(f"聚类预测失败 - data_key: {data_key}, 错误: {e}",exc_info=True)
            result["code"] = 1
            result["error_code"] = "NLU91023"
            result["error_type"] = 0
            result["msg"] = f"聚类错误: {e}"
        # self.redis.set_data(key=f"nlp_cluster_predict_{data_key}", msg=result)


    def conversation_predict(self,post_data):
        try:
            data_url = post_data['data_url']
            #重够callback_url
            ips = nacos_r.nacos_client.list_naming_instance('aicc-crm')['hosts'][0]
            ips = ips['ip'] + ':' + str(ips['port'])
            callback_url = f'http://{ips}/crm/callback/custIntention/calcInt'

            callback_data = copy.deepcopy(post_data)
        except:
            logger.error(f"聚类预测失败",exc_info=True)

        try:
            #[{'id': '通话id1', 'text': '通话A用户句子1||通话A用户句子2', 'tenantId': '1'},
            # {'id': '通话id2', 'text': '通话A用户句子1||通话A用户句子2', 'tenantId': '1'}]

            # cluster_recode.dump_train_record(callback_data, is_add=True)

            if type(data_url) == dict:  # 使用minio
                bucketname = data_url.get('bucket', '')
                url = data_url.get('url', '')
                os.makedirs(os.path.join(setting.SAVE_MODEL_DIR, 'train_data'), exist_ok=True)
                output_path = os.path.join(setting.SAVE_MODEL_DIR, 'train_data')
                output_path = minio.download_file(url, output_path=output_path, bucketname=bucketname)
                data = json.loads(open(output_path, 'r',encoding='utf-8-sig').read())
            else:
                data = self.url_to_json(data_url)

            data_df = pd.DataFrame(data)

            texts = [i['text'].replace('||','。') for i in data]
            logger.info(f'对话聚类数量: {len(texts)}')
            data_df['replace_text'] = texts
            cluster_text_list, cluster_ids, _ = self.kmeans.predict(texts)


            train_corpus = texts
            tfidf = TFIDFModel(train_corpus)


            for node in cluster_text_list:
                intent = self.jieba_textrank(''.join(node),5)
                data_df.loc[data_df['replace_text'].isin(node),'intention'] = str('。'.join(intent))

            data = data_df[['id','intention','tenantId']].to_dict(orient='records')
            post_data['data'] = data
            print(data)
            callback_for_http(callback_url, post_data)
            cluster_recode.dump_train_record(callback_data, is_add=False)
        except Exception as e:
            logger.error(f"聚类预测失败 - data: {callback_data}, 错误: {e}",exc_info=True)
            callback_for_http(callback_url, post_data)

    def url_to_json(self,url):
        save_dir = os.path.join(setting.SAVE_MODEL_DIR,'train_data')
        os.makedirs(save_dir,exist_ok=True)
        save_filename = url.split('/')[-1]
        file_path = os.path.join(save_dir, save_filename)
        try:
            logger.info(f'正在请求下载链接{url}')
            data = requests.get(url)
            with open(file_path, 'wb') as f:
                f.write(data.content)
                f.close()
            data = json.loads(open(file_path,'r').read())
            logger.info(f'{url}下载成功')
            return data
        except Exception as e:
            logger.error(f'文件下载失败:{url}',exc_info=True)

    @staticmethod
    def jieba_tfidf(texts,topK):
        # 基于TF-IDF算法的关键词抽取
        import jieba
        # keywords = jieba.analyse.extract_tags(texts, topK=10,  allowPOS=('n', 'nr', 'ns'))
        keywords = jieba.analyse.extract_tags(texts, topK=topK)
        return keywords

    @staticmethod
    def jieba_textrank(texts,topK):
        # 引入TextRank关键词抽取接口
        textrank = jieba.analyse.textrank
        # 原始文本
        keywords = textrank(texts,topK=topK)
        # 输出抽取出的关键词
        return keywords


if __name__ == "__main__":
    model = ClusterMain()
    # data_key = "cluster_461513533_1742749204735"
    # model.predict(data_key)

    path = r"C:\Users\<USER>\Desktop\1897942842671501312.txt"
    path = r"C:\Users\<USER>\Desktop\诈骗模型录音聚类\all_content_dict.json"
    data = json.loads(open(path,'r',encoding='utf-8-sig').read())
    data_df = pd.DataFrame(data)

    texts = [i['text'].replace('nomatch','').replace('_end_','') for i in data]
    # texts = [i['text'].replace('||', '') for i in data]
    texts = [i['text'].split('||') for i in data]
    texts = [i for j in texts for i in j]
    texts = list(set(texts))
    logger.info(f'对话聚类数量: {len(texts)}')

    # data_df['replace_text'] = texts
    embeddings = model.kmeans.get_embedding(texts)
    cluster_text_list, cluster_ids = model.kmeans.predict(texts,100,embeddings)
    [len(i) for i in cluster_text_list]
    train_corpus = texts

    check = []
    for node_id,node in enumerate(cluster_text_list):
        intent = model.jieba_tfidf(''.join(node), 5)
        # intent = model.jieba_textrank(''.join(node), 5)
        check.append({'text':node,'intent':intent})
        # data_df.loc[data_df['replace_text'].isin(node), 'intention'] = str(intent[0])
        # data_df.loc[data_df['replace_text'].isin(node), 'intentions'] = str(intent)
        # data_df.loc[data_df['replace_text'].isin(node), 'node_id'] = node_id

    check_df = pd.DataFrame(check)

    # data = data_df[['id', 'intention', 'tenantId']].to_dict(orient='records')
    # print(data)

    check = []
    tfidf = TFIDFModel(texts,ngram=(2,3))
    for node in cluster_text_list:
        intent = tfidf.predict([''.join(node)], 5,idf_weight=10)
        intent = intent[0]
        intent = [i.replace(' ','') for i in intent]
        raw_sentence = {}
        for j in intent:
            raw_sentence[j] = []
            for i in node:
                if j in i:
                    raw_sentence[j].append(i)
        check.append({'text':node,'intent':intent,'raw_sentence':raw_sentence,'len':len(node)})
        # break
    check_df = pd.DataFrame(check)



    # print(type(keywords))
    # <class 'list'>


    #https://aicc-test.qnzsai.com/minio/aicccrm/826853126/2025/02/20/1892508537757634560.txt
    # model.conversation_predict(data)
    # minio.BUCKETNAME = 'nlpforjava'
    # minio.upload_file('aaa.wav',r"C:\Users\<USER>\Desktop\您好，如果您持有某啲特定专业资格，而该特定专业资格设有CPD培训要求_volume50.wav")


    # ==================== predict model_id ====================
    # model.predict(data_key=data_key)
    # model.mysql.execute_sql('aicc',"""delete from t_robot_none_match where robot_name = '测试萨达速度阿萨'""")

    # import json
    # # corpus = json.loads(open(r"D:\work\jupyter_notebook\RoleSeperation\Engine_role_sep\data\yanguan.json",'r',encoding='utf-8').read())
    # corpus = json.loads(open(r"/data/sammy/serve/yanguan.json",'r',encoding='utf-8').read())
    # data = corpus['data']
    # train = []
    # sentences = []
    # for i in data:
    #     data_i = data[i]
    #     data_i['key'] = i
    #     label = data_i['labels']
    #     texts = data_i['texts']
    #     texts = [i for i,j in zip(texts,label) if j == 1]
    #     sentences.extend(texts)
    #     texts = ''.join(texts)
    #     train.append(texts)
    # sentences = list(set(sentences))
    #
    # print(len(train),'asdasfwegwrrhgwerh')
    # train = train[:50]
    #
    #
    # from module.Cluster.KmeansCluster.KmeansCluster import KmeansCluster
    # model.cluster_model = KmeansCluster()
    # model.cluster_model.model.max_len = 512
    # # setting.KMEANS_CLUSTER_EMB_TYPE = 'asdasd'
    #
    # # model.cluster_model.model_simple.max_len = 512
    #
    # output = model.cluster_model.predict(sentences,50)
    # if len(output)==3:
    #     sentences_cluster_text_list, sentences_cluster_ids, _ = output
    # else:
    #     sentences_cluster_text_list, sentences_cluster_ids = output
    #
    # # 1+'1'
    # [len(i) for i in sentences_cluster_text_list]
    # wait_for_extract = sentences_cluster_text_list
    # #取出wait_for_extract数量最大的top5
    # top5 = sorted(wait_for_extract,key=lambda x:len(x),reverse=True)
    # # pd.DataFrame({"text":sentences_cluster_text_list}).to_excel(r"/tmp/haha.xlsx")
    # pd.DataFrame(sentences_cluster_text_list).T.to_excel(r"/tmp/haha_jina.xlsx")
    #
    # all_corpus = [j for i in sentences_cluster_text_list for j in i]
    # all_corpus = [' '.join(jieba.lcut(i)) for i in all_corpus]
    # tf_model = TFIDFModel(all_corpus,tuple([2,4]))
    #
    # top1 = [' '.join(jieba.lcut(i)) for i in top5[0]]
    # top1 = [' '.join(top1)]
    # tf_model.predict(top1,20)


