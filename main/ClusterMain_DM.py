# -*- coding: utf-8 -*-

import os
import time

import setting
from setting import logger

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)
# 限制CPU 占用，避免挤占其他服务
tf.config.threading.set_inter_op_parallelism_threads(setting.NUM_THREADS)
tf.config.threading.set_intra_op_parallelism_threads(setting.NUM_THREADS)

from database.REDIS import REDIS
from database.DMSQL import DMSQL
from module.Cluster.SinglePassCluster.SinglePassCluster_ancient import SinglePassCluster
import pandas as pd
import json
from datetime import datetime
import hashlib

class ClusterMain_DM:
    def __init__(self):
        os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
        self.redis = REDIS()
        self.mdsql = DMSQL(**setting.SQL_SETTING)

        # 粗排
        self.cluster_model = SinglePassCluster()

    def predict(self, data_key):
        result = {
            "code": 0,
            "data_key": data_key,
            "msg": "预测成功",
        }

        try:
            start = time.time()
            logger.debug(f'SinglePassCluster开始聚类data_key:{data_key}')
            data = self.redis.get_data(data_key)
            df = pd.DataFrame(data)

            # df1 = pd.read_csv(r"C:\Users\<USER>\Desktop\fack_cluster_data1.csv")
            # df2 = pd.read_csv(r"C:\Users\<USER>\Desktop\fack_cluster_data.csv").drop('sim_query',1)
            # df = pd.concat([df1,df2])
            #
            # df = df[df['score']<0.75]
            # df['session_time'] = pd.to_datetime(df['session_time'], unit='s').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
            # df['text_id'] = range(21978,21978+len(df))
            #
            # df_for_db = df.drop(['Unnamed: 0'],1).rename(columns={'text':'query_content',
            #                                                              'text_id':"class_id",
            #                                                              'relation_type':'relation_knowledge_type'})
            # df_for_db['updated_at'] = df_for_db['session_time']
            # df_for_db['call_type'] = 1
            # df_for_db['id'] = range(21978,21978+len(df))
            # df_for_db['robot_id'] = 1717094802336321537
            # df_for_db['robot_name'] = '测试萨达速度阿萨'
            # df_for_db['round'] = 3
            # df_for_db['round_count'] = 5
            # df_for_db['relation_knowledge_type'] = df_for_db['relation_knowledge_type'].map({'FAQ': 1, '闲聊': 2, '意图': 3})
            # self.mysql.to_sql(df_for_db, 'aicc', 't_robot_none_match', if_exists='append')

            df['tenant_id'] = data_key.split('_')[1]
            df['session_time'] = pd.to_datetime(df['session_time'],unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
            #按阈值在0.45以上的直接归为一类,剩下的走聚类模型
            good = df[df['score']>=0.75]
            bad = df[df['score']<0.75]
            intent_class = []
            for relation_intention_id in good['relation_intention_id'].unique():
                intent_class.append(good[good['relation_intention_id']==relation_intention_id]['text'].tolist())

            texts = bad['text'].tolist()


            from model.LanguageJudge.LanguageJudgeRegular import LanguageJudgeRegular
            language_judge_regular = LanguageJudgeRegular()
            is_sim =  [language_judge_regular.language_judge(text=i, lang='简体中文') for i in texts]
            is_eng =  [language_judge_regular.language_judge(text=i, lang='英语') for i in texts]

            sims = [texts[index] for index,i in enumerate(zip(is_sim,is_eng)) if i[0]==True and i[1]==False]#是简体和不是英文就是简体
            tras = [texts[index] for index,i in enumerate(zip(is_sim,is_eng)) if i[0]==False and i[1]==False]
            engs = [texts[index] for index,i in enumerate(is_eng) if i==True]


            sims_cluster_text_list, cluster_ids, _ = self.cluster_model.predict(sims,lang='sim')
            tras_cluster_text_list, cluster_ids, _ = self.cluster_model.predict(tras,lang='tra')
            engs_cluster_text_list, cluster_ids, _ = self.cluster_model.predict(engs,lang='tra')


            cluster_text_list = intent_class + sims_cluster_text_list + tras_cluster_text_list + engs_cluster_text_list
            cluster_text_list = [i for i in cluster_text_list if i]
            final_output = []
            mapper = {'FAQ': 1, '闲聊': 2, '意图': 3}
            for index, i in enumerate(cluster_text_list):
                class_id = hashlib.md5(''.join(i).encode(encoding='UTF-8')).hexdigest()
                sec = df[df['text'].isin(i)]
                sec = sec.fillna('')
                item = {}
                # item['id'] = index
                item['class_id'] = class_id
                item['query_content'] = i[0]
                item['relation_knowledge_type'] = mapper.get(sec['relation_type'].value_counts().index[0],0)
                item['relation_intention_id'] = sec['relation_intention_id'].value_counts().index[0]
                item['relation_intention'] = sec['relation_intention'].value_counts().index[0]
                item['query_count'] = sec['session_id'].unique().shape[0]
                item['session_time'] = sec['session_time'].max()
                item['text_id'] = json.dumps(sec['text_id'].tolist(), ensure_ascii=False)
                item['detail'] = json.dumps(sec['text'].tolist(), ensure_ascii=False)
                item['tenant_id'] = data_key.split('_')[1]
                item['create_time'] = datetime.now()
                final_output.append(item)
                # 反写问句详情页

                text_id = str(tuple(sec['text_id'].tolist()))
                text_id = text_id[:-2] + text_id[-2:].replace(',', '')
                self.mdsql.execute_sql('aicc', f"update AICC.t_robot_none_match set class_id='{class_id}' where id in {text_id}")

            final_df = pd.concat([pd.DataFrame(i, index=[0]) for i in final_output])
            final_df['session_time'] = final_df['session_time'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            final_df['create_time'] = final_df['create_time'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))

            self.mdsql.execute_sql('aicc', f"delete from AICC.t_robot_none_match_nlp where tenant_id = {data_key.split('_')[1]}")

            self.mdsql.to_sql(final_df, 'AICC', 't_robot_none_match_nlp', if_exists='append')

            result['耗时'] = time.time()-start
            logger.debug(f"聚类预测完成 - 耗时:{time.time()-start}")
        except Exception as e:
            logger.error(f"聚类预测失败 - data_key: {data_key}, 错误: {e}",exc_info=True)
            result["code"] = 1
            result["error_code"] = "NLU91023"
            result["error_type"] = 0
            result["msg"] = f"聚类错误: {e}"
        # self.redis.set_data(key=f"nlp_cluster_predict_{data_key}", msg=result)
        return final_df


if __name__ == "__main__":
    model = ClusterMain_DM()
    data_key = "cluster_831749243_1729011600071"
    # ==================== predict model_id ====================
    result = model.predict(data_key=data_key)
    # model.mysql.execute_sql('aicc',"""delete from t_robot_none_match where robot_name = '测试萨达速度阿萨'""")
