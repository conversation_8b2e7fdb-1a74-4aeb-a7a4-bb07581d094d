import sys
# sys.path.insert(0,'/data/79/aicc-nlp-nlu_RegularHttpServer_merge')
import pandas as pd
from tqdm import tqdm
from setting import logger
from database.REDIS import REDIS
import setting
redis = REDIS()
import importlib



from utils.callback_utils import RECODER,callback_for_http
rrr = RECODER()

def call_openai(input_):
    import openai
    importlib.reload(openai)
    openai.api_key = "sk-O9xxBNEt9aHZippw0234EcF4738b42768120Ec84B9284f6c"
    openai.api_base = "https://han9.plus7.plus/v1"
    logger.debug(f'语料推荐prompt输入:{input_}')
    for i in range(3):
        try:
            response = openai.ChatCompletion.create(
                # CHATPG GPT API REQQUEST
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": ""},
                    {'role': 'user', 'content': input_}
                ],
                temperature=0.7,
                max_tokens=2000,
                top_p=1.0,
                frequency_penalty=0.0,
                presence_penalty=0.0,
                stream=True,  # this time, we set stream=True
                request_timeout=10
            )

            answer = ''
            for event in response:
                # c.write(answer)
                # event_time = time.time() - start_time
                event_text = event['choices'][0]['delta']
                answer += event_text.get('content', '')
            logger.debug(f'语料推荐回答：{answer}')
            return answer
        except:
            logger.error('gpt3.5报错',exc_info=True)
    return ''


from model.CallLLM.CallOpenAI_ultra import CallOpenAI

base_url = setting.FILECHAT_MODEL_URL
# base_url = "http://region-3.seetacloud.com:46181/v1/"
api_key = "EMPTY"
our_model = CallOpenAI('zs-gpt',base_url=base_url,api_key=api_key)

save_result = []

def realtime(title,theme,labelData='',answer='',model_id='',return_50=True):
    result = []
    labelData = labelData.split('||')[:50]  #给模型看的语料最多50条
    labelData = [i for i in labelData if i.strip()]
    if return_50:
        need_lenth = 50
    else:
        need_lenth = 50 - len(labelData)

    if need_lenth<=0:
        logger.debug(f'目标语料长度：{need_lenth}，返回推荐语料长度：{len(result)}')
        return result

    history_recommend = []
    if model_id:
        history_data = redis.get_data(model_id,need_json=True)
        if str(history_data)!=str({}):
            theme = history_data['theme']
            history_recommend = history_data['history_recommend']

    prompt = f"""
    {theme}，在对活场景中，客户表示”{title}"，我的回答是“{answer}”，关于用户的问题“{title}”，会有如下表述方式:
    {'、'.join(list(set(labelData))[0:20])}
    还可能有哪些表述方式，要求要口语化、模拟用户真实问法，给出{need_lenth}个可能的表达，输出格式为:
    1.句子1
    2.句子2
    3.句子3
    """
    for i in range(5):
        # tmp_resut = call_openai(prompt)
        logger.info(f'大模型语料推荐输入：{prompt}')
        tmp_resut = our_model.run(prompt, llm_parameter={
            'temperature': 0.8,
            'presence_penalty': 1.5,
            'top_p': 1
        })
        logger.info(f'大模型语料推荐返回：{tmp_resut}')
        save_result.append({'prompt':prompt,'result':tmp_resut})
        tmp_resut = tmp_resut.split(':')[-1].split('：')[-1]
        tmp_resut = [i.split('.')[-1].replace(' ','') for i in tmp_resut.split('\n')]
        tmp_resut = [i for i in tmp_resut if i]
        if model_id:
            tmp_resut = list(set(tmp_resut) - set(history_recommend) - set(result))
        tmp_resut = list(set(tmp_resut) - set(labelData))
        tmp_resut = list(set(tmp_resut))
        result += tmp_resut
        result = list(set(result))
        prompt = f"""
        {theme}，在对活场景中，客户表示”{title}"，我的回答是“{answer}”，关于用户的问题“{title}”，会有如下表述方式:
        {'、'.join(list(set(labelData + result)))}
        还可能有哪些表述方式，要求要口语化、模拟用户真实问法，给出{need_lenth}个可能的表达，输出格式为:
        1.句子1
        2.句子2
        3.句子3
        """
        if len(result)>=need_lenth:
            break

    result = result[:need_lenth]   #限制死50条
    if model_id:
        history_recommend += result
        redis.set_data(model_id, {'theme':theme,'history_recommend':history_recommend} ,need_json=True)
    logger.debug(f'目标语料长度：{need_lenth}，返回推荐语料长度： {len(result)}')
    return result

def offline(redis_key,theme,callback_url,recall_data):
    data = redis.get_data(redis_key,need_json=True)
    train_record = {'sn':'','code':1,'msg':'服务重启','redis_key':redis_key,'callbackurl':callback_url}
    rrr.dump_train_record(train_record,is_add=True)
    for i in tqdm(data):
        try:
            title = i['title']
            labelData = i.get('labelData','')
            answer = i.get('answer','')
            sec_output = realtime(title=title,labelData=labelData,answer=answer,theme=theme,return_50=False)
            i['recommend'] = '||'.join(sec_output)
        except:
            logger.error('离线部分错误',exc_info=True)

    result_key = 'recommand_result_' + redis_key
    redis.set_data(result_key,data)
    recall_data_for_java = {'code':0,'redis_key':result_key,'msg':'语料推荐成功'}
    recall_data.update(recall_data_for_java)
    callback_for_http(callback_url,recall_data)
    rrr.dump_train_record(train_record,is_add=False)


def is_llm_available():
    output = our_model.run('请说你好两个字')
    if output:return True
    else:return False

if __name__=='__main__':
    title = '牌照續期登記費收取要求'
    labelData = "續費大概多少錢||續費大概收多少||續費收費怎麼算||續期費用是多少||續期費用你們怎麼收||牌照續費要多少錢||續費要多少錢||續期需要支付多少費用||續費的價格是多少||續期要花多少錢||續期的收費標準是什麼||續費要交多少錢||年度續期費多少||續期多少錢||牌照過期了，續費要多少錢||牌費續費要多少||牌費如果我要續的話，需要繳納多少錢||業務牌照續費需要多少澳門元||業務牌照續費怎麼收取||業務牌照費用要多少||業務牌照需要多少費用||我需要續費，麻煩告訴下手續費多少"
    theme = ''
    answer = """您好，根據2021年度的保險中介人年度登記費要求：
    1.保險代理人每一個業務牌照的續期費為600澳門元；
    2.保險推銷員每一個業務牌照的續期費為500澳門元；
    3.投資相連業務牌照，是人壽業務牌照的附屬牌照，無需繳付牌費。"""
    # result = realtime(title,labelData,answer,theme)

    data = [{"answer":"","id":"1809130850587492353","labelData":"","title":"可以"},{"answer":"","id":"1809130850608463872","labelData":"","title":"身体不便"},{"answer":"","id":"1809130850625241088","labelData":"","title":"已参加"},{"answer":"","id":"1809130850642018304","labelData":"","title":"不清楚"},{"answer":"","id":"1809130850658795520","labelData":"","title":"没称体重"},{"answer":"","id":"1809130850671378432","labelData":"","title":"血糖高"},{"answer":"","id":"1809130850688155648","labelData":"","title":"运动时长"},{"answer":"","id":"1809130850704932864","labelData":"","title":"体重下降"},{"answer":"","id":"1809130850721710080","labelData":"","title":"口味重"},{"answer":"","id":"1809130850738487296","labelData":"","title":"具体次数"},{"answer":"","id":"1809130850755264512","labelData":"","title":"口味轻"},{"answer":"","id":"1809130850772041728","labelData":"","title":"数值检测"},{"answer":"","id":"1809130850788818944","labelData":"","title":"不太确定"},{"answer":"","id":"1809130850805596160","labelData":"","title":"是本人"},{"answer":"","id":"1809130850818179072","labelData":"","title":"能否代答-不能"},{"answer":"","id":"1809130850834956288","labelData":"","title":"体重上升"},{"answer":"","id":"1809130850847539200","labelData":"","title":"非本人"},{"answer":"","id":"1809130850864316416","labelData":"","title":"是谁/什么事情"},{"answer":"","id":"1809130850881093632","labelData":"","title":"是家属"},{"answer":"","id":"1809130850893676544","labelData":"","title":"没测"},{"answer":"","id":"1809130850914648064","labelData":"","title":"抽烟量"},{"answer":"","id":"1809130850935619584","labelData":"","title":"降压药名字"},{"answer":"","id":"1809130850956591104","labelData":"","title":"不可以"},{"answer":"","id":"1809130850973368320","labelData":"","title":"口味合适"},{"answer":"","id":"1809130850985951232","labelData":"","title":"体重没变"},{"answer":"","id":"1809130851002728448","labelData":"","title":"测了"},{"answer":"","id":"1809130851015311360","labelData":"","title":"不确定"},{"answer":"","id":"1809130851032088576","labelData":"","title":"具体次数+时长"},{"answer":"","id":"1809130851053060096","labelData":"","title":"能否代答-可以"}]
    for i in tqdm(data):
        try:
            title = i['title']
            labelData = i.get('labelData','')
            answer = i.get('answer','')
            sec_output = realtime(title=title,labelData=labelData,answer=answer,theme=theme,return_50=False)
            i['recommend'] = '||'.join(sec_output)
        except:
            logger.error('离线部分错误',exc_info=True)

    df = pd.DataFrame(save_result)
    df.to_excel(r"C:\Users\<USER>\Desktop\语料推荐连续请求.xlsx")
    # for i in range(10):
    #     result = call_openai('你好')
    #     break












