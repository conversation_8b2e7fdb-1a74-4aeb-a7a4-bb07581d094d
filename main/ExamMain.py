import os
import shutil
import time
import requests
from database.REDIS import REDIS
from module.Exam.ComposeExam.ComposeExam import ComposeExam
from setting import logger, SAVE_MODEL_DIR
from utils.filechat_utils import ToFile, ultra_extract


class ExamMain:
    def __init__(self):
        self.compose_exam_model = ComposeExam()
        self.redis = REDIS()

    def train(self, model_id, data=None, data_key='', sendredis=False,**kwargs):
        start_time = time.time()
        redis_key = f"nlp_exam_result_{model_id}"

        try:
            logger.debug(f'开始抽题,model_id:{model_id}')
            if data_key:
                data = self.redis.get_data(data_key)

            data = [i for i in data if i['filename']]
            logger.debug(f"抽题数据:{data}")
            filenames = [i['filename'] for i in data]
            prompt_data_key_list = [i.get('prompt_data_key', "") for i in data]
            base64s = [i.get('base64', '') for i in data]
            need_check = data[0].get("need_check", 0)
            question_types = data[0].get("question_types", [])
            tab_txt = data[0].get("tab_txt", "0")
            ocr_switch = data[0].get("ocr_switch", False)

            other_model_id = data[0].get("other_model_id", "")
            other_model_type = data[0].get("other_model_type", "")
            enterprise_id = data[0].get("enterprise_id", "")

            save_dir = os.path.join(SAVE_MODEL_DIR, model_id, self.compose_exam_model.__class__.__name__)
            file_save_dir = os.path.join(SAVE_MODEL_DIR, model_id, 'files')
            os.makedirs(save_dir, exist_ok=True)
            os.makedirs(file_save_dir, exist_ok=True)

            contents = []

            for index, filename in enumerate(filenames):
                try:
                    save_filename = filename.split('/')[-1]
                    file_path = os.path.join(file_save_dir, save_filename)
                    if base64s[index]:
                        ToFile(base64s[index], file_path)
                    else:
                        if os.path.exists(filename):
                            shutil.copy(filename, file_path)
                        else:
                            self.download(filename, file_path)
                    texts = ultra_extract(file_path, ocr_switch)
                    contents.append(texts)
                except:
                    logger.error('解析文件报错', exc_info=True)
                    contents.append(["这是一段没意义的文字"])
            code, msg = self.compose_exam_model.train(model_id, filenames, contents, save_dir, need_check, prompt_data_key_list, question_types, other_model_id, other_model_type, enterprise_id)

            q_result = self.query(model_id=model_id)
            self.redis.set_data(redis_key, msg=q_result)
            logger.debug(f"抽题耗时统计,code:{code},msg:{msg},model_id:{model_id},字数:{sum([len(c) for content in contents for c in content])},总耗时:{time.time()-start_time},")
            if code != 0:
                result = {"key": redis_key, "model_id": model_id, "code": 1, "msg": "失败", "error_code": "NLU91020", "error_type": 0, "task": "train", "score": 0}
            else:
                result = {"key": redis_key, "model_id": model_id, "code": 0, "msg": "成功", "task": "train", "score": 0}
        except Exception as e:
            logger.debug(f"{model_id} 抽题报错:{e}")
            q_result = self.query(model_id=model_id)
            q_result["code"] = 1
            q_result["msg"] = f"抽题报错:{e}"
            self.redis.set_data(redis_key, msg=q_result)
            result = {"key": redis_key, "model_id": model_id, "code": 1, "msg": "失败", "error_code": "NLU91020",
                      "error_type": 0, "task": "train", "score": 0}
        return result

    def query(self, model_id):
        save_dir = os.path.join(SAVE_MODEL_DIR, model_id, self.compose_exam_model.__class__.__name__)
        query_result = self.compose_exam_model.query(model_id=model_id, save_dir=save_dir)
        return query_result

    def query_test(self, model_id):
        save_dir = os.path.join(SAVE_MODEL_DIR, model_id, self.compose_exam_model.__class__.__name__)
        query_result = self.compose_exam_model.query_test(model_id=model_id, save_dir=save_dir)
        return query_result

    def download(self,url,download_path):
        try:
            if os.path.exists(download_path):  # 检查对应wav是否存在
                return '已下载'
            logger.info(f'正在请求下载链接{url}')
            data = requests.get(url)
            with open(download_path, 'wb') as f:
                f.write(data.content)
                f.close()
            time.sleep(1)
            return '下载完成'
        except Exception as e:
            logger.error(f'文件下载失败:{url}',exc_info=True)
            return e

    def train_time(self, data):
        # 测试统计每个字训练时间0.02s
        base64s = [i.get('base64', '') for i in data]
        filenames = [i['filename'] for i in data]
        file_save_dir = os.path.join(SAVE_MODEL_DIR, "exam_count", 'files')
        save_dir = os.path.join(SAVE_MODEL_DIR, "exam_count", self.compose_exam_model.__class__.__name__)
        os.makedirs(save_dir, exist_ok=True)
        os.makedirs(file_save_dir, exist_ok=True)
        contents = []
        for index, filename in enumerate(filenames):
            try:
                save_filename = filename.split('/')[-1]
                file_path = os.path.join(file_save_dir, save_filename)
                if base64s[index]:
                    ToFile(base64s[index], file_path)
                else:
                    if os.path.exists(filename):
                        shutil.copy(filename, file_path)
                    else:
                        self.download(filename, file_path)
                time.sleep(3)
                texts = ultra_extract(file_path, False)
                contents.append(texts)
            except:
                logger.warning('纯图片未计算出文字数量')
                contents.append(["这是一段没意义的文字"])
        sum_word = sum([len(c) for content in contents for c in content])
        return int(sum_word * 0.2 * 2)


if __name__ == '__main__':
    data = [
        {
            'filename': "/data/cbk/kaopei_llm/data/test_file/关于开展开门红期间家庭业务激励竞赛的通知.pdf",
            'prompt_data_key': ""
        }
    ]
    model_id = 'test_exam_main_AAA'
    main = ExamMain()
    main.train(model_id, data)
    main_result = main.query(model_id)
    print("wait")

