import datetime
import sys
import time
from collections import defaultdict
import requests,json
from setting import logger,FILECHAT_MODEL
import setting
from database.REDIS import REDIS
import re

from main.RetrievalMain_bge import RetrievalMain_bge
from module.Prompt.prompt import get_prompt,answer_replacer
from module.LLM.our_LLM import our_LLM
if FILECHAT_MODEL == 'tx':
    from module.LLM.Tencent_LLM import tx_LLM

from module.FileChatBot.FileChatPipelineMain_yield_doc2000 import FileChatPipelineMain
from model.CallLLM.CallOurModel import CallOurModel
# filechat_llm = CallOurModel(url='http://region-3.seetacloud.com:46181', temperature=0.1)
# our_model = CallOurModel(url=setting.FILECHAT_MODEL_URL)


from model.CallLLM.CallOpenAI_ultra import CallOpenAI
# base_url = "http://region-3.seetacloud.com:19965/v1/"
base_url = setting.FILECHAT_MODEL_URL
# base_url = "http://region-3.seetacloud.com:46181/v1/"
api_key = "EMPTY"
our_model = CallOpenAI('zs-gpt',base_url=base_url,api_key=api_key)



from model.FileChatAction.LawyerBot import LawyerBot
agent = LawyerBot(our_model)


from module.FileChatBot.FileChatPipelineMain_yield_lawyer import FileChatPipelineMain
chatbot = FileChatPipelineMain(our_model)
from module.FileChatBot.FileChatPipelineMain_yield_doc2000 import FileChatPipelineMain
filechatbot = FileChatPipelineMain(our_model)

from model.CallLLM.qwen.qwen_token_lener import qwen_lener


class FileChatMain:
    def __init__(self):
        self.answers = defaultdict(dict)
        self.history = defaultdict(list)
        self.redis = REDIS()
        self.our_model = our_model

    def transform_result_to_openai_stype(self, model_id,llm_parameter,responce):
        # 预测进来的时候就要写self.answers,因为FileChatServer需要根据self.answers判断有没有训练过。
        stream = llm_parameter.get('stream',False)
        logger.info(f'开始stream:{stream}解码:{llm_parameter}')
        if stream:
            # responce = self.our_model.stream_run(llm_parameter)
            return_data = {"model_id":model_id,
                          "id": "chatcmpl-Mb65700CC3MOkhJ",
                          "object": "chat.completion.chunk",
                          "created": int(time.time()),
                          "model": "zs-gpt",
                          "choices": [{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":""}],
                           "usage":{}
                           }

            encode_done = False
            start = time.time()
            current_text = ''
            for text,update_info in responce:
                if not encode_done:
                    return_data['usage']['encode_time'] = time.time() - start
                    encode_done = True
                return_text = text[len(current_text):]   #流式只返回几个字
                current_text = text
                if return_text == '':
                    continue
                if current_text=='大模型报错':
                    break
                return_data['choices'][0]['delta']['content'] = return_text
                # yield return_data
                yield json.dumps(return_data,ensure_ascii=False)


            #信息补充
            return_data['choices'][0]['delta']['content'] = ''
            return_data['usage']['prompt_tokens'] = qwen_lener(update_info.get('4final_answer_prompt',''))
            return_data['usage']['completion_tokens'] = qwen_lener(text)
            return_data['usage']['total_tokens'] = return_data['usage']['prompt_tokens'] + return_data['usage']['completion_tokens']
            return_data['choices'][0]['finish_reason'] = 'stop'
            return_data['usage']['total_time'] = time.time() - start

            yield json.dumps(return_data,ensure_ascii=False)
            # yield return_data
        else:
            # responce = self.our_model.stream_run(llm_parameter)
            start = time.time()
            return_data = {
                "model_id":model_id,
                        "id": "chatcmpl-Mb65700CC3MOkhJ",
                              "object": "chat.completion.chunk",
                              "created": int(time.time()),
                              "model": "zsai",
                              "choices": [
                                {
                                  "index": 0,
                                  "message": {
                                    "role": "assistant",
                                    "content": ""
                                  },"finish_reason": "stop"}],
                                  }

            encode_done = False
            for text,update_info in responce:
                if not encode_done:
                    return_data['usage']['encode_time'] = time.time() - start
                    encode_done = True
                return_data['choices'][0]['message']['content'] = text
            # return json.dumps(return_data,ensure_ascii=False)

            #信息补充
            return_data['usage']['prompt_tokens'] = qwen_lener(update_info.get('4final_answer_prompt',''))
            return_data['usage']['completion_tokens'] = qwen_lener(text)
            return_data['usage']['total_tokens'] = return_data['usage']['prompt_tokens'] + return_data['usage']['completion_tokens']
            return_data['choices'][0]['finish_reason'] = 'stop'
            return_data['usage']['total_time'] = time.time() - start
            yield return_data


    def pipeline(self, model_id, retrievalmain_bge:RetrievalMain_bge,llm_parameter,use_agent=False,use_rewrite=False,use_prerank=False):

        #初始化
        update_info = {}
        task_list = ['task3']

        # 使用agent
        if use_agent:
            update_info, task_list = agent.get_task(llm_parameter)
            logger.debug(f"task解析:{update_info['0task_llm_output']}")

            # 选择模型
            import re
            if re.findall('task3', str(task_list)) and 'task5' not in str(task_list):
                usebot = chatbot
            else:
                usebot = filechatbot
        else:
            usebot = filechatbot


        chat_model_config = {'use_rewrite':use_rewrite,
                             'use_prerank':use_prerank}
        messages_list = llm_parameter['messages']
        user_query = [i['content'] for i in messages_list if i['role']=='user']
        query = user_query[-1]

        def not_model_id_return(llm_parameter):
            response = self.our_model.stream_run(llm_parameter = llm_parameter,return_raw=True)
            for i in response:
                yield i

        # llm_parameter['max_tokens'] = 450
        messages = llm_parameter['messages']
        if 'system' not in str(messages):
            messages = [{'role':'system','content':'你是智能机器人，请你热情回答用户的问题。'}] + messages
            llm_parameter['messages'] = messages
        if not model_id:
            response = not_model_id_return(llm_parameter)
        else:
            response = usebot.search_and_chat(model_id, query,retrieval = retrievalmain_bge,llm_parameter = llm_parameter,
                                              update_info=update_info, task_list=task_list, chat_model_config=chat_model_config)

        stream = llm_parameter.get('stream',False)

        def process_openai_style(response):
            if stream:
                for i in response:
                    yield json.dumps(i,ensure_ascii=False)
                logger.info(f'解码结束：参数：{llm_parameter},结果：{i}')
            else:
                for i in response:
                    pass
                yield i

        return process_openai_style(response)

        # return self.transform_result_to_openai_stype(model_id,llm_parameter,response)





if __name__=='__main__':
    model_id = 'test_1'
    data = {"model_id": model_id,   #绑定的model_id
                "llm_parameter":{"messages": [
                                    {"role":"system", "content":"你是一个机器人助手"},#如果传了就用system的prompt
                                    {"role":"user", "content": "这个公司病假带薪吗" }
                                  ],
                                    "temperature": 0.1,
                                    "stream": True,
                                    "max_tokens":12123,
                                    "top_k":3,
                                    "top_p":0.85
                                  },
                "prerank":False,#是否使用精排策略，默认False
                "rewrite":False,#query重写检索策略，默认False
                "agent":False#是否使用agent控制闲聊，默认False
                    }

    chat = FileChatMain()
    from main.RetrievalMain_bge import RetrievalMain_bge
    retrieval = RetrievalMain_bge()
    result = chat.pipeline(model_id,retrieval,data['llm_parameter'])
    for i in result:
        print(i)



    # import requests
    # import json
    # # response = requests.post('http://127.0.0.1:5001/nlp/filechat/predict',data=json.dumps(data,ensure_ascii=False),stream=True)
    # response = requests.post('http://127.0.0.1:5001/nlp/filechat/predict',data=json.dumps(data,ensure_ascii=False))
    # for i in response:
    #     print(i)


    # query_id = '2'
    # query = '哪个城市最发达'
    # chat.chat(query,[],session_id,query_id)

    """
curl -X POST http://**********:9899/nlp/filechat/predict \
    -H "Content-Type: application/json" \
    -d '{
    "model_id": "3wr5ut4cb5aaksnsw6ue",
    "llm_parameter": {
        "messages": [
            {
                "id": "",
                "role": "system",
                "content": "null"
            },
            {
                "id": "",
                "role": "user",
                "content": "你是谁"
            }
        ],
        "max_tokens": 4096,
        "temperature": 0.3,
        "top_k": 3,
        "top_p": 0.85,
        "stream": true
    }}'
    """


