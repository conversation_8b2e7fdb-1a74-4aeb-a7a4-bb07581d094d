import datetime
import sys
from collections import defaultdict
import requests,json
from setting import logger,FILECHAT_MODEL
import setting
from database.REDIS import REDIS
import re

import openai
openai.api_key = "sk-O9xxBNEt9aHZippw0234EcF4738b42768120Ec84B9284f6c"
openai.api_base = "https://han9.plus7.plus"

from module.Prompt.prompt import get_prompt,answer_replacer
from module.LLM.our_LLM import our_LLM
if FILECHAT_MODEL == 'tx':
    from module.LLM.Tencent_LLM import tx_LLM

class Openai_Decoder:
    def __init__(self):
        self.answers = defaultdict(dict)
        self.history = defaultdict(list)
        self.redis = REDIS()

    def search_and_chat(self, retrieval_main, model_id, query, session_id, query_id, lang):
        # 预测进来的时候就要写self.answers,因为FileChatServer需要根据self.answers判断有没有训练过。
        answer = {'answer': "", "end": "0", 'raw_answer': '',
                  'starttime': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                  'session_id': session_id,
                  'query_id': query_id}
        self.answers[session_id][query_id] = answer

        try:
            search_result = retrieval_main.predict(model_id, query, topn=20)
            logger.debug(f"retrieval_main检索结果:{search_result}")
        except:
            key = f'chatGptFileQuery_{session_id}_{query_id}'
            self.redis.set_data(key, {'end':'1','answer':'您问的问题我回答不上来呢','raw_answer':''}, need_json=True)
            logger.error('检索报错',exc_info=True)
            return
        self.chat(query, search_result,session_id, query_id, lang)

    def chat(self, query, retrieval_result, session_id='', query_id='' , lang='', max_length=2048, top_p=0.2, temperature=0.5):
        try:
            if type(retrieval_result) == dict:
                self.answers[session_id][query_id].update(retrieval_result)
                content = retrieval_result.get('retrieval_and_info_content','')
            else:
                content = retrieval_result
                content = '。'.join(content)

            # input_ = '公司资料：\n【' + '\n'.join(
            #     search_result) + '】\n' + f"\n根据前面公司资料中的全部内容总结回答问题：{query}\n准确、全面的回答，如果没有答案请说“根据已有资料，我无法回答”"
            # search_result) + '】\n' + f"问题：{query}\n"
            # search_result) + '】\n' + f"根据资料回答问题：：{query}\n，如果不能回答请回复\"根据已知信息无法回答\""

            # prompt = "\"\"\"\n@文章内容@\n\"\"\"\n根据三引号中的内容回答问题，不要编造答案，如果不能回答请说“很抱歉，当前所提供的信息无法回答您的问题，希望您能提供更多细节。”，\\n问题:@问题@"
            # prompt = "\"\"\"\n@文章内容@\n\"\"\"\n根据三引号中的内容总结回答问题，记住问题的答案在文本中可以找到，可能需要一定的推理和常识才能得到。\\n问题:@问题@"
            # prompt = "@文章内容@\n根据前面的资料回答问题，没有答案就说“根据已有资料无法回答该问题”\n问题：:@问题@"
            chat_history_str, history_doc_content_list = self.load_history(session_id)
            logger.debug(f'chat_history_str:{chat_history_str}')
            logger.debug(f'history_doc_content_list:{history_doc_content_list}')
            self.answers[session_id][query_id]['history'] = chat_history_str
            self.answers[session_id][query_id]['history_list'] = history_doc_content_list
            if setting.FILECHAT_MODEL == "our":
                doc_names = ",".join(retrieval_result.get("real_filename_map_url", {}).keys())
                input_ = get_prompt(query, chat_history_str, history_doc_content_list, content, doc_names, lang=lang)
            else:
                input_ = get_prompt(query, chat_history_str, history_doc_content_list, content, lang=lang)

                # logger.debug(f'\ndata: {data}\n')
            logger.debug(f'输入数据:{input_}')
            self.answers[session_id][query_id]['model'] = FILECHAT_MODEL
            if FILECHAT_MODEL=='gpt4':
                self.call_googlecloud_api(session_id,query_id,input_)
            elif FILECHAT_MODEL=='gpt3.5':
                self.call_openai_api(session_id,query_id,input_,lang=lang)
            elif FILECHAT_MODEL=='tx':
                self.call_tx_LLM(session_id,query_id,input_)
            elif FILECHAT_MODEL=='our':
                self.call_our_model(session_id,query_id,input_,lang=lang)


            # 查询来源文档
            self.find_the_filename(session_id, query_id)
            # 查找参考文档和内容
            self.find_hit_doc_and_content(session_id, query_id)
            # 去除来源句
            # self.replace_sourcefrom_sentence(session_id,query_id)
            self.answers[session_id][query_id]['end'] = "1"
            key = f'chatGptFileQuery_{session_id}_{query_id}'
            self.redis.set_data(key, self.answers[session_id][query_id], need_json=True)

            #对话总结和历史记忆
            answer = self.answers[session_id][query_id]['answer']
            history_doc_content_list = self.answers[session_id][query_id]['history_doc_content_list']
            logger.debug(f"解码结束,answer: {self.answers[session_id][query_id]}")
            self.save_hisotry(session_id, query, answer, history_doc_content_list)
            # self.get_summary(query_id)
        except:
            logger.error('openai解码报错',exc_info=True)
            self.answers[session_id][query_id]['answer'] = 'openai解码报错'
            self.answers[session_id][query_id]['end'] = "1"

    def load_history(self, session_id):
        history_doc_content_list = self.history[session_id][-1]["history_doc_content_list"] if len(self.history[session_id]) else []
        chat_history_str = '\n'.join([f"用户:{i['query']}\n机器人:{i['answer']}" for i in self.history[session_id][-3:]])
        return chat_history_str, history_doc_content_list

    def save_hisotry(self, session_id, query, answer, history_doc_content_list):
        self.history[session_id] += [{"query": query, "answer": answer, "history_doc_content_list": history_doc_content_list}]

    def call_openai_api(self,session_id,query_id,input_,lang="英语"):
        key = f'chatGptFileQuery_{session_id}_{query_id}'
        self.answers[session_id][query_id]['prompt'] = input_
        delay_time = 0.05
        for i in range(20):
            try:
                response = openai.ChatCompletion.create(
                    # CHATPG GPT API REQQUEST
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": ""},
                        {'role': 'user', 'content': input_}
                    ],
                    temperature=0.7,
                    top_p=1.0,
                    frequency_penalty=0.0,
                    presence_penalty=0.0,
                    stream=True,  # this time, we set stream=True
                )
                break
            except:
                logger.error('openai解码报错', exc_info=True)

        import time
        try:
            for event in response:
                # c.write(answer)
                # event_time = time.time() - start_time
                event_text = event['choices'][0]['delta']
                self.answers[session_id][query_id]['raw_answer'] += event_text.get('content', '')
                time.sleep(delay_time)
                answer = self.answers[session_id][query_id]['raw_answer']
                #各种替换
                answer = answer_replacer(answer, lang)
                self.answers[session_id][query_id]['answer'] = answer
                self.redis.set_data(key, self.answers[session_id][query_id], need_json=True)
        except:
            logger.error('llm报错',exc_info=True)
            self.answers[query_id]['answer']='llm报错'

    def call_googlecloud_api(self,session_id,query_id,input_):
        query_id_for_gc = session_id + '_' + query_id
        key = f'chatGptFileQuery_{session_id}_{query_id}'
        import requests, time
        url = 'http://34.125.226.43:1237/chatgpt/predict'
        self.answers[session_id][query_id]['prompt'] = input_
        for i in range(1000): #200秒超时
            try:
                data = {'mode': 'gpt-4-stream', 'query_id': query_id_for_gc, 'prompt': input_}
                output = requests.post(url, data=data).json()
                if output['answer']=='llm报错':
                    output['answer'] = '服务出错，请重新再试'
                answer = output['answer']
                self.answers[session_id][query_id]['raw_answer'] = answer

                #各种替换
                answer = answer_replacer(answer)
                self.answers[session_id][query_id]['answer'] = answer
                self.redis.set_data(key, self.answers[session_id][query_id], need_json=True)
                if output['end'] == '1':
                    break
                time.sleep(0.1)
            except:
                logger.error('googlecloud接口报错',exc_info=True)

    def call_tx_LLM(self,session_id,query_id,input_):
        key = f'chatGptFileQuery_{session_id}_{query_id}'
        self.answers[session_id][query_id]['prompt'] = input_
        try:
            response = tx_LLM.run(input_)
            for text in response:
                # c.write(answer)
                # event_time = time.time() - start_time
                self.answers[session_id][query_id]['raw_answer'] = text
                answer = self.answers[session_id][query_id]['raw_answer']
                # 各种替换
                answer = answer_replacer(answer)
                self.answers[session_id][query_id]['answer'] = answer
                self.redis.set_data(key, self.answers[session_id][query_id], need_json=True)
        except:
            logger.error('llm报错', exc_info=True)
            self.answers[query_id]['answer'] = 'llm报错'

    def call_our_model(self,session_id,query_id,input_,lang="英语"):
        key = f'chatGptFileQuery_{session_id}_{query_id}'
        self.answers[session_id][query_id]['prompt'] = input_
        try:
            response = our_LLM(key,input_)
            for text in response:
                # c.write(answer)
                # event_time = time.time() - start_time
                self.answers[session_id][query_id]['raw_answer'] = text
                answer = self.answers[session_id][query_id]['raw_answer']
                # 各种替换
                answer = answer_replacer(answer, lang)
                self.answers[session_id][query_id]['answer'] = answer
                self.redis.set_data(key, self.answers[session_id][query_id], need_json=True)
        except:
            logger.error('llm报错', exc_info=True)
            self.answers[query_id]['answer'] = 'llm报错'


    def find_the_filename(self,session_id,query_id):
        if 'doc_num_map_filename' not in self.answers[session_id][query_id]:
            return
        answer = self.answers[session_id][query_id]['raw_answer']
        mapper = self.answers[session_id][query_id]['doc_num_map_filename']
        real_filename_map_url = self.answers[session_id][query_id]['real_filename_map_url']
        output = []
        hit_filenames_map = []
        for i in mapper:
            if i in answer:
                output.append(mapper[i])
                hit_filenames_map.append(i)
        if len(hit_filenames_map) == 0:
            for m_k in mapper.keys():
                doc_num_result = re.findall(r"^文档(\d+)$", m_k)
                if len(doc_num_result):
                    doc_num = doc_num_result[0]
                    if len(re.findall(r"((?:document|文档).{0,2}"+f"{doc_num})", answer)):
                        output.append(mapper[m_k])
                        hit_filenames_map.append(m_k)
        if 'source：' in answer:
            source_sentence = answer.split('source：')[-1].split(';')
            source_sentence = [i.split(',') for i in source_sentence]
            source_sentence = [j for i in source_sentence for j in i]
            source_sentence = [real_filename_map_url.get(i,'') for i in source_sentence if real_filename_map_url.get(i,'')]
            hit_filenames_map += source_sentence
        hit_filenames_map = list(set(hit_filenames_map))
        self.answers[session_id][query_id]['hit_filenames'] = list(set(output))
        self.answers[session_id][query_id]['hit_filenames_map'] = hit_filenames_map

    def find_hit_doc_and_content(self, session_id, query_id):
        # 保存文档id和内容id，作为历史信息
        answer = self.answers[session_id][query_id]['raw_answer']
        self.answers[session_id][query_id]["history_doc_content_list"] = []
        doc_content_id_res = re.findall(r"(?:document|文档)[^0123456789内容]{0,2}(\d+)[^0123456789]{0,3}(?:content|内容)[^0123456789]{0,3}(\d+)", answer)
        doc_index_map_content = self.answers[session_id][query_id].get("doc_index_map_content", {})
        if len(doc_content_id_res) == 0 or len(doc_index_map_content) == 0:
            return
        history_doc_content_list = []
        for doc_content_id in doc_content_id_res:
            if len(doc_content_id) != 2:
                continue
            content = doc_index_map_content.get(f"文档{doc_content_id[0]}内容{doc_content_id[1]}", "")
            if len(content):
                history_doc_content_list.append(content)
        self.answers[session_id][query_id]["history_doc_content_list"] = history_doc_content_list

    def replace_sourcefrom_sentence(self,session_id,query_id):
        answer = self.answers[session_id][query_id]['raw_answer']
        # answer = "凡年满18周岁，具有完全民事行为能力、有合法稳定收入的中国公民和在中国境内有居留权等条件的外国人及港澳台同胞均可购买贵金属。参考文档1：常见问题解答.docx，内容5"
        hit_filenames_map = self.answers[session_id][query_id]['hit_filenames_map']
        final_sentence = answer.split('source：')[-1]
        if len(hit_filenames_map)>0:
            if final_sentence:
                self.answers[session_id][query_id]['final_sentence'] = final_sentence
                # if '来自于' in final_sentence:
                answer = answer.split(final_sentence)[0]
                if answer.strip():
                    answer = re.sub('文档(\d+)', '文档', answer)
                    answer = re.sub('内容(\d+)', '内容', answer)
                    self.answers[session_id][query_id]['answer'] = answer

    def source_to_content(self,session_id,query_id):
        answer = self.answers[session_id][query_id]['raw_answer']
        doc_index_map_content = self.answers[session_id][query_id]['doc_index_map_content']
        if 'source：' not in answer:
            return
        final_sentence = answer.split('source：')[-1].split(';')
        final_sentence = [i.replace(' ','') for i in final_sentence]
        #parse: source：
        # 文档1,内容1,内容7
        # 文档1,内容7; 文档1,内容6
        # 文档1内容1, 文档1内容6
        # for i in final_sentence:
        #     if len(i.split(','))>2:


if __name__=='__main__':
    chat = Openai_Decoder()
    session_id = 'efefegegegegegeg'
    query = """当前问题可以参考的资料有：
###文档1:青牛智胜-员工手册-第二版.pdf
内容1:
第一节 薪资结构
薪资结构：综合薪资 = 基本薪资（固定薪资） + 绩效薪资 + 浮动
薪资 + 福利津贴 + 五险一金
1. 基本薪资：员工任职岗位的基本薪资，基本薪资=基本薪资基数*
（实际月度到岗天数/月度应到岗天数）；
2. 绩效薪资：员工上个季度工作的考核结果及体现，绩效薪资=绩效
薪资基数*绩效系数*（实际月度到岗天数/月度应到岗天数），绩效
系数为员工的绩效考核成绩；
3. 浮动薪资=其他贡献奖+年终双薪+年终奖金
3.1 其他贡献奖=项目贡献奖+个人贡献奖
3.2 年终双薪=固定月薪*（当年工作月份/12）
3.3 年终奖金=固定月薪*年终绩效考核系数*(当年转正后工作月份
/12)+其他
注：
1．公司会根据经营情况及员工年度工作表现，给部分同事发放浮动薪资；
2．其他是指根据公司会根据经营情况拿出部分利润作为奖金包，由总裁办
根据员工贡献进行分配；
3．年终双薪及年终奖金不适用于未转正员工。
内容2:
第四节 公司组织结构
青牛智胜（深圳）科技有限公司-ygsc002
第 7 页 共 38 页
青牛智胜（深圳）科技有限公司-ygsc002
内容3:
实报实销（飞机要求经济舱）
其他员工 经济舱 二等座 二等座 实报实销 油费及过路费
内容4:
第二节 薪资计算及发薪日期
1. 公司按照标准工作时间计算薪资，以自然月为周期，根据每月实
际出勤日计算薪资。
2. 当月薪资于次月 10 号前转入员工个人名下的工资卡账户中。
第 14 页 共 38 页
青牛智胜（深圳）科技有限公司-ygsc002
内容5:
第二节 公司发展历程
青牛智胜（深圳）科技有限公司-ygsc002
内容6:
第三节 薪资调整机制
员工出现薪资调整的情况如下：
1. 员工常规薪资调整，指公司根据经营业绩情况、社会综合物价水
平情况及行业薪资水平情况，同时结合员工的技能、当前薪资及
对公司的贡献进行综合评估，如发现有不符者就会进行调整，调
整幅度一般为 5%-20%之间，原则上公司每年的薪酬调整时间为四
月份，如有特殊情况除外。
2. 特殊员工薪酬调整，员工在连续一次或多次的绩效考核中获得优
异成绩，将获得即时调薪的机会。
3. 员工晋级薪资调整，公司根据员工在工作业绩表现突出者，为公
司创造有效、有利成绩显著者，员工在获得晋级的同时将调整薪
酬，调整幅度一般为 10%-30%之间，如有特殊情况除外。
4. 员工在日常考核中，被公司认为工作绩效低于平均水平时，公司
有权进行岗位及薪资调整。
5. 公司进行薪资调整时，新的薪资支付周期从次月生效执行。如有
其他通知，按照通知执行。
内容7:
公司激励费
如该项报销费用时是多笔报销时，需提供费用明细表，如会务费、培训费，相关材料：时间、地点、参与人数、具体内容、目的、费用标准、支付凭证等
注：以上费用管理及报销管理中未涉及到具体内容，请与财务管理部人员联系咨
询。
内容8:
第一节 公司基本概述
青牛智胜（深圳）科技有限公司成立于 2018 年 5 月，总部位于
深圳市南山区，是国内领先的对话式 ai 国家高新企业，2023 年被认
证为深圳市专精特新中小企业。
公司核心产研团队来自于原腾讯 teg 智能客服团队，专注于生成
式 ai，自然语言理解、对话处理、融合网络、深度学习、知识工程、
文本处理等技术研究，已构建 aicc 智能客服平台，aicp 智能培训平
台， aidh 互动数智人平台等产品矩阵。
公司已为金融、运营商、健康、政务、物流、电商等行业的多家
标杆企业提供服务，帮助客户快速实现数字化智能化转型，降低运营
成本，提升沟通效率及用户体验，为企业带来超预期的使用价值。
第 5 页 共 
###

你是一个非常聪明且很听话的文档查找和回答助手,你知道的全部知识仅仅是上面的内容。
                     现在请你根据以上你知道的内容回答用户当前的问题，当你从文档中找不到直接的答案信息回答用户时也要说明原因。
                     要求：如果你从参考信息中找不到直接相关的答案信息，那你就不能回答问题。
                     如果参考资料###中的内容没有直接的信息可以回答用户当前的问题，则一定要给出不能回答的理由。
                     不要输出参考资料中没有内容；
                     您的输出格式应该是这样的：
                     {"answer": "", "source": [文档*, 内容名称*]}
用户当前的问题:公司的薪资结构是怎么样的"""
    query_id = 'asdasdasdasdasdqqqasdasdq'
    # chat.chat(query,[],session_id,query_id)

    chat.answers[session_id][query_id] = {'answer': "", 'raw_answer':'',"end": "0", 'starttime': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
    # chat.call_googlecloud_api(session_id,query_id,query)
    # chat.call_openai_api(session_id,query_id,query)
    # query = '你好'
    chat.call_our_model(session_id,query_id,query)
    print(chat.answers[session_id][query_id])
    # query_id = '2'
    # query = '哪个城市最发达'
    # chat.chat(query,[],session_id,query_id)


