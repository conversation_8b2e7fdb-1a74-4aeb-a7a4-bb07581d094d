# -*- coding: utf-8 -*-

import time
import numpy as np

from database.REDIS import REDIS
from module.TextPairSim.PretrainTextPairSim.PretrainTextPairSim import PretrainTextPairSim
from setting import logger


class PretrainSimilarityScoreMain:
    def __init__(self):
        self.redis = REDIS()
        self.model = PretrainTextPairSim()

    def predict(self, model_id, query, answers):
        result = {
            "code": 0,
            "model_id": model_id,
            "query": query,
            "answers": answers,
            "score": 0,
            "msg": "预测成功",
        }

        try:
            start = time.time()
            scores = self.model.predict(query=query, text_list=answers)
            max_score = float(np.max(scores))
            logger.debug(f"预测完成 - model_id: {model_id}, query: {query}, answers: {answers}, 最高分: {max_score}, 耗时: {time.time()-start}")
            result["score"] = max_score
            result["all_score"] = scores[0].tolist()
        except Exception as e:
            logger.error(f"预测失败 - model_id: {model_id}, query: {query}, query: {query}, 错误: {e}")
            raise e

        return result


if __name__ == "__main__":
    model = PretrainSimilarityScoreMain()

    # ==================== predict ====================
    text_1 = "有什么办法可以测试文本相似度？"
    text_2 = "文本相似度的测量方法"
    text_3 = "这是一段测试代码"
    print(model.predict(model_id=None, query=text_1, answers=[text_2] * 20))
    print(model.predict(model_id=None, query=text_2, answers=[text_3]))

    # import pandas as pd
    #
    # data = pd.read_csv("../utils/EXAM.csv").values
    # for text_1, text_2 in data:
    #     print(f"\n{text_1}\n{text_2}")
    #     print(model.predict(model_id=None, text1=text_1, text2=text_2)["score"])
