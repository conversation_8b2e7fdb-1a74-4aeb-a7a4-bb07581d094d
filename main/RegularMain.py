# -*- coding: utf-8 -*-
import fcntl
import json
import os
import threading
import re, time
import setting
from database.REDIS import REDIS
from database.RabbitMQ import RabbitMQ
from module.Regular.RegularDataType import RegularDataType
from module.SimWordReplace.SimWordReplace import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from model.TraAndSim.TraAndSim import <PERSON>raAnd<PERSON>im
from setting import logger, SAVE_MODEL_DIR, CLIENT
from utils import my_utils


mutex = threading.Lock()
service_name = "Regular"
model_record_file = setting.MODEL_RECORD_FILE + f".{service_name}"
switch_model_channel = setting.SERVICE_CHANNEL_DICT["RegularMain"]["switch_model"]

class RegularMain:
    def __init__(self, callout=False):
        self.CALLOUR_SERIVE = callout
        self.redis = REDIS()
        self.model = RegularDataType()
        self.sim_word_replace_model = SimWordReplace()
        self.tra_and_sim_model = TraAndSim()
        self.load_model_dict = {}
        self.lastest_switch_time = {}

        if setting.USE_RABBIT_MQ:
            self.rabbit_listen = RabbitMQ()
        else:
            self.rabbit_listen = None

        if CLIENT != "train":
            if setting.USE_RABBIT_MQ:
                self.switch_thread = threading.Thread(target=self.rabbit_listen.listen_msg, args=(self.switch_callback,))
                self.switch_thread.start()
            else:
                self.redis.subscribe(switch_model_channel)
                callback_dict = {
                    switch_model_channel: self.switch_model
                }
                t1 = threading.Thread(target=self.redis.listen_msg, args=(callback_dict,))
                t1.start()

        # 加载已上线模型
        self.model_record = {}
        if setting.CLIENT == 'predict' and (not setting.AUTO_ONLINE_MODEL or setting.LRU_MODEL_LOAD_NUM):
            self.init_model()


        if setting.AUTO_ONLINE_MODEL and setting.CLIENT == 'predict' and not setting.LRU_MODEL_LOAD_NUM:
            t4 = threading.Thread(target=my_utils.auto_offline, args=(self, self.load_model_dict))
            t4.start()
        if setting.CLIENT == 'train' or setting.AUTO_ONLINE_MODEL:
            t5 = threading.Thread(target=my_utils.get_model_record_time, args=(self, self.lastest_switch_time,))
            t5.start()

        # 测试加载的模型
        self.train_client_load_model_list = []

        #外呼自动上下线逻辑
        if self.CALLOUR_SERIVE == True:
            #外呼加载模型列表
            self.callout_load_model_dict = {}
            # 外呼自动下线模型
            self.last_predict_model_record = {}
            t2 = threading.Thread(target=self.auto_offline)
            t2.start()
            #模型自动上线更新过的模型
            self.last_train_time_record = {}
            t3 = threading.Thread(target=self.auto_online_trained_model)
            t3.start()

    def switch_callback(self, ch, method, properties, body):
        if method.routing_key == switch_model_channel:
            logger.debug(f'rabbit_mq接收到监听消息:{switch_model_channel},msg:{body}')
            logger.debug(f"ch:{ch}")
            logger.debug(f"method:{method}")
            logger.debug(f"properties:{properties}")
            logger.debug(f"body:{body}")
            body_dict = json.loads(body)
            self.switch_model(**body_dict)

    def init_model(self):
        # 加载已上线模型
        model_ids = []
        if os.path.exists(model_record_file):
            with open(model_record_file, 'r', encoding='utf-8') as f:
                try:
                    self.model_record = json.load(f)
                    self.model_record = dict(sorted(self.model_record.items(), key=lambda x: x[1])) #从小到大
                    model_ids = list(self.model_record.keys())[-setting.LRU_MODEL_LOAD_NUM:] #只加载最近发布的模型
                    logger.info(f'{self.__class__.__name__}待加载列表:{model_ids}')
                except Exception as e:
                    logger.warning(f"加载历史上线模型记录文件失败: {e}")
        try:
            for model_id in model_ids:
                if self._load_model(model_id=model_id): # 加载失败的要在 model_record 里面移除
                    self.model_record.pop(model_id, 0)
                logger.debug('[{}] 已加载历史上线模型'.format(model_id))
            self.dump_record()
        except Exception as e:
            logger.warning(f"加载历史上线模型失败: {e}")

    def dump_record(self):
        if setting.SEND_MODEL_MODE == "Nas":
            return
        """记录已上线模型"""
        with open(model_record_file, 'w', encoding='utf-8') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.model_record, f, ensure_ascii=False)
        logger.debug('保存已上线模型记录')

    def train(self, model_id, data_key, sendredis=True,**kwargs):
        data = self.redis.get_data(data_key)
        for index, item in enumerate(data):
            item["title"] = self.tra_and_sim_model.tra_or_sim(item.get("title", ""), target="sim")
            keywords = item.get("keywords", [])
            if isinstance(keywords, str):
                item["keywords"] = self.tra_and_sim_model.tra_or_sim(keywords, target="sim")
            else:
                item["keywords"] = [self.tra_and_sim_model.tra_or_sim(k, target="sim") for k in keywords]
            new_label_data = []
            for k in item.get("labelData", "").split("||"):
                new_label_data.append(self.tra_and_sim_model.tra_or_sim(k, target="sim"))
            item["labelData"] = "||".join(new_label_data)
            data[index] = item

        model_save_dir = os.path.join(SAVE_MODEL_DIR, f"{model_id}/{self.model.__class__.__name__}/")
        code, msg = self.model.train(model_id, data, save_dir=model_save_dir)
        if code != 0:
            result = {"code": 1, "model_id": model_id, "task": "train", "score": 0, "error_code": "NLU91020", "error_type": 0, "msg": msg}
        else:
            result = {"code": code, "model_id": model_id, "task": "train", "score": 0, "msg": msg}
        if sendredis:
            self.redis.set_data(key=f"re_train_{model_id}", msg=result)
        if model_id in self.train_client_load_model_list:
            self._load_model(model_id=model_id)
        return result

    def predict(self, model_id, query, labelIds, detail, enterprise_id="", full_match=False, need_trans_to_simple=True):
        if setting.CLIENT == "train" and model_id not in self.train_client_load_model_list:
            load_result = self._load_model(model_id=model_id)
            if load_result == 0:
                self.train_client_load_model_list.append(model_id)
            if len(self.train_client_load_model_list) > setting.MAX_MODELS_TRAIN_CLIENT:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]
            self.lastest_switch_time[model_id] = int(time.time())


        if self.CALLOUR_SERIVE == True and setting.CLIENT == "predict":
            # 外呼预测自动上线逻辑
            self.auto_online(model_id, 1)
            #自动下线逻辑
            self.last_predict_model_record[model_id] = time.time()

        if setting.CLIENT == "predict" and setting.AUTO_ONLINE_MODEL:
            my_utils.auto_online(self, self.load_model_dict, model_id,self.lastest_switch_time)

        # 繁体转简体
        if need_trans_to_simple:
            if isinstance(query, list):
                tmp = []
                for q in query:
                    tmp.append(self.tra_and_sim_model.tra_or_sim(q, target="sim"))
                query = tmp
            else:
                query = self.tra_and_sim_model.tra_or_sim(query, target="sim")

        replaced_query, error_msg = self.sim_word_replace_model.predict(query=query, enterprise_id=enterprise_id)
        if error_msg:
            logger.warning(f"正则预测相似词替换出错, query:{query}, enterprise_id:{enterprise_id}, {error_msg}")
        logger.debug(f"正则预测相似词替换, 替换前:{query}, 替换后:{replaced_query}")
        result, error_dict = self.model.predict(model_id, replaced_query, labelIds, detail,full_match)
        return result, error_dict

    def test(self, query, regexs, ners=None):
        if isinstance(regexs, str):
            regexs = str(regexs).strip().split('||')
        if ners is None or len(ners) != len(regexs):
            ners = regexs
        result, error_dict = self.model.test(query, regexs, ners)
        return result, error_dict

    def switch_model(self, model_id, flag):
        code = 0
        try:
            if flag == 1:
                logger.debug('[{}] 收到消息,准备上线正则,flag:{}'.format(model_id, flag))
                code = self._load_model(model_id)
                if code == 0:
                    self.model_record[model_id] = time.time()
                    self.load_model_dict[model_id] = time.time()
                else:
                    self.model_record.pop(model_id, 0)
                    code = 1
                logger.debug('上线正则成功')
            elif flag == 0:
                logger.debug('[{}] 收到消息,准备下线正则,flag:{}'.format(model_id, flag))
                self._offline_model(model_id)
                self.model_record.pop(model_id, 0)
                logger.debug('[{}] 下线正则成功,flag:{}'.format(model_id, flag))
                if model_id in self.load_model_dict:
                    self.load_model_dict.pop(model_id)
            else:
                logger.error('[{}] 请求数据有误,flag:{}'.format(model_id, flag))
            self.dump_record()
        except Exception as e:
            code = 1
            logger.error('更新失败,flag:{},报错:{}'.format(flag, e))
        return code

    def _load_model(self, model_id):
        save_dir = os.path.join(SAVE_MODEL_DIR, f"{model_id}/{self.model.__class__.__name__}/")
        msg = self.model.load_model(model_id, save_dir=save_dir)
        if msg == "":
            self.load_model_dict[model_id] = time.time()
            # 先进先出
            for model_id in list(self.load_model_dict)[:-setting.LRU_MODEL_LOAD_NUM]:
                try:
                    self._offline_model(model_id)
                except:
                    logger.error(f'lru模型下线失败,model_id:{model_id}', exc_info=True)
            return 0
        else:
            return 400

    def _offline_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "offline_model",
            "msg": "下线模型成功"
        }
        offline_result = self.model.offline_model(model_id)
        if offline_result != "":
            result["code"] = 400
            result["msg"] = str(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{offline_result}")
        if model_id in self.load_model_dict:
            self.load_model_dict.pop(model_id)
        return result

    def train_time(self,data):
        return 300 

    @staticmethod
    def match_reg_highlight(query, regs: list):
        result = {
            "code": 0,
            "data": None,
            "msg": "预测成功",
        }
        pred = {}
        for index, reg in enumerate(regs):
            res = re.match(reg, query)
            if res:
                start = res.start()
                end = res.end()
                highlight_index = list(range(start, end))
                pred[index] = {'highlight_index': highlight_index, 'match_string': query[start:end]}
        result['data'] = pred
        return result

    @staticmethod
    def search_reg(query, regs: list):
        result = {
            "code": 0,
            "data": None,
            "msg": "预测成功",
        }
        query = query.split(' ')
        index = [index for index, reg in enumerate(regs) if eval('and'.join([f" '{i}' in reg " for i in query]))]
        result['data'] = index
        return result

    def auto_online(self,model_id,flag):
        if model_id not in self.callout_load_model_dict:   #每个模型id分配一个信号量(需要预先知道有哪些模型id,显然不成立),加锁,这样不会妨碍其他其他模型id io
            mutex.acquire()
            if self.callout_load_model_dict.get(model_id,None)==None:
                self.callout_load_model_dict[model_id] = 1  # 加锁
            else:
                flag = 0
            mutex.release()
            if flag==1:
                logger.debug(f'模型清单{self.callout_load_model_dict},正在加载模型{model_id}')
                load_result = self._load_model(model_id=model_id)  # 上线失败的异常
                if load_result == 0:
                    self.callout_load_model_dict[model_id] = 2  # 上线成功,解锁
                else:
                    self.callout_load_model_dict.pop(model_id)  # 模型加载失败
            else:logger.debug('有奇怪的东西跑进来了!!!!!!')
        while self.callout_load_model_dict[model_id] == 1:
            time.sleep(0.00001)
            pass

    def auto_offline(self):
        while True:
            model_list = list(self.last_predict_model_record.keys())
            for model_id in model_list:
                if time.time() - self.last_predict_model_record[model_id] > setting.CALLOUT_MODEL_EXIST_HOUR * 3600:
                    try:
                        logger.debug(f'启动自动下线分类模型:{model_id}')
                        self._offline_model(model_id)
                        self.callout_load_model_dict.pop(model_id)
                        self.last_predict_model_record.pop(model_id)
                    except:
                        self.last_predict_model_record.pop(model_id)
                        logger.error(f'自动下线分类模型失败:{model_id}',exc_info=True)
            time.sleep(60)

    def auto_online_trained_model(self):
        while True:
            model_list = list(self.callout_load_model_dict.keys())
            for model_id in model_list:
                train_log_path = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'download_finish', 'download_finish.tag')
                if os.path.exists(train_log_path):
                    finish_tag_time = os.path.getmtime(train_log_path)
                else:
                    finish_tag_time = os.path.getmtime(os.path.join(setting.SAVE_MODEL_DIR, model_id))
                last_finish_time = self.last_train_time_record.get(model_id,finish_tag_time)
                if finish_tag_time-last_finish_time<3:#训练完,发送完会修改两次文件,所以会重新上线两次,10秒内修改的就不重新上线了
                    pass
                else:
                    try:
                        self.callout_load_model_dict[model_id] = 1    #加锁
                        load_result = self._load_model(model_id=model_id)
                        if load_result==0:
                            self.callout_load_model_dict[model_id] = 2
                            logger.debug(f"分类模型自动上线新训练的模型成功 - model_id: {model_id}")
                        else:
                            self.callout_load_model_dict.pop(model_id)
                            logger.debug(f"分类模型自动上线失败 - model_id: {model_id}")
                    except Exception as e:
                        self.callout_load_model_dict.pop(model_id)
                        logger.error(f"分类模型自动上线失败 - model_id: {model_id}, 错误: {e}",exc_info=True)
                self.last_train_time_record[model_id] = finish_tag_time
            time.sleep(0.2)


if __name__ == "__main__":
    m = RegularMain()
    m.train(model_id="djy", data_key="regular_test_data", sendredis=False)
    print(m.predict(model_id="djy", query="听不清", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="我没听清啊", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="我现在在忙，你等等打过来把", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="啊你等会再打过来他这会正在忙", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="我的家人这会在忙", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="你吃饱没", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="吃饱没啊", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="去跑步把", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="去跑步", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="下雨", labelIds=[], detail=True))
    print(m.predict(model_id="djy", query="下雨了", labelIds=[], detail=True))
