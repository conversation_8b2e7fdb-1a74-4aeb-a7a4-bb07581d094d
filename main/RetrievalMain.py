import shutil
import time
import os
import json
import requests

from collections import defaultdict
from setting import logger, SAVE_MODEL_DIR, CLIENT
import setting
import pandas as pd

from database.REDIS import REDIS
from utils.my_utils import MyEncoder
from utils.filechat_utils import ToFile, ultra_extract
from module.Retrieval.Retrieval import Retrieval
from model.Translate.TranslateGPT import TranslateGPT



class RetrievalMain:
    def __init__(self):
        self.source_maps = {}
        self.train_client_load_model_list = []
        self.retrieval = Retrieval()
        self.translate_model = TranslateGPT()
        self.Redis = REDIS()

    def train(self, model_id, data=None, data_key='', sendredis=False,**kwargs):
        start_time = time.time()
        text_num = 0
        char_num = 0
        trans_text_num = 0
        trans_char_num = 0

        logger.debug(f'检索模型开始训练model_id:{model_id}')
        if data_key:
            data = self.Redis.get_data(data_key)
        data = [i for i in data if i['filename']]
        filenames = [i['filename'] for i in data]
        base64s = [i['base64'] for i in data if i.get('base64', '')]
        source_lang_list = [i['source_lang'] for i in data if i.get('source_lang', '中文简体')]
        target_lang_list = [i['target_langs'] for i in data if i.get('target_langs', ['中文简体'])]

        save_dir = os.path.join(SAVE_MODEL_DIR, model_id, 'Retrieval')
        file_save_dir = os.path.join(save_dir, 'files')
        os.makedirs(save_dir, exist_ok=True)
        os.makedirs(file_save_dir, exist_ok=True)

        contents = []
        source_map = defaultdict(list)

        for index, filename in enumerate(filenames):
            try:
                save_filename = filename.split('/')[-1]
                file_path = os.path.join(file_save_dir, save_filename)
                if base64s:
                    ToFile(base64s[index], file_path)
                # shutil.copy(filename, file_path)
                self.download(filename, file_path)
                texts = ultra_extract(file_path)
                text_num += len(texts)
                char_num += sum([len(t_) for t_ in texts])
                logger.debug(f"准备翻译,source_lang:{source_lang_list[index]},target_langs:{target_lang_list[index]}")
                translate_texts = self.translate_model.translate_text(
                    text_list=texts,
                    source_lang=source_lang_list[index],
                    target_langs=target_lang_list[index],
                    parallel=True
                )
                texts = translate_texts
                trans_text_num += len(texts)
                trans_char_num += sum([len(t_) for t_ in texts])
                for index, text in enumerate(texts):
                    source_map[text].append({'filename': filename, 'index': index, 'text': text})
                    if '\n' in text:
                        source_map[text.split('\n')[0]].append({'filename': filename, 'index': index, 'text': text.split('\n')[0]})
                contents.append(texts)
            except:
                logger.error('解析文件报错',exc_info=True)
                contents.append(["这是一段没意义的文字"])
        logger.debug(f"训练耗时统计,model_id:{model_id},翻译耗时:{time.time()-start_time},文本行数:{text_num},文本字符数:{char_num},翻译后文本行数:{trans_text_num},翻译后字符数:{trans_char_num}")
        start_time = time.time()
        code, msg,source_map_kvs = self.retrieval.train(model_id, filenames,contents, save_dir)
        logger.debug(f"训练耗时统计,model_id:{model_id},检索训练总耗时:{time.time()-start_time}")
        source_map.update(source_map_kvs)
        with open(os.path.join(save_dir, 'source_map.json'), 'w', encoding='utf-8') as f:
            json.dump(source_map, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)

        if code != 0:
            result = {"code": 1, "model_id": model_id, "task": "train", "score": 0, "error_code": "NLU91020", "error_type": 0, "msg": msg}
        else:
            result = {"code": code, "model_id": model_id, "task": "train", "score": 0, "msg": msg}
        if sendredis:
            self.Redis.set_data(key=f"retrieval_train_{model_id}", msg=result)
        if model_id in self.train_client_load_model_list:
            self.load_model(model_id)
        return result

    def predict(self, model_id, query, topn=10, need_detail=False):
        query = query.lower()
        result = {
            "code": 0,
            "model_id": model_id,
            "text": query,
            "msg": "预测成功",
        }
        start = time.time()
        if setting.CLIENT == "train" and model_id not in self.train_client_load_model_list:
            load_result = self.load_model(model_id=model_id)
            if load_result == 0:
                self.train_client_load_model_list.append(model_id)
            if len(self.train_client_load_model_list) > 30:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]
        try:
            retrieval_result,source_track = self.retrieval.predict(model_id, query, topn=topn, need_detail=need_detail)
            result['retrival_content'] = retrieval_result

            if not need_detail:
                retrival_info = []
                for i in retrieval_result:
                    try:
                        if i in self.source_maps[model_id]:
                            source = self.source_maps[model_id][i]
                        elif len(i.split('\n'))>1:
                            source = self.source_maps[model_id]['\n'.join(i.split('\n')[1:])]
                        #增加检索来源
                        search_engine_from = source_track[source_track['text']==i]['source'].tolist()[0]
                        for j in source:
                            j['search_engine_from'] = search_engine_from
                        retrival_info.append(source)
                    except:
                        logger.error(f"""source_map匹配失效:{i}""",exc_info=True)
                        retrival_info.append([{'filename': '无来源', 'index': 0,'text':i}])
                result['retrival_info'] = retrival_info
                hit_filenames = ''
                # if retrival_info:
                #     filenames = [i['filename'] for j in retrival_info for i in j]
                #     hit_filenames = max(filenames, key=filenames.count)
                result['hit_filenames'] = [hit_filenames]
                #组织检索信息
                if result['retrival_content']:
                    result.update(self.retrieval_and_info_content(result))
            logger.debug(f"Retrieval 预测完成,model_id{model_id},耗时:{time.time() - start}")
        except Exception as e:
            logger.error(f"预测失败 - model_id: {model_id}, text: {query}, 错误: {e}",exc_info=True)
            raise e
        return result

    def load_model(self,model_id):
        save_dir = os.path.join(SAVE_MODEL_DIR, model_id, 'Retrieval')
        self.retrieval.load_model(model_id,save_dir)
        with open(os.path.join(save_dir, 'source_map.json'), 'r', encoding='utf-8') as f:
            self.source_maps[model_id] = json.load(f)
        logger.debug(f'检索模型上线成功,model_id:{model_id}')
        return 0

    def _offline_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "offline_model",
            "msg": "下线模型成功"
        }

        logger.debug(f"开始下线模型 - model_id: {model_id}, 进程: {os.getpid()}")
        try:
            self.retrieval.offline_model(model_id=model_id)
            logger.debug(f"下线模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
        except Exception as e:
            logger.error(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 1
            result["msg"] = str(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")

    def train_time(self,data):
        return 8

    def download(self,url,download_path):
        try:
            if os.path.exists(download_path):  # 检查对应wav是否存在
                return '已下载'
            logger.info(f'正在请求下载链接{url}')
            data = requests.get(url)
            with open(download_path, 'wb') as f:
                f.write(data.content)
                f.close()
            return '下载完成'
        except Exception as e:
            logger.error(f'文件下载失败:{url}',exc_info=True)
            return e

    def retrieval_and_info_content(self,result):
        output_result = {}
        data = []
        for content, info in zip(result['retrival_content'], result['retrival_info']):
            for i in info:
                item = {}
                item.update(i)
                item['content'] = content
                data.append(item)
        data = pd.DataFrame(data)
        data['real_filename'] = data['filename'].apply(lambda x: x.split('/')[-1])
        data = data.drop_duplicates(subset=['content'])

        all_content = ''
        doc_index_map_content = {}
        for index, filename in enumerate(data['real_filename'].unique()):
            index += 1
            start = f'文档{index}:{filename}\n'
            sec = data[data['real_filename'] == filename]
            sec.index = range(len(sec))
            sec['内容index'] = '内容' + pd.Series(sec.index + 1).map(str)
            sec['文档内容index'] = f'文档{index}' + sec['内容index']
            sec['with_index_content'] = sec['内容index'] + ':\n' + sec['content']
            content = '\n'.join(sec['with_index_content'].tolist())
            content = start + content + '\n'
            all_content += content
            doc_index_map_content.update(dict(zip(sec['文档内容index'],sec['content'])))
        output_result['retrieval_and_info_content'] = all_content
        output_result['doc_num_map_filename'] = {f'文档{index+1}':i for index,i in enumerate(data['filename'].unique())}
        output_result['doc_index_map_content'] = doc_index_map_content
        output_result['real_filename_map_url'] = dict(zip(data['real_filename'].unique(),data['filename'].unique()))
        output_result['doc_num_map_filename'].update(output_result['real_filename_map_url'])
        return output_result

if __name__ == '__main__':
    from utils.filechat_utils import ToBase64
    # start = time.time()
    # base64s = []
    # paths = [r"D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2023-09\青牛智胜-员工手册-第二版.pdf"]
    # for path in paths:
    #     base64s.append(ToBase64(path))
    # filenames = [os.path.split(i)[-1] for i in paths]
    # data = [{'filename':"https://testagent.qnzsai.com/crm-api/sss-boot/sys/common/static/llm/A青牛介绍.docx",'base64':base64_, "source_lang": "简体中文", "target"} for filename,base64_ in zip(filenames,base64s)]

    data = [
        {
            'filename': "/data/cbk/temp/test2.txt",
            "source_lang": "简体中文",
            "target_langs": ["简体中文", "繁体中文", "英语"]
        }
    ]
    model_id = 'test_1'
    main = RetrievalMain()
    main.train(model_id, data)
    print("wait")

    query = "为什么要做意图管理"
    main_result = main.predict(model_id, query)
    print("wait")

    # query = 'oneplus 4K'
    output = main.predict(model_id,query,50,need_detail=True)['retrival_content']
    check = pd.DataFrame.from_dict(output, orient='index').T
    from module.LLM.llama.llama_tokenzier import llama_lener
    check_2 = check[check['es_result'].str.contains('battery charger')]
    llama_lener(check['es_bm_bert_with_kv'].dropna().tolist())

    # for i in range(10):
    output = main.predict(model_id,query,10,need_detail=False)
    # ['retrival_content']
    # check = pd.DataFrame.from_dict(output, orient='index').T

    # output = main.predict(model_id,query,10,need_detail=False)
    # check = pd.DataFrame(output)

    # result = main.predict(model_id,'公司什么时候发工资', need_detail=True)
    # pd.DataFrame(result).to_csv('check.csv',encoding='utf-8-sig')

    # import requests
    # url = 'http://127.0.0.1:5001/filechat/upload'
    #
    # data = {'model_id':'test','base64s':base64s,'filenames':filenames}
    # data = json.dumps(data,ensure_ascii=False)
    # wb_data = requests.post(url, data=data)
    # output = ultra_extract(r"D:\develop\NLP_Model\检索测试\Retrieval\files\-Unlicensed-大学生实习劳动合同.txt")
    # for i in output:print(i,end='================\n')

    print(len(main.retrieval.models[model_id]['bm25'].doc))


