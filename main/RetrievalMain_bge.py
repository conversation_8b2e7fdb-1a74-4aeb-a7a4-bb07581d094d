import copy
import time
import os

import jieba
import requests
from collections import defaultdict
from setting import logger, SAVE_MODEL_DIR
import setting
import pandas as pd
import threading
import fcntl
import json
from hashlib import md5
from collections import defaultdict

from database.REDIS import REDIS
from database.RabbitMQ import RabbitMQ
from utils.filechat_utils import ToFile, ultra_extract,doc_extract
from module.Retrieval.BEGRetrieval import BEGRetrieval
from module.Retrieval.VolcengineRetrieval import VolcengineR<PERSON><PERSON>val
from utils.my_utils import find_highlight_indices
from model.Translate.TranslateGPT import TranslateGPT
from module.FileChatBot.FilechatPrompt import FINAL_ANSWER_PROMPT
from model.CallLLM.qwen.qwen_token_lener import qwen_lener

from model.FileChatAction.ChatWithDF.MyDFChat import mydfchat
from utils.filechat_utils import excelpath_to_filldf
import shutil
from utils import my_utils
from database.MINIO import MINIO
minio = MINIO()
from model.CallLLM.CallOpenAI_ultra import CallOpenAI as CallOpenAI_ultra
llm = CallOpenAI_ultra('qwen2.5-14b-instruct', base_url='https://dashscope.aliyuncs.com/compatible-mode/v1', api_key="sk-12650c62ac694a959175757f9ec6b7c4")
# llm = CallOpenAI_ultra('qwen2.5-14b-instruct', base_url='https://dashscope.aliyuncs.com/compatible-mode/v1', api_key="sk-12650c62ac694a959175757f9ec6b7c4")

service_name = "Retrieval_bge"
model_record_file = setting.MODEL_RECORD_FILE + f".{service_name}"
switch_model_channel = setting.SERVICE_CHANNEL_DICT["RetrievalMain_bge"]["switch_model"]





from utils.callback_utils import callback_for_http
from utils.callback_utils import RECODER
filechat_train_record = RECODER('filechat_train')



class RetrievalMain_bge:
    def __init__(self,client='train'):
        self.client = client
        self.source_maps = {}
        self.train_client_load_model_list = []
        self.retrieval = BEGRetrieval()
        self.volcengine_retrieval = VolcengineRetrieval()
        self.translate_model = TranslateGPT()
        self.redis = REDIS()
        self.load_model_dict = {}
        self.lastest_switch_time = {}
        os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)

        if setting.USE_RABBIT_MQ:
            self.rabbit_listen = RabbitMQ()
        else:
            self.rabbit_listen = None
        # Redis
        if self.client != "train":
            if setting.USE_RABBIT_MQ:
                self.switch_thread = threading.Thread(target=self.rabbit_listen.listen_msg, args=(self.switch_callback,))
                self.switch_thread.start()
            else:
                logger.info(f'RetrievalMain_bge开启监听：{switch_model_channel}')
                self.redis.subscribe(switch_model_channel)
                callback_dict = {
                    switch_model_channel: self.switch_model
                }
                t1 = threading.Thread(target=self.redis.listen_msg, args=(callback_dict,))
                t1.start()

        # 加载已上线模型
        self.model_record = {}
        if self.client == 'predict' and (not setting.AUTO_ONLINE_MODEL or setting.LRU_MODEL_LOAD_NUM):
            self.init_model()

        # 测试加载的模型
        self.train_client_load_model_list = []
        self.train_client_load_model_trained_time = {}

        if setting.AUTO_ONLINE_MODEL and setting.CLIENT == 'predict' and not setting.LRU_MODEL_LOAD_NUM:
            t4 = threading.Thread(target=my_utils.auto_offline, args=(self, self.load_model_dict))
            t4.start()
        if setting.CLIENT == 'train' or setting.AUTO_ONLINE_MODEL:
            t5 = threading.Thread(target=my_utils.get_model_record_time, args=(self, self.lastest_switch_time,))
            t5.start()



    def train(self, model_id, data=None, data_key='', sendredis=False,test_mode = False,**kwargs):
        """
        文件训练
        """
        start_time = time.time()
        text_num = 0
        char_num = 0
        trans_text_num = 0
        trans_char_num = 0

        logger.debug(f'检索模型开始训练model_id:{model_id}')
        if data_key:
            data = self.redis.get_data(data_key)
        data = [i for i in data if i['filename']]
        filenames = [i['filename'] for i in data]
        base64s = [i.get('base64', '') for i in data]
        source_lang_list = [i.get('source_lang', '中文简体') for i in data]
        target_lang_list = [i.get('target_langs', ['中文简体']) for i in data]
        cut_lens = [i['cut_len'] for i in data]
        parse_result_redis_key = [i['parse_result_redis_key'] for i in data if i['parse_result_redis_key']]
        if parse_result_redis_key:
            parse_result_redis_key = parse_result_redis_key[0]
        upload_time = [i['upload_time'] for i in data][0]


        os.makedirs(os.path.join(setting.FILECHAT_FILE_DIR, model_id), exist_ok=True)
        save_dir = os.path.join(setting.FILECHAT_FILE_DIR, model_id, 'Retrieval')
        os.makedirs(save_dir, exist_ok=True)
        file_save_dir = os.path.join(save_dir, 'files')
        os.makedirs(file_save_dir, exist_ok=True)

        doc_dicts = []
        for index, filename in enumerate(filenames):
            try:
                save_filename = os.path.split(filename)[-1]
                file_path = os.path.join(file_save_dir, save_filename)
                if base64s[index]:
                    ToFile(base64s[index], file_path)
                if test_mode:
                    shutil.copy(filename, file_path)
                if not test_mode:
                    md5_code = self.download(filename, file_path)
                sim_answer_dict = self.retrieval.path_to_traindata(file_path,filename,cut_lens[index])
                doc_dicts.append(sim_answer_dict)
                if parse_result_redis_key:
                    set_data = {}
                    texts = sim_answer_dict["answer"]
                    content = '\n'.join(texts)
                    set_data['filename'] = save_filename
                    set_data['parse_result'] = f"文档名称:{save_filename}\n文档内容:{content}"
                    set_data['upload_time'] = upload_time
                    set_data['token_len'] = qwen_lener(set_data['parse_result'])
                    self.redis.set_data(key=parse_result_redis_key, msg=set_data)

            except Exception as e:
                logger.error(f'解析文件报错:{filename}',exc_info=True)
                if parse_result_redis_key:
                    result = {"code": 1, "model_id": model_id, "task": "upload", "score": 0, "error_code": "NLU91020",
                              "error_type": 0, "msg": str(e)}
                    return result

        logger.debug(f"训练耗时统计,model_id:{model_id},翻译耗时:{time.time()-start_time},文本行数:{text_num},文本字符数:{char_num},翻译后文本行数:{trans_text_num},翻译后字符数:{trans_char_num}")
        start_time = time.time()

        if not parse_result_redis_key:
            code, msg = self.retrieval.train(model_id, doc_dicts,seg_lens=1000,model_save_dir=save_dir,**kwargs)
            logger.debug(f"训练耗时统计,model_id:{model_id},检索训练总耗时:{time.time()-start_time}")
            if model_id in self.train_client_load_model_list:
                self._load_model(model_id)

            if code != 0:
                result = {"code": 1, "model_id": model_id, "task": "train", "score": 0, "error_code": "NLU91020", "error_type": 0, "msg": msg}
            else:
                result = {"code": code, "model_id": model_id, "task": "train", "score": 0, "msg": msg}
            if sendredis:
                self.redis.set_data(key=f"retrieval_train_{model_id}", msg=result)
            result.update(kwargs)
            return result
        else:
            return {"code": 0, "model_id": model_id, "task": "train", "score": 0, "msg": '文件解析成功','key':parse_result_redis_key}

    def predict(self, model_id, query, topk=10,rerank=None,is_text_len=True,filename_base=False,rewrite=False,debug=False,one_file_only=False,history=[],**kwargs):
        """
        模型预测
        """
        # query = query.lower()
        result = {
            "code": 0,
            "model_id": model_id,
            "text": query,
            "msg": "预测成功",
        }
        provider = kwargs.get('provider', 'zhisheng')
        text_len = kwargs.get('text_len', None)
        start = time.time()
        if self.client == "train" and model_id not in self.train_client_load_model_list:
            if one_file_only:
                load_result = self.load_one_file_model(model_id)
            else:
                load_result = self._load_model(model_id=model_id)
            if load_result == 0:
                self.train_client_load_model_list.append(model_id)
            if len(self.train_client_load_model_list) > 30:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]
            self.lastest_switch_time[model_id] = int(time.time())

        if setting.AUTO_ONLINE_MODEL and self.client == "predict":
            my_utils.auto_online(self, self.load_model_dict, model_id, self.lastest_switch_time)

        try:
            if provider == 'zhisheng':
                if filename_base:
                    retrieval_result = self.retrieval.filename_base_predict(model_id, query, top_k=topk, rerank=rerank, is_text_len=is_text_len)
                elif rewrite and history!=None and len([i for i in history if i['role'] == 'user'])>0:
                    history = [i for i in history if i['role'] == 'user'][-1:]
                    history = [i['content'] for i in history]
                    history = [i.split('用户问题:')[-1] for i in history]
                    history = history + [query]
                    #重写query
                    prompt = f"@history上面是用户说的两个query,如果不拼接可能意图表达不明确,特别是第一个query是实体第二个query没有实体的情况基本都要拼接,是否要拼接请直接输出yes/no，不需要解释:"
#                     prompt = """case1:['iphone的内存有多大','屏幕呢'],rewrite:iphone的屏幕有多大
# case2:['电脑的内存有多大','电脑的屏幕有多大'],rewrite:no
# case3:['飞机怎么起飞','汽车怎么开'],rewrite:no
# case4:@history,rewrite:
# 请直接输出rewrite结果:"""
                    llm_result = llm.run(prompt.replace('@history',str(history)))
                    logger.debug(f"重写history:{history}，重写llm结果:{llm_result}")
                    if 'yes' in llm_result:
                        query = ''.join(history[-2:])
                        logger.debug(f"重写query结果:{query}")
                    else:
                        query = history[-1]
                    retrieval_result = self.retrieval.predict(model_id, query, top_k=topk,rerank=rerank,is_text_len=is_text_len,debug=debug)
                    retrieval_result['rewrite_info'] = {'history': history, 'rewrite_query': query,'llm_output':llm_result}
                elif query.startswith('http'):  # 图义搜索
                    # temp_path = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'Retrieval', query.split('/')[-1])
                    # self.download(query, temp_path)
                    retrieval_result = self.retrieval.predict(model_id, query, top_k=1,debug=debug)
                    query = '本轮对话请直接介绍已有的信息（不需要说根据xx资料），禁止泄漏元数据。'
                else:  # 默认使用
                    retrieval_result = self.retrieval.predict(model_id, query, top_k=topk, rerank=rerank,
                                                              is_text_len=is_text_len, debug=debug)

            elif provider == 'volcengine':
                retrieval_result = self.volcengine_retrieval.predict(model_id, query, top_k=topk, is_text_len=is_text_len,embedding_model=kwargs['embedding_model'])
            else:
                retrieval_result = self.retrieval.predict(model_id, query, top_k=topk, rerank=rerank,
                                                          is_text_len=is_text_len, debug=debug, text_len=text_len)

            filename2seg_infos = self.retrieval.predict_models[model_id].get('filename2seg_infos',{})
            result['retrival_content'] = retrieval_result
            #整理内容给llm
            logger.debug(f"Retrieval 检索耗时,model_id{model_id},耗时:{time.time() - start}")
            retrieval_result_convert_to_llm = self.retrieval_result_convert_to_llm(model_id,query, retrieval_result,filename2seg_infos)
            result.update({i:j for i,j in retrieval_result_convert_to_llm.items() if i in ['fit_for_llm','fileid2showindex','sql_info']})
            token_query = jieba.lcut(query)
            highlight_indices = find_highlight_indices(result['fit_for_llm'],token_query)
            result['highlight_index'] = highlight_indices
            result['topk'] = topk
            logger.debug(f"Retrieval 预测完成,model_id{model_id},耗时:{time.time() - start}")
        except Exception as e:
            logger.error(f"预测失败 - model_id: {model_id}, text: {query}, 错误: {e}",exc_info=True)
            raise e
        return result

    def retrieval_result_convert_to_llm(self, model_id, query, retrieval_result,filename2seg_infos=None):
        result = {}
        match_text = retrieval_result['match_text']
        filename = retrieval_result['filename']
        url = retrieval_result.get("url", "")
        score = retrieval_result['score']
        print([len(match_text),len(filename),len(url),len(score)])
        df = pd.DataFrame({'filename': filename, 'url': url, 'match_text': match_text,'score': score})
        #按文件名称再组装
        tmp_df = []
        for filename in df['filename'].unique():
            sec_df = df[df['filename'] == filename]
            item = {}
            sec_match_text = sec_df['match_text'].tolist()
            #合并滑窗文本
            if filename2seg_infos is not None and filename2seg_infos and setting.FILECHAT_CUT_MERGE:
                text2seg_id = filename2seg_infos[filename]['text2seg_id']
                seg = filename2seg_infos[filename]['seg']
                columns = filename2seg_infos[filename]['columns']
                match_text_seg_ids = [text2seg_id[i] for i in match_text if i in text2seg_id]
                if len(match_text_seg_ids) >= 2:
                    match_text_seg_ids_df = pd.DataFrame(match_text_seg_ids).sort_values([0,1])
                    match_text_seg_ids_df['gap'] = match_text_seg_ids_df[1].shift(1) - match_text_seg_ids_df[0]
                    match_text_seg_ids_df['gap'] = match_text_seg_ids_df['gap'].fillna(0)
                    group = []
                    new_sec_match_text = []
                    for index,i in enumerate(match_text_seg_ids_df['gap']):
                        if i >= 0:
                            group.append([match_text_seg_ids_df[0].iloc[index],match_text_seg_ids_df[1].iloc[index]])
                        else:
                            if group:
                                print(group,'groupgroupgroup')
                                new_sec_match_text.append(f'{columns}\n' + ''.join(seg[group[0][0]:group[-1][1]]))
                            group = [[match_text_seg_ids_df[0].iloc[index],match_text_seg_ids_df[1].iloc[index]]]
                    if group:
                        new_sec_match_text.append(f'{columns}\n' + ''.join(seg[group[0][0]:group[-1][1]]))
                    logger.info(f'合并前{len(sec_match_text)}:{sec_match_text}')
                    logger.info(f'合并后{len(new_sec_match_text)}:{new_sec_match_text}')
                    sec_match_text = new_sec_match_text
            #对一个文件的所有切片,只有第一个切片有文件名称
            sec_match_text_replace_head = []
            for index, i in enumerate(sec_match_text):
                if i.startswith('文件元信息'):
                    if index == 0:
                        sec_match_text_replace_head.append(i)
                    else:
                        sec_match_text_replace_head.append('\n'.join(i.split('\n')[2:]))
                else:
                    sec_match_text_replace_head.append(i)

            #合并滑窗文本
            item['match_text'] = '\n-------------------------------------------\n'.join(sec_match_text)
            item['filename'] = '_'.join(filename.split('_')[:-1]) + '.' + filename.split('.')[-1] if '_' in filename else filename
            item['raw_filename'] = filename
            item['url'] = sec_df['url'].values[0]
            if item['url'] == '':
                item['url'] = f"https://qnzs-ai.oss-cn-zhangjiakou.aliyuncs.com/sammy/doc2000/{filename.replace('.txt', '.docx')}"
            if 'http' not in item['url']:
                item['url'] = 'http://' + item['url']
            item['score'] = sec_df['score'].values[0]
            tmp_df.append(item)
        df = pd.DataFrame(tmp_df)

        # 针对表格进行fit_for_llm表格改写
        if setting.FILECHAT_TABLE_SQL:
            try:
                start_time = time.time()
                select_df = df.iloc[0]
                df_filename  = select_df['raw_filename']
                info = self.retrieval.predict_models[model_id].get('info',{})
                if info :
                    df_fileid = info.get(df_filename,'')
                    df_filename = os.path.basename(df_filename)

                    df_path = os.path.join(setting.FILECHAT_FILE_DIR,df_fileid,'Retrieval','files',df_filename)
                    # df_path = os.path.join(setting.FILECHAT_FILE_DIR,df_fileid,'Retrieval','files',select_df['filename'])

                    if 'csv' in df_filename or 'xlsx' in df_filename:
                        if 'csv' in df_filename:
                            chat_df = pd.read_csv(df_path)
                        else:
                            # chat_df = pd.read_excel(df_path)
                            chat_df = excelpath_to_filldf(df_path)
                        df_decribe = df_filename
                        sql_result,sql,final_input = mydfchat.chat(df_decribe,chat_df,query)
                        result['sql_info'] = {'sql':sql,'sql_result':sql_result,'file':df_path}
                        if sql_result:
                            # item = {}
                            # item['match_text'] = f"【{select_df['filename']}】这个表格执行：{sql}后得到结果：{sql_result}"
                            # item['filename'] = select_df['filename']
                            # item['url'] = select_df['url']
                            # item['score'] = 1
                            # df = pd.DataFrame([item])
                            df.loc[0, 'match_text'] = df.loc[0, 'match_text'] + f"\n【{select_df['filename']}】这个表格执行sql：{sql}\n得到结果：{sql_result[:1000]}"

                            # if len(sql_result)<1000:
                            #     pass
                            # else:
                            #     df = df[:-1] # 如果字数很多就少返回一个结果
                logger.info(f'表格查询sql耗时:{time.time()-start_time}')
            except:
                logger.error('表格sql解析失败',exc_info=True)


        df['index'] = range(1,len(df) + 1)
        df['index'] = df['index'].astype(str)
        df['index1'] = '[[citation:' + df['index'] + ']]'
        df['index2'] = '[ citation:' + df['index'] + ' ]'
        df['index3'] = '[citation:' + df['index'] + ']'
        df['index4'] = 'citation:' + df['index']

        # try:
        if not url and not df.get("url", ""):
            df['showindex'] = []
            result['url'] = []

        else:
            result['url'] = df.get("url", "").tolist()
            df['showindex'] = [f'<a href="{url}" target="_blank">【{url.split("/")[-1] if filename == ".docx" else filename}】</a>' for url,filename in zip(df['url'], df['filename'])]
    # df['showindex'] = [f'<a href="{url}" target="_blank">[{index}]</a>' for url,index in zip(df['url'], df['index'])]
    # except:
            # logger.warning(f"url error: {url}", exc_info=True)
        # fit_for_llm = df['index1'] + '：\n' + df['match_text']
        fit_for_llm = df['index1'] + '：\n' + df.get("match_text", "")
        fit_for_llm = fit_for_llm.tolist()
        # if len(fit_for_llm)==1:
        #     fit_for_llm += ['[[citation:2]]：empty','[[citation:3]]：empty']
        # elif len(fit_for_llm)==2:
        #     fit_for_llm += ['[[citation:3]]：empty']

        fit_for_llm = '\n\n-----------------\n\n'.join(fit_for_llm)
        fit_for_llm = FINAL_ANSWER_PROMPT.replace('@doc@', fit_for_llm)
        fit_for_llm = fit_for_llm.replace('@query@', query)
        # fit_for_llm = fit_for_llm.replace("\n\n用户问题:@query@",'').replace('@query@', query)

        result['fit_for_llm'] = fit_for_llm

        fileid2showindex = defaultdict(list)
        def update_dict(d, keys, values):
            for key,value in zip(keys, values):
                d[key].append(value)
            return d

        fileid2showindex = update_dict(fileid2showindex, df['showindex'],df['index1'])
        fileid2showindex = update_dict(fileid2showindex, df['showindex'],df['index2'])
        fileid2showindex = update_dict(fileid2showindex, df['showindex'],df['index3'])
        fileid2showindex = update_dict(fileid2showindex, df['showindex'],df['index4'])
        fileid2showindex[''] = ['[citation:无]','[citation:NA]','[citation:*]','[citation:0]','[citation:None]',
                                'citation:[]','[citation:miss]','[citation:missing]']
        fileid2showindex['图片'] = ['citation_image_id']

        #替换其他citation
        # for i in range(len(df) + 1 ,10):
        #     result['fileid2showindex'].update({f'[citation:{i}]':'','[[citation:' + str(i) + ']]':''})
        fileid2showindex_mapper = []
        for i, j in fileid2showindex.items():
            #j长度最长的排在最前面
            j = sorted(j, key=lambda x: len(x), reverse=True)
            fileid2showindex_mapper.append({'origin':j,"translate":i})

        result['fileid2showindex'] = fileid2showindex_mapper

        #补充图片url
        if 'image_map_url' in self.retrieval.predict_models.get(model_id,{}):
            for i in self.retrieval.predict_models[model_id]['image_map_url']:
                if i in fit_for_llm:
                    url = self.retrieval.predict_models[model_id]['image_map_url'][i]
                    # result['fileid2showindex'].append({'origin':i,"translate":f"<a href=\"{url}\" target=\"_blank\">【图】</a>"})
                    url = ['minio']  + url.split('/')[-2:]
                    url =  "/" + '/'.join(url)
                    result['fileid2showindex'].append({'origin': [f'[{i}]',i], "translate": f"<img src=\"{url}\" alt=\"\" title=\"【图】\" />"})
        return result


    def _load_model(self,model_id):
        try:
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id, "Retrieval")
            self.retrieval.load_model(model_id,model_save_dir)
            logger.debug(f'检索模型上线成功,model_id:{model_id}')
            self.load_model_dict[model_id] = time.time()
            #先进先出
            for model_id in list(self.load_model_dict)[:-setting.LRU_MODEL_LOAD_NUM]:
                try:
                    self._offline_model(model_id)
                except:
                    logger.error(f'lru模型下线失败,model_id:{model_id}',exc_info=True)
            return 0
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            return 1

    def dump_record(self):
        if setting.SEND_MODEL_MODE == "Nas":
            return
        """记录已上线模型"""
        with open(model_record_file, 'w', encoding='utf-8') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.model_record, f, ensure_ascii=False)
        logger.debug('保存已上线模型记录')

    def load_one_file_model(self,model_id):
        try:
            model_save_dir = os.path.join(setting.FILECHAT_FILE_DIR ,model_id, "Retrieval")
            self.retrieval.load_model(model_id,model_save_dir)
            logger.debug(f'检索one_file模型上线成功,one_file_id:{model_id}')
            return 0
        except Exception as e:
            logger.error(f"加载模型失败 - one_file_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            return 1

    def _offline_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "offline_model",
            "msg": "下线模型成功"
        }

        logger.debug(f"开始下线模型 - model_id: {model_id}, 进程: {os.getpid()}")
        try:
            self.retrieval.offline_model(model_id=model_id,info=True)
            logger.debug(f"下线模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
        except Exception as e:
            logger.error(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 1
            result["msg"] = str(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")

    def switch_model(self, model_id, flag):
        #
        save_log = os.path.join(SAVE_MODEL_DIR, f"{model_id}", "train_record", 'train_log.json')
        if os.path.exists(save_log):
            with open(save_log, encoding='utf-8', mode='r') as f:
                train_record = json.load(f)
                logger.debug(f'模型上下线,读取train_record成功,train_record:{train_record}')
                rag_main = [i for i in train_record if i['model_class']=='RetrievalMain_bge']
                if not rag_main:
                    logger.debug('没有训练RetrievalMain_bge,无需加载')
                    return 0
                if rag_main and rag_main[0]['code']==1:
                    logger.debug('RetrievalMain_bge,训练失败')
                    return 1


        code = 0
        if 'LawLLMRetrieval0528_docnum_996705' in model_id:
            return
        if flag == 1:
            #如果是上线,但是rerieval文件是空的,则下线
            if not os.path.exists(os.path.join(setting.SAVE_MODEL_DIR, model_id, "Retrieval")):
                self._offline_model(model_id)
                self.model_record.pop(model_id, 0)
                logger.debug(f'rag模型为空,下线模型,model_id:{model_id}')
                return 0
            code = self._load_model(model_id)
            if code == 0:
                self.model_record[model_id] = time.time()
            else:
                self.model_record.pop(model_id, 0)
                code = 1
        else:
            self._offline_model(model_id)
            self.model_record.pop(model_id, 0)
        self.dump_record()
        return code

    def dump_record(self):
        with open(model_record_file, 'w', encoding='utf-8') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.model_record, f, ensure_ascii=False)
        logger.debug('保存已上线模型记录')

    def switch_callback(self, ch, method, properties, body):
        if method.routing_key == switch_model_channel:
            logger.debug(f'rabbit_mq接收到监听消息:{switch_model_channel},msg:{body}')
            logger.debug(f"ch:{ch}")
            logger.debug(f"method:{method}")
            logger.debug(f"properties:{properties}")
            logger.debug(f"body:{body}")
            body_dict = json.loads(body)
            self.switch_model(**body_dict)

    def init_model(self):
        # 加载已上线模型
        model_ids = []
        if os.path.exists(model_record_file):
            with open(model_record_file, 'r', encoding='utf-8') as f:
                try:
                    self.model_record = json.load(f)
                    self.model_record = dict(sorted(self.model_record.items(), key=lambda x: x[1])) #从小到大
                    model_ids = list(self.model_record.keys())[-setting.LRU_MODEL_LOAD_NUM:] #只加载最近发布的模型
                    logger.info(f'{self.__class__.__name__}待加载列表:{model_ids}')
                except Exception as e:
                    logger.warning(f"加载历史上线模型记录文件失败: {e}")
        try:
            for model_id in model_ids:
                if self._load_model(model_id=model_id):  # 加载失败的要在 model_record 里面移除
                    self.model_record.pop(model_id, 0)
                logger.debug('[{}] 已加载历史上线模型'.format(model_id))
            self.dump_record()
        except Exception as e:
            logger.warning(f"加载历史上线模型失败: {e}")

    def train_time(self,data):
        return 8

    def download(self,url,download_path):
        try:
            # if os.path.exists(download_path):  #不进行检查，直接下载
            #     with open(download_path, 'rb') as fp:
            #         data = fp.read()
            #         md5_code = md5(f'{data}'.encode()).hexdigest()
            #         return md5_code
            logger.info(f'正在请求下载链接{url}')
            data = requests.get(url)
            with open(download_path, 'wb') as f:
                f.write(data.content)
                f.close()
            md5_code = md5(f'{str(data.content)}'.encode()).hexdigest()
            logger.info(f'下载完成:{url}')
            return md5_code
        except Exception as e:
            logger.error(f'文件下载失败:{url}',exc_info=True)
            return str(time.time())

    def train_from_fileids(self,model_id, fileids,rag_config):
        results = []
        # rag_config = [{"provider":"zhisheng", "cut_len":1000},{"provider":"volcengine", "embedding_model":"doubao-embeddingAAA", "cut_len":1000,}]
        for i in rag_config:
            result = copy.deepcopy(i)
            result['msg'] = '绑定成功'
            provider = i.get('provider','zhisheng')
            #如果绑定为空则删除模型
            if not fileids:
                self.model_delete(model_id, provider)
                if model_id in self.train_client_load_model_list:
                    self._offline_model(model_id)
                logger.debug(f'绑定为空，模型删除，model_id:{model_id}')
                return result
            if provider == 'zhisheng':
                code = self.retrieval.train_from_fileids(model_id, fileids)
                if code ==0:
                    result['fail_list'] = []
                    result['success_list'] = fileids
                else:
                    result['fail_list'] = fileids
                    result['success_list'] = []
                results.append(result)
                if model_id in self.train_client_load_model_list:
                    self._load_model(model_id=model_id)
            elif provider == 'volcengine':
                success_list = self.volcengine_retrieval.train_from_fileids(model_id, fileids,**i)
                result.update(success_list)
                results.append(result)
        return results

    def file_delete(self,fileids:list):
        self.retrieval.file_delete(fileids)

    def offline_train_from_fileids(self, model_id, fileids,rag_config,callback_url,callback_data):
        """
        中文到英文使用模型
        "source_lang":"chs",          #来源语种
        "target_lang":["en"],   #目标语种list
        "text":["你好","我很好"]}      #待译文list
        """
        try:
            filechat_train_record.dump_train_record(callback_data, is_add=True)
            logger.debug(f"filechat正在绑定:{callback_data['sn']}")
            results = self.train_from_fileids(model_id, fileids,rag_config)
            code = 0
            if [i['fail_list'] for i in results if i['fail_list']]:
                code = 1
            if not [i['success_list'] for i in results if i['success_list']]:
                code = -1
            logger.info(f"filechat绑定完成:{callback_data['sn']}")

            if callback_url:
                filechat_train_record.dump_train_record(callback_data,is_add=False)
                callback_data['result'] = results
                callback_data['code'] = code
                callback_for_http(callback_url, callback_data)
            logger.debug(f"filechat绑定完成:{callback_data['sn']},结果为{results}")
        except:
            logger.error(f'filechat绑定报错:{callback_data}',exc_info=True)

    def model_delete(self,model_id,provider,embedding_models=[]):
        if provider == 'zhisheng':
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'Retrieval')
            shutil.rmtree(model_save_dir,ignore_errors=True)
            self._offline_model(model_id)
            # self.model_record.pop(model_id, 0)
            # self.dump_record()
        else:
            self.volcengine_retrieval.drop_collection(model_id,embedding_models)

    def rewrite_history(self,query,history):
        history = [i for i in history if i['role'] == 'user'][-1:]
        history = [i['content'] for i in history]
        history = [i.split('用户问题:')[-1] for i in history]
        history = history + [query]
        result,info = self.retrieval.rewrite_history(history)
        return info



if __name__ == '__main__':
    start = time.time()
    main = RetrievalMain_bge()
    model_id = '1891772354672562178_500'
    # model_id = '1891396006230999040'
    model_id = '1891396006230999040_chunk_无语料'
    data = [{'filename': 'https://aicc-test.qnzsai.com//minio/customer/audioFile/2025/3/18/刘氏庄园——泥塑卷(文.图)_1742301968697.docx', 'source_lang': '简体中文', 'target_langs': ['简体中文'], 'cut_len': 1000, 'parse_result_redis_key': '', 'upload_time': 1742301973, 'window_size': 500}]
    model_id = 'aaaa'
    # main.train('aaaa',data=data)
    main.load_model('aaaa')
    # result = main.predict(model_id, "第一幕，送租",topk=3)

    # print(result['fit_for_llm'])
    # check = pd.Series(result['retrival_content']['match_text'])

    # print(result['fit_for_llm'])
    # check = pd.Series(result['retrival_content']['match_text'])



