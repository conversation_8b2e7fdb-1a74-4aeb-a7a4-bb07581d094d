# -*- coding: utf-8 -*-
import json
import os
import threading
import time
import traceback
from typing import List
from utils import my_utils

import fcntl
import shutil
import setting
from setting import logger

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)
# 限制CPU 占用，避免挤占其他服务
tf.config.threading.set_inter_op_parallelism_threads(setting.NUM_THREADS)
tf.config.threading.set_intra_op_parallelism_threads(setting.NUM_THREADS)

from utils.my_utils import RankReturnObject, remove_front_end_symbol,remove_symbol
from database.REDIS import REDIS
from module.PreRank.ArcCSEPreRank.SimCSEPreRankDataType import SimCSEPreRankDataType
from database.RabbitMQ import RabbitMQ
from module.Regular.RegularDataType import RegularDataType
from module.SimWordReplace.SimWordReplace import SimWordReplace
from module.PreRank.LLMPrerank.LLMPrerank import llmprerank

service_name = "Search"
model_record_file = setting.MODEL_RECORD_FILE + f".{service_name}"
switch_model_channel = setting.SERVICE_CHANNEL_DICT["SearchMain"]["switch_model"]

class SearchMain:
    def __init__(self):
        os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
        self.redis = REDIS()
        self.load_model_dict = {}
        self.lastest_switch_time = {}
        if setting.USE_RABBIT_MQ:
            self.rabbit_listen = RabbitMQ()
        else:
            self.rabbit_listen = None
        self.threshold_file = os.path.join(setting.MAIN_DIR, "conf/threshold.json")

        # 粗排
        self.regular_model = RegularDataType()
        self.pre_rank_model = SimCSEPreRankDataType()
        self.sim_word_replace_model = SimWordReplace()

        # Redis
        if setting.CLIENT != "train":
            if setting.USE_RABBIT_MQ:
                self.switch_thread = threading.Thread(target=self.rabbit_listen.listen_msg, args=(self.switch_callback,))
                self.switch_thread.start()
            else:
                self.redis.subscribe(switch_model_channel)
                callback_dict = {
                    switch_model_channel: self.switch_model
                }
                t1 = threading.Thread(target=self.redis.listen_msg, args=(callback_dict,))
                t1.start()

        # 加载已上线模型
        self.model_record = {}
        if setting.CLIENT == 'predict' and (not setting.AUTO_ONLINE_MODEL or setting.LRU_MODEL_LOAD_NUM):
            self.init_model()

        # 测试加载的模型
        self.train_client_load_model_list = []
        self.train_client_load_model_trained_time = {}

        if setting.AUTO_ONLINE_MODEL and setting.CLIENT == 'predict' and not setting.LRU_MODEL_LOAD_NUM:
            t4 = threading.Thread(target=my_utils.auto_offline, args=(self, self.load_model_dict))
            t4.start()
        if setting.CLIENT == 'train' or setting.AUTO_ONLINE_MODEL:
            t5 = threading.Thread(target=my_utils.get_model_record_time, args=(self, self.lastest_switch_time,))
            t5.start()

    def switch_callback(self, ch, method, properties, body):
        if method.routing_key == switch_model_channel:
            logger.debug(f'rabbit_mq接收到监听消息:{switch_model_channel},msg:{body}')
            logger.debug(f"ch:{ch}")
            logger.debug(f"method:{method}")
            logger.debug(f"properties:{properties}")
            logger.debug(f"body:{body}")
            body_dict = json.loads(body)
            self.switch_model(**body_dict)

    def init_model(self):
        # 加载已上线模型
        model_ids = []
        if os.path.exists(model_record_file):
            with open(model_record_file, 'r', encoding='utf-8') as f:
                try:
                    self.model_record = json.load(f)
                    self.model_record = dict(sorted(self.model_record.items(), key=lambda x: x[1])) #从小到大
                    model_ids = list(self.model_record.keys())[-setting.LRU_MODEL_LOAD_NUM:] #只加载最近发布的模型
                    logger.info(f'{self.__class__.__name__}待加载列表:{model_ids}')
                except Exception as e:
                    logger.warning(f"加载历史上线模型记录文件失败: {e}")
        try:
            for model_id in model_ids:
                if self._load_model(model_id=model_id):  # 加载失败的要在 model_record 里面移除
                    self.model_record.pop(model_id, 0)
                logger.debug('[{}] 已加载历史上线模型'.format(model_id))
            self.dump_record()
        except Exception as e:
            logger.warning(f"加载历史上线模型失败: {e}")

    def dump_record(self):
        if setting.SEND_MODEL_MODE == "Nas":
            return
        with open(model_record_file, 'w', encoding='utf-8') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.model_record, f, ensure_ascii=False)
        logger.debug('保存已上线模型记录')

    def train(self, model_id, data_key='', sendredis=False,**kwargs):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "train",
            "msg": "训练成功",
            "score": 0,
        }
        try:
            data = self.redis.get_data(key=data_key)

            # 开始训练正则模型
            start = time.time()
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.regular_model.__class__.__name__}/")
            code, msg = self.regular_model.train(model_id=model_id, data=data, save_dir=model_save_dir)
            if code != 0:
                result["code"] = code
                result["msg"] = msg
            logger.debug(f"全量训练{model_id},{self.regular_model.__class__.__name__}训练成功,耗时:{time.time()-start}s")

            # 开始训练粗排模型
            start = time.time()
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.pre_rank_model.__class__.__name__}/")
            score, mixed = self.pre_rank_model.train(model_id=model_id, data=data, save_dir=model_save_dir)
            result["score"] = float(score)
            result["suggest"] = mixed
            logger.debug(f"全量训练{model_id},{self.pre_rank_model.__class__.__name__}训练成功,耗时:{time.time()-start}s")
        except Exception as e:
            traceback.print_exc()
            logger.error(f"全量训练{model_id},训练失败,错误:{e}",exc_info=True)
            result["code"] = 1
            result["msg"] = f'全量训练报错'
            result["error_code"] = "NLU91020"
            result["error_type"] = 0

        if sendredis:
            self.redis.set_data(key=f"faq_train_{model_id}", msg=result)
        return result

    def increment_train(self, model_id, data_key='', sendredis=False,**kwargs):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "train",
            "msg": "训练成功",
            "score": 0,
        }
        try:
            data = self.redis.get_data(key=data_key)
            #如果没有数据则下线训练端，并且删除模型
            if not data:
                if model_id in self.train_client_load_model_list:
                    self._offline_model(model_id)
                model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'SimCSEPreRankDataType')
                shutil.rmtree(model_save_dir,ignore_errors=True)
                logger.debug(f'增量训练为空，模型删除，model_id:{model_id}')
                return result

            # 开始训练正则模型,正则模型直接全量训练
            start = time.time()
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.regular_model.__class__.__name__}/")
            code, msg = self.regular_model.train(model_id=model_id, data=data, save_dir=model_save_dir)
            if code != 0:
                result["code"] = code
                result["msg"] = msg
            logger.debug(f"增量训练{model_id},{self.regular_model.__class__.__name__}训练成功,耗时:{time.time()-start}s")

            # SimCSE支持增量训练
            start = time.time()
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.pre_rank_model.__class__.__name__}/")
            score, mixed = self.pre_rank_model.increment_train(model_id=model_id, data=data, save_dir=model_save_dir)
            result["score"] = float(score)
            result["suggest"] = mixed
            logger.debug(f"增量训练{model_id},{self.pre_rank_model.__class__.__name__}训练成功,耗时:{time.time()-start}s")
        except Exception as e:
            logger.error(f"增量训练{model_id},训练失败,错误:{e}",exc_info=True)
            result["code"] = 1
            result["msg"] = f'增量训练报错'
            result["error_code"] = "NLU91020"
            result["error_type"] = 0

        if sendredis:
            self.redis.set_data(key=f"faq_train_{model_id}", msg=result)
        return result

    def switch_model(self, model_id, flag):
        code = 0
        if flag == 1:
            if not os.path.exists(os.path.join(setting.SAVE_MODEL_DIR, model_id, "SimCSEPreRankDataType")):
                self._offline_model(model_id)
                self.model_record.pop(model_id, 0)
                if model_id in self.load_model_dict:
                    self.load_model_dict.pop(model_id)
                logger.debug(f'rag模型为空,下线模型,model_id:{model_id}')
                return 0
            code = self._load_model(model_id)
            if code == 0:
                self.model_record[model_id] = time.time()
                self.load_model_dict[model_id] = time.time()
            else:
                self.model_record.pop(model_id, 0)
                code = 1
        else:
            self._offline_model(model_id)
            self.model_record.pop(model_id, 0)
            if model_id in self.load_model_dict:
                self.load_model_dict.pop(model_id)
        self.dump_record()
        return code
        
    def _load_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "load_model",
            "msg": "加载模型成功"
        }

        logger.debug(f"开始加载模型 - model_id: {model_id}, 进程: {os.getpid()}")
        # 加载模型
        try:
            try:
                save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.regular_model.__class__.__name__}/")
                self.regular_model.load_model(model_id=model_id, save_dir=save_dir)
            except Exception as e:
                logger.warning(f"旧模型没有正则:{e}")
            save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.pre_rank_model.__class__.__name__}/")
            self.pre_rank_model.load_model(model_id=model_id, save_dir=save_dir)

            logger.debug(f"加载模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
            self.load_model_dict[model_id] = time.time()
            #先进先出
            for model_id in list(self.load_model_dict)[:-setting.LRU_MODEL_LOAD_NUM]:
                try:
                    self._offline_model(model_id)
                except:
                    logger.error(f'lru模型下线失败,model_id:{model_id}',exc_info=True)
            return 0
        except Exception as e:
            logger.error(f"SearchMainERROR:{traceback.format_exc()}")
            logger.error(f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}",exc_info=True)
            result["code"] = 400
            result["msg"] = f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}"
            return 400

    def _offline_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "offline_model",
            "msg": "下线模型成功"
        }

        logger.debug(f"开始下线模型 - model_id: {model_id}, 进程: {os.getpid()}")
        try:
            self.regular_model.offline_model(model_id=model_id)
            self.pre_rank_model.offline_model(model_id=model_id)
            logger.debug(f"下线模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
            if model_id in self.load_model_dict:
                self.load_model_dict.pop(model_id)
        except Exception as e:
            logger.error(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 400
            result["msg"] = str(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
        return result

    def predict(self, model_id, text, return_num=5, context_input_process='', enterprise_id="", remove_stop_words_flag=True,
                need_trans_to_simple=True,robot_model_type="",history=[]):
        """
        context_input_process： 是否仅语义推荐，推荐不走正则
        """
        result = {
            "code": 0,
            "model_id": model_id,
            "text": text,
            "msg": "预测成功",
            "intent_result": {},
            "faq_result": {},
            "chat_result": {},
            "data": {}
        }
        if robot_model_type=='混合模型机器人':
            return_num = 20 #如果用大模型,确保每个数据类型返回20个
        model_id_trained_time = self.get_model_id_trained_time(model_id)
        # logger.debug(f"model_id_trained_time:{model_id_trained_time},model_id:{model_id}")
        # if setting.CLIENT == "train":
        #     if model_id in self.train_client_load_model_list and model_id in self.train_client_load_model_trained_time and \
        #             model_id_trained_time != self.train_client_load_model_trained_time[model_id]:
        #         self.train_client_load_model_list.remove(model_id)  # 当前训练端加载的模型不是最新训练的
        #         logger.debug(f"当前训练端加载的模型不是最新训练的,已经弹出,old_model_id_trained_time:{self.train_client_load_model_trained_time[model_id]},model_id_trained_time:{model_id_trained_time},model_id:{model_id}")

        if setting.CLIENT == "train" and model_id not in self.train_client_load_model_list:
            load_result = self._load_model(model_id=model_id)
            if load_result == 0:
                self.train_client_load_model_list.append(model_id)
            if len(self.train_client_load_model_list) > setting.MAX_MODELS_TRAIN_CLIENT:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]
            self.train_client_load_model_trained_time[model_id] = model_id_trained_time
            logger.debug(f"自动加载模型,model_id_trained_time:{model_id_trained_time},model_id:{model_id}")
            self.lastest_switch_time[model_id] = int(time.time())


        if setting.CLIENT == "predict" and setting.AUTO_ONLINE_MODEL:
            # 需要调用训练端的模型上线，copy一份加switch后缀的模型文件
            my_utils.auto_online(self, self.load_model_dict, model_id,self.lastest_switch_time)

        try:
            origin_text = text
            text = remove_front_end_symbol(text)
            if setting.SEARCHMAIN_REPLACE_PUNCTUATION:
                text = remove_symbol(text,' ')


            # 正则(_end_)匹配_end_时去掉符号匹配不上了，改成用原字符串匹配
            replaced_text, error_msg = self.sim_word_replace_model.predict(query=origin_text, enterprise_id=enterprise_id)
            if error_msg:
                logger.warning(f"SimCSE预测相似词替换出错, 替换前:{text}, enterprise_id:{enterprise_id}, {error_msg}")
            logger.debug(f"SimCSE预测相似词替换, 替换前:{text}, 替换后:{replaced_text}")

            if text not in replaced_text:
                replaced_text.append(text)
            is_short_text = True if len([t for t in replaced_text if len(t) > 8]) == 0 else False

            if context_input_process:
                pre_rank_model_return = self.pre_rank_model.predict(
                    model_id=model_id, query=replaced_text[0], query_list=replaced_text, topk=return_num,
                    context_input_process=context_input_process, remove_stop_words_flag=remove_stop_words_flag, need_trans_to_simple=need_trans_to_simple
                )
                text_item_list = pre_rank_model_return[0]
                # 增加一个高亮index_list的字段
                highlight_index = []
                for sentence in text_item_list[1]:
                    cur_index = []
                    for index, i in enumerate(sentence):
                        for j in text:
                            if j == i:
                                cur_index.append(index)
                    highlight_index.append(list(set(cur_index)))
                result["msg"] = "返回联想输入结果"
                result["data"] = {'labelId': text_item_list[0], 'context_input_process': text_item_list[1], 'highlight_index': highlight_index}
            else:
                # 正则预测
                regular_result, error_dict = self.regular_model.search_predict(
                    model_id=model_id, replaced_query_list=replaced_text, labelIds=None, full_match=True, detail=1
                )
                if error_dict["error_code"] != 0:
                    result["code"] = 1
                    result["msg"] = error_dict["error_msg"]
                    result["error_code"] = error_dict["error_code"]
                    result["error_type"] = error_dict["error_type"]
                    return result
                for regular_result_key in regular_result.keys():
                    result_key = f"{regular_result_key}_result"
                    if result_key not in result:
                        result[result_key] = {}
                    if len(regular_result[regular_result_key]):
                        regular_result_item = []
                        for r_result in regular_result[regular_result_key]:
                            item = RankReturnObject(query=text, sim_query="",
                                                    norm_query=r_result["match_title"],
                                                    norm_query_id=r_result["match_labelId"], score=1, save_bigger=True,
                                                    match_re=r_result.get("match_re", [""])[0])
                            regular_result_item.append(item)
                        if len(regular_result_item):
                            search_result = self._get_search_result(regular_result_item, short_text=is_short_text)
                            if len(result[result_key]):
                                for i in range(len(search_result["norm_query_id"])):
                                    if search_result["norm_query_id"][i] not in result[result_key]["norm_query_id"]:
                                        result[result_key]["sim_query"].append(search_result["sim_query"][i])
                                        result[result_key]["match_re"].append(search_result["match_re"][i])
                                        result[result_key]["norm_query"].append(search_result["norm_query"][i])
                                        result[result_key]["norm_query_id"].append(search_result["norm_query_id"][i])
                                        result[result_key]["score"].append(search_result["score"][i])
                            else:
                                search_result["match_type"] = "regular"
                                result[result_key] = search_result
                # SimCSE
                simcse_result, error_dict, threshold_dict = self.pre_rank_model.predict(
                    model_id=model_id, query=origin_text, query_list=replaced_text, topk=return_num,
                    context_input_process=context_input_process, remove_stop_words_flag=remove_stop_words_flag
                )
                update_threshold = self._get_search_result([],{}, short_text=is_short_text)
                update_threshold = {"complete_score": update_threshold['complete_score'],
                                    "threshold": update_threshold['threshold'],
                                    "low_threshold": update_threshold['low_threshold']}
                if error_dict["error_code"] != 0:
                    result["code"] = 1
                    result["msg"] = error_dict["error_msg"]
                    result["error_code"] = error_dict["error_code"]
                    result["error_type"] = error_dict["error_type"]
                    return result
                for simcse_result_key in simcse_result.keys():
                    result_key = f"{simcse_result_key}_result"
                    if result_key not in result:
                        result[result_key] = {}
                    if len(simcse_result[simcse_result_key]):
                        search_result = self._get_search_result(simcse_result[simcse_result_key], threshold_dict[simcse_result_key], short_text=is_short_text)
                        if len(result[result_key]):
                            for i in range(len(search_result["norm_query_id"])):
                                if search_result["norm_query_id"][i] not in result[result_key]["norm_query_id"]:
                                    result[result_key]["sim_query"].append(search_result["sim_query"][i])
                                    result[result_key]["match_re"].append(search_result["match_re"][i])
                                    result[result_key]["norm_query"].append(search_result["norm_query"][i])
                                    result[result_key]["norm_query_id"].append(search_result["norm_query_id"][i])
                                    result[result_key]["score"].append(search_result["score"][i])
                        else:
                            search_result["match_type"] = "model"
                            result[result_key] = search_result
                        if len(result[result_key]["sim_query"]) > return_num:
                            result[result_key]["sim_query"] = result[result_key]["sim_query"][:return_num]
                            result[result_key]["match_re"] = result[result_key]["match_re"][:return_num]
                            result[result_key]["norm_query"] = result[result_key]["norm_query"][:return_num]
                            result[result_key]["norm_query_id"] = result[result_key]["norm_query_id"][:return_num]
                            result[result_key]["score"] = result[result_key]["score"][:return_num]
                    if simcse_result_key == "faq":
                        result["data"] = result[result_key]

                    #go要求所有数据类型都要返回动态阈值
                    result[result_key].update(update_threshold)
                if robot_model_type == '混合模型机器人':
                    result = llmprerank(text,result,update_threshold,history)


        except Exception as e:
            logger.error(f"预测失败 - model_id: {model_id}, text: {text}, 错误: {e}",exc_info=True)
            raise e
        return result

    @staticmethod
    def get_model_id_trained_time(model_id):
        try:
            if setting.CLIENT != "train":
                return 0
            train_log_file_path = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/train_record/train_log.json")
            trained_time = int(os.path.getctime(train_log_file_path))
            return trained_time
        except Exception as e:
            logger.debug(f"获取{model_id}训练时间出错:{e}")
            return 0

    def labeling(self, model_id, data_key):
        start = time.time()
        result = {
            "code": 0,
            "model_id": model_id,
            "data_key": data_key,
            "msg": "标注成功",
            "label_result": []
        }

        model_id_trained_time = self.get_model_id_trained_time(model_id)
        logger.debug(f"model_id_trained_time:{model_id_trained_time},model_id:{model_id}")
        if setting.CLIENT == "train":
            if model_id in self.train_client_load_model_list and model_id in self.train_client_load_model_trained_time and \
                    model_id_trained_time != self.train_client_load_model_trained_time[model_id]:
                self.train_client_load_model_list.remove(model_id)  # 当前训练端加载的模型不是最新训练的
                logger.debug(f"当前训练端加载的模型不是最新训练的,已经弹出,old_model_id_trained_time:{self.train_client_load_model_trained_time[model_id]},model_id_trained_time:{model_id_trained_time},model_id:{model_id}")

        if setting.CLIENT == "train" and model_id not in self.train_client_load_model_list:
            if len(self.train_client_load_model_list) == setting.MAX_MODELS_TRAIN_CLIENT:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]
            self._load_model(model_id=model_id)
            self.train_client_load_model_list.append(model_id)
            self.train_client_load_model_trained_time[model_id] = model_id_trained_time
            logger.debug(f"自动加载模型,model_id_trained_time:{model_id_trained_time},model_id:{model_id}")

        try:
            data = self.redis.get_data(key=data_key)
            texts = []
            text_ids = []
            for data_dict in data:
                texts.append(data_dict["text"])
                text_ids.append(data_dict["id"])
            label_result = self.pre_rank_model.labeling(model_id=model_id, texts=texts, text_ids=text_ids)
            result["label_result"] = label_result
            logger.debug(f"标注成功, model_id:{model_id}, 耗时:{time.time()-start}s")
        except Exception as e:
            logger.error(f"标注失败, model_id:{model_id}, 错误信息:{e}")
            result["code"] = 1
            result["msg"] = f'标注错误:{e}'
            result["error_code"] = "NLU91021"
            result["error_type"] = 0

        self.redis.set_data(key=f"faq_labeling_{data_key}", msg=result)

    def _get_search_result(self, items: List[RankReturnObject], threshold_list=None, short_text=False):
        """
        :param items: list of RankReturnObject
        :return: search_result, dict
        """
        with open(self.threshold_file, "r", encoding="utf-8") as f:
            threshold_dict = json.load(f)
        if short_text:
            low_threshold, threshold, complete_score = threshold_dict["short"]
        else:
            low_threshold, threshold, complete_score = threshold_dict["long"]

        # if short_text:
        #     complete_score = 0.93
        #     threshold = 0.8
        #     low_threshold = 0.5
        # elif threshold_list is None:
        #     # complete_score = 0.98
        #     # threshold = 0.9
        #     # low_threshold = 0.75
        #     # 中行
        #     # complete_score = 0.952
        #     # threshold = 0.848
        #     # low_threshold = 0.474
        #     complete_score = 0.93
        #     threshold = 0.75
        #     low_threshold = 0.65
        # else:
        #     complete_score = threshold_list[2]
        #     threshold = threshold_list[1]
        #     low_threshold = threshold_list[0]

        search_result = {
            "match_type": "model",
            "sim_query": [],
            "match_re": [],
            "norm_query": [],
            "norm_query_id": [],
            "score": [],
            "complete_score": complete_score,
            "threshold": threshold,
            "low_threshold": low_threshold
        }

        for item in items:
            if item.norm_query_id not in search_result["norm_query_id"]:
                search_result["sim_query"].append(item.sim_query)
                search_result["match_re"].append(item.match_re)
                search_result["norm_query"].append(item.norm_query)
                search_result["norm_query_id"].append(item.norm_query_id)
                search_result["score"].append(item.score)
        return search_result

    def train_time(self, data, is_inc=False):
        return self.pre_rank_model.train_time(data, is_inc=is_inc)

if __name__ == "__main__":
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    main_model = SearchMain()
    model_id = '1894234140361461762'
    pred_result_complete = main_model.predict(model_id=model_id, text="客戶服務電話是哪一個？", return_num=3,robot_model_type='混合模型机器人')

    1+'1'
    # 加载数据
    R = REDIS()
    train_data = R.get_data(f'test_data_1_tra')[:]
    inc_train_data = R.get_data(f'test_data_2_tra')[:]
    train_model_id = f"train_model_id"

    # 训练
    start = time.time()
    print(f"训练预估时间:{main_model.train_time(data=train_data, is_inc=False)}")
    train_result = main_model.train(model_id=train_model_id, data_key="test_data_1_tra")
    print(f"训练得分:{train_result['score']},耗时:{time.time()-start}")
    main_model.switch_model(model_id=train_model_id, flag=1)
    pred_result_reg = main_model.predict(model_id=train_model_id, text="龍年", return_num=3)
    pred_result_complete = main_model.predict(model_id=train_model_id, text="阿克苏振宇汽车销售有限责任公司展厅电话是多少", return_num=3)
    pred_result_pred = main_model.predict(model_id=train_model_id, text="电话是多少", return_num=3)
    context_result = main_model.predict(model_id=train_model_id, text="阿克苏振宇汽车销售有限责任公司展厅", context_input_process="1")

    # 预测耗时统计
    all_start_time = time.time()
    for i in range(10):
        pred_result_pred = main_model.predict(model_id=train_model_id, text="电话是多少", return_num=3)
    all_start_time = (time.time()-all_start_time)/10
    faq_start_time = time.time()
    for i in range(10):
        pred_result_pred = main_model.predict(model_id=train_model_id, text="电话是多少", return_num=3, model_type=["faq"])
    faq_start_time = (time.time()-faq_start_time)/10
    intent_start_time = time.time()
    for i in range(10):
        pred_result_pred = main_model.predict(model_id=train_model_id, text="电话是多少", return_num=3, model_type=["intent"])
    intent_start_time = (time.time()-intent_start_time)/10
    chat_start_time = time.time()
    for i in range(10):
        pred_result_pred = main_model.predict(model_id=train_model_id, text="电话是多少", return_num=3, model_type=["chat"])
    chat_start_time = (time.time()-chat_start_time)/10
    print(f"faq耗时:{faq_start_time}")
    print(f"intent耗时:{intent_start_time}")
    print(f"chat耗时:{chat_start_time}")
    print(f"faq、intent、chat耗时:{all_start_time}")

    # 增量训练
    start = time.time()
    print(f"训练预估时间:{main_model.train_time(data=inc_train_data, is_inc=True)}")
    inc_train_result = main_model.increment_train(model_id=train_model_id, data_key="test_data_2_tra")
    print(f"训练得分:{inc_train_result['score']},耗时:{time.time()-start}")
    main_model.switch_model(model_id=train_model_id, flag=1)
    inc_pred_result_reg = main_model.predict(model_id=train_model_id, text="陳续保500元优惠券怎么使用BCD", return_num=3)
    inc_pred_result_complete = main_model.predict(model_id=train_model_id, text="续保500元优惠券如何使用", return_num=3)
    inc_pred_result_pred = main_model.predict(model_id=train_model_id, text="500元优惠券如何使用", return_num=3)
    inc_context_result = main_model.predict(model_id=train_model_id, text="阿克苏振宇汽车销售有限责任公司展厅", context_input_process="1")
    print("wait")
