# -*- coding: utf-8 -*-

import fcntl
import json
import os
import threading
import time

import setting
from setting import logger

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)
# 限制CPU 占用，避免挤占其他服务
tf.config.threading.set_inter_op_parallelism_threads(setting.NUM_THREADS)
tf.config.threading.set_intra_op_parallelism_threads(setting.NUM_THREADS)

from database.REDIS import REDIS
from database.RabbitMQ import RabbitMQ
from module.TextPairSim.SentBertTextPairSim.SentBertTextPairSim import SentBertTextPairSim

service_name = "SimilarityScore"
model_record_file = setting.MODEL_RECORD_FILE + f".{service_name}"
switch_model_channel = setting.SERVICE_CHANNEL_DICT["SimilarityScoreMain"]["switch_model"]

class SimilarityScoreMain:
    def __init__(self):
        os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
        self.redis = REDIS()

        if setting.USE_RABBIT_MQ:
            self.rabbit_listen = RabbitMQ()
        else:
            self.rabbit_listen = None

        # 粗排
        self.sbert_tps_model = SentBertTextPairSim()
        self.sbert_tps_model.load_model(model_id="pretrain", save_dir=setting.PRETRAIN_BERT_DIR)

        # Redis
        if setting.CLIENT != "train":
            if setting.USE_RABBIT_MQ:
                self.switch_thread = threading.Thread(target=self.rabbit_listen.listen_msg, args=(self.switch_callback,))
                self.switch_thread.start()
            else:
                self.redis.subscribe(switch_model_channel)
                callback_dict = {
                    switch_model_channel: self.switch_model
                }
                t1 = threading.Thread(target=self.redis.listen_msg, args=(callback_dict,))
                t1.start()

        # 加载已上线模型
        self.model_record = {}
        if setting.CLIENT =='predict':
            self.init_model()

        # 测试加载的模型
        self.train_client_load_model_list = []

    def switch_callback(self, ch, method, properties, body):
        if method.routing_key == switch_model_channel:
            logger.debug(f'rabbit_mq接收到监听消息:{switch_model_channel},msg:{body}')
            logger.debug(f"ch:{ch}")
            logger.debug(f"method:{method}")
            logger.debug(f"properties:{properties}")
            logger.debug(f"body:{body}")
            body_dict = json.loads(body)
            self.switch_model(**body_dict)

    def init_model(self):
        # 加载已上线模型
        model_ids = []
        if os.path.exists(model_record_file):
            with open(model_record_file, 'r', encoding='utf-8') as f:
                try:
                    self.model_record = json.load(f)
                    model_ids = list(self.model_record.keys()) # 防止 model_record 发生变化而报错
                except Exception as e:
                    logger.warning(f"加载历史上线模型记录文件失败: {e}")
        try:
            for model_id in model_ids:
                if self._load_model(model_id=model_id): # 加载失败的要在 model_record 里面移除
                    self.model_record.pop(model_id, 0)
                logger.debug('[{}] 已加载历史上线模型'.format(model_id))
            self.dump_record()
        except Exception as e:
            logger.warning(f"加载历史上线模型失败: {e}")

    def dump_record(self):
        with open(model_record_file, 'w', encoding='utf-8') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.model_record, f, ensure_ascii=False)
        logger.debug('保存已上线模型记录')

    def train(self, model_id, data_key='', sendredis=True,**kwargs):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "train",
            "msg": "训练成功",
            "score": 0,
        }
        try:
            data = self.redis.get_data(key=data_key)

            # 开始训练 SentBertTextPairSim 模型
            start = time.time()
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.sbert_tps_model.__class__.__name__}/")
            score = self.sbert_tps_model.train(model_id=model_id, data=data, save_dir=model_save_dir)
            logger.debug(f"SentBertTextPairSim 训练成功 - model_id: {model_id}, 得分: {score}, 耗时:{time.time() - start}s")
            result["score"] = score
        except Exception as e:
            logger.error(f"训练失败 - model_id: {model_id}, 错误: {e}")
            result["code"] = 400
            result["msg"] = f"训练失败 - model_id: {model_id}, 错误: {e}"

        if sendredis:
            self.redis.set_data(key=f"similarity_score_train_{model_id}", msg=result)
        return result

    def switch_model(self, model_id, flag):
        code = 0
        if flag == 1:
            code = self._load_model(model_id)
            if code == 0:
                self.model_record[model_id] = 1
            else:
                self.model_record.pop(model_id, 0)
                code = 1
        else:
            self._offline_model(model_id)
            self.model_record.pop(model_id, 0)
        self.dump_record()
        return code
        
    def _load_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "load_model",
            "msg": "加载模型成功"
        }

        logger.debug(f"开始加载模型 - model_id: {model_id}, 进程: {os.getpid()}")
        # 加载模型
        try:
            save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.sbert_tps_model.__class__.__name__}/")
            self.sbert_tps_model.load_model(model_id=model_id, save_dir=save_dir)
            logger.debug(f"加载模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
            return 0
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 400
            result["msg"] = f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}"
            return 400

    def _offline_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "offline_model",
            "msg": "下线模型成功"
        }

        logger.debug(f"开始下线模型 - model_id: {model_id}, 进程: {os.getpid()}")
        try:
            self.sbert_tps_model.offline_model(model_id=model_id)
            logger.debug(f"下线模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
        except Exception as e:
            logger.error(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 400
            result["msg"] = str(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")

    def predict(self, model_id, text1, text2):
        if model_id is None:
            model_id = "pretrain"

        result = {
            "code": 0,
            "model_id": model_id,
            "text1": text1,
            "text2": text2,
            "score": 0,
            "msg": "预测成功",
        }
        if setting.CLIENT == "train" and model_id != "pretrain" and model_id not in self.train_client_load_model_list:
            if len(self.train_client_load_model_list) == setting.MAX_MODELS_TRAIN_CLIENT:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]
            self._load_model(model_id=model_id)
            self.train_client_load_model_list.append(model_id)

        try:
            start = time.time()
            sim_score = float(self.sbert_tps_model.predict(model_id=model_id, text_list1=text1, text_list2=text2)[0])
            logger.debug(f"预测完成 - model_id: {model_id}, text_1: {text1}, text_2: {text2}, 分数: {sim_score}, 耗时: {time.time()-start}")
            result["score"] = sim_score
        except Exception as e:
            logger.error(f"预测失败 - model_id: {model_id}, text_1: {text1}, text_2: {text2}, 错误: {e}")
            result["code"] = 400
            result["msg"] = f"预测失败 - model_id: {model_id}, text_1: {text1}, text_2: {text2}, 错误: {e}"

        return result


if __name__ == "__main__":
    model = SimilarityScoreMain()

    model_id = "model1"
    data_key = "faq_model1_all"

    # ==================== train ====================
    model.train(model_id=model_id, data_key=data_key)

    # ==================== load model ====================
    model.switch_model(model_id=model_id, flag=1)
    time.sleep(5)

    # ==================== predict model_id ====================
    text_1 = "有什么办法可以测试文本相似度？"
    text_2 = "文本相似度的测量方法"
    text_3 = "这是一段测试代码"
    print(model.predict(model_id=model_id, text1=text_1, text2=text_2))
    print(model.predict(model_id=model_id, text1=text_2, text2=text_3))

    # ==================== predict pretrain ====================
    text_1 = "有什么办法可以测试文本相似度？"
    text_2 = "文本相似度的测量方法"
    text_3 = "这是一段测试代码"
    print(model.predict(model_id=None, text1=text_1, text2=text_2))
    print(model.predict(model_id=None, text1=text_2, text2=text_3))
