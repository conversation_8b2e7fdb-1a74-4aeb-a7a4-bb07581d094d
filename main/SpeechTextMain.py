import json
import os
import shutil
import time

import requests

from concurrent.futures import ThreadPoolExecutor

import setting
from database.REDIS import REDIS
from module.Exam.SpeechText.SpeechText import SpeechText
from setting import logger, SAVE_MODEL_DIR
from utils.filechat_utils import ToFile, ppt_text_extract_page, write_comments, download_url_file
from utils.callback_utils import callback_for_http


class SpeechTextMain:
    def __init__(self):
        self.speech_text_model = SpeechText()
        self.redis = REDIS()
        self.pool_executor = ThreadPoolExecutor(max_workers=1)

    def train(self, model_id, data=None, data_key='', sendredis=False,**kwargs):
        start_time = time.time()
        callback_url = ""
        redis_key = f"nlp_ppt_result_{model_id}"

        try:
            logger.debug(f'PPT生成讲稿,开始,model_id:{model_id}')
            if data_key:
                data = self.redis.get_data(data_key)

            data = [i for i in data if i['filename']]
            logger.debug(f"PPT生成讲稿,数据:{data}")
            filenames = [i['filename'] for i in data]
            image_list = [i.get("imageList", []) for i in data]
            prompt_data_key_list = [i.get('prompt_data_key', "") for i in data]
            base64s = [i.get('base64', '') for i in data]
            callback_url = data[0].get("callback", "")

            other_model_id = data[0].get("other_model_id", "")
            other_model_type = data[0].get("other_model_type", "")
            enterprise_id = data[0].get("enterprise_id", "")

            logger.debug(f"PPT生成讲稿,回调地址:{callback_url}")

            save_dir = os.path.join(SAVE_MODEL_DIR, model_id, self.speech_text_model.__class__.__name__)
            file_save_dir = os.path.join(SAVE_MODEL_DIR, model_id, 'files')
            os.makedirs(save_dir, exist_ok=True)
            os.makedirs(file_save_dir, exist_ok=True)

            contents = []
            image_local_list = []
            write_comments_record = {}
            for index, filename in enumerate(filenames):
                try:
                    save_filename = filename.split('/')[-1]
                    file_path = os.path.join(file_save_dir, save_filename)
                    comment_file_path = os.path.join(file_save_dir, "comment_" + save_filename)
                    write_comments_record[filename] = {"file_path": file_path, "comment_file_path": comment_file_path}
                    if base64s[index]:
                        ToFile(base64s[index], file_path)
                    else:
                        if os.path.exists(filename):
                            shutil.copy(filename, file_path)
                        else:
                            download_url_file(filename, file_path, exists_download=True)

                    image_local = []
                    for image_url in image_list[index]:
                        image_filename = image_url.split('/')[-1]
                        image_path = os.path.join(file_save_dir, image_filename)
                        if os.path.exists(image_url):
                            shutil.copy(image_url, image_path)
                        else:
                            download_url_file(image_url, image_path, exists_download=True)
                        image_local.append(image_path)
                    image_local_list.append(image_local)

                    texts = ppt_text_extract_page(file_path)
                    contents.append(texts)
                except:
                    logger.error('PPT生成讲稿,解析文件报错', exc_info=True)
                    contents.append(["这是一段没意义的文字"])
            code, msg, ppt_speech_text_dict = self.speech_text_model.train(model_id, filenames, contents, image_local_list, save_dir, prompt_data_key_list=prompt_data_key_list, other_model_id=other_model_id, other_model_type=other_model_type, enterprise_id=enterprise_id)

            # 写入PPT备注
            for filename in write_comments_record.keys():
                if not ppt_speech_text_dict.get(filename):
                    continue
                comment_list = [temp_d["result"] for temp_d in ppt_speech_text_dict[filename]]
                write_comments(write_comments_record[filename]["file_path"], write_comments_record[filename]["comment_file_path"], comment_list)

            q_result = self.query(model_id=model_id)
            self.redis.set_data(redis_key, msg=q_result)
            logger.debug(f"PPT生成讲稿,耗时统计,code:{code},msg:{msg},model_id:{model_id},字数:{sum([len(c) for content in contents for c in content])},总耗时:{time.time()-start_time},")
            if code != 0:
                result = {"code": 1, "model_id": model_id, "task": "train", "score": 0, "error_code": "NLU91020", "error_type": 0, "msg": "失败", "key": redis_key}
                if msg == "模型已停止":
                    result["msg"] = "取消训练"
                    result["error_code"] = ""

            else:
                result = {"code": code, "model_id": model_id, "task": "train", "score": 0, "msg": "成功", "key": redis_key}
        except Exception as e:
            logger.error(f"PPT生成讲稿,报错:{e}", exc_info=True)
            q_result = self.query(model_id=model_id)
            q_result["code"] = 1
            q_result["msg"] = f"PPT生成讲稿报错:{e}"
            self.redis.set_data(redis_key, msg=q_result)
            result = {"code": 1, "model_id": model_id, "task": "train", "score": 0, "error_code": "NLU91020", "error_type": 0, "msg": "失败", "key": redis_key}
        return result

    def query(self, model_id):
        save_dir = os.path.join(SAVE_MODEL_DIR, model_id, self.speech_text_model.__class__.__name__)
        query_result = self.speech_text_model.query(model_id=model_id, save_dir=save_dir)
        return query_result

    def single_page_gen(self, filename, model_id, page, image_url, prompt_data_key, callback, task_type=0, old_text="", other_model_dict={}):
        redis_key = f"single_page_gen_result_{model_id}"
        if self.redis.get_data(redis_key)["msg"] == "取消训练":
            return
        else:
            try:
                file_save_dir = os.path.join(SAVE_MODEL_DIR, f"single_ppt_{model_id}", 'files')
                save_dir = os.path.join(SAVE_MODEL_DIR, f"single_ppt_{model_id}", 'single_page_gen')
                os.makedirs(file_save_dir, exist_ok=True)
                os.makedirs(save_dir, exist_ok=True)
                save_filename = filename.split('/')[-1]
                file_path = os.path.join(file_save_dir, save_filename)
                if os.path.exists(filename):
                    shutil.copy(filename, file_path)
                else:
                    download_url_file(filename, file_path, exists_download=True)

                if image_url:
                    image_filename = image_url.split('/')[-1]
                    image_path = os.path.join(file_save_dir, image_filename)
                    if os.path.exists(image_url):
                        shutil.copy(image_url, image_path)
                    else:
                        download_url_file(image_url, image_path, exists_download=True)
                else:
                    image_path = ""

                all_page_texts = ppt_text_extract_page(file_path)
                logger.debug(f"other_model_dict:{other_model_dict}, id:{id(other_model_dict)}")
                self.speech_text_model.single_page_gen(all_page_texts, model_id, page, image_path, task_type, old_text, prompt_data_key, save_dir, other_model_dict)
            except Exception as e:
                single_page_gen_data = {
                    'code': 1,
                    'msg': f'生成单页PPT讲稿报错:{e}',
                    'finish': 1,
                    'content': "",
                    "model_id": model_id
                }
                self.redis.set_data(redis_key, single_page_gen_data)

        if callback:
            logger.debug(f"生成单页PPT讲稿,回调地址:{callback}")
            callback_for_http(url=callback, data=self.redis.get_data(redis_key))
            logger.debug(f"生成单页PPT讲稿,回调结束:{callback}")
        return self.redis.get_data(redis_key)

    def add_comments(self, filename, comments, sn="", upload_file_url=""):
        logger.debug(f"准备把备注写入PPT,{filename}")
        file_save_dir = os.path.join(SAVE_MODEL_DIR, "add_comments")
        os.makedirs(file_save_dir, exist_ok=True)
        save_filename = filename.split('/')[-1]
        file_path = os.path.join(file_save_dir, f"{sn}_" + save_filename)
        comment_file_path = os.path.join(file_save_dir, f"comment_{sn}_" + save_filename)

        if os.path.exists(filename):
            shutil.copy(filename, file_path)
        else:
            download_url_file(filename, file_path, exists_download=True)
        write_comments(file_path, comment_file_path, comments)

        payload = {}
        files = [('file', (save_filename, open(comment_file_path, 'rb'), 'text/plain'))]
        headers = {}
        logger.debug(f"准备上传备注后的PPT")
        if not upload_file_url:
            upload_file_url = setting.UPLOAD_FILE_URL
        response = requests.request("POST", upload_file_url, headers=headers, data=payload, files=files)
        logger.debug(f"结束上传备注后的PPT,response:{response}")
        response = json.loads(response.text)
        return response

    def single_page_query(self, filename, model_id, page, image_url, prompt_data_key, callback, is_new_request=True, task_type=0, old_text="", need_callback=True, other_model_dict={}):
        try:
            if need_callback:
                redis_key = f"single_page_gen_result_{model_id}"
                if not is_new_request and self.redis.key_exist(redis_key):
                    logger.debug(f"准备查询单页PPT讲稿,filename:{filename},model_id:{model_id},page:{page}")
                    single_page_gen_data = self.redis.get_data(redis_key)
                    return single_page_gen_data
                else:
                    logger.debug(f"准备生成单页PPT讲稿,filename:{filename},model_id:{model_id},page:{page}")
                    redis_key = f"single_page_gen_result_{model_id}"
                    single_page_gen_data = {
                        'code': 1,
                        'msg': '等待训练',
                        'finish': 0,
                        'content': "",
                        'model_id': model_id
                    }
                    self.redis.set_data(redis_key, single_page_gen_data)

                    self.pool_executor.submit(self.single_page_gen, filename=filename, model_id=model_id, page=page, image_url=image_url, prompt_data_key=prompt_data_key, callback=callback, task_type=task_type, old_text=old_text, other_model_dict=other_model_dict)
                    single_page_gen_data["msg"] = "成功"
                    single_page_gen_data["code"] = 0
                    return single_page_gen_data
            else:
                redis_key = f"single_page_gen_result_{model_id}"
                single_page_gen_data = {
                    'code': 0,
                    'msg': '成功',
                    'finish': 0,
                    'content': "",
                    'model_id': model_id
                }
                self.redis.set_data(redis_key, single_page_gen_data)
                single_page_gen_data = self.single_page_gen(filename=filename, model_id=model_id, page=page, image_url=image_url, prompt_data_key=prompt_data_key, callback=callback, task_type=task_type, old_text=old_text, other_model_dict=other_model_dict)
                return single_page_gen_data
        except Exception as e:
            logger.debug(f"查询单页PPT讲稿报错,filename:{filename},model_id:{model_id},page:{page},{e}")
            single_page_gen_data = {
                'code': 1,
                'msg': f'生成单页PPT讲稿报错:{e}',
                'finish': 1,
                'content': "",
                'model_id': model_id
            }
            logger.debug(f"生成单页PPT讲稿,回调地址:{callback}")
            callback_for_http(url=callback, data=single_page_gen_data)
            logger.debug(f"生成单页PPT讲稿,回调结束:{callback}")
            return single_page_gen_data

    def train_time(self, data):
        base64s = [i.get('base64', '') for i in data]
        filenames = [i['filename'] for i in data]

        contents = []
        write_comments_record = {}
        save_dir = os.path.join(SAVE_MODEL_DIR, "ppt_count", self.speech_text_model.__class__.__name__)
        file_save_dir = os.path.join(SAVE_MODEL_DIR, "ppt_count", 'files')
        os.makedirs(save_dir, exist_ok=True)
        os.makedirs(file_save_dir, exist_ok=True)
        for index, filename in enumerate(filenames):
            try:
                save_filename = filename.split('/')[-1]
                file_path = os.path.join(file_save_dir, save_filename)
                comment_file_path = os.path.join(file_save_dir, "comment_" + save_filename)
                write_comments_record[filename] = {"file_path": file_path, "comment_file_path": comment_file_path}
                if base64s[index]:
                    ToFile(base64s[index], file_path)
                else:
                    if os.path.exists(filename):
                        shutil.copy(filename, file_path)
                    else:
                        download_url_file(filename, file_path, exists_download=True)
                texts = ppt_text_extract_page(file_path)
                contents.append(len(texts))
            except:
                logger.error('PPT生成讲稿,解析文件报错', exc_info=True)
                contents.append("这是一段没意义的文字")
                return 1
        return int(sum(contents) * 120)   # 测试统计一页训练需要17.4s ,现在固定一页2分钟


if __name__ == '__main__':
    pass
    # data = [
    #     {
    #         'filename': "https://kaopei.qnzsai.com/minio/train/cause/2024/2/20/大模型基础知识培训-2页_1708410483256.pptx",
    #         'imageList': [
    #             "https://kaopei.qnzsai.com/minio/filetrans/大模型基础知识培训-2页1708410483256/0_1708410619612.jpg",
    #             "https://kaopei.qnzsai.com/minio/filetrans/大模型基础知识培训-2页1708410483256/1_1708410619685.jpg"
    #         ]
    #     }
    # ]
    #
    # model_id = 'test_ppt_main'
    # main = SpeechTextMain()
    # # main.train(model_id, data)
    # # main_result = main.query(model_id)
    #
    # model_id = "single_ppt_main"
    # single_result = main.single_page_query(
    #     filename="https://kaopei.qnzsai.com/minio/train/cause/2024/2/20/大模型基础知识培训-2页_1708410483256.pptx",
    #     model_id=model_id,
    #     page=0,
    #     image_url="https://kaopei.qnzsai.com/minio/filetrans/大模型基础知识培训-2页1708410483256/0_1708410619612.jpg",
    #     prompt_data_key="",
    #     callback="",
    #     task_type=0,
    #     old_text="",
    #     need_callback=True,
    #     is_new_request=True
    # )
    # print(single_result)
    # while True:
    #     time.sleep(10)
    #     single_result = main.single_page_query(
    #         filename="https://kaopei.qnzsai.com/minio/train/cause/2024/2/20/大模型基础知识培训-2页_1708410483256.pptx",
    #         model_id=model_id,
    #         page=0,
    #         image_url="https://kaopei.qnzsai.com/minio/filetrans/大模型基础知识培训-2页1708410483256/0_1708410619612.jpg",
    #         prompt_data_key="",
    #         callback="",
    #         task_type=0,
    #         old_text="",
    #         need_callback=True,
    #         is_new_request=False
    #     )
    #     if single_result["finish"]:
    #         print(single_result)
    #         break
    #
    # for task_type in [1, 2, 3]:
    #     task_type_to_task = {
    #         0: "重新生成",
    #         1: "改写",
    #         2: "缩写",
    #         3: "扩写"
    #     }
    #     print(f"开始{task_type_to_task[task_type]}")
    #     old_text = "各位领导，同事们，大家好。我是文小库，非常荣幸在这里为大家进行一场关于大模型基础知识的培训。希望通过这次培训，我们能对大模型有更深入的理解和把握。今年是2024年,祝大家新年快乐,新年发大财。"
    #     single_result = main.single_page_query(
    #         filename="https://kaopei.qnzsai.com/minio/train/cause/2024/2/20/大模型基础知识培训-2页_1708410483256.pptx",
    #         model_id=model_id+f"_{task_type_to_task.get(task_type)}",
    #         page=0,
    #         image_url="https://kaopei.qnzsai.com/minio/filetrans/大模型基础知识培训-2页1708410483256/0_1708410619612.jpg",
    #         prompt_data_key="",
    #         callback="",
    #         task_type=task_type,
    #         old_text=old_text,
    #         need_callback=False,
    #         is_new_request=True
    #     )
    #     print(f"{task_type_to_task[task_type]}前:{old_text}")
    #     print(f"{task_type_to_task[task_type]}后:{single_result.get('content', '')}")
    # print("wait")

