# -*- coding: utf-8 -*-

import fcntl
import json
import os
import threading
import time
from typing import List

import setting
from setting import logger
from utils import my_utils



from utils.my_utils import RankReturnObject
from database.REDIS import REDIS
from database.RabbitMQ import RabbitM<PERSON>
from module.SpellCheck.Ngram.NLTKngramSpellCheck import NLTKngramSpellCheck


service_name = "SpellCheck"
model_record_file = setting.MODEL_RECORD_FILE + f".{service_name}"
switch_model_channel = setting.SERVICE_CHANNEL_DICT["SpellCheckMain"]["switch_model"]


class SpellCheckMain:
    def __init__(self):
        os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
        self.redis = REDIS()
        self.load_model_dict = {}

        if setting.USE_RABBIT_MQ:
            self.rabbit_listen = RabbitMQ()
        else:
            self.rabbit_listen = None

        # 粗排
        self.spell_check = NLTKngramSpellCheck()

        # Redis
        if setting.CLIENT != "train":
            if setting.USE_RABBIT_MQ:
                self.switch_thread = threading.Thread(target=self.rabbit_listen.listen_msg, args=(self.switch_callback,))
                self.switch_thread.start()
            else:
                self.redis.subscribe(switch_model_channel)
                callback_dict = {
                    switch_model_channel: self.switch_model
                }
                t1 = threading.Thread(target=self.redis.listen_msg, args=(callback_dict,))
                t1.start()

        # 加载已上线模型
        self.model_record = {}
        if setting.CLIENT == 'predict' and (not setting.AUTO_ONLINE_MODEL or setting.LRU_MODEL_LOAD_NUM):
            self.init_model()

        # 测试加载的模型
        self.train_client_load_model_list = []
        self.train_client_load_model_trained_time = {}

        if setting.AUTO_ONLINE_MODEL and setting.CLIENT == 'predict' and not setting.LRU_MODEL_LOAD_NUM:
            t4 = threading.Thread(target=my_utils.auto_offline, args=(self, self.load_model_dict))
            t4.start()

    def switch_callback(self, ch, method, properties, body):
        if method.routing_key == switch_model_channel:
            logger.debug(f'rabbit_mq接收到监听消息:{switch_model_channel},msg:{body}')
            logger.debug(f"ch:{ch}")
            logger.debug(f"method:{method}")
            logger.debug(f"properties:{properties}")
            logger.debug(f"body:{body}")
            body_dict = json.loads(body)
            self.switch_model(**body_dict)

    def init_model(self):
        # 加载已上线模型
        model_ids = []
        if os.path.exists(model_record_file):
            with open(model_record_file, 'r', encoding='utf-8') as f:
                try:
                    self.model_record = json.load(f)
                    self.model_record = dict(sorted(self.model_record.items(), key=lambda x: x[1])) #从小到大
                    model_ids = list(self.model_record.keys())[-setting.LRU_MODEL_LOAD_NUM:] #只加载最近发布的模型
                    logger.info(f'{self.__class__.__name__}待加载列表:{model_ids}')
                except Exception as e:
                    logger.warning(f"加载历史上线模型记录文件失败: {e}")
        try:
            for model_id in model_ids:
                if self._load_model(model_id=model_id):  # 加载失败的要在 model_record 里面移除
                    self.model_record.pop(model_id, 0)
                logger.debug('[{}] 已加载历史上线模型'.format(model_id))
            self.dump_record()
        except Exception as e:
            logger.warning(f"加载历史上线模型失败: {e}")

    def dump_record(self):
        if setting.SEND_MODEL_MODE == "Nas":
            return
        with open(model_record_file, 'w', encoding='utf-8') as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.model_record, f, ensure_ascii=False)
        logger.debug('保存已上线模型记录')

    def train(self, model_id, data_key='', sendredis=True,**kwargs):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "train",
            "msg": "训练成功",
            "score": 0,
        }
        try:
            data = self.redis.get_data(key=data_key)

            # 开始训练粗排模型
            start = time.time()
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR,
                                          f"{model_id}/{self.spell_check.__class__.__name__}/")
            score = self.spell_check.train(model_id=model_id, data=data, save_dir=model_save_dir)
            logger.debug(
                f"{self.spell_check.__class__.__name__} 训练成功 - model_id:{model_id},耗时:{time.time() - start}s")

            result["score"] = float(score)
        except Exception as e:
            logger.error(f"训练失败 - model_id: {model_id}, 错误: {e}")
            result["code"] = 1
            result["msg"] = f'训练错误:{e}'
            result["error_code"] = "NLU91020"
            result["error_type"] = 0

        if sendredis:
            self.redis.set_data(key=f"faq_train_{model_id}", msg=result)
        if model_id in self.train_client_load_model_list:
            self._load_model(model_id=model_id)
        return result

    def switch_model(self, model_id, flag):
        code = 0
        if flag == 1:
            code = self._load_model(model_id)
            if code == 0:
                self.model_record[model_id] = 1
                self.load_model_dict[model_id] = time.time()
            else:
                self.model_record.pop(model_id, 0)
                code = 1
        else:
            self._offline_model(model_id)
            self.model_record.pop(model_id, 0)
            if model_id in self.load_model_dict:
                self.load_model_dict.pop(model_id)
        self.dump_record()
        return code

    def _load_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "load_model",
            "msg": "加载模型成功"
        }

        logger.debug(f"开始加载模型 - model_id: {model_id}, 进程: {os.getpid()}")
        # 加载模型
        try:
            save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.spell_check.__class__.__name__}/")
            self.spell_check.load_model(model_id=model_id, save_dir=save_dir)

            logger.debug(f"加载模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
            self.load_model_dict[model_id] = time.time()
            #先进先出
            for model_id in list(self.load_model_dict)[:-setting.LRU_MODEL_LOAD_NUM]:
                try:
                    self._offline_model(model_id)
                except:
                    logger.error(f'lru模型下线失败,model_id:{model_id}',exc_info=True)
            return 0
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 400
            result["msg"] = f"加载模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}"
            return 400

    def _offline_model(self, model_id):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "offline_model",
            "msg": "下线模型成功"
        }

        logger.debug(f"开始下线模型 - model_id: {model_id}, 进程: {os.getpid()}")
        try:
            self.spell_check.offline_model(model_id=model_id)
            logger.debug(f"下线模型成功 - model_id: {model_id}, 进程: {os.getpid()}")
            if model_id in self.load_model_dict:
                self.load_model_dict.pop(model_id)
        except Exception as e:
            logger.error(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
            result["code"] = 400
            result["msg"] = str(f"下线模型失败 - model_id: {model_id}, 进程: {os.getpid()}, 错误信息:{e}")
        return result

    def predict(self, model_id, text):
        result = {
            "code": 0,
            "model_id": model_id,
            "text": text,
            "msg": "预测成功",
        }
        if setting.CLIENT == "train" and model_id not in self.train_client_load_model_list:
            load_result = self._load_model(model_id=model_id)
            if load_result == 0:
                self.train_client_load_model_list.append(model_id)
            if len(self.train_client_load_model_list) > setting.MAX_MODELS_TRAIN_CLIENT:
                self._offline_model(self.train_client_load_model_list[0])
                self.train_client_load_model_list = self.train_client_load_model_list[1:]

        if setting.CLIENT == "predict" and setting.AUTO_ONLINE_MODEL:
            my_utils.auto_online(self, self.load_model_dict, model_id)

        try:
            start = time.time()
            text_item_list, error_dict = self.spell_check.predict(model_id=model_id, query=text)
            if error_dict["error_code"] != 0:
                result["code"] = 1
                result["msg"] = error_dict["error_msg"]
                result["error_code"] = error_dict["error_code"]
                result["error_type"] = error_dict["error_type"]

            if text_item_list[1]:
                data = {'result': text_item_list[0], 'change_word': text_item_list[1][0][0:2], 'change_index': text_item_list[1][0][2:]}
            else:
                data = {'result': text_item_list[0], 'change_word': [], 'change_index': []}

            result["msg"] = "返回纠错结果"
            result["data"] = data

        except Exception as e:
            logger.error(f"预测失败 - model_id: {model_id}, text: {text}, 错误: {e}")
            raise e
        return result



if __name__ == "__main__":
    model = SpellCheckMain()

    model_id = "test"
    data_key = "faq_model1_all"

    # ==================== train ====================
    # model.train(model_id=model_id, data_key=data_key)

    # ==================== load model ====================
    model.switch_model(model_id=model_id, flag=1)
    time.sleep(5)

    # ==================== predict ====================

    # 联想输入测试
    lines = ['ATM是不是坏了']


    # with open(r'/data/sammy/project/ngramlm/ngramCorrectorService/dataprocess/text.txt', 'r',encoding='utf-8') as f:
    #     lines = f.read().split('\n')
    #     lines = [i.replace(' ', '') for i in lines]


    output = []
    from tqdm import tqdm
    for query in tqdm(lines):
        # print(f"\n\n查询句子: {query}")
        result = model.predict(model_id=model_id, text=query)
        output.append(result)
    output = [{'text':i['text'],'result':i['data']['result']} for i in output]
    import pandas as pd
    output = pd.DataFrame(output)
    print(output[output['text'] != output['result']].shape)