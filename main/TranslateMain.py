# -*- coding: utf-8 -*-
# import sys
# sys.path.insert(0,'/app')
# import setting
# import os
# os.environ['NLP_PORT'] = "9881"
# from utils.nacos_register import NacosHelper
# nacos_r = NacosHelper("nlp-nlu-" + setting.CLIENT)
# nacos_r.nacos_fun()


import json
import os,re
import shutil
import threading
import time
import traceback
from typing import List
from tqdm import tqdm
import pandas as pd
import re

import setting
from setting import logger

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf

gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)
# 限制CPU 占用，避免挤占其他服务
tf.config.threading.set_inter_op_parallelism_threads(setting.NUM_THREADS)
tf.config.threading.set_intra_op_parallelism_threads(setting.NUM_THREADS)

from database.REDIS import REDIS
from model.CallLLM.CallOpenAI import CallOpenAI
from model.CallLLM.CallOurModelOld import CallOurModel
from model.CallLLM.CallOpenAI_ultra import CallOpenAI as CallOpenAI_ultra
from model.TraAndSim.TraAndSim import TraAndSim
from model.LanguageJudge.LanguageJudgeRegular import LanguageJudgeRegular
tra_and_sim_model = TraAndSim()

from utils.callback_utils import callback_for_http
from utils.callback_utils import RECODER
translate_recode = RECODER('translate')

from database.MINIO import MINIO

from setting import SAVE_MODEL_DIR
import requests
from hashlib import md5
#lru
from functools import lru_cache

#翻译缓存
cache_mapper = {}
en_cache_mapper = {}
cache_path = os.path.join(setting.MAIN_DIR, 'data','yue_mapper.csv')
if os.path.exists(cache_path):
    try:
        raw_df = pd.read_csv(cache_path)
        df = raw_df[raw_df['语种']=='yue']
        cache_mapper = dict(zip(df['source_text'],df['result']))

        df = raw_df[raw_df['语种']=='en']
        en_cache_mapper = dict(zip(df['source_text'], df['result']))
    except:
        logger.error('翻译缓存加载失败',exc_info=True)



class TranslateMain:
    def __init__(self):
        os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
        self.redis = REDIS()
        self.minio = MINIO()
        self.lg = LanguageJudgeRegular()

        if setting.TRANSLATE_MODEL == "gpt3.5":
            self.call_llm_model = CallOpenAI(use_azure=True)
        elif setting.TRANSLATE_MODEL == "gpt4":
            self.call_llm_model = CallOpenAI(use_azure=True, model_name="gpt-4")
        else:
            self.call_llm_model = CallOurModel(url=setting.TRANSLATE_MODEL_URL)

        self.call_llm_model = CallOpenAI_ultra('qwen2.5-14b-instruct', base_url=setting.YUE_TRANSLATE_MODEL_URL, api_key="sk-12650c62ac694a959175757f9ec6b7c4")
        self.yue_model = CallOpenAI_ultra('qwen2.5-14b-instruct', base_url=setting.YUE_TRANSLATE_MODEL_URL, api_key="sk-12650c62ac694a959175757f9ec6b7c4")
        # self.yue_model = CallOpenAI_ultra('gpt-4o-mini', base_url='https://api3.apifans.com/v1/',
        #                                   api_key="sk-9hvZQzOyFbl3GRdoB2AbE39e7dE34691978046B0C267259b")
        # self.call_llm_model = self.yue_model
        # self.yue_model = CallOpenAI('zs-gpt', base_url='http://127.0.0.1:6010/v1', api_key="EMPTY")


    def train(self, model_id, data_key='', sendredis=True):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "train",
            "msg": "训练成功",
            "score": 0,
        }

        return result

    def increment_train(self, model_id, data_key='', sendredis=True):
        result = {
            "code": 0,
            "model_id": model_id,
            "task": "train",
            "msg": "训练成功",
            "score": 0,
        }
        return result

    def predict(self, text, source_lang, target_lang):
        """
        中文到英文使用模型
        "source_lang":"chs",          #来源语种
        "target_lang":["en"],   #目标语种list
        "text":["你好","我很好"]}      #待译文list
        """
        lang_map = {'chs':'中文',
                    'en':'英文',
                    'cht':'中文',
                    'yue':'粤语'}
        result = {
            "code": 0,
            "text": text,
            'source_lang':source_lang,
            'target_lang':target_lang,
            "translate_result":[{'source_text':i} for i in text],
            "msg": "预测成功",
        }
        error_dict = {
            "error_code": 0,
            "error_type": 0,
            "error_msg": ""
        }
        fail_list = []

        try:
            start = time.time()
            assert source_lang in lang_map,f'待翻译语种不在列表内：{source_lang}'
            for tl in target_lang:
                try:
                    assert tl in lang_map,f'翻译目标语种不在范围内:{tl}'

                    if tl==source_lang:#同语种不翻译
                        result_list = text
                    elif tl in ['cht','chs'] and source_lang in ['cht','chs']: #简繁互译
                        result_list = [tra_and_sim_model.tra_or_sim(i, target="sim" if tl == 'chs' else 'cht') for i in text]
                    else:
                        # prompt =  "Human: 请把句子翻译成{}的\n句子：{}\nAssistant:"
                        result_list = [] #每个语种的翻译结果
                        for t in tqdm(text,desc='翻译进度'):
                            try:
                                t = re.split('(\<img.+?\>)', t)#对表情的处理

                                t = [i for i in t if i]

                                tmp_result = []
                                for i in t:
                                    if '<img' not in i:
                                        # user_prompt_list.append(prompt.format(lang_map[tl],i))
                                        source_lang_from_rate = self.lg.language_judge_use_rate(i)
                                        if source_lang_from_rate==lang_map[tl] or source_lang_from_rate!=lang_map[source_lang]: #检测语种和target语种一致或检测语种不等于source语种,不翻译
                                            tmp_result.append(i)
                                        else:
                                            # current_prompt = prompt.format(lang_map[tl], i)
                                            if tl=='yue':
                                                currrnt_output = self.yue_translate_predict(i)
                                            else:
                                                currrnt_output = self.en_zh_translate_predict(i)
                                            tmp_result.append(currrnt_output)

                                for index,i in enumerate(t):
                                    if '<img' not in i:
                                        t[index] = tmp_result[0]
                                        tmp_result.pop(0)
                                result_list.append(''.join(t))
                            except:
                                result_list.append('')
                                fail_list.append(t)
                                logger.error(f'翻译语种{tl},文本:{t}翻译报错', exc_info=True)
                        if tl == 'cht':
                            result_list = [tra_and_sim_model.tra_or_sim(i, target= 'cht') for i
                                           in result_list]
                    for index,i in enumerate(result['translate_result']):
                        if result_list[index]:
                            source_lang_from_rate = self.lg.language_judge_use_rate(i['source_text'])
                            i.update({tl:result_list[index],'lang_judege':source_lang_from_rate})
                except:
                    logger.error(f'翻译语种{tl}报错',exc_info=True)
            consume = time.time() - start
            result['time_consume'] = consume
        except Exception as e:
            error_dict["error_code"] = "NLU91027"
            error_dict["error_msg"] = f"翻译接口错误，待翻译语种不在列表内"
            result['code'] = 1
            result['msg'] = '翻译接口预测错误'
            logger.error(f"翻译接口报错, 错误信息: 待翻译语种不在列表内")
            raise e
        result['fail_list'] = fail_list
        return result,error_dict

    def offline_predict(self, data_url,callback_url,callback_data):
        """
        中文到英文使用模型
        "source_lang":"chs",          #来源语种
        "target_lang":["en"],   #目标语种list
        "text":["你好","我很好"]}      #待译文list
        """
        try:
            translate_recode.dump_train_record(callback_data, is_add=True)
            logger.debug(f"正在离线翻译:{callback_data['sn']}")
            model_id = callback_data['model_id']
            data = self.url_to_json(model_id,data_url)
            source_lang = data.get('source_lang', '')
            target_lang = data.get('target_lang', '')
            text = data.get('text', '')

            predict_result, error_dict = self.predict(text, source_lang, target_lang)
            logger.info(f"离线翻译完成:{callback_data['sn']}")
            if callback_url:
                translate_recode.dump_train_record(callback_data,is_add=False)
                try:
                    callback_data['data_url'] = self.data_to_url(model_id,predict_result)
                except:
                    logger.error('翻译结果minio上传失败',exc_info=True)
                callback_data.update(predict_result)
                callback_for_http(callback_url, callback_data)
            logger.debug(f"离线翻译完成:{callback_data['sn']},结果为{predict_result}")
        except:
            logger.error(f'离线翻译报错:{callback_data}',exc_info=True)



    def url_to_json(self,model_id,url):
        save_dir = os.path.join(SAVE_MODEL_DIR, model_id,'train_data')
        os.makedirs(save_dir,exist_ok=True)
        save_filename = url.split('/')[-1]
        file_path = os.path.join(save_dir, save_filename)
        try:
            logger.info(f'正在请求下载链接{url}')
            data = requests.get(url,timeout=3)
            with open(file_path, 'wb') as f:
                f.write(data.content)
                f.close()
            data = json.loads(open(file_path,'r').read())
            logger.info(f'{url}下载成功')
            return data
        except Exception as e:
            logger.error(f'文件下载失败:{url}',exc_info=True)


    def data_to_url(self,model_id,data):
        save_dir = os.path.join(SAVE_MODEL_DIR, model_id,'train_data')
        os.makedirs(save_dir,exist_ok=True)
        filename =   md5(str(data).encode()).hexdigest() + '_translate_result.json'
        file_path = os.path.join(save_dir, filename)
        with open(file_path, 'w') as f:
            f.write(json.dumps(data,ensure_ascii=False))
            f.close()
        # pd.DataFrame(data['translate_result']).to_csv(file_path.replace('.json','.csv'),encoding='utf-8-sig',index=False)
        upload_down = self.minio.upload_file(filename,file_path)
        if upload_down:
            return self.minio.get_download_url(filename)
        else:
            return ''

    @lru_cache(maxsize=100000)
    def yue_translate_predict(self,text):
        if text in cache_mapper:
            return cache_mapper[text]
        prompt = """案例：
<before>医疗保险怎么购买</before>
<after>点样购买医疗保险</after>
<before>日常热量摄入推荐多少</before>
<after>日常热量摄入推荐几多</after>
<before>生病了怎么办</before>
<after>生病了点算</after>
<before>哪些可以吃</before>
<after>边啲可以食</after>
<before>哪些检查要禁食</before>
<after>边啲检查要禁食</after>
<before>怎么提升健康</before>
<after>点样提升健康</after>
<before>怎么引起</before>
<after>点样引起</after>
<before>如何用</before>
<after>点样用</after>
<before>@text</before>
<after>"""

        prompt = prompt.replace('@text',text)
        llm_parameter = {
  "model": "zs-gpt",
  "messages": [
        {
            "role": "system",
            "content": "你是一名粤语语言专家，专有名词保留不翻译,直接翻译句子,不要聊天,不要续写"
        },
        {
            "role": "user",
            "content": prompt,
        }]  ,"max_tokens": 1024,
  "temperature": 0.001,
        "stop":['<|im_end|>']}
        result = self.yue_model.run(llm_parameter=llm_parameter)

        #大模型结果后处理
        yue = result
        if '</翻译后>' in yue:
            yue = yue.split('<翻译后>')[-1]
        yue = re.sub("</翻译后>|<翻译后>|</翻译后|</translation>|\"|」|</after>|<after>", '', yue)
        yue = yue.strip()

        if '《' not in text:
            yue = yue.replace('《', '').replace('》', '')
        yue = yue.replace(' ','')
        if '\n' in yue:
            yue = yue.split('\n')[0]
        result = yue

        #繁转简
        result = tra_and_sim_model.tra_or_sim(result, 'sim')
        return result

    @lru_cache(maxsize=100000)
    def en_zh_translate_predict(self,text):
        if text in en_cache_mapper:
            return en_cache_mapper[text]
        prompt ="""请地道的翻译下文,翻译结果符合当地人的表达习惯,中译英不要中式表达:\n@text"""
        prompt = prompt.replace('@text', text)
        llm_parameter = {
            "model": "zs-gpt",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一名中英语言专家，不要聊天,不要回答问题,直接翻译句子"
                },
                {
                    "role": "user",
                    "content": prompt,
                }], "max_tokens": 1024,
            "temperature": 0.001,
            "stop": ['<|im_end|>']}
        result = self.call_llm_model.run(llm_parameter=llm_parameter)

        # 大模型结果后处理
        yue = result
        if '</翻译后>' in yue:
            yue = yue.split('<翻译后>')[-1]
        yue = re.sub("</翻译后>|<翻译后>|</翻译后|</translation>|\"|」|</after>|<after>", '', yue)
        yue = yue.strip()

        if '《' not in text:
            yue = yue.replace('《', '').replace('》', '')
        # if '\n' in yue:
        #     yue = yue.split('\n')[0]
        result = yue

        # 繁转简
        result = tra_and_sim_model.tra_or_sim(result, 'sim')
        return result


if __name__ == "__main__":
    model = TranslateMain()
    text = """<img class="qqemoji smiley_13" title="[呲牙]" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAADUExURUxpcU3H2DoAAAABdFJOUwBA5thmAAAACklEQVQI12NgAAAAAgAB4iG8MwAAAABJRU5ErkJggg==" style="--positionX: -66; --positionY: -198;">请问你在说什么?"""
    text = """how are you"""
    text = "你好"
    text = "查电子保单"
    text = "帮我查下保单"
    text = '我的保单还有效吗'

    # df = pd.read_excel(r"C:\Users\<USER>\Desktop\粤语播报话术.xlsx")
    # df = df[df['粤语播报答案'].isnull()]
    # texts = df['普通话播报答案'].tolist()


    # text = "南山慢病院公共卫生部前身为业务科，在2017 年11月成立，2023 年 4 月更名为公共卫生部。集行政管理与防治业务管理为一体，负责多项工作统筹协调和质量控制。承担全市老年人心理关爱项目牵头单位等职责，对多个项目开展督导培训和质控考核。科室现有12 人，中级以上职称 4 人，硕士研究生以上学历 6 人，均为公共卫生相关专业技术人员。"
    source_lang = 'chs'
    # target_lang = ['chs']
    target_lang = ['en']
    # text = """今天的天气正好"""
    texts = ['请问你会用粤语提供帮助吗']
    texts = pd.read_csv(r'D:\develop\aicc-nlp-nlu\data\yue_mapper.csv')['source_text'].sample(100,random_state=2024).tolist()
    output = []
    print(model.yue_translate_predict('结核潜伏感染抽血结果为阳性该怎么办？'))
    print(model.en_zh_translate_predict('如果体检的胸片显示疑似结核，我应该做哪些检查以确认是否得了结核？'))
    1+'1'
    for index,text in enumerate(tqdm(texts)):
        item = {}
        item['input'] = text

        # result,error_dict = model.predict([text],source_lang,target_lang)
        # result = result['translate_result'][0]
        # item.update(result)


        # result = model.yue_translate_predict(text)
        # item['result'] = result

        result = model.en_zh_translate_predict(text)
        # result = model.en_zh_translate_predict('What are the specific symptoms of bipolar disorder?')
        item['result'] = result

        output.append(item)
        # if index == 10:
        #     break

    output_df = pd.DataFrame(output)
    # output_df.to_excel(r"C:\Users\<USER>\Desktop\南小慢中译英.xlsx",index=False)
    print('||'.join(output_df['result']))
    1+'1'


    for index in list(range(2)):
        df = pd.read_excel(r"D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2025-01\粤语-语料转译0106.xlsx",sheet_name=index)
        #['标准问', '语料']
        output = []
        info_df = []
        count = 0
        for norm,labels in zip(df['标准问'],df['语料']):
            new_label = []
            for label in tqdm(labels.split('||')):
                count+=1
                for _ in range(3):
                    try:
                        result = model.yue_translate_predict(label)
                    except:
                        import logging
                        logging.error(f"error:{label}",exc_info=True)

                new_label.append(result)
                info_df.append({'标准问':norm,'语料':label,'粤语':result})
            output.append({'标准问':norm,'语料':'||'.join(new_label)})

        output_df = pd.DataFrame(output)

        output_df.to_excel(rf"C:\Users\<USER>\Desktop\{index}_广交粤语-粤语-语料转译0106_result.xlsx",index=False)
        info_df = pd.DataFrame(info_df)
        info_df.to_excel(rf"C:\Users\<USER>\Desktop\{index}_广交粤语-粤语-语料转译0106_info.xlsx",index=False)
        break
#
#     # data_url = 'https://aicc.qnzsai.com/minio/aiccsrb/translate/faq/translate_1789978115303878657_1789978947291561985_1715603567455.json'
#     # callback_url = 'http://172.16.0.31:8488/srb/nlp/translate/predict/train/callback'
#     # result = model.offline_predict(data_url, callback_url, callback_data={'sn': 'test', 'model_id': 'test'})
#
# text = '老年健康指导有政府卫生机构和社会组织参与，人群覆盖面广。参与机构有社区健康中心、医院和诊所、志愿者组织等。参与人群包括老年人群体、家庭成员和照护者、志愿者和健康专业人士，旨在提升老年人健康水平和生活质量，促进社区健康和社会参与。'
# text = '南山区开展深圳市老年失能（失智）防控项目，为 65 岁及以上老人提供筛查及转诊指导、健康教育和干预服务等。该项目旨在提升基层服务能力，建立防控模式，通过系列举措预防或延缓失能（失智）发生。老人可凭身份证在就近社康免费做评估表，筛查阳性的老年人会获得健康指导和转诊建议。'
# text = '您想要了解关于老年人心理关爱的哪些问题呢？您可以点击右上方的问题或者直接向我提问。'
#
# text = '不知道'
#
#
# text = '谢谢'
#
# text = '老年健康指导有政府卫生机构和社会组织参与，人群覆盖面广。参与机构有社区健康中心、医院和诊所、志愿者组织等。参与人群包括老年人群体、家庭成员和照护者、志愿者和健康专业人士，旨在提升老年人健康水平和生活质量，促进社区健康和社会参与。'
# text = "南山慢病院公共卫生部前身为业务科，在2017 年11月成立，2023 年 4 月更名为公共卫生部。集行政管理与防治业务管理为一体，负责多项工作统筹协调和质量控制。承担全市老年人心理关爱项目牵头单位等职责，对多个项目开展督导培训和质控考核。科室现有12 人，中级以上职称 4 人，硕士研究生以上学历 6 人，均为公共卫生相关专业技术人员。"
# prompt = """案例：
# <翻译前>一方面会影响台上表演的情绪，另一方面你争我夺看秩序怎么维持啊。</翻译前>
# <翻译后>一方面會影響臺上表演嘅情緒另方面你爭我奪去睇秩序點樣維持啊</翻译后>
# <翻译前>@text</翻译前>
# <翻译后>"""
# prompt = prompt.replace('@text', text)
# llm_parameter = {
#     "model": "zs-gpt",
#     "messages": [
#         {
#             "role": "system",
#             "content": "你是一名粤语语言专家，直接明了的给出翻译结果"
#         },
#         {
#             "role": "user",
#             "content": prompt,
#         }], "max_tokens": 1024,
#     "temperature": 0.001}
# yue_model.run(llm_parameter=llm_parameter)
