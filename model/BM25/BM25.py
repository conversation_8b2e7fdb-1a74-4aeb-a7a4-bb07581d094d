import math
import numpy as np
from multiprocessing import Pool, cpu_count
import jieba
import pandas as pd
import collections
import hnswlib
import faiss
from setting import ARCCSE_PRE_RANK_HNSW_EF

# 只加浮点限制66t/s
# ES200加浮点限制89t/s     精准率99.5%
# ES50加浮点限制118t/s     精准率98.7%
# 更换tfidf召回200     143t/s    精准率98%
# 更换python版本BM25 召回200      140t/s      精准率99.6%


"""
All of these algorithms have been taken from the paper:
<PERSON><PERSON><PERSON><PERSON> et al, Improvements to BM25 and Language Models Examined

Here we implement all the BM25 variations mentioned. 
"""

class baseBM25:
    def __init__(self, corpus, tokenizer=None):
        self.corpus_size = len(corpus)
        self.avgdl = 0
        self.doc_freqs = []
        self.idf = {}
        self.doc_len = []
        self.tokenizer = tokenizer

        if tokenizer:
            corpus = self._tokenize_corpus(corpus)

        nd = self._initialize(corpus)
        self._calc_idf(nd)

    def _initialize(self, corpus):
        nd = {}  # word -> number of documents with word
        num_doc = 0
        for document in corpus:
            self.doc_len.append(len(document))
            num_doc += len(document)

            frequencies = {}
            for word in document:
                if word not in frequencies:
                    frequencies[word] = 0
                frequencies[word] += 1
            self.doc_freqs.append(frequencies)

            for word, freq in frequencies.items():
                try:
                    nd[word]+=1
                except KeyError:
                    nd[word] = 1

        self.avgdl = num_doc / self.corpus_size
        return nd

    def _tokenize_corpus(self, corpus):
        pool = Pool(cpu_count())
        tokenized_corpus = pool.map(self.tokenizer, corpus)
        return tokenized_corpus

    def _calc_idf(self, nd):
        raise NotImplementedError()

    def get_scores(self, query):
        raise NotImplementedError()

    def get_batch_scores(self, query, doc_ids):
        raise NotImplementedError()

    def get_top_n(self, query, documents, n=5):

        assert self.corpus_size == len(documents), "The documents given don't match the index corpus!"

        scores = self.get_scores(query)
        top_n = np.argsort(scores)[::-1][:n]
        return [documents[i] for i in top_n]

class BM25Okapi(baseBM25):
    def __init__(self, corpus, tokenizer=None, k1=1.5, b=0.75, epsilon=0.25):
        self.k1 = k1
        self.b = b
        self.epsilon = epsilon
        super().__init__(corpus, tokenizer)

    def _calc_idf(self, nd):
        """
        Calculates frequencies of terms in documents and in corpus.
        This algorithm sets a floor on the idf values to eps * average_idf
        """
        # collect idf sum to calculate an average idf for epsilon value
        idf_sum = 0
        # collect words with negative idf to set them a special epsilon value.
        # idf can be negative if word is contained in more than half of documents
        negative_idfs = []
        for word, freq in nd.items():
            idf = math.log(self.corpus_size - freq + 0.5) - math.log(freq + 0.5)
            self.idf[word] = idf
            idf_sum += idf
            if idf < 0:
                negative_idfs.append(word)
        self.average_idf = idf_sum / len(self.idf)

        eps = self.epsilon * self.average_idf
        for word in negative_idfs:
            self.idf[word] = eps

    def get_scores(self, query):
        """
        The ATIRE BM25 variant uses an idf function which uses a log(idf) score. To prevent negative idf scores,
        this algorithm also adds a floor to the idf value of epsilon.
        See [Trotman, A., X. Jia, M. Crane, Towards an Efficient and Effective Search Engine] for more info
        :param query:
        :return:
        """
        score = np.zeros(self.corpus_size)
        doc_len = np.array(self.doc_len)
        for q in query:
            q_freq = np.array([(doc.get(q) or 0) for doc in self.doc_freqs])
            score += (self.idf.get(q) or 0) * (q_freq * (self.k1 + 1) /
                                               (q_freq + self.k1 * (1 - self.b + self.b * doc_len / self.avgdl)))
        return score

    def get_batch_scores(self, query, doc_ids):
        """
        Calculate bm25 scores between query and subset of all docs
        """
        assert all(di < len(self.doc_freqs) for di in doc_ids)
        score = np.zeros(len(doc_ids))
        doc_len = np.array(self.doc_len)[doc_ids]
        for q in query:
            q_freq = np.array([(self.doc_freqs[di].get(q) or 0) for di in doc_ids])
            score += (self.idf.get(q) or 0) * (q_freq * (self.k1 + 1) /
                                               (q_freq + self.k1 * (1 - self.b + self.b * doc_len / self.avgdl)))
        return score.tolist()


class BM25model:
    def __init__(self,doc):
        self.doc = doc #不小写了
        self.token_doc = [jieba.lcut(i) for i in self.doc]
        self.bm25 = BM25Okapi(self.token_doc)
        # self.init_FREQ = jieba.dt.FREQ.copy()
        for i in self.bm25.idf.keys():
            jieba.add_word(i)
        # self.userdict = jieba.dt.FREQ.copy()
        # jieba.dt.FREQ = self.init_FREQ

    def search(self,sentence,topn = 200,overlap_score=False):
        #加载BM25的用户分词词典
        #not for sure this kind of memories copy's problem
        if overlap_score:
            # jieba.dt.FREQ = self.userdict
            tokens = jieba.lcut(sentence)
            # jieba.dt.FREQ = self.init_FREQ
        else:
            tokens = jieba.lcut(sentence)

        out = self.bm25.get_top_n(tokens, self.doc, n=topn)

        if overlap_score:
            out = pd.DataFrame({'sentence':out})
            out['bm_score'] = list(range(len(out)))[::-1]
            out['overlap_score'] = out['sentence'].apply(lambda x:len(set(list(sentence)) & set(list(x))))
            out['sentence_len'] = out['sentence'].apply(lambda x: -len(x))
            out = out.sort_values(['overlap_score', 'bm_score', 'sentence_len'], ascending=False)
            return out[out['overlap_score']>0]['sentence'].tolist()
        else:
            return out


class InvertedIndex:
    """
    倒排索引
    """
    def __init__(self, documents:list, stop_words=None):
        self.documents = {i:{'query':j}for i, j in enumerate(documents)}
        self.stop_words = stop_words if stop_words is not None else []
        self.vocabulary = self.__build_vocab()
        self.word_index = {word: [] for word in self.vocabulary}
        self.__build_index()

    def __build_vocab(self):
        """构建词表"""
        vocabulary = []
        for index, doc in self.documents.items():
            # words =jieba.lcut(str(doc['query']).strip())
            words = [w for w in doc["query"]]
            self.documents[index]['seg_query'] = words
            vocabulary.extend(words)
        word_count = collections.Counter(vocabulary)
        return list(word_count.keys())

    def __build_index(self):
        """构建索引"""
        for k, doc in self.documents.items():
            query = doc['seg_query']
            counter = collections.Counter(query)
            for word, count in counter.items():
                if word not in self.word_index:
                    continue
                self.word_index[word].append(k)

    def search(self, query, topk=200):
        """
        问句索引
        """
        # query = jieba.lcut(query.strip())
        query = [w for w in query]
        index_counter = collections.Counter()
        for word in query:
            index = self.word_index.get(word, [])
            index_counter.update(index)

        if len(index_counter) == 0:
            index_counter.update(list(range(min(len(self.documents), topk))))

        docs = [self.documents[i]['query'] for i, _ in index_counter.most_common(topk)]
        return docs

class HNSWModel:
    def __init__(self, documents: list, embeddings):
        self.ef_construction = 200
        self.M = 16
        self.ef = ARCCSE_PRE_RANK_HNSW_EF
        self.space = "cosine" # "l2" "cosine"

        self.documents = documents
        num_elements, dim = embeddings.shape
        ids = np.arange(num_elements)

        # Declaring index
        self.p = hnswlib.Index(space=self.space, dim=dim) # possible options are l2, cosine or ip

        # Initializing index - the maximum number of elements should be known beforehand
        self.p.init_index(max_elements=num_elements, ef_construction=self.ef_construction, M=self.M)

        self.p.add_items(embeddings, ids)
        self.p.set_ef(self.ef)

    def search(self, query, top_k=200, return_emb=True):
        """
        问句索引
        """
        labels, distances = self.p.knn_query(query, k=top_k)
        docs = [self.documents[i] for i in labels[0]]
        if return_emb:
            embeddings = np.array(self.p.get_items(labels[0]))
            embeddings = embeddings.astype(np.float16)
            return docs, embeddings
        else:
            return docs, distances
    
    def get_max_topk(self, max_search=None):
        """
        获取最大topk
        max_search是业务上可能检索的最大数量。
        """
        max_topk = 0
        test_emb = np.random.rand(1, self.p.dim).astype(np.float16)

        if max_search:
            big_topk = min(max_search, self.p.max_elements)
        else:
            big_topk = self.p.max_elements
        min_topk = 1
        # 二分查找
        while min_topk <= big_topk:
            mid_topk = (min_topk + big_topk) // 2
            try:
                _ = self.p.knn_query(test_emb, k=mid_topk)
                if mid_topk > max_topk:
                    max_topk = mid_topk
                min_topk = mid_topk + 1
            except:
                big_topk = mid_topk - 1
        return max_topk

class FAISSModel:
    def __init__(self, documents:list, embeddings):
        dim, measure = embeddings.shape[1], faiss.METRIC_L2
        # param =  'IVF1000, PQ13'
        param =  'HNSW64'
        # param = "LSH"
        self.faiss_index = faiss.index_factory(dim, param, measure)
        self.faiss_index.train(embeddings.astype(np.float32))
        self.faiss_index.add(embeddings.astype(np.float32))
        self.documents = documents

    def search(self, query, topk=200):
        """
        问句索引
        """
        _, I = self.faiss_index.search(query, topk)
        docs = [self.documents[i] for i in I[0, :]]
        return docs


if __name__ == "__main__":
    from database.REDIS import REDIS
    from tqdm import tqdm

    R = REDIS()
    data_faq = R.get_data(f'faq_model1_all')

    documents2id = {}
    for data in data_faq:
        labelid = data["labelId"]
        documents2id[data["title"].strip()] = labelid
        for text in data["labelData"].split("||"):
            documents2id[text.strip()] = labelid

    model = BM25model(list(documents2id.keys()))

    k_list = [10, 20, 50, 100, 150, 200]
    all_count = 0
    right_count_list = [0] * len(k_list)
    max_k = max(k_list)

    pbar = tqdm(documents2id.items())
    for query, labelid in pbar:
        return_query = model.search(query, max_k+1)
        return_labelid = [documents2id[q] for q in return_query if q != query]
        for i, k in enumerate(k_list):
            if labelid in return_labelid[:k]:
                right_count_list[i] += 1
        all_count += 1
        print_str = ""
        for r in right_count_list:
            print_str += "%.3f " % (r/all_count)
        pbar.set_postfix(acc=print_str)

    for i, k in enumerate(k_list):
        print(f"{k}: {right_count_list[i] / all_count}")

    while True:
        i = input("输入：")
        print(model.search(i, 200))
    print("wait")
