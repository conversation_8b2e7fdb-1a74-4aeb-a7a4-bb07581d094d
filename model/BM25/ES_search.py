import pandas as pd
from tqdm import tqdm
import os
import hashlib
from elasticsearch import Elasticsearch, client, helpers
import setting

database_info = {
    "mappings": {
        "properties": {
            "question": {  # 内容
                "type": "text",
                # "analyzer": "ik_max_word",
                # "search_analyzer": "ik_smart"
            }
        }
    }
}

class ES:
    def __init__(self):
        if isinstance(setting.ES_HOST, str):
            setting.ES_HOST = [setting.ES_HOST]
        self.es = Elasticsearch(setting.ES_HOST, http_auth=f"{setting.ES_USERNAME}:{setting.ES_PASSWORD}")
        self.ic = client.IndicesClient(self.es)

    def train(self,model_id,doc):
        index = 'es_filechat_' + model_id
        if index in list(self.es.indices.get_alias().keys()):
            print(index,'已存在')
            self.ic.delete(index=index)
        self.ic.create(index=index, body=database_info)

        output = []
        for data in tqdm(doc):
            output.append({'_source': {'question': str(data)}})
        helpers.bulk(self.es, output, index=index)

    def search(self,model_id,query, topn=5):
        index = 'es_filechat_' + model_id
        condition = {"query":{"match":{"question":query}},"size":topn}
        ret = self.es.search(index=index, body=condition)
        output = [i['_source']['question'] for i in ret['hits']['hits']]
        return output

    def md5(self,text):
        return hashlib.md5(text.encode(encoding='UTF-8')).hexdigest()


if __name__=='__main__':
    # path = r'../doc/test2.txt'
    # # path = r"D:\work\jupyter_notebook\hongan\手工标注filechat文档问题\text\专业影视广告公司策划书.txt"
    # doc1 = open(path, 'r', encoding='utf-8').readlines()
    # doc1 = [i.strip() for i in doc1 if i.strip()]
    doc1 = ['你好']
    model1 = ES()
    model_id = 'test'
    model1.train(model_id,doc1)
    # for i in model1.search('方案三年在员工工资上的投入有多少',10):print(i)
    result = model1.search(model_id,'你好',10)
    for index,i in enumerate(result):print(index,i)






