from tqdm import tqdm
from elasticsearch import Elasticsearch, client, helpers
import setting

database_info = {
    "settings": {
        "number_of_shards": 3,
        "number_of_replicas": 2
    },
    "mapping": {
        "_doc": {
            "properties": {
                "knowledge_detail_id": {
                    "type": "long"
                },
                "full_file_name": {
                    "type": "text"
                },
                "file_text": {
                    "type": "text"
                },
                "order": {
                    "type": "long"
                }
            }
        }
    }
}

class ES:
    def __init__(self):
        self.es = Elasticsearch(setting.ES_HOST, http_auth=f"{setting.ES_USERNAME}:{setting.ES_PASSWORD}")

    def train(self,index,knowledge_detail_id,full_file_name,file_texts):
        output = []
        for order,file_text in enumerate(tqdm(file_texts)):
            output.append({'_source': {'knowledge_detail_id': knowledge_detail_id,'full_file_name': full_file_name,
                                       'file_text':file_text,'order': order}})
        helpers.bulk(self.es, output, index=index)

    def search(self,index,query, topn=5):
        condition = {"query": {"match": {"file_text": query}}, "size": topn}
        ret = self.es.search(index=index, body=condition)
        output = [i['_source']['file_text'] for i in ret['hits']['hits']]
        return output


if __name__=='__main__':
    # path = r'../doc/test2.txt'
    # # path = r"D:\work\jupyter_notebook\hongan\手工标注filechat文档问题\text\专业影视广告公司策划书.txt"
    # doc1 = open(path, 'r', encoding='utf-8').readlines()
    # doc1 = [i.strip() for i in doc1 if i.strip()]
    es = ES()
    # def train(index,knowledge_detail_id,full_file_name,file_texts):
    index = 'skm_283345398'
    knowledge_detail_id = 12345
    full_file_name = 'test.txt'
    file_texts = ['很好']

    def train(index,knowledge_detail_id,full_file_name,file_texts):
        output = []
        for order,file_text in enumerate(tqdm(file_texts)):
            output.append({'_source': {'knowledge_detail_id': knowledge_detail_id,'full_file_name': full_file_name,
                                       'file_text':file_text,'order': order}})
        helpers.bulk(es.es, output, index=index)


    #search
    condition = {"query": {"match": {"file_text": '很好'}}, "size": 2}
    ret = es.es.search(index=index, body=condition)
    output = [i['_source']['file_atext'] for i in ret['hits']['hits']]




