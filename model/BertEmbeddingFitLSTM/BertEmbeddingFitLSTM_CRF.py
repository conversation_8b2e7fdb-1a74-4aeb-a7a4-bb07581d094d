#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file: BERT
@time: 2023/4/21
"""
import numpy as np
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "1"

import jieba
import pandas as pd
import tensorflow as tf
from transformers import TFBertModel, Bert<PERSON>okenizer, BertConfig
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)  # 限制GPU内存自增长
from utils.my_utils import my_cosine_similarity
from tensorflow.keras.models import Model
from tf2crf import CRF
from setting import MAIN_DIR

# model_path = r'/data/sammy/project/aicc-nlu/pretrain_model/roberta_chinese_clue_tiny_anto'
model_path = f'{MAIN_DIR}/pretrain_model/roberta_chinese_clue_tiny'
# model_path = r'D:\develop\aicc-nlp-nlu\pretrain_model\roberta_chinese_clue_tiny'
# model_path = r'/data/sammy/bert/chinese_roberta_wwm_ext_pytorch'

class BertEmbeddingFitLSTM_CRF(tf.keras.Model):
    def __init__(self,label_num):
        self.label_num = label_num
        super(BertEmbeddingFitLSTM_CRF, self).__init__()
        self.sentence_max_len = 32
        self.doc_max_len = 128
        self.embedding_shape = 312 #312
        self.emb_layer = [1, -1]
        self.config = BertConfig.from_pretrained(model_path)
        self.tokenizer = BertTokenizer.from_pretrained(os.path.join(model_path, 'vocab.txt'))
        self.bert = self._load_model()
        # self.attention = self._load_model()
        # self.lstm_model = self.lstm()
        self.lstm_crf_model = self.lstm_crf()

    def __call__(self, inputs, **kwargs):
        input_ids = inputs["input_ids"]
        token_type_ids = inputs.get("token_type_ids", None)
        attention_mask = inputs.get("attention_mask", None)
        length_mask = inputs.get("length_mask", None)

        embs = self.get_encoder_layer(input_ids, token_type_ids,attention_mask,length_mask)
        embs = tf.reshape(embs,[-1,self.doc_max_len,self.embedding_shape])
        output = self.lstm_crf_model(embs)
        return output

    def lstm(self):
        x_input = tf.keras.layers.Input(shape=(self.doc_max_len, self.embedding_shape))
        gru1 = tf.keras.layers.Bidirectional(tf.keras.layers.GRU(256, return_sequences=True, dropout=0.3))(x_input)
        # gru2 = tf.keras.layers.Bidirectional(tf.keras.layers.GRU(256, return_sequences=True, dropout=0.3))(gru1)
        fn1 = tf.keras.layers.Dense(128, activation='relu')(gru1)
        drop1 = tf.keras.layers.Dropout(0.2)(fn1)
        fn2 = tf.keras.layers.Dense(64, activation='relu')(drop1)
        drop2 = tf.keras.layers.Dropout(0.2)(fn2)
        N = tf.keras.layers.LayerNormalization()(drop2)
        out = tf.keras.layers.Dense(self.label_num, activation='softmax')(N)
        model = Model([x_input], outputs=out)
        return model

    def lstm_crf(self):
        x_input = tf.keras.layers.Input(shape=(self.doc_max_len, self.embedding_shape))
        gru1 = tf.keras.layers.Bidirectional(tf.keras.layers.GRU(256, return_sequences=True, dropout=0.3))(x_input)
        # gru2 = tf.keras.layers.Bidirectional(tf.keras.layers.GRU(256, return_sequences=True, dropout=0.3))(gru1)
        fn1 = tf.keras.layers.Dense(128, activation='relu')(gru1)
        drop1 = tf.keras.layers.Dropout(0.2)(fn1)
        fn2 = tf.keras.layers.Dense(64, activation='relu')(drop1)
        drop2 = tf.keras.layers.Dropout(0.2)(fn2)
        N = tf.keras.layers.LayerNormalization()(drop2)
        crf = CRF(units=self.label_num, dtype='float32')
        crf.accuracy_fn = None
        out = crf(N)[1]
        logit = tf.keras.layers.Softmax()(out)
        model = Model([x_input], outputs=logit)
        return model

    def _load_model(self):
        try:
            model = TFBertModel.from_pretrained(model_path, from_pt=False)
        except:
            model = TFBertModel.from_pretrained(model_path, from_pt=True)

        # model = TFBertModel.from_pretrained('hfl/rbt4-h312')
        inputs = self.get_data(["测试一下"], max_len=128)
        input = {
            "input_ids": inputs['input_ids'],
            "token_type_ids": inputs['token_type_ids'],
            'attention_mask': inputs['attention_mask']
        }
        model(input)
        # model.load_weights(os.path.join(model_path, 'tf_model.h5'))
        return model

    def text_to_embedding(self, texts):
        embeddings_norm = self.encode_sentences(texts)
        return embeddings_norm

    def get_encoder_layer(self, input_ids, token_type_ids,attention_mask, length_mask=None, length_div=None, texts = None,**kwargs):
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            output_hidden_states=True,
            **kwargs
        )
        hidden_states = outputs[2]

        if isinstance(self.emb_layer, str):
            emb = hidden_states[-1][:, 0, :]
        elif isinstance(self.emb_layer, list):
            if length_mask is not None and length_div is not None:
                emb = None
                for layer in self.emb_layer:
                    emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
                emb = emb / len(self.emb_layer)  # 不同层输出的均值
                emb = emb * length_mask

                #tfidf权重
                emb = tf.reduce_sum(emb, axis=1)
                emb = emb / length_div
            else:
                emb = None
                for layer in self.emb_layer:
                    emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
                emb = emb / len(self.emb_layer)
                emb = tf.reduce_mean(emb, axis=1)
        else:
            raise Exception(f"{self.__class__.__name__} 参数出错: emb_layer {self.emb_layer}")
        return emb

    def get_data(self, texts, max_len, return_tensor=False):
        if isinstance(texts, str):
            texts = [texts]
        data = {
            "input_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "token_type_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "attention_mask": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "length_mask": np.zeros(shape=(len(texts), max_len, self.config.hidden_size), dtype=np.float32),
            "length_div": np.ones(shape=(len(texts), self.config.hidden_size), dtype=np.float32)
        }

        for i, text in enumerate(texts):
            inputs = self.tokenizer.encode_plus(text, add_special_tokens=True, max_length=max_len, truncation=True, padding="max_length")
            data["input_ids"][i, :] = inputs["input_ids"]
            data["token_type_ids"][i, :] = inputs["token_type_ids"]
            data["attention_mask"][i, :] = inputs["attention_mask"]

        lengths = np.sum(data["attention_mask"], -1) - 2
        for i, l in enumerate(lengths):
            data["length_mask"][i, 1:l + 1, :] = 1
            if l < 1:
                raise Exception("句子长度有问题")
        for i, l in enumerate(lengths):
            data["length_div"][i, :] = l

        if return_tensor:
            data["input_ids"] = tf.convert_to_tensor(data["input_ids"])
            data["token_type_ids"] = tf.convert_to_tensor(data["token_type_ids"])
            data["attention_mask"] = tf.convert_to_tensor(data["attention_mask"])
            data["length_mask"] = tf.convert_to_tensor(data["length_mask"])
            data["length_div"] = tf.convert_to_tensor(data["length_div"])
        # data["length_div"] = None
        # data["length_mask"] = None
        return data

    def encode_sentences(self, texts):
        embeddings = list()
        for start_idx in range(0, len(texts), 64):
            end_idx = min(start_idx + 64, len(texts))
            inputs = self.get_data(texts=texts[start_idx:end_idx], max_len=self.sentence_max_len, return_tensor=True)
            inputs['texts'] = texts[start_idx:end_idx]
            emb = self.get_encoder_layer(**inputs)
            embeddings.append(emb.numpy())

        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings




if '__main__'==__name__:
    doc = pd.read_excel(r'../../data/doc手动标注数据/train.xlsx')['raw_string'].tolist()[:128]
    model2 = BertEmbeddingFitLSTM_CRF(2)
    input_dict2 = model2.get_data(doc,128,return_tensor=True)
    output2 = model2(input_dict2)


    from model.BertEmbeddingFitLSTM.BertEmbeddingFitLSTM import BertEmbeddingFitLSTM
    model1 = BertEmbeddingFitLSTM(2)
    input_dict1 = model1.get_data(doc,128,return_tensor=True)
    output1 = model1(input_dict1)



