import openai
import time
from setting import logger
from concurrent.futures import ThreadPoolExecutor, wait, ALL_COMPLETED


class CallOpenAI:
    def __init__(self, use_azure=True, model_name="gpt-35-turbo"):
        self.use_azure = use_azure
        if use_azure:
            model_name = model_name.replace("gpt-3.5-turbo", "gpt-35-turbo")
        else:
            model_name = model_name.replace("gpt-35-turbo", "gpt-3.5-turbo")
        self.model_name = model_name
        if use_azure:
            # Azure
            openai.api_key = "********************************"
            openai.api_base = "https://openai-kb-qnzs.openai.azure.com"
            openai.api_type = "azure"
            openai.api_version = "2023-03-15-preview"
        else:
            # OpenAI
            # openai.api_key = "sk-1dQ90SghmXSEcpAG3001Dc9585054d50A33fE71e5bF66554"
            # openai.api_base = "https://gpt.717.buzz/v1"

            # openai.api_key = "sk-JzTlBjinBTy6OQbSC9849a94C7354f3eB35c9cB0566662B6"
            # openai.api_base = "https://gpt.717.buzz/v1"

            openai.api_key = "sk-hzwueEZCZAVAaOHR048e32CdA2Ef4b51B9458a5e3cCa0140"
            openai.api_base = "https://gpt.717.buzz/v1"

            # # GPT4
            # openai.api_key = "sk-02vAoAHtJKUc4xnaEa517d6cB2E647B79d5dCc5c423b140e"
            # openai.api_base = "https://www.plus9.plus/v1"
            # openai.api_key = "sk-j5axpBIXNNHI3WTSC3B5E030E2724a089578C426A016B046"
            # openai.api_base = "https://lonlie.plus7.plus/"

    def run(self, user_prompt, system_prompt="", retry_times=3, assert_func=None):
        """
        retry_times: 失败后是否重试,0不重试
        assert_func: 判断openai返回结果是否符合要求的函数,assert_func返回True表示符合要求,为空不判断。
                     如果多次判断都有错误,则返回第一次结果
        """
        fail_times = 0
        res_list = []
        while fail_times <= retry_times:
            try:
                messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}]
                if self.use_azure:
                    res = openai.ChatCompletion.create(engine=self.model_name, messages=messages)
                else:
                    res = openai.ChatCompletion.create(model=self.model_name, messages=messages)
                res = res.choices[0]["message"]["content"]
                if assert_func is not None:
                    if assert_func(res):
                        return res
                    else:
                        logger.debug(f"生成结果不符合assert_func要求")
                        res_list.append(res)
                else:
                    return res
                fail_times += 1
            except Exception as e:
                fail_times += 1
                logger.debug(f"调用OPENAI出错:{e}")
                time.sleep(0.05)
        if len(res_list):
            return res_list[0]

    def run_parallel(self, user_prompt_list, system_prompt_list=None, retry_times=3, assert_func=None, n_threads=10):
        """
        并发调用run
        user_prompt_list: list,多个prompt
        system_prompt_list: list,可以为空
        retry_times: 失败后是否重试,0不重试
        assert_func: 判断openai返回结果是否符合要求的函数,assert_func返回True表示符合要求,为空不判断。
                     如果多次判断都有错误,则返回第一次结果
        """

        def func_(user_p_, system_p_, retry_times_, assert_func_, result_list_, result_idx_):
            res_ = self.run(user_prompt=user_p_, system_prompt=system_p_, retry_times=retry_times_, assert_func=assert_func_)
            result_list_[result_idx_] = res_

        result_list = [""] * len(user_prompt_list)
        if system_prompt_list is None:
            system_prompt_list = [""] * len(user_prompt_list)
        executor = ThreadPoolExecutor(n_threads)
        all_task_list = []

        for i in range(len(user_prompt_list)):
            while executor._work_queue.qsize() > n_threads:
                time.sleep(0.1)
            all_task_list.append(executor.submit(lambda p: func_(**p), {
                "user_p_": user_prompt_list[i],
                "system_p_": system_prompt_list[i],
                "retry_times_": retry_times,
                "assert_func_": assert_func,
                "result_list_": result_list,
                "result_idx_": i}))
        wait(all_task_list, return_when=ALL_COMPLETED)
        return result_list

    def stream_run(self):
        pass

if __name__ == "__main__":
    model = CallOpenAI()

    def assert_func_1(res_string):
        import json
        try:
            temp = json.loads(res_string)
            assert isinstance(temp, dict) and "answer2" in temp
            return True
        except:
            return False

    def assert_func_2(res_string):
        import json
        try:
            temp = json.loads(res_string)
            assert isinstance(temp, dict) and "answer" in temp
            return True
        except:
            return False

    res_list = []
    user_prompt = """当前问题的参考资料包括: \n### 文档1:青牛智胜-员工手册-第二版.pdf\n内容1:\n环境、快速成长的优秀平台、敏捷高效的工作流程………\n内容2:\n成就客户：致力于客户的满意与成功，做客户的好帮手。\n文档2:智能客服系统操作手册.docx\n内容1:\n4.2.2.1 新增意图\n新增意图的方式可分为单个新增和批量新增，单个新增时，可先选择添加的分类后，再点击新增，按照表单要求完成知识创建后，保存即完成意图的新增。若需要批量新增时，则需要按照导入模板进行导入，可实现批量新增或更新现有的意图标准问。\n全量导入模板：适用场景：（1）批量导入新的意图��（2）对已有意图先导出，再修改，后导入，实现批量更新分类、正则、语料、有效期、启用状态等；\n增量导入：适用场景：为已有的意图批量新增语料；\n4.2.2.2 意图搜索\n可根据标准问、语料、生效状态、累计命中次数、有效期、更新时间、更新人、启用状态对意图列表进行检索\n4.2.2.3  删除意图\n点击‘删除’按钮，弹出确认删除提示框，点击‘确认’后删除当前意图；点击‘取消’取消当前操作。也可以选择多个意图点击批量操作进行批量删除\n4.2.2.3 意图导出\n支持将系统中已有的意图进行导出，可选择全部导出或选择部分内容导出。\n4.3 闲聊管理\n4.3.1 需求描述\n闲聊管理可配置用户可能会询到的非业务性的问题，如：你好，你是谁等，通过配置闲聊知识，可让机器人更为拟人，避免过于机械只能回复业务知识。\n内容2:\n4.2.2.1 新增意圖\n新增意圖的方式可分為單個新增和批量新增，單個新增時，可先選擇添加的分類後，再點擊新增，按照表單要求完成知識創建後，保存即完成意圖的新增。若需要批量新增時，則需要按照導入模板進行導入，可實現批量新增或更新現有的意圖標準問。\n全量導入模板：適用場景：（1）批量導入新的意圖；（2）對已有意圖先導出，再修改，後導入，實現批量更新分類、正則、語料、有效期、啟用狀態等；\n增量導入：適用場景：為已有的意圖批量新增語料；\n4.2.2.2 意圖搜索\n可根據標準問、語料、生效狀態、累計命中次數、有效期、更新時間、更新人、啟用狀態對意圖列表進行檢索\n4.2.2.3  刪除意圖\n點擊‘刪除’按鈕，彈出確認刪除提示框，點擊‘確認’後刪除當前意圖；點擊‘取消’取消當前操作。也可以選擇多個意圖點擊批量操作進行批量刪除\n4.2.2.3 意圖導出\n支持將系統中已有的意圖進行導出，可選擇全部導出或選擇部分內容導出。\n4.3 閑聊管理\n4.3.1 需求描述\n閑聊管理可配置用戶可能會詢到的非業務性的問題，如：你好，你是誰等，通過配置閑聊知識，可讓機器人更為擬人，避免過於機械只能回覆業務知識。\n ###\n----------\n\n这是一项文档测试任务，测试从参考资料找到正确的文档内容并回复，看看你是否可以根据给定的资料而不是你所知道的来回答用户的问题。\n我需要你忘记你所知道的一切，忘记你所学到的一切，你所了解的只是上面提到资料内容。\n现在，你只能根据上述聊天历史和参考资料来回答当前用户的问题，因为不依赖这些会导致毫无根据地给出答案。 \n注意：如果你在资料中找不到确切的答案，那么你需要澄清并告诉用户为什么，只有当你确信你能从上面的信息中找到答案时，你才能提供答案，否则，使用“可能”这样的词会更安全。 \n注意：如果客户是和你打招呼，你只需要热情礼貌回复用户，回复尽量详细。\n注意：参考资料里的内容可能是一个目录。 \n\n输出格式: {"answer": "你必须使用中文回答", "quote": "文档 id, 内容 id，如果资料中没有答案或者不是从资料中得到答案的，quote保留空即可"}\n\n当前用户问题：你好\n"""
    for _ in range(10):
        res_1 = model.run(user_prompt=user_prompt)
        res_list.append(res_1)

    res_list_2 = []
    user_prompt = """当前问题的参考资料包括: \n### 文档1:青牛智胜-员工手册-第二版.pdf\n内容1:\n环境、快速成长的优秀平台、敏捷高效的工作流程………\n内容2:\n成就客户：致力于客户的满意与成功，做客户的好帮手。\n文档2:智能客服系统操作手册.docx\n内容1:\n4.2.2.1 新增意图\n新增意图的方式可分为单个新增和批量新增，单个新增时，可先选择添加的分类后，再点击新增，按照表单要求完成知识创建后，保存即完成意图的新增。若需要批量新增时，则需要按照导入模板进行导入，可实现批量新增或更新现有的意图标准问。\n全量导入模板：适用场景：（1）批量导入新的意图��（2）对已有意图先导出，再修改，后导入，实现批量更新分类、正则、语料、有效期、启用状态等；\n增量导入：适用场景：为已有的意图批量新增语料；\n4.2.2.2 意图搜索\n可根据标准问、语料、生效状态、累计命中次数、有效期、更新时间、更新人、启用状态对意图列表进行检索\n4.2.2.3  删除意图\n点击‘删除’按钮，弹出确认删除提示框，点击‘确认’后删除当前意图；点击‘取消’取消当前操作。也可以选择多个意图点击批量操作进行批量删除\n4.2.2.3 意图导出\n支持将系统中已有的意图进行导出，可选择全部导出或选择部分内容导出。\n4.3 闲聊管理\n4.3.1 需求描述\n闲聊管理可配置用户可能会询到的非业务性的问题，如：你好，你是谁等，通过配置闲聊知识，可让机器人更为拟人，避免过于机械只能回复业务知识。\n内容2:\n4.2.2.1 新增意圖\n新增意圖的方式可分為單個新增和批量新增，單個新增時，可先選擇添加的分類後，再點擊新增，按照表單要求完成知識創建後，保存即完成意圖的新增。若需要批量新增時，則需要按照導入模板進行導入，可實現批量新增或更新現有的意圖標準問。\n全量導入模板：適用場景：（1）批量導入新的意圖；（2）對已有意圖先導出，再修改，後導入，實現批量更新分類、正則、語料、有效期、啟用狀態等；\n增量導入：適用場景：為已有的意圖批量新增語料；\n4.2.2.2 意圖搜索\n可根據標準問、語料、生效狀態、累計命中次數、有效期、更新時間、更新人、啟用狀態對意圖列表進行檢索\n4.2.2.3  刪除意圖\n點擊‘刪除’按鈕，彈出確認刪除提示框，點擊‘確認’後刪除當前意圖；點擊‘取消’取消當前操作。也可以選擇多個意圖點擊批量操作進行批量刪除\n4.2.2.3 意圖導出\n支持將系統中已有的意圖進行導出，可選擇全部導出或選擇部分內容導出。\n4.3 閑聊管理\n4.3.1 需求描述\n閑聊管理可配置用戶可能會詢到的非業務性的問題，如：你好，你是誰等，通過配置閑聊知識，可讓機器人更為擬人，避免過於機械只能回覆業務知識。\n ###\n----------\n\n这是一项文档测试任务，测试从参考资料找到正确的文档内容并回复，看看你是否可以根据给定的资料而不是你所知道的来回答用户的问题。\n我需要你忘记你所知道的一切，忘记你所学到的一切，你所了解的只是上面提到资料内容。\n现在，你只能根据上述聊天历史和参考资料来回答当前用户的问题，因为不依赖这些会导致毫无根据地给出答案。 \n注意：如果你在资料中找不到确切的答案，那么你需要澄清并告诉用户为什么，只有当你确信你能从上面的信息中找到答案时，你才能提供答案，否则，使用“可能”这样的词会更安全。 \n注意：如果客户是和你打招呼，你只需要热情礼貌回复用户，回复尽量详细。\n注意：参考资料里的内容可能是一个目录。 \n\n输出格式: {"answer": "你必须使用中文回答", "quote": "文档 id, 内容 id，如果资料中没有答案或者不是从资料中得到答案的，quote保留空即可"}\n\n当前用户问题：意图搜索可以搜索哪些内容\n"""
    for _ in range(10):
        res_1 = model.run(user_prompt=user_prompt)
        res_list_2.append(res_1)
    print("wait")

    user_prompt_main_list = ["请问1+1等于几？\n请返回json结构,字段包括answer\n"] * 10

    start_time = time.time()
    for user_prompt_main in user_prompt_main_list:
        res_1 = model.run(user_prompt=user_prompt_main)
        res_2 = model.run(user_prompt=user_prompt_main, retry_times=1, assert_func=assert_func_1)
        res_3 = model.run(user_prompt=user_prompt_main, retry_times=1, assert_func=assert_func_2)
    print(f"非并发耗时:{time.time()-start_time}")

    start_time = time.time()
    res_1_p = model.run_parallel(user_prompt_list=user_prompt_main_list)
    res_2_p = model.run_parallel(user_prompt_list=user_prompt_main_list, retry_times=1, assert_func=assert_func_1)
    res_3_p = model.run_parallel(user_prompt_list=user_prompt_main_list, retry_times=1, assert_func=assert_func_2)
    print(f"并发耗时:{time.time()-start_time}")
    print("wait")
