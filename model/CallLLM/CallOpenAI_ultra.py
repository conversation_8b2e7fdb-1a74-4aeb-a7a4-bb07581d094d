from openai import OpenAI
import time
from setting import logger
# import logging as logger
from concurrent.futures import ThreadPoolExecutor, wait, ALL_COMPLETED

class CallOpenAI:
    def __init__(self,model_name,base_url,api_key):
        self.client = OpenAI(base_url=base_url,api_key=api_key)
        self.model_name = model_name

    def run(self,query=None,llm_parameter=None, retry_times=3, assert_func=None,return_raw=False):
        """
        llm_parameter:{"messages": [
                                {"role":"system", "content":"你是一名旅游顾问"},#如果传了就用system的prompt
                                {"role":"user", "content": "我计划7月中旬出发，请给我一份旅游计划" }
                              ],
                                "temperature": 0.3,
                                "stream": False,
                                "max_tokens":12123,
                                "top_k":3,
                                "top_p":0.85
                              }
        retry_times: 失败后是否重试,0不重试
        assert_func: 判断openai返回结果是否符合要求的函数,assert_func返回True表示符合要求,为空不判断。
                     如果多次判断都有错误,则返回第一次结果
        """
        fail_times = 0
        res_list = []
        raw_res = {}
        if llm_parameter==None:
            llm_parameter = {}
        while fail_times <= retry_times:
            try:
                llm_parameter.pop('model', None)
                llm_parameter.pop('top_k', None)
                llm_parameter['stream'] = False
                if query is not None:
                    llm_parameter['messages'] = llm_parameter.get('messages', []) + [{"role": "user", "content": query}]
                    llm_parameter['temperature'] = llm_parameter.get('temperature', 0.1)

                if llm_parameter['messages'][0].get('role', 'system') == 'system':
                    if llm_parameter['messages'][0]['content'] == None:
                        llm_parameter['messages'] = llm_parameter['messages'][1:]
                raw_res = self.client.chat.completions.create(model=self.model_name, **llm_parameter)
                res = raw_res.choices[0].message.content
                if assert_func is not None:
                    if assert_func(res):
                        if return_raw:
                            return raw_res.dict()
                        else:
                            return res
                else:
                    if return_raw:
                        return raw_res.dict()
                    else:
                        return res
            except Exception as e:
                fail_times += 1
                logger.error(f"调用OPENAI出错:{e}",exc_info=True)
                time.sleep(0.05)


    def run_parallel(self, user_prompt_list, system_prompt_list=None, retry_times=3, assert_func=None, n_threads=10):
        """
        并发调用run
        user_prompt_list: list,多个prompt
        system_prompt_list: list,可以为空
        retry_times: 失败后是否重试,0不重试
        assert_func: 判断openai返回结果是否符合要求的函数,assert_func返回True表示符合要求,为空不判断。
                     如果多次判断都有错误,则返回第一次结果
        """

        def func_(user_p_, system_p_, retry_times_, assert_func_, result_list_, result_idx_):
            res_ = self.run(user_prompt=user_p_, system_prompt=system_p_, retry_times=retry_times_, assert_func=assert_func_)
            result_list_[result_idx_] = res_

        result_list = [""] * len(user_prompt_list)
        if system_prompt_list is None:
            system_prompt_list = [""] * len(user_prompt_list)
        executor = ThreadPoolExecutor(n_threads)
        all_task_list = []

        for i in range(len(user_prompt_list)):
            while executor._work_queue.qsize() > n_threads:
                time.sleep(0.1)
            all_task_list.append(executor.submit(lambda p: func_(**p), {
                "user_p_": user_prompt_list[i],
                "system_p_": system_prompt_list[i],
                "retry_times_": retry_times,
                "assert_func_": assert_func,
                "result_list_": result_list,
                "result_idx_": i}))
        wait(all_task_list, return_when=ALL_COMPLETED)
        return result_list

    def stream_run(self,query=None,llm_parameter={},return_raw=False):
        llm_parameter.pop('model',None)
        llm_parameter.pop('top_k', None)
        llm_parameter['stream'] = True
        if query is not None:
            messages =  llm_parameter.get('messages',[])
            if len(messages)>0:
                if messages[-1].get('role','system') == 'user':
                    messages = messages[:-1] #如果最后一个是user,则去掉
            llm_parameter['messages'] = messages + [{"role":"user", "content":query}]
        if llm_parameter['messages'][0].get('role','system') == 'system':
            if llm_parameter['messages'][0]['content']==None:
                llm_parameter['messages'] = llm_parameter['messages'][1:]
        logger.info('stream_run llm_parameter:{}'.format(llm_parameter))
        stream = self.client.chat.completions.create(model=self.model_name,**llm_parameter)
        answer = ''
        start = time.time()
        encoding = True
        for chunk in stream:
            if encoding:
                encoding = False
                logger.info('stream_run encoding time:{}'.format(time.time()-start))
            content = chunk.choices[0].delta.content
            if content!=None:
                answer +=chunk.choices[0].delta.content
            if return_raw:
                chunk = chunk.model_dump()
                chunk['answer'] = answer
                print(answer,'answeranswer')
                yield chunk
            else:
                yield answer


if __name__ == "__main__":

    # base_url = "http://region-3.seetacloud.com:19965/v1/"
    # base_url = "http://region-3.seetacloud.com:19965/v1/"
    # base_url = "http://region-3.seetacloud.com:19965/v1/"
    base_url = "http://region-3.seetacloud.com:46181/v1/"
    base_url = 'https://u116511-a8bb-12b33b8c.cqa1.seetacloud.com:8443/v1'
    api_key = "EMPTY"
    asd = """角色设定：
你是一位擅长扮演各种市民角色的AI助手，能够根据不同的情境灵活切换身份。你的任务是接听骗子电话，并尽可能长时间地与骗子周旋，拖延时间，获取更多信息，同时避免被骗子识破。
任务目标：
拖延时间：尽可能延长通话时间，消耗骗子的耐心和资源。
获取信息：在不暴露自己的前提下，尝试获取骗子的个人信息、诈骗手法等。
避免被识破：保持自然，扮演好当前角色，避免引起骗子的怀疑。
请君入瓮：适当的假装自己被骗到了，接着话题聊上2句，再继续跟骗子周旋。

角色选择：
你是一个中年男人：忙碌但好奇，有一定的警惕心，说话比较简短，表达口语化。会以工作忙为借口拖延，但是要尽量配合骗子的操作。

对话策略：
装傻充愣：假装对骗子的说法不太理解，反复询问细节，拖延时间。
表现出兴趣但犹豫：让骗子觉得你有可能上钩，但总是以各种理由推迟行动。
转移话题：适时将话题转移到无关紧要的事情上，打乱骗子的节奏。
假装配合：在关键时刻假装配合骗子的要求，但总是“操作失误”或“遇到问题”。
反问骗子：通过反问骗子问题，让对方感到压力，甚至暴露更多信息。

注意事项：
保持自然：语气、用词要符合当前角色的身份，避免过于生硬或机械。
避免暴露：不要直接揭穿骗子，也不要表现出明显的怀疑或敌意。
灵活应变：根据骗子的反应及时调整策略，确保对话能够持续进行。
无旁白对话：直接以角色身份与骗子对话，不添加任何旁白或说明。
模拟真实反应：尽量模拟普通市民的真实反应，避免过于主动或积极回应，让骗子主导对话，你只需配合。

总结：
你的目标是通过扮演不同角色，与骗子斗智斗勇，尽可能拖延时间并获取信息，同时避免被识破。记住，保持自然和灵活应变是关键！
说话长度要短，10个字内，且说话要停顿，有卡顿，随机多一些空格，有的句子是第一个字停顿，有的是第三个字停顿，说太多就不真实了，真人说话都比较短
你的角色是市民，回复内容不要带入旁白，现在开始骗子的电话已经接入，请说：喂"""

    our_model = CallOpenAI('zs-gpt', base_url=base_url, api_key=api_key)
    result = our_model.stream_run(asd + ',百度是什么公司')
    start = time.time()
    for i in result:
        print(time.time() - start,i.replace(' ',''))
        pass

    1+'1'

    a = '审查员在开始实质审查后，首先要仔细阅读申请文件，并\n充分了解背景技术整体状况，力求准确地理解发明。\n重点在于\n了解发明所要解决的技术问题，理解解决所述技术问题的技术\n方案和该技术方案所能带来的技术效果，并且明确该技术方案\n的全部必要技术特征，特别是其中区别于背景技术的特征，进\n而明确发明相对于背景技术所作出的改进。\n审查员在阅读和理\n解发明时，可以作必要的记录，便于进一步审查。\n（ 2-105 ） 237\n4.3 不必检索即可发出审查意见通知书的情况\n专利申请的全部主题明显属于本部分第七章第 10 节情形\n的，审查员不必检索即可发出第一次审查意见通知书。\n应当指出的是，如果申请中只有部分主题属于上述情形，\n而其他主题不属于上述情形，则应当对不属于上述情形的其他\n主题进行检索后再发出第一次审查意见通知书。\n4.4 对缺乏单一性申请的处理\n专利申请缺乏单一性的缺陷有时是明显的，有时要通过检\n索与审查后才能确定。\n缺乏单一性的缺陷既可能存在于相互并\n列的独立权利要求之间，也可能因所引用的独立权利要求不具\n备新颖性或创造性而存在于相互并列的从属权利要求之间，还\n可能存在于一项权利要求的多个并列技术方案之间。\n法 31.1\n细则 39 及 48.2\n对于缺乏单一性的申请，审查员可以采用下述之一的方法\n（ 1 ）先通知申请人修改\n审查员在阅读申请文件时，立即能判断出申请的主题之间\n明显缺乏单一性的，可以暂缓进行检索（参见本部分第七章第\n9 . 2 . 1 节（ 1 ） ） ，先向申请人发出分案通知书，通知申请人在指\n定的两个月期限内对其申请进行修改。\n（ 2 ）检索后再通知申请人修改\n检索后才能确定申请的主题之间缺乏单一性的，审查员可\n以视情况决定是暂缓进一步检索和审查还是继续进一步检索\n和审查（参见本部分第七章第 9.2.2 节） ：\n如果经检索和审查后认为第一独立权利要求或者其从属\n权利要求具有被授权的前景，而其他独立权利要求与该有授权\n前景的权利要求之间缺乏单一性，则审查员可以暂缓对其他独\n立权利要求的检索和审查，并且在第一次审查意见通知书中只\n针对第一独立权利要求或者其从属权利要求提出审查意见，同\n时要求申请人删除或者修改权利要求，以克服申请缺乏单一性\n的缺陷。\n如果经检索和审查后确认第一独立权利要求和其从属权\n利要求没有授权前景，而其他的独立权利要求之间缺乏单一\n性，审查员可以暂缓对其他独立权利要求的检索和审查，在第\n238 （ 2-106 ）\n一次审查意见通知书中指出第一独立权利要求和其从属权利\n要求没有授权前景的同时，指出该专利申请缺乏单一性的缺\n陷；也可以继续检索和审查其他独立权利要求，尤其是当检索\n领域非常接近或者在很大程度上重叠时，并在第一次审查意见\n通知书中，同时指出单一性缺陷和其他缺陷（参见本部分第七\n章第 9 . 2 . 2 节（ 1 ）或（ 2 ） ） 。\n如果申请人按照第一次审查意见通知书的要求，对申请进\n行了符合本章第 5 . 2 节规定的修改，且权利要求书已不存在缺\n乏单一性的缺陷，审查员应当对该权利要求书继续进行审查。\n对于因独立权利要求不具备新颖性或创造性而导致其相\n互并列的从属权利要求之间缺乏单一性的情况，参照上述（ 1 ）\n或（ 2 ）的方式处理。\n应当注意的是，有时申请的主题之间虽然缺乏单一性，特\n别是因独立权利要求不具备新颖性或创造性而导致其相互并\n列的从属权利要求之间缺乏单一性，但是它们所对应的检索领\n域非常接近，或者在很大程度上是重叠的，在这种情况下，审\n查员最好一并检索和审查这些权利要求，在审查意见通知书中\n指出这些权利要求不符合专利法及其实施细则的其他规定的\n缺陷，同时指出申请缺乏单一性的缺陷，以利于节约审查程序\n（参见本部分第七章第 9 . 2 . 1 节（ 2 ） ） 。\n细则 48.2 无论申请属于上述第（ 1 ） 、 （ 2 ）项中的哪一种情形，申请\n人都应当在指定的期限内，对其申请进行修改，例如对权利要\n求书进行限制，以克服单一性缺陷。\n申请人期满不答复的，该\n申请被视为撤回。\n申请人在答复中对审查员关于申请缺乏单一性的论点提\n出了反对意见，审查员认为反对意见成立，或者申请人修改了\n权利要求书并克服了单一性缺陷的，申请的审查程序应当继续\n进行；反对意见不成立，或者未消除单一性缺陷的，审查员可\n以根据专利法第三十八条的有关规定驳回该申请。\n4.5 检 索\n每一件发明专利申请在被授予专利权之前都应当进行检\n如何确定检索的技术领域及如何进行检索，参见本部分第\n七章的内容。\n（ 2-107 ） 239\n4.6 优先权的核实\n4.6.1 需要核实优先权的情况\n审查员应当在检索后确定是否需要核实优先权。\n当检索得\n到的所有对比文件的公开日都早于申请人所要求的优先权日\n\n-----------------\n\n[[citation:2]]：\n查员认为专利申请的部分内容属于上述情形之一，而又难以从\n该申请中分割出来时，在初步审查中可不作处理，留待实质审\n查时处理。\n52 （ 1-40 ）\n7.5 根据专利法第三十一条第一款的审查\n根据专利法第三十一条第一款的规定，一件发明专利申请\n应当限于一项发明，属于一个总的发明构思的两项以上的发\n明，可以作为一件申请提出。\n细则 48.2 初步审查中，只有当一件专利申请包含了两项以上完全不\n相关联的发明时，审查员才需发出审查意见通知书，通知申请\n人修改其专利申请，使其符合单一性规定；申请人无正当理由\n而又拒绝对其申请进行修改的，审查员可以作出驳回决定。\n7.6 根据专利法第三十三条的审查\n根据专利法第三十三条的规定，申请人可以对其专利申请\n文件进行修改。\n但是，对专利申请文件的修改不得超出原说明\n书和权利要求书记载的范围。\n初步审查中，只有当审查员发出了审查意见通知书，要求\n申请人修改申请文件时，才需对申请人就此作出的修改是否明\n显超出原说明书和权利要求书记载范围进行审查。\n修改明显超\n范围的，例如申请人修改了数据或者扩大了数值范围，或者增\n加了原说明书中没有相应文字记载的技术方案的权利要求，或\n者增加一页或者数页原说明书或者权利要求中没有记载的发\n明的实质内容，审查员应当发出审查意见通知书，通知申请人\n该修改不符合专利法第三十三条的规定，申请人陈述意见或者\n补正后仍不符合规定的，审查员可以作出驳回决定。\n在初步审查程序中， 申请人根据专利法实施细则第五十七条\n的规定提出了主动修改文本的， 审查员除对补正书进行形式审查\n外， 仅需对主动修改的提出时机是否符合专利法实施细则第五十\n七条的规定进行核实。\n符合规定的， 作出合格的处理意见后存档；\n不符合规定的，作出供实质审查参考的处理意见后存档。\n对主动\n修改文本的内容不进行审查，留待实质审查时处理。\n7.7 根据专利法实施细则第二十条的审查\n在说明书中，不得使用与技术无关的词句，也不得使用商\n业性宣传用语以及贬低或者诽谤他人或者他人产品的词句，但\n客观地指出背景技术所存在的技术问题不应当认为是贬低行\n为。\n说明书中应当记载发明的技术内容。\n说明书明显不符合上\n述规定的，审查员应当发出审查意见通知书，说明理由，并通\n（ 1-41 ） 53\n知申请人在指定期限内陈述意见或者补正；申请人未在指定期\n限内答复的，审查员应当发出视为撤回通知书；申请人陈述意\n见或者补正后仍不符合规定的，审查员可以作出驳回决定。\n初步审查中，只要说明书中描述了发明的部分技术特征，\n并且形式上符合本章第 4 . 2 节的规定，对其他实质性问题不必\n审查，留待实质审查时处理。\n7.8 根据专利法实施细则第二十二条的审查\n权利要求书应当记载发明的技术特征。\n权利要求书中不得使用与技术方案的内容无关的词句，例\n如“请求保护该专利的生产、销售权”等，不得使用商业性宣\n传用语，也不得使用贬低他人或者他人产品的词句。\n初步审查中，权利要求书明显不符合上述规定的，审查员\n应当发出审查意见通知书，说明理由，并通知申请人在指定期\n限内陈述意见或者补正；申请人未在指定期限内答复的，审查\n员应当发出视为撤回通知书；申请人陈述意见或者补正后仍不\n符合规定的，审查员可以作出驳回决定。\n7.9 根据专利法实施细则第十一条的审查\n对发明专利申请是否符合专利法实施细则第十一条规定\n的审查，适用《规范申请专利行为的规定》 。\n8. 依职权修改\n根据专利法实施细则第五十七条第四款的规定，对于发明\n专利申请文件中文字和符号的明显错误，审查员可以在初步审\n查合格之前依职权进行修改，并通知申请人。\n依职权修改的常\n见情形如下：\n（ 1 ）请求书：修改申请人地址或者联系人地址中漏写、错\n写或者重复填写的省（自治区、直辖市） 、市、邮政编码等信\n息。\n（ 2 ）权利要求书和说明书：改正明显的文字错误和标点符\n号错误，修改明显的文本编辑错误，删除明显多余的信息。\n但\n是，'
    start = time.time()
    # rr = our_model.stream_run(a)
    # for i in rr:
    #     print(time.time() - start)
    #     pass
    # base_url = "https://api3.apifans.com/v1/"
    # api_key = "sk-zoNxcqd9VWsGnFVD348e78181dE24525B171Bc3fBcB978F3"
    # our_model = CallOpenAI('gpt-4o', base_url=base_url, api_key=api_key)


    q = """facts:数字化管理_1718173957485_1719487206211.docx:\n第五章 议事规则\n第十二条 科技与创新委员会根据需要可不定期召开会议，并于会议召开前三天通知全体委员，会议由主任委员召集并主持，主任委员不能出席时可委托一名委员主持。\n紧急情况下可不受上述通知时限限制。\n第十三条 委员会会议须有三分之二以上的委员（包括以书面形式委托其他委员出席会议的委员）出席方可举行；每一名委员有一票的表决权；会议做出的决议，必须经全体委员的过半数通过。\n第十四条 委员会委员可以亲自出席会议，也可以委托其他委员代为出席会议并行使表决权，委托其他委员代为出席会议并行使表决权的，应向会议主持人提交授权委托书，授权委托书应注明委托权限、委托事项并经委托人和被委托人双方签字。\n授权委托书应不迟于会议表决前提交给会议主持人。\n每一名委员最多接受一名委员委托。\n委员会委员既不亲自出席会议，也未委托其他委员代为出席会议的，视为放弃在该次会议上的投票权。\n第十五条 委员会会议可以采取现场、通讯或现场和通讯相结合的方式召开。\n第十六条 日常工作机构成员可列席委员会会议，必要时可邀请公司董事、监事及高级管理人员列席会议。\n第十七条 公司董事会秘书负责组织、协调委员会与相关各部门的工作；董事会秘书列席委员会会议。\n第十八条 委员会会议应当有会议记录，出席会议的委员应当在会议记录上签名；会议记录由公司董事会秘书保存。\n第十九条 委员会会议通过的议案及表决结果，应以书面形式报公司董事会。\n第二十条 委员会会议的召开程序、表决方式和会议通过的议案必须遵循有关法律、法规、《公司章程》及本工作细则的规定。\n第二十一条 出席会议的委员均对会议所议事项负有保密义务，不得擅自披露有关信息。\n第六章附则\n第二十二条 除非有特别说明，本工作细则所使用的术语与《公司章程》中该等术语的含义相同。\n第二十三条 本工作细则未尽事宜，按国家有关法律、法规、规范性文件、《公司章程》的规定执行；本工作细则如与国家日后颁布的法律、法规、规范性文件或经合法程序修改后的《公司章程》相抵触时，按国家有关法律、法规、规范性文件、《公司章程》的规定执行，并及时修订后报董事会审议。\n第二十四条 本工作细则由公司董事会负责解释和修订。\n第二十五条 本工作细则自公司董事会审议通过之日起施行。\n数字化管理办法（试行）\n第一章  总则\n第一条  为了有效利用信息技术，加强公司数字化转型发展，有效整合公司的各类数据资源，提升公司竞争力，充分发挥数字化技术在企业决策、管理等方面的重要作用，切实达到技术创新和生产经营的相互融合，降低公司经营管理成本，提高工作效率，根据国家、重庆市有关法律法规和规章，按照上级主管部门相关要求，并结合公司实际，特制定本办法。\n第二条  本办法所指数字化的管理范畴主要为本公司内部的数字化发展规划、支撑公司数字化转型的软件系统建设、运维、企业软件正版化等内容。\n与此内容相关的项目，不论是单独立项，还是包含在工程、生产、科研等项目中均属于数字化的管理范畴。\n第三条  数字化项目建设应遵循国家、市及交通运输行业相关技术标准和要求，执行建设程序，坚持统一规划、分步实施、效益驱动、重点突破的方针和互联互通、资源共享、安全保密的原则，避免盲目投资和重复建设。\n第四条  本办法适用于公司各部门、中心。\n第二章  管理机构及职责\n第五条  公司数字化领导小组是公司数字化工作的领导机构，主要职责是：\n（一）成立数字化领导小组，领导层由公司主要领导和业务分管领导组成，小组成员由公司各部门、中心相关负责人组成。\n（二）贯彻落实国家方针和政策、行业有关数字化工作的法律、法规和规章。\n（三）研究制定公司数字化工作的方针、政策以及有关规章制度。\n（四）审批公司数字化相关的发展战略、总体规划、管理制度、技术标准以及实施方案，并监督实施。\n（五）定期召开数字化工作会议，听取相关进度汇报并进行工作分析与研判。\n（六）组织制定数字化培训计划。\n（七）保障数字化工作所需的资金投入和有效实施。\n（八）统筹协调公司数字化建设、管理等方面的关键问题和重大事项。\n（九）统筹管理公司数字化、企业软件正版化工作。\n第六条\t  综合管理部是公司数字化工作的归口管理部门，其主要职责是：\n（一）负责根据领导小组决议统筹组织开展公司数字化管理工作。\n（二）负责牵头组织编制公司数字化发展规划，收集汇总各部门规划需求。\n（三）负责组织开展公司年度数字化项目预算编制，收集汇总各部门数字化项目需求和实施方案。\n第七条\t  技术研发中心是公司数字化工作的技术指导部门，其主要职责是：\n（一）负责组织制订公司数字化管理规章制度，指导数字化工作的开展。\n（二）协调、落实领导小组决议，监督各部门执行情况。\n（三）负责提出公司数字化转型创新发展新技术、新需求。\n（四）配合综合管理部开展公司数字化发展规划的编制。\n-------------------------------\n\n2,数字化管理_1718173957485_1719487206211.docx:\n第二条 审计与风险委员会是公司董事会下设的专门工作机构，主要负责公司内外部审计的沟通、监督和核查工作，以及指导法治建设和合规管理工作，向董事会报告工作并对董事会负责。\n第三条 公司董事会办公室负责审计与风险委员会日常联络、资料存档。\n公司合规监管部为审计与风险委员会开展工作支持的日常办事机构，负责审计与风险委员会的会议准备及支持工作。\n第四条 审计与风险委员会由三名委员组成，委员均由外部董事担任，且至少有一名外部董事为会计专业人士。\n第五条 审计与风险委员会委员由董事长、二分之一以上外部董事或者全体董事的三分之一提名，并由董事会选举产生。\n第六条 审计与风险委员会设主任委员一名，由委员中的外部董事担任，并由董事会选举产生。\n主任委员不能或无法履行职责时， 由其指定一名外部董事委员代行其职责；主任委员既不履行职责，也不指定其他委员代行其职责时，任何一名委员均可将有关情况向公司董事会报告，由公司董事长指定一名外部董事委员代行主任委员职责。\n第七条 审计与风险委员会任期与当届董事会成员的任期一致，委员任期届满，连选可以连任。\n审计与风险委员会委员任期届满前，除非出现《公司法》《公司章程》或本细则规定的不得任职之情形，不得被无故解除职务。\n第八条 审计与风险委员会委员任期期间，如不再担任公司董事职务，则自动失去委员资格。\n审计与风险委员会委员可以在任期届满以前提出辞职，委员辞职应当向董事会提交书面辞职报告，辞职报告经董事会批准后方能生效，且在补选出的委员就任前，原委员仍应当依照本细则的规定，履行相关职责。\n第九条 审计与风险委员会因委员辞职、免职或其他原因而导致其人数减少时，公司董事会应按本细则的规定尽快选举产生新的委员。\n第十条 审计与风险委员会的主要职责权限包括：\n（一）指导和监督内部审计制度的建立和实施，监督及评估外部审计机构工作，提议聘请或更换外部审计机构；\n（二）指导内部审计工作；\n（三）审阅公司的财务报告并对其发表意见；\n（四）评估内部控制的有效性；\n（五）协调管理层、内部审计部门及相关部门与外部审计机构的沟通；\n（六）指导法治建设体系和合规管理体系建设工作；\n（七）研究合规风险识别预警机制、合规审查及论证机制等；\n（八）研究合规管理重大事项；\n（九）监督和评价合规管理工作。\n（十） 至少每年度召开一次会议，审议日常办事机构收集的相关资料以及其他有关事项；\n（十一） 至少每年度向董事会报告一次，内容包括但不限于内部审计工作进度、质量以及发现的重大问题；\n（十二）公司董事会授权的其他事宜。\n第十一条 公司董事会应充分尊重审计与风险委员会关于聘请或更换外部审计机构的建议，在无充分理由或可靠证据的情况下，董事会不得对审计与风险委员会的建议予以搁置或不予表决。\n第十二条 审计与风险委员会履行职责时，公司相关部门应给予配合，所需费用由公司承担。\n第十三条 审计与风险委员会认为必要时，可以聘请中介机构为其决策提供专业意见，费用由公司支付。\n第十四条 日常办事机构负责收集审计委员会评议涉及的相关书面资料，书面资料包括：\n（一）公司相关财务报告；\n（二）内外部审计机构的工作报告；\n（三）外部审计合同及相关工作报告；\n（四）其他相关资料。\n第十五条 日常办事机构组织、协调相关部门或中介机构提供资料后进行汇总，并将汇总后的书面资料提交至董事会办公室。\n第十六条 董事会办公室将相关资料提交审计与风险委员会主任委员审核，审核通过后及时召集审计与风险委员会会议。\n第十七条 审计与风险委员会召开会议通过报告、决议或提出建议，并以书面形式呈报公司董事会。\n对需要董事会或股东大会审议批准的，由审计与风险委员会向董事会提出提案，并按相关法律、法规及公司章程规定履行审批程序。\n第十八条 若超过半数的董事会成员对审计与风险委员会会议通过的报告、决议存在异议的，可及时向审计与风险委员会提出书面反馈意见。\n第十九条 审计与风险委员会会议分为定期会议和临时会议。\n第二十条 审计与风险委员会会议可根据情况采用现场会议的形式，也可采用传真、视频、可视电话、电话等通讯方式。\n第二十一条 定期会议应当于会议召开三日以前通知全体委员，临时会议应当在合理的时间内发出通知。\n第二十二条 审计与风险委员会应由三分之二以上委员出席方可举行。\n第二十三条 审计与风险委员会委员可以亲自出席会议，也可以委托其他委员代为出席会议并行使表决权。\n审计与风险委员会委员每次只能委托一名其他委员代为行使表决权，委托二人或二人以上代为行使表决权的，该项委托无效。\n第二十四条 审计与风险委员会如认为必要，可以召集与会议议案有关的其他人员列席会议介绍情况或发表意见，但非审计与风险委员会委员对议案没有表决权。\n-------------------------------\n\n3,数字化管理_1718173957485_1719487206211.docx:\n（十一） 至少每年度向董事会报告一次，内容包括但不限于内部审计工作进度、质量以及发现的重大问题；\n（十二）公司董事会授权的其他事宜。\n第十一条 公司董事会应充分尊重审计与风险委员会关于聘请或更换外部审计机构的建议，在无充分理由或可靠证据的情况下，董事会不得对审计与风险委员会的建议予以搁置或不予表决。\n第十二条 审计与风险委员会履行职责时，公司相关部门应给予配合，所需费用由公司承担。\n第十三条 审计与风险委员会认为必要时，可以聘请中介机构为其决策提供专业意见，费用由公司支付。\n第十四条 日常办事机构负责收集审计委员会评议涉及的相关书面资料，书面资料包括：\n（一）公司相关财务报告；\n（二）内外部审计机构的工作报告；\n（三）外部审计合同及相关工作报告；\n（四）其他相关资料。\n第十五条 日常办事机构组织、协调相关部门或中介机构提供资料后进行汇总，并将汇总后的书面资料提交至董事会办公室。\n第十六条 董事会办公室将相关资料提交审计与风险委员会主任委员审核，审核通过后及时召集审计与风险委员会会议。\n第十七条 审计与风险委员会召开会议通过报告、决议或提出建议，并以书面形式呈报公司董事会。\n对需要董事会或股东大会审议批准的，由审计与风险委员会向董事会提出提案，并按相关法律、法规及公司章程规定履行审批程序。\n第十八条 若超过半数的董事会成员对审计与风险委员会会议通过的报告、决议存在异议的，可及时向审计与风险委员会提出书面反馈意见。\n第十九条 审计与风险委员会会议分为定期会议和临时会议。\n第二十条 审计与风险委员会会议可根据情况采用现场会议的形式，也可采用传真、视频、可视电话、电话等通讯方式。\n第二十一条 定期会议应当于会议召开三日以前通知全体委员，临时会议应当在合理的时间内发出通知。\n第二十二条 审计与风险委员会应由三分之二以上委员出席方可举行。\n第二十三条 审计与风险委员会委员可以亲自出席会议，也可以委托其他委员代为出席会议并行使表决权。\n审计与风险委员会委员每次只能委托一名其他委员代为行使表决权，委托二人或二人以上代为行使表决权的，该项委托无效。\n第二十四条 审计与风险委员会如认为必要，可以召集与会议议案有关的其他人员列席会议介绍情况或发表意见，但非审计与风险委员会委员对议案没有表决权。\n第二十五条 审计与风险委员会会议对所议事项采取集中审议、依次表决的规则，即全部议案经所有与会委员审议完毕后，依照议案审议顺序对议案进行逐项表决。\n第二十六条 审计与风险委员会在对相关事项进行表决时，关联委员应当回避。\n第二十七条 审计与风险委员会进行表决时，每名委员享有一票表决权。\n审计与风险委员会所作决议应经全体委员过半数同意方为通过。\n第二十八条 审计与风险委员会进行表决时，既可采取记名投票表决方式，也可采取举手表决、通讯表决或其他表决方式。\n第二十九条 审计与风险委员会会议通过的议案及表决结果，应以书面形式报公司董事会。\n第三十条 审计与风险委员会会议应当有记录，出席会议的委员应当在会议记录上签名。\n第三十一条 日常办事机构形成会议议案资料、会议记录、会议决议提交董事会办公室，支持专门委员会向董事会报告工作；董事会办公室汇总会议档案，包括会议通知、会议签到簿、委员代为出席的授权委托书、表决票后统一存档保存。\n审计与风险委员会会议档案的保存期限为10年。\n第三十二条 在公司依法定程序将审计与风险委员会决议予以公开之前，与会委员和会议列席人员、记录和服务人员等负有对决议内容保密的义务。\n第三十三条 董事会可根据有关法律、法规和规范性文件以及《公司章程》的规定对本细则进行修改。\n第三十四条 本细则未尽事项，按有关法律、法规和规范性文件以及《公司章程》的规定执行。\n第三十五条 本细则的解释权属公司董事会。\n第三十六条 本细则自公司董事会审议通过之日起生效和实施。\n董事会提名委员会工作细则\n第一条 为规范重庆首讯科技股份有限公司董事和高级管理人员的产生，完善公司治理，根据《公司法》《上市公司治理准则》《全国中小企业股份转让系统挂牌公司治理规则》等相关法律、法规和规范性文件以及《重庆首讯科技股份有限公司章程》（以下简称“《公司章程》”）等的规定，并制定本细则。\n第二条 董事会提名委员会是公司董事会下设的专门工作机构，主要负责对公司董事和高级管理人员的人选、选择标准和程序进行研究并提出建议。\n第三条 公司董事会办公室负责提名委员会日常联络、资料存档。\n公司党群人力部为提名委员会开展工作的支持部门，负责提名委员会的会议准备及支持工作。\n第四条 提名委员会由三名委员组成，委员由董事担任，其中两名委员为外部董事。\n第五条 提名委员会委员由董事长、二分之一以上外部董事或者全体董事的三分之一提名，并由董事会选举产生。\n第六条 提名委员会设主任委员一名，由董事会选举产生。\n-------------------------------\n\n4,数字化管理_1718173957485_1719487206211.docx:\n第二十五条 审计与风险委员会会议对所议事项采取集中审议、依次表决的规则，即全部议案经所有与会委员审议完毕后，依照议案审议顺序对议案进行逐项表决。\n第二十六条 审计与风险委员会在对相关事项进行表决时，关联委员应当回避。\n第二十七条 审计与风险委员会进行表决时，每名委员享有一票表决权。\n审计与风险委员会所作决议应经全体委员过半数同意方为通过。\n第二十八条 审计与风险委员会进行表决时，既可采取记名投票表决方式，也可采取举手表决、通讯表决或其他表决方式。\n第二十九条 审计与风险委员会会议通过的议案及表决结果，应以书面形式报公司董事会。\n第三十条 审计与风险委员会会议应当有记录，出席会议的委员应当在会议记录上签名。\n第三十一条 日常办事机构形成会议议案资料、会议记录、会议决议提交董事会办公室，支持专门委员会向董事会报告工作；董事会办公室汇总会议档案，包括会议通知、会议签到簿、委员代为出席的授权委托书、表决票后统一存档保存。\n审计与风险委员会会议档案的保存期限为10年。\n第三十二条 在公司依法定程序将审计与风险委员会决议予以公开之前，与会委员和会议列席人员、记录和服务人员等负有对决议内容保密的义务。\n第三十三条 董事会可根据有关法律、法规和规范性文件以及《公司章程》的规定对本细则进行修改。\n第三十四条 本细则未尽事项，按有关法律、法规和规范性文件以及《公司章程》的规定执行。\n第三十五条 本细则的解释权属公司董事会。\n第三十六条 本细则自公司董事会审议通过之日起生效和实施。\n董事会提名委员会工作细则\n第一条 为规范重庆首讯科技股份有限公司董事和高级管理人员的产生，完善公司治理，根据《公司法》《上市公司治理准则》《全国中小企业股份转让系统挂牌公司治理规则》等相关法律、法规和规范性文件以及《重庆首讯科技股份有限公司章程》（以下简称“《公司章程》”）等的规定，并制定本细则。\n第二条 董事会提名委员会是公司董事会下设的专门工作机构\n\n你是一个非常聪明且很听话的文档索引助手,你知道的全部知识仅仅是上面的facts内容。\n你只能引用参考资料中的内容来详细分析回答，不要使用自己的知识\n如果参考资料和用户问题无关，请拒绝回答\n markdown格式输出\n用户问题：ASR内容\n\n"""
    q = """[[citation:1]]：\n审查员在开始实质审查后，首先要仔细阅读申请文件，并\n充分了解背景技术整体状况，力求准确地理解发明。\n重点在于\n了解发明所要解决的技术问题，理解解决所述技术问题的技术\n方案和该技术方案所能带来的技术效果，并且明确该技术方案\n的全部必要技术特征，特别是其中区别于背景技术的特征，进\n而明确发明相对于背景技术所作出的改进。\n审查员在阅读和理\n解发明时，可以作必要的记录，便于进一步审查。\n（ 2-105 ） 237\n4.3 不必检索即可发出审查意见通知书的情况\n专利申请的全部主题明显属于本部分第七章第 10 节情形\n的，审查员不必检索即可发出第一次审查意见通知书。\n应当指出的是，如果申请中只有部分主题属于上述情形，\n而其他主题不属于上述情形，则应当对不属于上述情形的其他\n主题进行检索后再发出第一次审查意见通知书。\n4.4 对缺乏单一性申请的处理\n专利申请缺乏单一性的缺陷有时是明显的，有时要通过检\n索与审查后才能确定。\n缺乏单一性的缺陷既可能存在于相互并\n列的独立权利要求之间，也可能因所引用的独立权利要求不具\n备新颖性或创造性而存在于相互并列的从属权利要求之间，还\n可能存在于一项权利要求的多个并列技术方案之间。\n法 31.1\n细则 39 及 48.2\n对于缺乏单一性的申请，审查员可以采用下述之一的方法\n（ 1 ）先通知申请人修改\n审查员在阅读申请文件时，立即能判断出申请的主题之间\n明显缺乏单一性的，可以暂缓进行检索（参见本部分第七章第\n9 . 2 . 1 节（ 1 ） ） ，先向申请人发出分案通知书，通知申请人在指\n定的两个月期限内对其申请进行修改。\n（ 2 ）检索后再通知申请人修改\n检索后才能确定申请的主题之间缺乏单一性的，审查员可\n以视情况决定是暂缓进一步检索和审查还是继续进一步检索\n和审查（参见本部分第七章第 9.2.2 节） ：\n如果经检索和审查后认为第一独立权利要求或者其从属\n权利要求具有被授权的前景，而其他独立权利要求与该有授权\n前景的权利要求之间缺乏单一性，则审查员可以暂缓对其他独\n立权利要求的检索和审查，并且在第一次审查意见通知书中只\n针对第一独立权利要求或者其从属权利要求提出审查意见，同\n时要求申请人删除或者修改权利要求，以克服申请缺乏单一性\n的缺陷。\n如果经检索和审查后确认第一独立权利要求和其从属权\n利要求没有授权前景，而其他的独立权利要求之间缺乏单一\n性，审查员可以暂缓对其他独立权利要求的检索和审查，在第\n238 （ 2-106 ）\n一次审查意见通知书中指出第一独立权利要求和其从属权利\n要求没有授权前景的同时，指出该专利申请缺乏单一性的缺\n陷；也可以继续检索和审查其他独立权利要求，尤其是当检索\n领域非常接近或者在很大程度上重叠时，并在第一次审查意见\n通知书中，同时指出单一性缺陷和其他缺陷（参见本部分第七\n章第 9 . 2 . 2 节（ 1 ）或（ 2 ） ） 。\n如果申请人按照第一次审查意见通知书的要求，对申请进\n行了符合本章第 5 . 2 节规定的修改，且权利要求书已不存在缺\n乏单一性的缺陷，审查员应当对该权利要求书继续进行审查。\n对于因独立权利要求不具备新颖性或创造性而导致其相\n互并列的从属权利要求之间缺乏单一性的情况，参照上述（ 1 ）\n或（ 2 ）的方式处理。\n应当注意的是，有时申请的主题之间虽然缺乏单一性，特\n别是因独立权利要求不具备新颖性或创造性而导致其相互并\n列的从属权利要求之间缺乏单一性，但是它们所对应的检索领\n域非常接近，或者在很大程度上是重叠的，在这种情况下，审\n查员最好一并检索和审查这些权利要求，在审查意见通知书中\n指出这些权利要求不符合专利法及其实施细则的其他规定的\n缺陷，同时指出申请缺乏单一性的缺陷，以利于节约审查程序\n（参见本部分第七章第 9 . 2 . 1 节（ 2 ） ） 。\n细则 48.2 无论申请属于上述第（ 1 ） 、 （ 2 ）项中的哪一种情形，申请\n人都应当在指定的期限内，对其申请进行修改，例如对权利要\n求书进行限制，以克服单一性缺陷。\n申请人期满不答复的，该\n申请被视为撤回。\n申请人在答复中对审查员关于申请缺乏单一性的论点提\n出了反对意见，审查员认为反对意见成立，或者申请人修改了\n权利要求书并克服了单一性缺陷的，申请的审查程序应当继续\n进行；反对意见不成立，或者未消除单一性缺陷的，审查员可\n以根据专利法第三十八条的有关规定驳回该申请。\n4.5 检 索\n每一件发明专利申请在被授予专利权之前都应当进行检\n如何确定检索的技术领域及如何进行检索，参见本部分第\n七章的内容。\n（ 2-107 ） 239\n4.6 优先权的核实\n4.6.1 需要核实优先权的情况\n审查员应当在检索后确定是否需要核实优先权。\n当检索得\n到的所有对比文件的公开日都早于申请人所要求的优先权日\n\n-----------------\n\n[[citation:2]]：\n查员认为专利申请的部分内容属于上述情形之一，而又难以从\n该申请中分割出来时，在初步审查中可不作处理，留待实质审\n查时处理。\n52 （ 1-40 ）\n7.5 根据专利法第三十一条第一款的审查\n根据专利法第三十一条第一款的规定，一件发明专利申请\n应当限于一项发明，属于一个总的发明构思的两项以上的发\n明，可以作为一件申请提出。\n细则 48.2 初步审查中，只有当一件专利申请包含了两项以上完全不\n相关联的发明时，审查员才需发出审查意见通知书，通知申请\n人修改其专利申请，使其符合单一性规定；申请人无正当理由\n而又拒绝对其申请进行修改的，审查员可以作出驳回决定。\n7.6 根据专利法第三十三条的审查\n根据专利法第三十三条的规定，申请人可以对其专利申请\n文件进行修改。\n但是，对专利申请文件的修改不得超出原说明\n书和权利要求书记载的范围。\n初步审查中，只有当审查员发出了审查意见通知书，要求\n申请人修改申请文件时，才需对申请人就此作出的修改是否明\n显超出原说明书和权利要求书记载范围进行审查。\n修改明显超\n范围的，例如申请人修改了数据或者扩大了数值范围，或者增\n加了原说明书中没有相应文字记载的技术方案的权利要求，或\n者增加一页或者数页原说明书或者权利要求中没有记载的发\n明的实质内容，审查员应当发出审查意见通知书，通知申请人\n该修改不符合专利法第三十三条的规定，申请人陈述意见或者\n补正后仍不符合规定的，审查员可以作出驳回决定。\n在初步审查程序中， 申请人根据专利法实施细则第五十七条\n的规定提出了主动修改文本的， 审查员除对补正书进行形式审查\n外， 仅需对主动修改的提出时机是否符合专利法实施细则第五十\n七条的规定进行核实。\n符合规定的， 作出合格的处理意见后存档；\n不符合规定的，作出供实质审查参考的处理意见后存档。\n对主动\n修改文本的内容不进行审查，留待实质审查时处理。\n7.7 根据专利法实施细则第二十条的审查\n在说明书中，不得使用与技术无关的词句，也不得使用商\n业性宣传用语以及贬低或者诽谤他人或者他人产品的词句，但\n客观地指出背景技术所存在的技术问题不应当认为是贬低行\n为。\n说明书中应当记载发明的技术内容。\n说明书明显不符合上\n述规定的，审查员应当发出审查意见通知书，说明理由，并通\n（ 1-41 ） 53\n知申请人在指定期限内陈述意见或者补正；申请人未在指定期\n限内答复的，审查员应当发出视为撤回通知书；申请人陈述意\n见或者补正后仍不符合规定的，审查员可以作出驳回决定。\n初步审查中，只要说明书中描述了发明的部分技术特征，\n并且形式上符合本章第 4 . 2 节的规定，对其他实质性问题不必\n审查，留待实质审查时处理。\n7.8 根据专利法实施细则第二十二条的审查\n权利要求书应当记载发明的技术特征。\n权利要求书中不得使用与技术方案的内容无关的词句，例\n如“请求保护该专利的生产、销售权”等，不得使用商业性宣\n传用语，也不得使用贬低他人或者他人产品的词句。\n初步审查中，权利要求书明显不符合上述规定的，审查员\n应当发出审查意见通知书，说明理由，并通知申请人在指定期\n限内陈述意见或者补正；申请人未在指定期限内答复的，审查\n员应当发出视为撤回通知书；申请人陈述意见或者补正后仍不\n符合规定的，审查员可以作出驳回决定。\n7.9 根据专利法实施细则第十一条的审查\n对发明专利申请是否符合专利法实施细则第十一条规定\n的审查，适用《规范申请专利行为的规定》 。\n8. 依职权修改\n根据专利法实施细则第五十七条第四款的规定，对于发明\n专利申请文件中文字和符号的明显错误，审查员可以在初步审\n查合格之前依职权进行修改，并通知申请人。\n依职权修改的常\n见情形如下：\n（ 1 ）请求书：修改申请人地址或者联系人地址中漏写、错\n写或者重复填写的省（自治区、直辖市） 、市、邮政编码等信\n息。\n（ 2 ）权利要求书和说明书：改正明显的文字错误和标点符\n号错误，修改明显的文本编辑错误，删除明显多余的信息。\n但\n是，可能导致原始申请文件记载范围发生变化的修改，不属于\n依职权修改的范围。\n（ 3 ）摘要：添加明显遗漏的内容，改正明显的文字错误和\n标点符号错误，删除明显多余的信息，指定摘要附图。\n54 （ 1-42 ）\n实用新型专利申请的初步审查 专利审查指南第一部分第二章\n##### 第二章 实用新型专利申请的初步审查\n根据专利法第三条和第四十条的规定，专利局受理和审查\n实用新型专利申请，经初步审查没有发现驳回理由的，作出授\n予实用新型专利权的决定，发给相应的专利证书，同时予以登\n记和公告。\n实用新型专利申请的初步审查是受理实用新型专利\n申请之后、授予专利权之前的一个必要程序。\n细则 50.1 实用新型专利申请初步审查的范围：\n（ 1 ）申请文件的形式审查，包括专利申请是否包含专利法\n\n-----------------\n\n[[citation:3]]：\n# 专利审查指南\n### 2023\n##### 国家知识产权局 制定\n#### 总 目 录\n第一部分 初步审查 **..................................................................................................................... 3**\n第一章 发明专利申请的初步审查 ...................................................................................... 13\n第二章 实用新型专利申请的初步审查 ............................................................................... 55\n第三章 外观设计专利申请的初步审查 ............................................................................... 75\n第四章 专利分类 ................................................................................................................ 106\n第二部分 实质审查 **................................................................................................................. 119**\n第一章 不授予专利权的申请 ............................................................................................. 133\n第二章 说明书和权利要求书 ............................................................................................. 144\n第三章 新 颖 性 ................................................................................................................ 167\n第四章 创 造 性 ................................................................................................................ 185\n第五章 实 用 性 ................................................................................................................ 200\n第六章 单一性和分案申请 ................................................................................................. 204\n第七章 检 索 .................................................................................................................... 218\n第八章 实质审查程序 ........................................................................................................ 233\n第九章 关于涉及计算机程序的发明专利申请审查的若干规定 ........................................ 273\n第十章 关于化学领域发明专利申请审查的若干规定 ....................................................... 307\n第十一章 关于中药领域发明专利申请审查的若干规定 ................................................... 343\n\n-----------------\n\n[[citation:4]]：\n初 步 审 查\n##### 目 录\n第一章 发明专利申请的初步审查 **.................................................................................. 13**\n1. 引 言 ............................................................................................................. 13\n2. 审查原则 ......................................................................................................... 14\n3. 审查程序 ......................................................................................................... 15\n3.1 初步审查合格 .................................................................................................. 15\n3.2 申请文件的补正 .............................................................................................. 15\n3.3 明显实质性缺陷的处理 ................................................................................... 15\n3.4 通知书的答复 .................................................................................................. 15\n3.5 申请的驳回 ...................................................................................................... 16\n3.6 前置审查与复审后的处理 ............................................................................... 16\n4. 申请文件的形式审查 ...................................................................................... 16\n4.1 请求书 ............................................................................................................. 16\n4.1.1 发明名称 ......................................................................................................... 16\n4.1.2 发明人 ............................................................................................................. 17\n4.1.3 申请人 ............................................................................................................. 17\n4.1.3.1 申请人是本国人 .............................................................................................. 17\n4.1.3.2 申请人是外国人、外国企业或者外国其他组织 ............................................. 18\n\n-----------------\n\n[[citation:5]]：\n6. 分类的步骤 ..................................................................................................... 114\n7. 对不同公布级专利申请的分类 ...................................................................... 114\n7.1 对未检索专利申请的分类 .............................................................................. 114\n10\n7.2 对已检索和审查后专利申请的分类 ............................................................... 114\n8. 特定技术主题的分类方法 .............................................................................. 114\n8.1 化合物 ............................................................................................................ 114\n8.2 化学混合物或者组合物 .................................................................................. 115\n8.3 化合物的制备或者处理 .................................................................................. 115\n8.4 设备或者方法 ................................................................................................. 115\n8.5 制造的物品 ..................................................................................................... 115\n8.6 多步骤方法、成套设备 .................................................................................. 116\n8.7 零件、结构部件 ............................................................................................. 116\n8.8 化学通式 ........................................................................................................ 116\n8.9 组合库 ............................................................................................................ 117\n11\n发明专利申请的初步审查 专利审查指南第一部分第一章\n##### 第一章 发明专利申请的初步审查\n1. 引 言\n根据《中华人民共和国专利法》 （以下简称专利法）第三\n十四条的规定，专利局收到发明专利申请后，经初步审查认为\n符合专利法要求的，自申请日起满十八个月，即行公布。\n专利\n局也可以根据申请人的请求早日公布其申请。\n发明专利申请的\n初步审查是受理发明专利申请之后、公布该申请之前的一个必\n要程序。\n发明专利申请初步审查的主要任务：\n法 26\n细则 50\n细则 51\n法 81\n细则 112 及 113\n细则 116\n（ 1 ）审查申请人提交的申请文件是否符合专利法及其实施\n细则的规定，发现存在可以补正的缺陷时，通知申请人以补正\n的方式消除缺陷，使其符合公布的条件；发现存在不可克服的\n缺陷时，发出审查意见通知书，指明缺陷的性质，并通过驳回\n的方式结束审查程序。\n（ 2 ）审查申请人在提出专利申请的同时或者随后提交的与\n专利申请有关的其他文件是否符合专利法及其实施细则的规\n定，发现文件存在缺陷时，根据缺陷的性质，通知申请人以补\n正的方式消除缺陷，或者直接作出文件视为未提交的决定。\n\n-----------------\n\n[[citation:6]]：\n设计人 ⅰ.ⅲ－ 4 . 1 . 2\n设计要素 ⅳ.ⅴ－ 5 . 2 . 6\n涉案专利 ⅳ.ⅴ－ 5 . 2 . 4 . 2\n涉及计算机程序的发明 ⅱ.ⅸ－ 1\n涉及计算机程序的发明专利申请的审查 ⅱ.ⅸ\n涉及生物材料的申请 ⅰ.ⅰ－ 5 . \n\n You are a large language AI assistant. You are given a user question, and please write clean, concise and accurate answer to the question. You will be given a set of related contexts to the question, each starting with a reference number like [[citation:x]], where x is a number. Please use the context and cite the context at the end of each sentence if applicable.\n\nYour answer must be correct, accurate and written by an expert using an unbiased and professional tone. Please keep your answer within 1024 tokens. If the provided context does not offer enough information, please use your own knowledge to answer the user question.\n\nPlease cite the contexts with the reference numbers, in the format [citation:x]. If a sentence comes from multiple contexts, please list all applicable citations, like [citation:3][citation:5]. Other than code and specific names and citations, your answer must be written in the same language as the question. \nresponse with chinese.\n\nUser question:发明专利申请初步审查的范围\n"""
    p = {'messages': [{'role': 'user', 'content': '[[citation:1]]：\n审查员在开始实质审查后，首先要仔细阅读申请文件，并\n充分了解背景技术整体状况，力求准确地理解发明。\n重点在于\n了解发明所要解决的技术问题，理解解决所述技术问题的技术\n方案和该技术方案所能带来的技术效果，并且明确该技术方案\n的全部必要技术特征，特别是其中区别于背景技术的特征，进\n而明确发明相对于背景技术所作出的改进。\n审查员在阅读和理\n解发明时，可以作必要的记录，便于进一步审查。\n（ 2-105 ） 237\n4.3 不必检索即可发出审查意见通知书的情况\n专利申请的全部主题明显属于本部分第七章第 10 节情形\n的，审查员不必检索即可发出第一次审查意见通知书。\n应当指出的是，如果申请中只有部分主题属于上述情形，\n而其他主题不属于上述情形，则应当对不属于上述情形的其他\n主题进行检索后再发出第一次审查意见通知书。\n4.4 对缺乏单一性申请的处理\n专利申请缺乏单一性的缺陷有时是明显的，有时要通过检\n索与审查后才能确定。\n缺乏单一性的缺陷既可能存在于相互并\n列的独立权利要求之间，也可能因所引用的独立权利要求不具\n备新颖性或创造性而存在于相互并列的从属权利要求之间，还\n可能存在于一项权利要求的多个并列技术方案之间。\n法 31.1\n细则 39 及 48.2\n对于缺乏单一性的申请，审查员可以采用下述之一的方法\n（ 1 ）先通知申请人修改\n审查员在阅读申请文件时，立即能判断出申请的主题之间\n明显缺乏单一性的，可以暂缓进行检索（参见本部分第七章第\n9 . 2 . 1 节（ 1 ） ） ，先向申请人发出分案通知书，通知申请人在指\n定的两个月期限内对其申请进行修改。\n（ 2 ）检索后再通知申请人修改\n检索后才能确定申请的主题之间缺乏单一性的，审查员可\n以视情况决定是暂缓进一步检索和审查还是继续进一步检索\n和审查（参见本部分第七章第 9.2.2 节） ：\n如果经检索和审查后认为第一独立权利要求或者其从属\n权利要求具有被授权的前景，而其他独立权利要求与该有授权\n前景的权利要求之间缺乏单一性，则审查员可以暂缓对其他独\n立权利要求的检索和审查，并且在第一次审查意见通知书中只\n针对第一独立权利要求或者其从属权利要求提出审查意见，同\n时要求申请人删除或者修改权利要求，以克服申请缺乏单一性\n的缺陷。\n如果经检索和审查后确认第一独立权利要求和其从属权\n利要求没有授权前景，而其他的独立权利要求之间缺乏单一\n性，审查员可以暂缓对其他独立权利要求的检索和审查，在第\n238 （ 2-106 ）\n一次审查意见通知书中指出第一独立权利要求和其从属权利\n要求没有授权前景的同时，指出该专利申请缺乏单一性的缺\n陷；也可以继续检索和审查其他独立权利要求，尤其是当检索\n领域非常接近或者在很大程度上重叠时，并在第一次审查意见\n通知书中，同时指出单一性缺陷和其他缺陷（参见本部分第七\n章第 9 . 2 . 2 节（ 1 ）或（ 2 ） ） 。\n如果申请人按照第一次审查意见通知书的要求，对申请进\n行了符合本章第 5 . 2 节规定的修改，且权利要求书已不存在缺\n乏单一性的缺陷，审查员应当对该权利要求书继续进行审查。\n对于因独立权利要求不具备新颖性或创造性而导致其相\n互并列的从属权利要求之间缺乏单一性的情况，参照上述（ 1 ）\n或（ 2 ）的方式处理。\n应当注意的是，有时申请的主题之间虽然缺乏单一性，特\n别是因独立权利要求不具备新颖性或创造性而导致其相互并\n列的从属权利要求之间缺乏单一性，但是它们所对应的检索领\n域非常接近，或者在很大程度上是重叠的，在这种情况下，审\n查员最好一并检索和审查这些权利要求，在审查意见通知书中\n指出这些权利要求不符合专利法及其实施细则的其他规定的\n缺陷，同时指出申请缺乏单一性的缺陷，以利于节约审查程序\n（参见本部分第七章第 9 . 2 . 1 节（ 2 ） ） 。\n细则 48.2 无论申请属于上述第（ 1 ） 、 （ 2 ）项中的哪一种情形，申请\n人都应当在指定的期限内，对其申请进行修改，例如对权利要\n求书进行限制，以克服单一性缺陷。\n申请人期满不答复的，该\n申请被视为撤回。\n申请人在答复中对审查员关于申请缺乏单一性的论点提\n出了反对意见，审查员认为反对意见成立，或者申请人修改了\n权利要求书并克服了单一性缺陷的，申请的审查程序应当继续\n进行；反对意见不成立，或者未消除单一性缺陷的，审查员可\n以根据专利法第三十八条的有关规定驳回该申请。\n4.5 检 索\n每一件发明专利申请在被授予专利权之前都应当进行检\n如何确定检索的技术领域及如何进行检索，参见本部分第\n七章的内容。\n（ 2-107 ） 239\n4.6 优先权的核实\n4.6.1 需要核实优先权的情况\n审查员应当在检索后确定是否需要核实优先权。\n当检索得\n到的所有对比文件的公开日都早于申请人所要求的优先权日\n\n-----------------\n\n[[citation:2]]：\n查员认为专利申请的部分内容属于上述情形之一，而又难以从\n该申请中分割出来时，在初步审查中可不作处理，留待实质审\n查时处理。\n52 （ 1-40 ）\n7.5 根据专利法第三十一条第一款的审查\n根据专利法第三十一条第一款的规定，一件发明专利申请\n应当限于一项发明，属于一个总的发明构思的两项以上的发\n明，可以作为一件申请提出。\n细则 48.2 初步审查中，只有当一件专利申请包含了两项以上完全不\n相关联的发明时，审查员才需发出审查意见通知书，通知申请\n人修改其专利申请，使其符合单一性规定；申请人无正当理由\n而又拒绝对其申请进行修改的，审查员可以作出驳回决定。\n7.6 根据专利法第三十三条的审查\n根据专利法第三十三条的规定，申请人可以对其专利申请\n文件进行修改。\n但是，对专利申请文件的修改不得超出原说明\n书和权利要求书记载的范围。\n初步审查中，只有当审查员发出了审查意见通知书，要求\n申请人修改申请文件时，才需对申请人就此作出的修改是否明\n显超出原说明书和权利要求书记载范围进行审查。\n修改明显超\n范围的，例如申请人修改了数据或者扩大了数值范围，或者增\n加了原说明书中没有相应文字记载的技术方案的权利要求，或\n者增加一页或者数页原说明书或者权利要求中没有记载的发\n明的实质内容，审查员应当发出审查意见通知书，通知申请人\n该修改不符合专利法第三十三条的规定，申请人陈述意见或者\n补正后仍不符合规定的，审查员可以作出驳回决定。\n在初步审查程序中， 申请人根据专利法实施细则第五十七条\n的规定提出了主动修改文本的， 审查员除对补正书进行形式审查\n外， 仅需对主动修改的提出时机是否符合专利法实施细则第五十\n七条的规定进行核实。\n符合规定的， 作出合格的处理意见后存档；\n不符合规定的，作出供实质审查参考的处理意见后存档。\n对主动\n修改文本的内容不进行审查，留待实质审查时处理。\n7.7 根据专利法实施细则第二十条的审查\n在说明书中，不得使用与技术无关的词句，也不得使用商\n业性宣传用语以及贬低或者诽谤他人或者他人产品的词句，但\n客观地指出背景技术所存在的技术问题不应当认为是贬低行\n为。\n说明书中应当记载发明的技术内容。\n说明书明显不符合上\n述规定的，审查员应当发出审查意见通知书，说明理由，并通\n（ 1-41 ） 53\n知申请人在指定期限内陈述意见或者补正；申请人未在指定期\n限内答复的，审查员应当发出视为撤回通知书；申请人陈述意\n见或者补正后仍不符合规定的，审查员可以作出驳回决定。\n初步审查中，只要说明书中描述了发明的部分技术特征，\n并且形式上符合本章第 4 . 2 节的规定，对其他实质性问题不必\n审查，留待实质审查时处理。\n7.8 根据专利法实施细则第二十二条的审查\n权利要求书应当记载发明的技术特征。\n权利要求书中不得使用与技术方案的内容无关的词句，例\n如“请求保护该专利的生产、销售权”等，不得使用商业性宣\n传用语，也不得使用贬低他人或者他人产品的词句。\n初步审查中，权利要求书明显不符合上述规定的，审查员\n应当发出审查意见通知书，说明理由，并通知申请人在指定期\n限内陈述意见或者补正；申请人未在指定期限内答复的，审查\n员应当发出视为撤回通知书；申请人陈述意见或者补正后仍不\n符合规定的，审查员可以作出驳回决定。\n7.9 根据专利法实施细则第十一条的审查\n对发明专利申请是否符合专利法实施细则第十一条规定\n的审查，适用《规范申请专利行为的规定》 。\n8. 依职权修改\n根据专利法实施细则第五十七条第四款的规定，对于发明\n专利申请文件中文字和符号的明显错误，审查员可以在初步审\n查合格之前依职权进行修改，并通知申请人。\n依职权修改的常\n见情形如下：\n（ 1 ）请求书：修改申请人地址或者联系人地址中漏写、错\n写或者重复填写的省（自治区、直辖市） 、市、邮政编码等信\n息。\n（ 2 ）权利要求书和说明书：改正明显的文字错误和标点符\n号错误，修改明显的文本编辑错误，删除明显多余的信息。\n但\n是，可能导致原始申请文件记载范围发生变化的修改，不属于\n依职权修改的范围。\n（ 3 ）摘要：添加明显遗漏的内容，改正明显的文字错误和\n标点符号错误，删除明显多余的信息，指定摘要附图。\n54 （ 1-42 ）\n实用新型专利申请的初步审查 专利审查指南第一部分第二章\n##### 第二章 实用新型专利申请的初步审查\n根据专利法第三条和第四十条的规定，专利局受理和审查\n实用新型专利申请，经初步审查没有发现驳回理由的，作出授\n予实用新型专利权的决定，发给相应的专利证书，同时予以登\n记和公告。\n实用新型专利申请的初步审查是受理实用新型专利\n申请之后、授予专利权之前的一个必要程序。\n细则 50.1 实用新型专利申请初步审查的范围：\n（ 1 ）申请文件的形式审查，包括专利申请是否包含专利法\n\n-----------------\n\n[[citation:3]]：\n# 专利审查指南\n### 2023\n##### 国家知识产权局 制定\n#### 总 目 录\n第一部分 初步审查 **..................................................................................................................... 3**\n第一章 发明专利申请的初步审查 ...................................................................................... 13\n第二章 实用新型专利申请的初步审查 ............................................................................... 55\n第三章 外观设计专利申请的初步审查 ............................................................................... 75\n第四章 专利分类 ................................................................................................................ 106\n第二部分 实质审查 **................................................................................................................. 119**\n第一章 不授予专利权的申请 ............................................................................................. 133\n第二章 说明书和权利要求书 ............................................................................................. 144\n第三章 新 颖 性 ................................................................................................................ 167\n第四章 创 造 性 ................................................................................................................ 185\n第五章 实 用 性 ................................................................................................................ 200\n第六章 单一性和分案申请 ................................................................................................. 204\n第七章 检 索 .................................................................................................................... 218\n第八章 实质审查程序 ........................................................................................................ 233\n第九章 关于涉及计算机程序的发明专利申请审查的若干规定 ........................................ 273\n\n You are a large language AI assistant. You are given a user question, and please write clean, concise and accurate answer to the question. You will be given a set of related contexts to the question, each starting with a reference number like [[citation:x]], where x is a number. Please use the context and cite the context at the end of each sentence if applicable.\n\nYour answer must be correct, accurate and written by an expert using an unbiased and professional tone. Please keep your answer within 1024 tokens. If the provided context does not offer enough information, please use your own knowledge to answer the user question.\n\nPlease cite the contexts with the reference numbers, in the format [citation:x]. If a sentence comes from multiple contexts, please list all applicable citations, like [citation:3][citation:5]. Other than code and specific names and citations, your answer must be written in the same language as the question. \nresponse with chinese.\n\nUser question:发明专利申请初步审查的范围\n'}], 'temperature': 0.3, 'top_p': 0.85, 'max_tokens': 450, 'stream': True}
    p = {'messages': [{'role': 'user', 'content': '[[citation:1]]：\n审查员在开始实质审查后，首先要仔细阅读申请文件，并\n充分了解背景技术整体状况，力求准确地理解发明。\n重点在于\n了解发明所要解决的技术问题，理解解决所述技术问题的技术\n方案和该技术方案所能带来的技术效果，并且明确该技术方案\n的全部必要技术特征，特别是其中区别于背景技术的特征，进\n而明确发明相对于背景技术所作出的改进。\n审查员在阅读和理\n解发明时，可以作必要的记录，便于进一步审查。\n（ 2-105 ） 237\n4.3 不必检索即可发出审查意见通知书的情况\n专利申请的全部主题明显属于本部分第七章第 10 节情形\n的，审查员不必检索即可发出第一次审查意见通知书。\n应当指出的是，如果申请中只有部分主题属于上述情形，\n而其他主题不属于上述情形，则应当对不属于上述情形的其他\n主题进行检索后再发出第一次审查意见通知书。\n4.4 对缺乏单一性申请的处理\n专利申请缺乏单一性的缺陷有时是明显的，有时要通过检\n索与审查后才能确定。\n缺乏单一性的缺陷既可能存在于相互并\n列的独立权利要求之间，也可能因所引用的独立权利要求不具\n备新颖性或创造性而存在于相互并列的从属权利要求之间，还\n可能存在于一项权利要求的多个并列技术方案之间。\n法 31.1\n细则 39 及 48.2\n对于缺乏单一性的申请，审查员可以采用下述之一的方法\n（ 1 ）先通知申请人修改\n审查员在阅读申请文件时，立即能判断出申请的主题之间\n明显缺乏单一性的，可以暂缓进行检索（参见本部分第七章第\n9 . 2 . 1 节（ 1 ） ） ，先向申请人发出分案通知书，通知申请人在指\n定的两个月期限内对其申请进行修改。\n（ 2 ）检索后再通知申请人修改\n检索后才能确定申请的主题之间缺乏单一性的，审查员可\n以视情况决定是暂缓进一步检索和审查还是继续进一步检索\n和审查（参见本部分第七章第 9.2.2 节） ：\n如果经检索和审查后认为第一独立权利要求或者其从属\n权利要求具有被授权的前景，而其他独立权利要求与该有授权\n前景的权利要求之间缺乏单一性，则审查员可以暂缓对其他独\n立权利要求的检索和审查，并且在第一次审查意见通知书中只\n针对第一独立权利要求或者其从属权利要求提出审查意见，同\n时要求申请人删除或者修改权利要求，以克服申请缺乏单一性\n的缺陷。\n如果经检索和审查后确认第一独立权利要求和其从属权\n利要求没有授权前景，而其他的独立权利要求之间缺乏单一\n性，审查员可以暂缓对其他独立权利要求的检索和审查，在第\n238 （ 2-106 ）\n一次审查意见通知书中指出第一独立权利要求和其从属权利\n要求没有授权前景的同时，指出该专利申请缺乏单一性的缺\n陷；也可以继续检索和审查其他独立权利要求，尤其是当检索\n领域非常接近或者在很大程度上重叠时，并在第一次审查意见\n通知书中，同时指出单一性缺陷和其他缺陷（参见本部分第七\n章第 9 . 2 . 2 节（ 1 ）或（ 2 ） ） 。\n如果申请人按照第一次审查意见通知书的要求，对申请进\n行了符合本章第 5 . 2 节规定的修改，且权利要求书已不存在缺\n乏单一性的缺陷，审查员应当对该权利要求书继续进行审查。\n对于因独立权利要求不具备新颖性或创造性而导致其相\n互并列的从属权利要求之间缺乏单一性的情况，参照上述（ 1 ）\n或（ 2 ）的方式处理。\n应当注意的是，有时申请的主题之间虽然缺乏单一性，特\n别是因独立权利要求不具备新颖性或创造性而导致其相互并\n列的从属权利要求之间缺乏单一性，但是它们所对应的检索领\n域非常接近，或者在很大程度上是重叠的，在这种情况下，审\n查员最好一并检索和审查这些权利要求，在审查意见通知书中\n指出这些权利要求不符合专利法及其实施细则的其他规定的\n缺陷，同时指出申请缺乏单一性的缺陷，以利于节约审查程序\n（参见本部分第七章第 9 . 2 . 1 节（ 2 ） ） 。\n细则 48.2 无论申请属于上述第（ 1 ） 、 （ 2 ）项中的哪一种情形，申请\n人都应当在指定的期限内，对其申请进行修改，例如对权利要\n求书进行限制，以克服单一性缺陷。\n申请人期满不答复的，该\n申请被视为撤回。\n申请人在答复中对审查员关于申请缺乏单一性的论点提\n出了反对意见，审查员认为反对意见成立，或者申请人修改了\n权利要求书并克服了单一性缺陷的，申请的审查程序应当继续\n进行；反对意见不成立，或者未消除单一性缺陷的，审查员可\n以根据专利法第三十八条的有关规定驳回该申请。\n4.5 检 索\n每一件发明专利申请在被授予专利权之前都应当进行检\n如何确定检索的技术领域及如何进行检索，参见本部分第\n七章的内容。\n（ 2-107 ） 239\n4.6 优先权的核实\n4.6.1 需要核实优先权的情况\n审查员应当在检索后确定是否需要核实优先权。\n当检索得\n到的所有对比文件的公开日都早于申请人所要求的优先权日\n\n-----------------\n\n[[citation:2]]：\n查员认为专利申请的部分内容属于上述情形之一，而又难以从\n该申请中分割出来时，在初步审查中可不作处理，留待实质审\n查时处理。\n52 （ 1-40 ）\n7.5 根据专利法第三十一条第一款的审查\n根据专利法第三十一条第一款的规定，一件发明专利申请\n应当限于一项发明，属于一个总的发明构思的两项以上的发\n明，可以作为一件申请提出。\n细则 48.2 初步审查中，只有当一件专利申请包含了两项以上完全不\n相关联的发明时，审查员才需发出审查意见通知书，通知申请\n人修改其专利申请，使其符合单一性规定；申请人无正当理由\n而又拒绝对其申请进行修改的，审查员可以作出驳回决定。\n7.6 根据专利法第三十三条的审查\n根据专利法第三十三条的规定，申请人可以对其专利申请\n文件进行修改。\n但是，对专利申请文件的修改不得超出原说明\n书和权利要求书记载的范围。\n初步审查中，只有当审查员发出了审查意见通知书，要求\n申请人修改申请文件时，才需对申请人就此作出的修改是否明\n显超出原说明书和权利要求书记载范围进行审查。\n修改明显超\n范围的，例如申请人修改了数据或者扩大了数值范围，或者增\n加了原说明书中没有相应文字记载的技术方案的权利要求，或\n者增加一页或者数页原说明书或者权利要求中没有记载的发\n明的实质内容，审查员应当发出审查意见通知书，通知申请人\n该修改不符合专利法第三十三条的规定，申请人陈述意见或者\n补正后仍不符合规定的，审查员可以作出驳回决定。\n在初步审查程序中， 申请人根据专利法实施细则第五十七条\n的规定提出了主动修改文本的， 审查员除对补正书进行形式审查\n外， 仅需对主动修改的提出时机是否符合专利法实施细则第五十\n七条的规定进行核实。\n符合规定的， 作出合格的处理意见后存档；\n不符合规定的，作出供实质审查参考的处理意见后存档。\n对主动\n修改文本的内容不进行审查，留待实质审查时处理。\n7.7 根据专利法实施细则第二十条的审查\n在说明书中，不得使用与技术无关的词句，也不得使用商\n业性宣传用语以及贬低或者诽谤他人或者他人产品的词句，但\n客观地指出背景技术所存在的技术问题不应当认为是贬低行\n为。\n说明书中应当记载发明的技术内容。\n说明书明显不符合上\n述规定的，审查员应当发出审查意见通知书，说明理由，并通\n（ 1-41 ） 53\n知申请人在指定期限内陈述意见或者补正；申请人未在指定期\n限内答复的，审查员应当发出视为撤回通知书；申请人陈述意\n见或者补正后仍不符合规定的，审查员可以作出驳回决定。\n初步审查中，只要说明书中描述了发明的部分技术特征，\n并且形式上符合本章第 4 . 2 节的规定，对其他实质性问题不必\n审查，留待实质审查时处理。\n7.8 根据专利法实施细则第二十二条的审查\n权利要求书应当记载发明的技术特征。\n权利要求书中不得使用与技术方案的内容无关的词句，例\n如“请求保护该专利的生产、销售权”等，不得使用商业性宣\n传用语，也不得使用贬低他人或者他人产品的词句。\n初步审查中，权利要求书明显不符合上述规定的，审查员\n应当发出审查意见通知书，说明理由，并通知申请人在指定期\n限内陈述意见或者补正；申请人未在指定期限内答复的，审查\n员应当发出视为撤回通知书；申请人陈述意见或者补正后仍不\n符合规定的，审查员可以作出驳回决定。\n7.9 根据专利法实施细则第十一条的审查\n对发明专利申请是否符合专利法实施细则第十一条规定\n的审查，适用《规范申请专利行为的规定》 。\n8. 依职权修改\n根据专利法实施细则第五十七条第四款的规定，对于发明\n专利申请文件中文字和符号的明显错误，审查员可以在初步审\n查合格之前依职权进行修改，并通知申请人。\n依职权修改的常\n见情形如下：\n（ 1 ）请求书：修改申请人地址或者联系人地址中漏写、错\n写或者重复填写的省（自治区、直辖市） 、市、邮政编码等信\n息。\n（ 2 ）权利要求书和说明书：改正明显的文字错误和标点符\n号错误，修改明显的文本编辑错误，删除明显多余的信息。\n但\n是，可能导致原始申请文件记载范围发生变化的修改，不属于\n依职权修改的范围。\n（ 3 ）摘要：添加明显遗漏的内容，改正明显的文字错误和\n标点符号错误，删除明显多余的信息，指定摘要附图。\n54 （ 1-42 ）\n实用新型专利申请的初步审查 专利审查指南第一部分第二章\n##### 第二章 实用新型专利申请的初步审查\n根据专利法第三条和第四十条的规定，专利局受理和审查\n实用新型专利申请，经初步审查没有发现驳回理由的，作出授\n予实用新型专利权的决定，发给相应的专利证书，同时予以登\n记和公告。\n实用新型专利申请的初步审查是受理实用新型专利\n申请之后、授予专利权之前的一个必要程序。\n细则 50.1 实用新型专利申请初步审查的范围：\n（ 1 ）申请文件的形式审查，包括专利申请是否包含专利法\n\n-----------------\n\n[[citation:3]]：\n# 专利审查指南\n### 2023\n##### 国家知识产权局 制定\n#### 总 目 录\n第一部分 初步审查 **..................................................................................................................... 3**\n第一章 发明专利申请的初步审查 ...................................................................................... 13\n第二章 实用新型专利申请的初步审查 ............................................................................... 55\n第三章 外观设计专利申请的初步审查 ............................................................................... 75\n第四章 专利分类 ................................................................................................................ 106\n第二部分 实质审查 **................................................................................................................. 119**\n第一章 不授予专利权的申请 ............................................................................................. 133\n第二章 说明书和权利要求书 ............................................................................................. 144\n第三章 新 颖 性 ................................................................................................................ 167\n第四章 创 造 性 ................................................................................................................ 185\n第五章 实 用 性 ................................................................................................................ 200\n第六章 单一性和分案申请 ................................................................................................. 204\n第七章 检 索 .................................................................................................................... 218\n第八章 实质审查程序 ........................................................................................................ 233\n第九章 关于涉及计算机程序的发明专利申请审查的若干规定 ........................................ 273\n\n You are a large language AI assistant. You are given a user question, and please write clean, concise and accurate answer to the question. You will be given a set of related contexts to the question, each starting with a reference number like [[citation:x]], where x is a number. Please use the context and cite the context at the end of each sentence if applicable.\n\nYour answer must be correct, accurate and written by an expert using an unbiased and professional tone. Please keep your answer within 1024 tokens. If the provided context does not offer enough information, please use your own knowledge to answer the user question.\n\nPlease cite the contexts with the reference numbers, in the format [citation:x]. If a sentence comes from multiple contexts, please list all applicable citations, like [citation:3][citation:5]. Other than code and specific names and citations, your answer must be written in the same language as the question. \nresponse with chinese.\n\nUser question:发明专利申请初步审查的范围\n'}], 'temperature': 0.3, 'top_p': 0.85, 'max_tokens': 450, 'stream': True}

    query = """
序号 | 客户名称 | 客户类型1\n（下拉选项） | 客户类型2\n（下拉选项） | 客户行业 | 客户行业 | 法律服务/项目名称 | 业务部门\n（下拉选项） | 业务细分领域 | 案源合伙人 | 承办合伙人 | 服务开始日期 | 标的额/\n涉案金额\n（币种） | 合同金额（元） | 案件状态\n（下拉选项） | 是否可公开业绩\n（下拉选项） | 是否涉外\n（下拉选项） | 收费方式\n（下拉选项） | sheet_name\n
3 | 北京振兴华龙制冷设备有限责任公司 | 非上市民企 | none | 商业&服务业 | 批发业 | 北京振兴华龙制冷设备有限责任公司与奥丁河北新能源科技有限公司房屋租赁合同纠纷 | 争议解决 | 诉讼 | 张文良 | 刘尊思 | 2023-04-01 00:00:00 | 约1亿 | 基础代理费：一审阶段5万元；二审阶段5万元，如无二审，不收取该二审费用；执行阶段不再收取基础代理费。\n风险代理：实际回款金额的5% | 正在进行 | 是 | 否 | 组合收费 | 北京
已知pandas表格样例df如上，你可以根据表格样例推断表格的其他样本，df是一个有几百行的表格，系统变量已经有df，怎么查询输出用户问句的结果:24年4月份的项目有哪些？
请只输python代码最终结果变量以result命名,请根据用户问句确定要返回的result类型（DataFrame,list,string），输出只能有代码"""
    query = '我有一系列标注切片id，他们的标注规则是：'
    start = time.time()
    llm_parameter = {'messages': [{
	'role': 'system',
	'content': '你是机器人'
}, {
	'role': 'user',
	'content': '[[citation:1]]：\n1 张图看懂公摊：\n买房子的真金白银不能被公摊吃了！\nhaitang mansion\n同样10斤橘子\n剥掉皮\n剩下多少才重要\n海棠悦府\n其他项目\n\n-----------------\n\n[[citation:2]]：\n城市之光-99m²户型 / 108m²户型\n城市之光-99m²户型\n建筑面积(m²) 套内面积(m²) 公摊面积(m²) 得房率 (%)\n98.9 71.66 27.24 72.45\n城市之光-b户型108m²\n建筑面积(m²) 套内面积(m²) 公摊面积(m²) 得房率 (%)\n108.73 78.78 29.95 72.45\n\n-----------------\n\n[[citation:3]]：\n海棠悦府-a户型116m² vs 城市之光-c户型121m²\n海棠悦府-a户型116m²\n建筑面积(m²) 套内面积(m²) 公摊面积(m²) 得房率 (%)\n116.49 88.01 28.48 75.55\n城市之光-c户型121m²\n建筑面积(m²) 套内面积(m²) 公摊面积(m²) 得房率 (%)\n121.4 87.96 33.44 72.45\n\n-----------------\n\n[[citation:4]]：\n海棠悦府-c户型136m² vs 城市之光-d户型141m²\n海棠悦府-c户型136m²\n建筑面积(m²) 套内面积(m²) 公摊面积(m²) 得房率 (%)\n136.19 106.14 30.05 77.94\n城市之光-d户型141m²\n建筑面积(m²) 套内面积(m²) 公摊面积(m²) 得房率 (%)\n139.86 108.67 31.19 77.69\n\n-----------------\n\n[[citation:5]]：\n海棠悦府-d户型143㎡ vs 城市之光-d户型141㎡\n海棠悦府-d户型143㎡\n建筑面积(m²) 套内面积(m²) 公摊面积(m²) 得房率 (%)\n142.93 115.43 27.5 80.76\n城市之光-d户型141㎡\n建筑面积(m²) 套内面积(m²) 公摊面积(m²) 得房率 (%)\n139.86 108.67 31.19 77.69\n\n用户问题:下午好小房'
}], 'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 1024,'stream': True}
    # output = our_model.stream_run(llm_parameter=llm_parameter,return_raw=True)
    # for i in output:
    #     print(i)
    # print(time.time() - start)
    # print(output,end='++++++++++++++++++++++++++++++++++++++++++++++')

    # for i in our_model.stream_run(llm_parameter=p,return_raw=True):
    #     print(i)

    # q = '请说你好'
    # q = '请问你在吗'
    # output = our_model.run(q,return_raw=True)


    # for i in our_model.stream_run(q,return_raw=True):
    #     print(i)

    # p = r"""请一直说话,说个不停，什么都可以说，说到你不能说为止，实在不行你就背诵兰亭序一百遍"""
    # for i in our_model.stream_run(p,return_raw=True):
    #     print(i)
    # llm_parameter = {"messages":[{"id":"","role":"system","content":",,"},{"id":"","role":"user","content":"[[citation:1]]：\n###文件元信息:\nfilename:A青牛介绍\n旨在帮助b端企业||建立起连接企业员工与客户沟通的桥梁，并用人工智能技术打磨高度拟人的||数字员工替代大部分简单重复性工作，实现人机高度协同闭环，||帮助企业实现数字化运营，达到企业降本增效目标。\naicc智能化平台的服务渠道\n平台||打通现有市场上的主流全媒体渠道，||除传统电话语音渠道外，还包含企业app、官网、公众号、小程序等等，让企业员工可以多渠道及时高效服务客户，||通过如文本机器人、语音机器人、质检机器人、培训机器人、数字人、坐席辅助机器人等数字员工角色参与一起服务客户\naicc智能化平台的架构模式\n平台||通过saas+paas方式提供平台能力，||在技术上采用nlp技术，||结合分布式架构、微服务、低代码等技术，||用k8s与docker部署，可根据业务量动态扩容。\naicc智能化平台产品模块间可以||积木式自由组合，企业可以根据自己的业务诉求按需采购\n在线客服、智能语音、智能质检的基本应用场景\n文本机器人可以替换企业原有基础客服人员、业务咨询人员等；||语音机器人可以替换企业原有初中级营销人员、初中级客服人员、m0-1催收员、回访员、续保专员等；||质检机器人可以替换企业原有录音或信息质检员；||培训机器人可以替换企业培训讲师等……\n公司aicc智能化平台产品迭代评估的三个纬度\n产品技术维度：已经完成了行业早期关键词、正则表达式匹配到深度神经网络学习的蜕变，公司在自己创新型算法与模型加持下，ai技术指标在项目交付及与同行业厂商poc中有可观数据证明，真实业务场景上nlp准确率稳定在95%以上，是行业内非常高的水准；||场景维度：对话式ai早期的产品多应用在客服等标准化服务场景，随着ai技术成熟与企业需求驱动，产品应用场景逐步延伸到营销推荐等半标准化应用场景，助力企业实现服务营销一体化的升级，青牛智胜在大量头部客户中积累了由客服转向营销的业务场景及服务经验；||功能维度：各个产品间可以形成闭环，替代基础性人力，辅助人工提高效率更优质服务客户，最后还可以对话洞察质检帮助企业深层挖掘数据价值，发现企业痛点，为后续的企业策略规划提供数据支撑。\n-------------------------------------------\n###文件元信息:\nfilename:A青牛介绍\nnlp技术领先：自主研发nlp核心能力除了模型准确率高，还能将人工智能先进技术应用到真实业务场景，处于业内领先水平。\n||对话式ai专家：多年深耕对话式ai方向，在制造业、消费、金融、政务、运营商、交通、医疗等垂直领域，积累了大量头部案例及行业经验，专注用人工智能赋能企业数字化运营。\n||多元化产品，当前自研的智能aicc平台包含了市场上所有智能语音相关应用产品，如nlp中台、知识图谱、数字人、智能培训、智能标注，智能语音、在线客服、智能质检等等，充分解决企业复杂业务场景需求。\n||大并发海量运营：平台采用分布式架构设计，利用集群及容器技术保障平台快速水平扩容，保障平台大并发高可用。\n||持续化运营：智胜科技拥有专业的运营团队，提供专业运营指导，持续拓展业务场景，为更多企业发展赋能。\n||部署方式灵活，支持公有云、私有云和混合云灵活部署方式。\n打造aicc智能化平台的目标\n旨在帮助b端企业||建立起连接企业员工与客户沟通的桥梁，并用人工智能技术打磨高度拟人的||数字员工替代大部分简单重复性工作，实现人机高度协同闭环，||帮助企业实现数字化运营，达到企业降本增效目标。\naicc智能化平台的服务渠道\n平台||打通现有市场上的主流全媒体渠道，||除传统电话语音渠道外，还包含企业app、官网、公众号、小程序等等，让企业员工可以多渠道及时高效服务客户，||通过如文本机器人、语音机器人、质检机器人、培训机器人、数字人、坐席辅助机器人等数字员工角色参与一起服务客户\naicc智能化平台的架构模式\n平台||通过saas+paas方式提供平台能力，||在技术上采用nlp技术，||结合分布式架构、微服务、低代码等技术，||用k8s与docker部署，可根据业务量动态扩容。\naicc智能化平台产品模块间可以||积木式自由组合，企业可以根据自己的业务诉求按需采购\n在线客服、智能语音、智能质检的基本应用场景\n文本机器人可以替换企业原有基础客服人员、业务咨询人员等；||语音机器人可以替换企业原有初中级营销人员、初中级客服人员、m0-1催收员、回访员、续保专员等；||质检机器人可以替换企业原有录音或信息质检员；||培训机器人可以替换企业培训讲师等……\n公司aicc智能化平台产品迭代评估的三个纬度\n\n-----------------\n\n[[citation:2]]：\n###文件元信息:\nfilename:青牛智胜-员工手册-YGSC004\n4.5.2丧假须在葬礼前后一周内使用。\n生育类假期（产假、产检假、陪产假、流产假、哺乳假）自然日\n5.1产假：女性员工在职期间正常生育，并符合国家生育政策，产假为98天，其中可在产前休假15天；难产的，增加产假30天；多胞胎生育的，每多生育一个婴儿，增加产假15天；符合国家法律、法规规定的，广东省额外增加80天奖励假期；\n5.2产检假：女性员工在职期间正常生育，并符合国家生育政策，可享有产检假10天。\n5.3陪产假：男性员工在职期间配偶生育三十天内，可享有连续陪产假15天，请假时须上传子女的出生证明。\n5.4流产假：女性员工在职期间怀孕流产时，根据医院的证明，公司给予流产产假；怀孕未满四个月流产，给予15天产假；怀孕满四个月以上流产,给予42天产假；\n5.5哺乳假：有不满一周岁婴儿的女性员工，在每个工作日内有两次哺乳（含人工喂养）时间，每次三十分钟；多胞胎生育的，每多哺乳一个婴儿，每次哺乳时间增加30分钟；一天内的哺乳时间可合并。\n注：\n（1）以上假期全部为自然日假期，不可补、折现等，需提供《结婚证》、《医院出生证明》、《难产生育证》等相关证明；\n以上假期产检假可根据实际情况请假；\n以上产假及陪产假均应一次休完，未休完的部分视为自动放弃；\n以上产假、产检假、陪产假、哺乳假等福利假期均在员工转正后方可享有；\n以上假期中涉及的带薪假期，带薪特指基本薪资；\n员工违反婚姻法、计划生育规定等国家法律法规，不适用本产假、哺乳假、陪产假规定；\n其他假期为连休假，自然日假期（含双休日、法定节假日）过期不补；\n如有员工离职前尚未休完年休假及调休假，可在离职前申请休完剩余年休假及调休假，若未主动申请，未休完的年休假及调休假视为自动放弃，公司不予任何形式的补偿。\n第三节 保密管理\n公司一切未经公开披露的业务信息、财务资料、人事信息、招投标信息、合同文件、客户资料、技术方案、管理文件、会议内容等均属于公司内部机密，员工均有保守秘密的职责义务，不得私自外带、复印、外传，越过公司管理红线者，一经发现将做严肃处理。\n当不确定资料是否机密，应向上级领导或人力行政部反馈，由公司鉴定其属性。\n公司员工应当对自己薪酬、奖金等相关自身利益有关的数据及材料进行保密，一旦发现私下讨论及分析者，公司将严肃处理相关人等。\n-------------------------------------------\n###文件元信息:\nfilename:青牛智胜-员工手册-YGSC004\n试用期内旷工1天及以上的；\n有任何违反公司规章制度的行为，或给公司造成经济损失及损害公司名誉的；\n其他不符合录用条件的情形。\n转正流程\n（1）试用期期满人力行政部将\n\n-----------------\n\n[[citation:3]]：empty\n\n用户问题:红烧肉怎么做"},{"id":"","role":"assistant","content":"看起来您询问的是如何制作红烧肉。下面是一个简单的红烧肉食谱供参考：\n\n### 红烧肉的做法\n\n#### 材料\n- 五花肉 500克\n- 生姜 3片\n- 葱 2根\n- 八角 2颗\n- 香叶 2片\n- 料酒 适量\n- 生抽 (酱油) 适量\n- 老抽 (深色酱油) 少许\n- 冰糖 30克\n- 清水 适量\n- 盐 适量\n\n#### 步骤\n1. **准备食材**  \n   - 五花肉切成约3厘米见方的块。\n   - 姜切片，葱切段。\n\n2. **焯水**  \n   - 锅中加水，放入五花肉块，加入几片生姜和少许料酒，大火煮开后撇去浮沫，捞出五花肉沥干水分备用。\n\n3. **炒糖色**  \n   - 另起锅，放少量油，加入冰糖小火慢慢熬至融化并变成红色糖浆状。\n   - 放入焯好的五花肉块，翻炒均匀使肉块表面裹上一层糖色。\n\n4. **调味**  \n   - 加入足够的热水（没过肉块），再加入生抽、老抽调色调味。\n   - 放入八角、香叶、剩下的生姜片和葱段，大火烧开后转小火慢炖40分钟左右。\n\n5. **收汁**  \n   - 待汤汁浓稠时，尝一下味道，根据个人口味调整盐的用量。\n   - 继续用中小火收汁至汤汁粘稠即可关火。\n\n6. **装盘**  \n   - 捞出五花肉块，摆放在盘子里，撒上一些葱花点缀。\n\n7. **享用**  \n   - 最后淋上锅中的汤汁，一道美味的红烧肉就做好了！\n\n希望这个步骤对您有所帮助！如果您还有其他烹饪方面的问题，欢迎随时提问。"},{"id":"","role":"user","content":"[[citation:1]]：\n###文件元信息:\nfilename:A青牛介绍\n旨在帮助b端企业||建立起连接企业员工与客户沟通的桥梁，并用人工智能技术打磨高度拟人的||数字员工替代大部分简单重复性工作，实现人机高度协同闭环，||帮助企业实现数字化运营，达到企业降本增效目标。\naicc智能化平台的服务渠道\n平台||打通现有市场上的主流全媒体渠道，||除传统电话语音渠道外，还包含企业app、官网、公众号、小程序等等，让企业员工可以多渠道及时高效服务客户，||通过如文本机器人、语音机器人、质检机器人、培训机器人、数字人、坐席辅助机器人等数字员工角色参与一起服务客户\naicc智能化平台的架构模式\n平台||通过saas+paas方式提供平台能力，||在技术上采用nlp技术，||结合分布式架构、微服务、低代码等技术，||用k8s与docker部署，可根据业务量动态扩容。\naicc智能化平台产品模块间可以||积木式自由组合，企业可以根据自己的业务诉求按需采购\n在线客服、智能语音、智能质检的基本应用场景\n文本机器人可以替换企业原有基础客服人员、业务咨询人员等；||语音机器人可以替换企业原有初中级营销人员、初中级客服人员、m0-1催收员、回访员、续保专员等；||质检机器人可以替换企业原有录音或信息质检员；||培训机器人可以替换企业培训讲师等……\n公司aicc智能化平台产品迭代评估的三个纬度\n产品技术维度：已经完成了行业早期关键词、正则表达式匹配到深度神经网络学习的蜕变，公司在自己创新型算法与模型加持下，ai技术指标在项目交付及与同行业厂商poc中有可观数据证明，真实业务场景上nlp准确率稳定在95%以上，是行业内非常高的水准；||场景维度：对话式ai早期的产品多应用在客服等标准化服务场景，随着ai技术成熟与企业需求驱动，产品应用场景逐步延伸到营销推荐等半标准化应用场景，助力企业实现服务营销一体化的升级，青牛智胜在大量头部客户中积累了由客服转向营销的业务场景及服务经验；||功能维度：各个产品间可以形成闭环，替代基础性人力，辅助人工提高效率更优质服务客户，最后还可以对话洞察质检帮助企业深层挖掘数据价值，发现企业痛点，为后续的企业策略规划提供数据支撑。\n-------------------------------------------\n###文件元信息:\nfilename:A青牛介绍\nnlp技术领先：自主研发nlp核心能力除了模型准确率高，还能将人工智能先进技术应用到真实业务场景，处于业内领先水平。\n||对话式ai专家：多年深耕对话式ai方向，在制造业、消费、金融、政务、运营商、交通、医疗等垂直领域，积累了大量头部案例及行业经验，专注用人工智能赋能企业数字化运营。\n||多元化产品，当前自研的智能aicc平台包含了市场上所有智能语音相关应用产品，如nlp中台、知识图谱、数字人、智能培训、智能标注，智能语音、在线客服、智能质检等等，充分解决企业复杂业务场景需求。\n||大并发海量运营：平台采用分布式架构设计，利用集群及容器技术保障平台快速水平扩容，保障平台大并发高可用。\n||持续化运营：智胜科技拥有专业的运营团队，提供专业运营指导，持续拓展业务场景，为更多企业发展赋能。\n||部署方式灵活，支持公有云、私有云和混合云灵活部署方式。\n打造aicc智能化平台的目标\n旨在帮助b端企业||建立起连接企业员工与客户沟通的桥梁，并用人工智能技术打磨高度拟人的||数字员工替代大部分简单重复性工作，实现人机高度协同闭环，||帮助企业实现数字化运营，达到企业降本增效目标。\naicc智能化平台的服务渠道\n平台||打通现有市场上的主流全媒体渠道，||除传统电话语音渠道外，还包含企业app、官网、公众号、小程序等等，让企业员工可以多渠道及时高效服务客户，||通过如文本机器人、语音机器人、质检机器人、培训机器人、数字人、坐席辅助机器人等数字员工角色参与一起服务客户\naicc智能化平台的架构模式\n平台||通过saas+paas方式提供平台能力，||在技术上采用nlp技术，||结合分布式架构、微服务、低代码等技术，||用k8s与docker部署，可根据业务量动态扩容。\naicc智能化平台产品模块间可以||积木式自由组合，企业可以根据自己的业务诉求按需采购\n在线客服、智能语音、智能质检的基本应用场景\n文本机器人可以替换企业原有基础客服人员、业务咨询人员等；||语音机器人可以替换企业原有初中级营销人员、初中级客服人员、m0-1催收员、回访员、续保专员等；||质检机器人可以替换企业原有录音或信息质检员；||培训机器人可以替换企业培训讲师等……\n公司aicc智能化平台产品迭代评估的三个纬度\n\n-----------------\n\n[[citation:2]]：\n###文件元信息:\nfilename:青牛智胜-员工手册-YGSC004\n4.5.2丧假须在葬礼前后一周内使用。\n生育类假期（产假、产检假、陪产假、流产假、哺乳假）自然日\n5.1产假：女性员工在职期间正常生育，并符合国家生育政策，产假为98天，其中可在产前休假15天；难产的，增加产假30天；多胞胎生育的，每多生育一个婴儿，增加产假15天；符合国家法律、法规规定的，广东省额外增加80天奖励假期；\n5.2产检假：女性员工在职期间正常生育，并符合国家生育政策，可享有产检假10天。\n5.3陪产假：男性员工在职期间配偶生育三十天内，可享有连续陪产假15天，请假时须上传子女的出生证明。\n5.4流产假：女性员工在职期间怀孕流产时，根据医院的证明，公司给予流产产假；怀孕未满四个月流产，给予15天产假；怀孕满四个月以上流产,给予42天产假；\n5.5哺乳假：有不满一周岁婴儿的女性员工，在每个工作日内有两次哺乳（含人工喂养）时间，每次三十分钟；多胞胎生育的，每多哺乳一个婴儿，每次哺乳时间增加30分钟；一天内的哺乳时间可合并。\n注：\n（1）以上假期全部为自然日假期，不可补、折现等，需提供《结婚证》、《医院出生证明》、《难产生育证》等相关证明；\n以上假期产检假可根据实际情况请假；\n以上产假及陪产假均应一次休完，未休完的部分视为自动放弃；\n以上产假、产检假、陪产假、哺乳假等福利假期均在员工转正后方可享有；\n以上假期中涉及的带薪假期，带薪特指基本薪资；\n员工违反婚姻法、计划生育规定等国家法律法规，不适用本产假、哺乳假、陪产假规定；\n其他假期为连休假，自然日假期（含双休日、法定节假日）过期不补；\n如有员工离职前尚未休完年休假及调休假，可在离职前申请休完剩余年休假及调休假，若未主动申请，未休完的年休假及调休假视为自动放弃，公司不予任何形式的补偿。\n第三节 保密管理\n公司一切未经公开披露的业务信息、财务资料、人事信息、招投标信息、合同文件、客户资料、技术方案、管理文件、会议内容等均属于公司内部机密，员工均有保守秘密的职责义务，不得私自外带、复印、外传，越过公司管理红线者，一经发现将做严肃处理。\n当不确定资料是否机密，应向上级领导或人力行政部反馈，由公司鉴定其属性。\n公司员工应当对自己薪酬、奖金等相关自身利益有关的数据及材料进行保密，一旦发现私下讨论及分析者，公司将严肃处理相关人等。\n-------------------------------------------\n###文件元信息:\nfilename:青牛智胜-员工手册-YGSC004\n试用期内旷工1天及以上的；\n有任何违反公司规章制度的行为，或给公司造成经济损失及损害公司名誉的；\n其他不符合录用条件的情形。\n转正流程\n（1）试用期期满人力行政部将\n\n-----------------\n\n[[citation:3]]：empty\n\n用户问题:红烧肉怎么做"},{"id":"","role":"assistant","content":"看来您对红烧肉的制作方法很感兴趣。这里有一个详细的步骤，希望能帮到您：\n\n### 红烧肉的做法\n\n#### 材料\n- 五花肉 500克\n- 生姜 3片\n- 大葱 1根（切段）\n- 八角 2颗\n- 香叶 2片\n- 料酒 适量\n- 生抽（酱油） 适量\n- 老抽（深色酱油） 少许\n- 冰糖 30克\n- 清水 适量\n- 盐 适量\n\n#### 步骤\n1. **准备食材**\n   - 五花肉切成约3厘米见方的块。\n   - 生姜切片，大葱切段。\n\n2. **焯水**\n   - 锅中加水，放入五花肉块，加入几片生姜和少许料酒，大火煮开后撇去浮沫，捞出五花肉沥干水分备用。\n\n3. **炒糖色**\n   - 另起锅，放少量油，加入冰糖小火慢慢熬至融化并变成红色糖浆状。\n   - 放入焯好的五花肉块，翻炒均匀使肉块表面裹上一层糖色。\n\n4. **调味**\n   - 加入足够的热水（没过肉块），再加入生抽、老抽调色调味。\n   - 放入八角、香叶、剩下的生姜片和大葱段，大火烧开后转小火慢炖40分钟左右。\n\n5. **收汁**\n   - 待汤汁浓稠时，尝一下味道，根据个人口味调整盐的用量。\n   - 继续用中小火收汁至汤汁粘稠即可关火。\n\n6. **装盘**\n   - 捞出五花肉块，摆放在盘子里，撒上一些葱花点缀。\n\n7. **享用**\n   - 最后淋上锅中的汤汁，一道美味的红烧肉就做好了！\n\n希望这些步骤能帮助您做出美味的红烧肉！如果还有其他烹饪方面的问题，欢迎随时提问。祝您烹饪愉快！"},{"id":"","role":"user","content":"[[citation:1]]：\n###文件元信息:\nfilename:A青牛介绍\n旨在帮助b端企业||建立起连接企业员工与客户沟通的桥梁，并用人工智能技术打磨高度拟人的||数字员工替代大部分简单重复性工作，实现人机高度协同闭环，||帮助企业实现数字化运营，达到企业降本增效目标。\naicc智能化平台的服务渠道\n平台||打通现有市场上的主流全媒体渠道，||除传统电话语音渠道外，还包含企业app、官网、公众号、小程序等等，让企业员工可以多渠道及时高效服务客户，||通过如文本机器人、语音机器人、质检机器人、培训机器人、数字人、坐席辅助机器人等数字员工角色参与一起服务客户\naicc智能化平台的架构模式\n平台||通过saas+paas方式提供平台能力，||在技术上采用nlp技术，||结合分布式架构、微服务、低代码等技术，||用k8s与docker部署，可根据业务量动态扩容。\naicc智能化平台产品模块间可以||积木式自由组合，企业可以根据自己的业务诉求按需采购\n在线客服、智能语音、智能质检的基本应用场景\n文本机器人可以替换企业原有基础客服人员、业务咨询人员等；||语音机器人可以替换企业原有初中级营销人员、初中级客服人员、m0-1催收员、回访员、续保专员等；||质检机器人可以替换企业原有录音或信息质检员；||培训机器人可以替换企业培训讲师等……\n公司aicc智能化平台产品迭代评估的三个纬度\n产品技术维度：已经完成了行业早期关键词、正则表达式匹配到深度神经网络学习的蜕变，公司在自己创新型算法与模型加持下，ai技术指标在项目交付及与同行业厂商poc中有可观数据证明，真实业务场景上nlp准确率稳定在95%以上，是行业内非常高的水准；||场景维度：对话式ai早期的产品多应用在客服等标准化服务场景，随着ai技术成熟与企业需求驱动，产品应用场景逐步延伸到营销推荐等半标准化应用场景，助力企业实现服务营销一体化的升级，青牛智胜在大量头部客户中积累了由客服转向营销的业务场景及服务经验；||功能维度：各个产品间可以形成闭环，替代基础性人力，辅助人工提高效率更优质服务客户，最后还可以对话洞察质检帮助企业深层挖掘数据价值，发现企业痛点，为后续的企业策略规划提供数据支撑。\n-------------------------------------------\n###文件元信息:\nfilename:A青牛介绍\nnlp技术领先：自主研发nlp核心能力除了模型准确率高，还能将人工智能先进技术应用到真实业务场景，处于业内领先水平。\n||对话式ai专家：多年深耕对话式ai方向，在制造业、消费、金融、政务、运营商、交通、医疗等垂直领域，积累了大量头部案例及行业经验，专注用人工智能赋能企业数字化运营。\n||多元化产品，当前自研的智能aicc平台包含了市场上所有智能语音相关应用产品，如nlp中台、知识图谱、数字人、智能培训、智能标注，智能语音、在线客服、智能质检等等，充分解决企业复杂业务场景需求。\n||大并发海量运营：平台采用分布式架构设计，利用集群及容器技术保障平台快速水平扩容，保障平台大并发高可用。\n||持续化运营：智胜科技拥有专业的运营团队，提供专业运营指导，持续拓展业务场景，为更多企业发展赋能。\n||部署方式灵活，支持公有云、私有云和混合云灵活部署方式。\n打造aicc智能化平台的目标\n旨在帮助b端企业||建立起连接企业员工与客户沟通的桥梁，并用人工智能技术打磨高度拟人的||数字员工替代大部分简单重复性工作，实现人机高度协同闭环，||帮助企业实现数字化运营，达到企业降本增效目标。\naicc智能化平台的服务渠道\n平台||打通现有市场上的主流全媒体渠道，||除传统电话语音渠道外，还包含企业app、官网、公众号、小程序等等，让企业员工可以多渠道及时高效服务客户，||通过如文本机器人、语音机器人、质检机器人、培训机器人、数字人、坐席辅助机器人等数字员工角色参与一起服务客户\naicc智能化平台的架构模式\n平台||通过saas+paas方式提供平台能力，||在技术上采用nlp技术，||结合分布式架构、微服务、低代码等技术，||用k8s与docker部署，可根据业务量动态扩容。\naicc智能化平台产品模块间可以||积木式自由组合，企业可以根据自己的业务诉求按需采购\n在线客服、智能语音、智能质检的基本应用场景\n文本机器人可以替换企业原有基础客服人员、业务咨询人员等；||语音机器人可以替换企业原有初中级营销人员、初中级客服人员、m0-1催收员、回访员、续保专员等；||质检机器人可以替换企业原有录音或信息质检员；||培训机器人可以替换企业培训讲师等……\n公司aicc智能化平台产品迭代评估的三个纬度\n\n-----------------\n\n[[citation:2]]：\n###文件元信息:\nfilename:青牛智胜-员工手册-YGSC004\n4.5.2丧假须在葬礼前后一周内使用。\n生育类假期（产假、产检假、陪产假、流产假、哺乳假）自然日\n5.1产假：女性员工在职期间正常生育，并符合国家生育政策，产假为98天，其中可在产前休假15天；难产的，增加产假30天；多胞胎生育的，每多生育一个婴儿，增加产假15天；符合国家法律、法规规定的，广东省额外增加80天奖励假期；\n5.2产检假：女性员工在职期间正常生育，并符合国家生育政策，可享有产检假10天。\n5.3陪产假：男性员工在职期间配偶生育三十天内，可享有连续陪产假15天，请假时须上传子女的出生证明。\n5.4流产假：女性员工在职期间怀孕流产时，根据医院的证明，公司给予流产产假；怀孕未满四个月流产，给予15天产假；怀孕满四个月以上流产,给予42天产假；\n5.5哺乳假：有不满一周岁婴儿的女性员工，在每个工作日内有两次哺乳（含人工喂养）时间，每次三十分钟；多胞胎生育的，每多哺乳一个婴儿，每次哺乳时间增加30分钟；一天内的哺乳时间可合并。\n注：\n（1）以上假期全部为自然日假期，不可补、折现等，需提供《结婚证》、《医院出生证明》、《难产生育证》等相关证明；\n以上假期产检假可根据实际情况请假；\n以上产假及陪产假均应一次休完，未休完的部分视为自动放弃；\n以上产假、产检假、陪产假、哺乳假等福利假期均在员工转正后方可享有；\n以上假期中涉及的带薪假期，带薪特指基本薪资；\n员工违反婚姻法、计划生育规定等国家法律法规，不适用本产假、哺乳假、陪产假规定；\n其他假期为连休假，自然日假期（含双休日、法定节假日）过期不补；\n如有员工离职前尚未休完年休假及调休假，可在离职前申请休完剩余年休假及调休假，若未主动申请，未休完的年休假及调休假视为自动放弃，公司不予任何形式的补偿。\n第三节 保密管理\n公司一切未经公开披露的业务信息、财务资料、人事信息、招投标信息、合同文件、客户资料、技术方案、管理文件、会议内容等均属于公司内部机密，员工均有保守秘密的职责义务，不得私自外带、复印、外传，越过公司管理红线者，一经发现将做严肃处理。\n当不确定资料是否机密，应向上级领导或人力行政部反馈，由公司鉴定其属性。\n公司员工应当对自己薪酬、奖金等相关自身利益有关的数据及材料进行保密，一旦发现私下讨论及分析者，公司将严肃处理相关人等。\n-------------------------------------------\n###文件元信息:\nfilename:青牛智胜-员工手册-YGSC004\n试用期内旷工1天及以上的；\n有任何违反公司规章制度的行为，或给公司造成经济损失及损害公司名誉的；\n其他不符合录用条件的情形。\n转正流程\n（1）试用期期满人力行政部将\n\n-----------------\n\n[[citation:3]]：empty\n\n用户问题:红烧肉怎么做"}],"model":"zs-gpt","n":1,"temperature":0.4}
    # output = our_model.run(llm_parameter=llm_parameter, return_raw=True, retry_times=0)

    # result = []
    # from tqdm import tqdm
    # for i in tqdm(range(100)):
    #     output = our_model.run(llm_parameter=llm_parameter,return_raw=True,retry_times=0)
    #     result.append(output)
    #     time.sleep(20)

    api_key = "52c9e6b6-7cb7-4421-89d9-5d9bc7cc1b78"
    base_url = "https://ark.cn-beijing.volces.com/api/v3"
    model = "ep-20250211141955-lqklq"

    call_llm_model = CallOpenAI(model, base_url=base_url, api_key=api_key)
    import json
    llm_parameter = json.loads(open(r"C:\Users\<USER>\Desktop\给豆包deepseekV3的请求体2.json",'r',encoding='utf8').read())
    result = call_llm_model.stream_run(llm_parameter=llm_parameter)
    start = time.time()
    for i in result:
        print(time.time() - start)
        pass

    result = call_llm_model.run(llm_parameter=llm_parameter)



    # import pandas as pd
    # df = pd.read_excel(r"D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2025-02\20250226全量纵向图谱(1).xlsx")
    # with open(r"C:\Users\<USER>\Desktop\20250226全量纵向图谱_for_ragV3.txt",'w',encoding='utf-8') as f:
    #     for attri in df['属性'].unique():
    #         secsec = df[df['属性'] == attri]
    #         output = []
    #         for i in range(len(secsec)):
    #             sec = secsec.iloc[i]
    #             sec = sec.dropna()
    #             text = sec.index + ':' + sec.values
    #             text = '\n'.join(text)
    #             output.append(text)
    #         output = '\n\n'.join(output)
    #         f.write(output+'\n$$$\n')
    #     f.close()



