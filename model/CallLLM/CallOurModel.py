import time
from concurrent.futures import Thread<PERSON>oolExecutor, wait, ALL_COMPLETED
import requests
import json
from setting import logger
from requests.exceptions import Timeout
import setting
from hashlib import md5
import openai
if openai.__version__.startswith("0."):
    old_version_openai = True
else:
    old_version_openai = False
    from openai import OpenAI


class CallOurModel:
    def __init__(self, url=""):
        self.url = url if url else setting.OUR_MODEL_URL
        if old_version_openai:
            openai.api_key = "EMPTY"
            openai.api_base = self.url
        else:
            self.client = OpenAI(api_key="sk-12650c62ac694a959175757f9ec6b7c4", base_url=self.url)

    def run(self, user_prompt, system_prompt="", retry_times=3, assert_func=None, other_model_dict=None):
        """
        retry_times: 失败后是否重试,0不重试
        assert_func: 判断openai返回结果是否符合要求的函数,assert_func返回True表示符合要求,为空不判断。
                     如果多次判断都有错误,则返回第一次结果
        """
        logger.debug(f"other_model_dict:{other_model_dict}, id:{id(other_model_dict)}")
        if other_model_dict is None:
            other_model_dict = {}
        other_model_id = other_model_dict.get("other_model_id", "")
        other_model_type = other_model_dict.get("other_model_type", "")
        enterprise_id = other_model_dict.get("enterprise_id", "")
        model_id = other_model_dict.get("model_id", "")
        fail_times = 0
        res_list = []
        while fail_times <= retry_times:
            try:
                model_res = ""
                if other_model_id:
                    model_res = self.other_llm(user_prompt, other_model_id, other_model_type, enterprise_id)
                else:
                    messages = [
                        {
                            "role": "system",
                            "content": system_prompt,
                        },
                        {
                            "role": "user",
                            "content": user_prompt
                        }
                    ]
                    if old_version_openai:
                        m_response = openai.ChatCompletion.create(
                            model="qwen2.5-72b-instruct",
                            messages=messages,
                            stream=False,
                            temperature=0.4
                        )
                    else:
                        m_response = self.client.chat.completions.create(
                            model="qwen2.5-72b-instruct",
                            messages=messages,
                            stream=False,
                            temperature=0.4
                        )
                    if m_response:
                        model_res = m_response.choices[0].message.content
                if model_res:
                    # res = response.choices[0].message.content
                    if assert_func is not None:
                        if assert_func(model_res, model_id):
                            return model_res
                        else:
                            logger.debug(f"生成结果不符合assert_func要求")
                            res_list.append(model_res)
                    else:
                        return model_res
                else:
                    logger.warning(f"No responses! {other_model_id}")
                fail_times += 1
            except Exception as e:
                fail_times += 1
                logger.error(f"调用LLM出错:{e}, llm_url:{setting.OTHER_LLM_URL}, other_model_id:{other_model_id}, model_id:{model_id}")
        if len(res_list):
            return res_list[0]
        else:
            return ""

    def run_parallel(self, user_prompt_list, system_prompt_list=None, retry_times=3, assert_func=None, n_threads=10):
        """
        并发调用run
        user_prompt_list: list,多个prompt
        system_prompt_list: list,可以为空
        retry_times: 失败后是否重试,0不重试
        assert_func: 判断openai返回结果是否符合要求的函数,assert_func返回True表示符合要求,为空不判断。
                     如果多次判断都有错误,则返回第一次结果
        """

        def func_(user_p_, system_p_, retry_times_, assert_func_, result_list_, result_idx_):
            res_ = self.run(user_prompt=user_p_, system_prompt=system_p_, retry_times=retry_times_, assert_func=assert_func_)
            result_list_[result_idx_] = res_

        result_list = [""] * len(user_prompt_list)
        if system_prompt_list is None:
            system_prompt_list = [""] * len(user_prompt_list)
        executor = ThreadPoolExecutor(n_threads)
        all_task_list = []

        for i in range(len(user_prompt_list)):
            while executor._work_queue.qsize() > n_threads:
                time.sleep(0.1)
            all_task_list.append(executor.submit(lambda p: func_(**p), {
                "user_p_": user_prompt_list[i],
                "system_p_": system_prompt_list[i],
                "retry_times_": retry_times,
                "assert_func_": assert_func,
                "result_list_": result_list,
                "result_idx_": i}))
        wait(all_task_list, return_when=ALL_COMPLETED)
        return result_list

    def stream_run(self, user_prompt, system_prompt="", retry_times=3, assert_func=None):
        """
        retry_times: 失败后是否重试,0不重试
        assert_func: 判断openai返回结果是否符合要求的函数,assert_func返回True表示符合要求,为空不判断。
                     如果多次判断都有错误,则返回第一次结果
        """
        fail_times = 0
        res_list = []
        res = ''
        while fail_times <= retry_times:
            try:
                messages = [
                {
                    "role": "system",
                    "content": system_prompt,
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
                ]
                if old_version_openai:
                    stream = openai.ChatCompletion.create(
                        model="zs-gpt",
                        messages=messages,
                        stream=True,
                        max_tokens=1024,
                        temperature=0.4,
                        presence_penalty=1.1,
                        top_p=0.8)
                else:
                    stream = self.client.chat.completions.create(
                        model="zs-gpt",
                        messages=messages,
                        stream=True,
                        max_tokens=1024,
                        temperature=0.8,
                        presence_penalty=1.1,
                        top_p=0.8)
                if stream:
                    for chunk in stream:
                        res += chunk.choices[0].delta.content
                        yield res
                fail_times += 1
            except Exception as e:
                fail_times += 1
                logger.error(f"调用LLM出错:{e}")
        if len(res_list):
            return res_list[0]

    # 接入其他大模型
    def other_llm(self, query, other_model_id, other_model_type, enterprise_id):
        url = setting.OTHER_LLM_URL
        data = {
            "id": other_model_id,
            "type": other_model_type,
            "businessType": "SCP",
            "enterpriseID": enterprise_id,
            "explain": "你好",
            "query": query
        }
        if other_model_type == "aicc3":
            data["temperature"] = 0.4
        response = requests.post(url, data=json.dumps(data), timeout=240, headers={"Content-Type": "application/json"})
        if response.status_code == 200:
            json_response = response.json()
            if json_response.get("code") == 0:
                return json_response.get("data", {}).get("msg", "")
            else:
                logger.error(f"调用大模型失败:{other_model_id} {response.json()}")
                return False
        else:
            logger.error(f"调用大模型失败:{other_model_id} {response.text}")
        return False


if __name__ == "__main__":
    model = CallOurModel(url='http://region-3.seetacloud.com:46181/v1')
    content = "查询深圳的天气"

    # 测试run
    res = model.run(user_prompt=content)
    print(res)

    # 测试stream_run
    for i in model.stream_run(user_prompt=content):
        print(i)


    #测试并发run
    # def assert_func_1(res_string):
    #     import json
    #     try:
    #         temp = json.loads(res_string)
    #         assert isinstance(temp, dict) and "answer2" in temp
    #         return True
    #     except:
    #         return False

    # def assert_func_2(res_string):
    #     import json
    #     try:
    #         temp = json.loads(res_string)
    #         assert isinstance(temp, dict) and "answer" in temp
    #         return True
    #     except:
    #         return False

    # user_prompt_main_list = ["请问1+1等于几？\n请返回json结构,字段包括answer\n"] * 10

    # start_time = time.time()
    # res_1_p = model.run_parallel(user_prompt_list=user_prompt_main_list)
    # print(res_1_p)
    # res_2_p = model.run_parallel(user_prompt_list=user_prompt_main_list, retry_times=1, assert_func=assert_func_1)
    # print(res_2_p)
    # res_3_p = model.run_parallel(user_prompt_list=user_prompt_main_list, retry_times=1, assert_func=assert_func_2)
    # print(res_3_p)
    # print(f"并发耗时:{time.time()-start_time}")
    # print("wait")
