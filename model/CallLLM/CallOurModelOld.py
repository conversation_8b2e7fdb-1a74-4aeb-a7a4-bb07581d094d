import time
from concurrent.futures import Thread<PERSON>oolExecutor, wait, ALL_COMPLETED
import requests
import json
from setting import logger
from requests.exceptions import Timeout
import setting
from hashlib import md5


class CallOurModel:
    def __init__(self, url=""):
        self.url = url if url else setting.OUR_MODEL_URL

    def run(self, user_prompt, system_prompt="", retry_times=3, assert_func=None):
        """
        retry_times: 失败后是否重试,0不重试
        assert_func: 判断openai返回结果是否符合要求的函数,assert_func返回True表示符合要求,为空不判断。
                     如果多次判断都有错误,则返回第一次结果
        """
        fail_times = 0
        res_list = []
        while fail_times <= retry_times:
            try:
                payload = json.dumps({"query": user_prompt, "history": [], "temperature": 0.001})
                headers = {'Content-Type': 'application/json'}
                try:
                    response = requests.request("POST", self.url, headers=headers, data=payload, timeout=600)
                except Timeout:
                    logger.error(f'The request timed out fail_times:{fail_times}')
                    fail_times += 1
                    continue
                    # return []
                res = json.loads(response.text)["answer"]
                if assert_func is not None:
                    if assert_func(res):
                        return res
                    else:
                        logger.debug(f"生成结果不符合assert_func要求")
                        res_list.append(res)
                else:
                    return res
                fail_times += 1
            except Exception as e:
                fail_times += 1
                logger.error(f"调用LLM出错:{e}")
        if len(res_list):
            return res_list[0]

    def run_parallel(self, user_prompt_list, system_prompt_list=None, retry_times=3, assert_func=None, n_threads=10):
        """
        并发调用run
        user_prompt_list: list,多个prompt
        system_prompt_list: list,可以为空
        retry_times: 失败后是否重试,0不重试
        assert_func: 判断openai返回结果是否符合要求的函数,assert_func返回True表示符合要求,为空不判断。
                     如果多次判断都有错误,则返回第一次结果
        """

        def func_(user_p_, system_p_, retry_times_, assert_func_, result_list_, result_idx_):
            res_ = self.run(user_prompt=user_p_, system_prompt=system_p_, retry_times=retry_times_, assert_func=assert_func_)
            result_list_[result_idx_] = res_

        result_list = [""] * len(user_prompt_list)
        if system_prompt_list is None:
            system_prompt_list = [""] * len(user_prompt_list)
        executor = ThreadPoolExecutor(n_threads)
        all_task_list = []

        for i in range(len(user_prompt_list)):
            while executor._work_queue.qsize() > n_threads:
                time.sleep(0.1)
            all_task_list.append(executor.submit(lambda p: func_(**p), {
                "user_p_": user_prompt_list[i],
                "system_p_": system_prompt_list[i],
                "retry_times_": retry_times,
                "assert_func_": assert_func,
                "result_list_": result_list,
                "result_idx_": i}))
        wait(all_task_list, return_when=ALL_COMPLETED)
        return result_list



    def stream_run(self,llm_parameter={'temperature':0.1},query=None):
        if query is not None:
            query = query
        else:
            query = llm_parameter['messages']
            query = [i['content'] for i in query]
            query = '\n\n'.join(query)

        query_id = md5((query + str(time.time())).encode()).hexdigest()
        # query_id = query
        payload = {
            "query": query,
            "query_id": query_id,
            "history": [],
        }
        payload.update(llm_parameter)

        headers = {
            'Content-Type': 'application/json'
        }
        start = time.time()

        first = True
        count = 0
        count_return_round = 0
        while True:
            response = requests.request("POST", self.url + '/glm/stream_predict', headers=headers, data=json.dumps(payload))
            response = json.loads(response.text)
            answer = response["answer1"]
            print(answer,'answeransweransweranswer')
            if answer and first:
                first = False
                print('encode耗时',time.time() - start)
            # print(answer.replace('\n',''),end='\r')

            last_answer = ''
            to_long = False
            if answer:
                # 模型超长，自动结束
                count += 1
                if count > 40:#两秒同一个东西就算超长
                    if last_answer != answer:
                        last_answer = answer
                        count=0
                    else:
                        print('超长自动结束')
                        to_long = True
                        break

                if str(response['end'])=='1':
                    yield answer
                    break
                yield answer
            time.sleep(0.05)
            if not answer:
                count_return_round += 1
                if count_return_round > 1000:
                    yield '大模型服务超负债，请重新尝试'
                    break

        if to_long:
            yield '大模型服务超负债，请重新尝试'


if __name__ == "__main__":
    # model = CallOurModel(url='http://127.0.0.1:6088', temperature=0.1)
    # model = CallOurModel(url='http://127.0.0.1:6088', temperature=0.1)
    # model = CallOurModel(url='http://region-3.seetacloud.com:19965', temperature=0.1)
    model = CallOurModel(url='http://region-3.seetacloud.com:19965')
    # model = CallOurModel(url='http://region-3.seetacloud.com:46181')
    conversation = '喂！诶你好，那个打扰一下我是咱这个联通营业厅的，就这个华府对面的。呃，然后这边再跟您预约一下，这个上门就是网络检测的这个时间。嗯嗯，嗯。嗯嗯，嗯嗯。嗯嗯，嗯嗯。二。哦，不约明天吗？明天是吧？嗯嗯。明天大概什么时间？那您方便？嗯，122点12点到4点都行。哦。12点到4点行好的，那到时候再跟你联系啊，好打扰了，再见。嗯，嗯。嗯嗯嗯嗯，好谢谢啊。没事，嗯，再见。嗯嗯，嗯嗯，再见。嗯，银行。'
    inputs = f"""{conversation}
请根据上述对话时间是2023提取用户最终预约的时间,最终输出的格式为 %Y-%m-%d %H:%M 如果是时间范围需要输出%Y-%m-%d %H:%M-%H:%M"""

    inputs  = 'facts:文档1:报告解读指南第二版.pdf.docx\n内容1:\n健康体检报告解读指南## 第五节高尿酸一、报告解读询问要点尿酸高的客人要注意尿酸升高的程度， 并询问有没有高尿酸病史、 痛风病史以及饮食、生活习惯等。\n一些没有病史或原来尿酸不高而本次尿酸升高的客人，要侧重了解体检前1-2 周的饮食及生活情况。\n尿酸受饮食的影响很大， 代谢不好的人一次高嘌呤饮食就可引起尿酸的明显升高， 甚至诱发痛风。\n很多痛风的发作与尿酸的波动相关。\n尿酸高除了引起痛风外，对血管、肾功能也有不良因素。\n二、概念：是一种常见的生化异常，由尿酸盐生成过量和（或）肾脏尿酸排泄减少，或两者共同存在引起。\n三、病因：1、尿酸生成增多2、尿酸排泄减少：尿酸约2/3 通过肾脏排泄，其余1/3 通过肠道、胆道等肾外途径排泄。\n约90%持续高尿酸血症的患者存在肾脏处理尿酸的缺陷而表现为尿酸排泄减少。\n四、治疗原则：控制高尿酸血症，预防尿酸盐沉积，使血尿酸维持正常水平。\n建议多喝水，少吃海鲜、动物内脏、豆制品，限酒，必要时给予降尿酸治疗，如出现关节红、肿、热、痛，请及时到医院专科临床就诊。\n五、预防控制1、选择适当有氧运动，以免关节过劳。\n最好选择游泳、太极拳、乒乓球之类的有氧运动，控制体重，避免肥胖。\n2、多喝白开水或淡茶水，每天的饮水量至少2000ml 以上，保持尿量2000ml左右。\n西方人多以常饮苹果醋降低尿酸。\n不饮浓茶、咖甜饮料。\n第 17 页共 218 页\n内容2:\n健康体检报告解读指南男性：59-110 umol/l【临床意义】升高：见于各种原因引起的肾功能不全的病人，有肾前性因素（休克、严重脱水等） ， 肾后性因素 （尿路梗阻、 前列腺肥大或肿瘤等） ， 肾实质病变 （肾小球肾炎、肾病综合征、肾肿瘤等） 。\n饥渴时间长，尿少也会引起肌酐暂时升高。\n降低：见于长期蛋白摄入量不足的人群。\n16 尿酸正常参考值：女性：155-357 umol/l男性：208-428 umol/l【临床意义】血尿酸增高：(1)血尿酸增高主要见于痛风，但少数痛风患者在痛风发作时血尿酸测定正常。\n血尿酸增高无痛风发作者为高尿酸血症。\n(2)在细胞增殖周期快、核酸分解代谢增加时，如白血病及其他恶性肿瘤、多发性骨髓瘤、 真性红细胞增多症等血清尿酸值常见增高。\n肿瘤化疗后血尿酸升高更明显。\n(3)在肾功能减退时，常伴有血清尿酸增高。\n可见于肾脏疾病如急慢性肾炎，其他肾脏疾病的晚期如肾结核，肾盂肾炎，肾盂积水等。\n(4)在氯仿中毒、四氯化碳中毒及铅中毒、子痈、妊娠反应及食用富含嘌呤的食物等，均可引起血中尿酸含量增高。\n有些也与遗传因素有关。\n血尿酸降低：营养不良，恶性贫血，范科尼综合征血尿酸降低。\n17、葡萄糖正常参考值：3.89—6.11 mmol/l(70—110mg/dl)【临床意义】(1)升高：见于糖尿病、慢性胰腺炎、甲状腺功能亢进、心肌梗死、皮质醇增多症、肢端肥大症、嗜铬细胞瘤、胰高血糖素瘤；脑外伤、脑溢血、脑瘤、脑膜炎；妊娠呕吐、脱水、全身麻醉状态；肝硬化病人常有血糖升高，这可能与生长激素及胰高血糖素升高有关，近期进食大量含糖份高的食物也会出现血糖升高。\n第 201 页共 218 页\n内容3:\n健康体检报告解读指南正常参考值：阴性(-)【临床意义】阳性，有病理性因素也有生理性因素。\n1、病理性因素见于各种急慢性肾小球肾炎、急性肾盂肾炎、多发性骨髓瘤、肾移植术后等。\n此外，药物，汞等中毒引起肾小管上皮细胞损伤也可见阳性。\n2、生理性因素见于激烈运动、发热、寒冷刺激、标本被血液、脓液污染等。\n7、尿葡萄糖(u—glu)正常参考值：阴性(-)【临床意义】阳性，见于糖尿病、甲状腺机能亢进、垂体前叶机能亢进、嗜细胞瘤、胰腺炎、胰腺癌、严重肾功能不全等。\n此外，颅脑外伤、脑血管意外、急性心肌梗塞等，也可出现应激性糖尿;过多食入高糖物后，也可产生一过性血糖升高，使尿糖阳性。\n8、尿比重(sg)正常参考值：成人：1.005-1.030，新生儿：1.002-1.004;【临床意义】 尿比重减低： 常见于慢性肾盂肾炎、 尿崩症、 慢性肾小球肾炎、急性肾功能衰竭的多尿期等。\n正常人大量喝水后也会出现暂时性尿比重低。\n尿比重增高：多见于糖尿病、高热、呕吐、腹泻、脱水、急性肾小球肾炎及心力衰竭等。\n9、尿酸碱度(u—ph)正常参考值：尿ph 值(酸碱度)在4.5-8.0【临床意义】尿ph 值小于正常参考值：常见于酸中毒、糖尿病、痛风、服酸性药物、饮大量酸性饮料等。\n尿ph 值大于正常参考值：多见于碱中毒、膀胱炎、服用碳酸钠等碱性药物或饮用碱性饮料等。\n10、隐血(u—bld)正常参考值：阴性(-)【临床意义】若尿中出现多量红细胞，则可能由于肾脏出血、尿路出血、肾充血等原因所致。\n剧烈运动及血液循环障碍等，也可导致肾小球通透性增加，而在尿中出现蛋白质和红细胞。\n还见于泌尿系统结石、感染、肿瘤、急慢性肾炎、血小板减少性紫癫、血友病等。\n另一种情况，也见于标本被血液污染。\n11、维c第 179 页共 218 页\n内容4:\n健康体检报告解读指南3、戒烟限酒。\n4、忌用收缩血管的药物。\n5、保持理想体重，可减少下肢静脉曲张机会。\n二十七、痛风足跟部点状高代谢热，关节处见热区延伸，提示尿酸调节功能异常，常有痛风的可能。\n建议：1、痛风患者注意坚持的三大原则①不喝酒②不吃动物内脏③少吃海鲜。\n2、避免食用高嘌呤食物，如啤酒、海鲜、花生、动物内脏、浓肉汤汁、豆类和菠菜等，以免产生大量尿酸。\n提倡多食叶类蔬菜，少食油炸食品。\n3、多饮水，每日饮水达到2000 亳升以上，可帮助排泄尿酸，也可避免痛风引起的肾结石。\n4、避免服用抑制尿酸排泄的药物，如烟酸等。\n5、穿鞋不宜过紧，注意保暖。\n6、急性发作期应注意休息，抬高患肢，局部冷敷，防止关节过度、过多活动，或急性损伤。\n7、过于肥胖者要控制体重。\n8、适应运动。\n二十八、何谓颈部代谢热异常颈部代谢不对称通常代表了颈肌疲劳。\n肌肉的过渡使用， 造成局部代谢增加。\n建议：1、睡眠时枕头的高低要合适。\n第 162 页共 218 页\n内容5:\n健康体检报告解读指南(2)降低：见于各种原因引起的胰岛素分泌过多或对抗胰岛素的激素分泌不足、甲状腺功能不全、肾上腺功能不全、脑垂体恶病质、急性进行性肝脏疾病(急性黄色肝萎缩、急性肝炎、肝癌、磷及砷中毒等)、新生儿低血糖症等。\n18、糖化血红蛋白正常参考值：4.6%～6.2％（ngsp 值）【临床意义】糖化血红蛋白是检测人体样本中糖化血红蛋白的浓度，临床上主要用于糖尿病（diabetes） 的辅助诊断和血糖水平的监控。\n糖化血红蛋白的浓度主要反映被检者过去8～12 周体内血糖的平均水平，不受抽血时间、是否空腹、是否使用胰岛素以及其他能使血糖水平短暂波动的因素影响。\n19、视黄醇结合蛋白正常参考值：尿液≤0.700 mg/l【临床意义】(1)升高：肾功能不全、营养过剩性脂肪肝。\n尿液rbp↑:急慢性肾炎，糖尿病肾病，慢性肾衰。\n(2)降低：维生素a 缺乏症、低蛋白血症、吸收不良综合征、肝疾病(除外营养过剩性脂肪肝)、阻塞性黄疸、甲状腺功能亢进症、感染症、外伤等。\n20、胱抑素c正常参考值：0,55～1.05mg/l【临床意义】当肾功能受损时，cys c 在血液中的浓度随肾小球滤过率变化而变化、肾衰时, 肾小球滤过率下降，cys c 在血液中浓度可增加10 倍以上;若肾小球滤过率正常,而肾小管功能失常时，会阻碍cys c 在肾小管吸收并迅速分解,使尿中的浓度增加100 倍以上。\n21、总蛋白正常参考值：60-80 g/l【临床意义】(1) 升高：脱水，腹泻，发热，外伤，急性感染等；多发性骨髓瘤，白血病；慢性感染性疾病：细菌，病毒，寄生虫。\n第 202 页共 218 页\n内容6:\n健康体检报告解读指南【临床意义】增高：主要见于心肌梗死、急性或慢性肝炎、肝癌（尤其是转移性肝癌） ，其他可见于骨骼肌疾病、血液系统疾病、肺梗死、甲状腺功能减退、肾病综合征及晚期恶性肿瘤等12、羟丁酸脱氢酶hbdh正常参考值：90～182u/l【临床意义】（1）升高：①急性心肌梗死、恶性贫血、溶血性贫血、畸胎瘤(ldh/α-hbdh 比例增加)。\n②白血病、淋巴瘤、传染性单核细胞增多症(ldh/α-hbdh 比例不增加)。\n（2）降低：免疫抑制剂、抗癌剂(lda 也降低)，遗传性变异的ldh-h 亚型欠缺症(ldh/α-hbdh 比值下降)。\n13、同型半胱氨酸hcy正常参考值：5～15μmol/l【临床意义】增高： （1）可见于动脉粥样硬化性血管病、脑卒中、类风湿性关节炎、多种癌症等。\n（2）维生素b6、叶酸缺乏也会出现同型半胱氨酸升高。\n14、尿素氮正常参考值：女性：2.6-7.5 mol/l男性：3.1-8.0 mol/l【临床意义】（1）升高：常见于肾脏疾病,是肾功能变化的重要指标。\n如慢性肾小球肾炎、肾动脉硬化、严重肾盂肾炎、晚期肾结核、多囊肾等等所引起的肾功能不全。\n（2）因脱水、水肿、酸中毒、循环功能不全、休克等引起肾血流量减少时,血尿素氮增加。\n（3）尿结石、前列腺肥大等引起尿潴留等也可引起尿素氮升髙。\n（4）体内蛋白分解旺盛(如甲亢)以及高蛋白饮食后,可有轻度尿素氮增高。\n15、肌酐正常参考值：女性：41-81 umol/l第 200 页共 218 页\n内容7:\n健康体检报告解读指南### 第十章检验科检查项目报告解读#### 第一节自检项目 一、体液检测项目（一）尿常规检查项目1、尿白细胞(u—leu)正常参考值：阴性【临床意义】阳性时，提示尿中含有白细胞，表示泌尿系有感染性疾病，如肾盂肾炎、膀胱炎及尿道炎等。\n也可见于月经、白带污染。\n2、尿酮体(u-ket)正常参考值：阴性(-)【临床意义】阳性，见于糖尿病酮症、妊娠呕吐、子痫、腹泻、中毒、伤寒、麻疹、猩红热、肺炎、败血症、急性风湿热、急性粟粒性肺结、惊厥等。\n此外，饥饿、分娩后摄入过多的脂肪和蛋白质等也可出现阳性。\n3、尿亚硝酸盐(nit)正常参考值：阴性(-)【临床意义】阳性，常见于泌尿系感染，如：膀肮炎、肾盂肾炎等。\n也可见于尿标本储存时间过长，大量进吃泡菜，酸菜等。\n4、尿胆原(uro 或ubg)正常参考值：阴性(-)【临床意义】阳性，见于溶血性黄疽、肝病等。\n偶尔也见于正常人，如：饱餐后或尿液显碱性的情况。\n5、尿胆红素(u-bil)正常参考值：阴性(-)【临床意义】阳性，见于胆石症、胆道肿瘤、胆道蛔虫、胰头癌等引起的梗阻性黄疸和肝癌、肝硬化、急慢性肝炎、肝细胞坏死等导致的肝细胞性黄疽。\n也可见于喝水少，尿液浓缩的人。\n6、尿蛋白(r-pro)第 178 页共 218 页\n内容8:\n健康体检报告解读指南[1、支原体（人型、解脲）培养加药敏]卵泡期：≤0.95黄体期：3.82-50.56绝经期：≤0.64孕早期：8.90-468.41孕中期：71.55-303.05孕晚期：88.72-771.15排卵期：1.24-4.13十三、骨源性碱性磷酸酶正常参考值：成人男性：≤20.10成人绝经前女性：≤14.30成人绝经后女：≤22.40婴儿期（0-1 岁） ：37.00-300.00幼儿期（>1-9 岁） ：40.00-196.00青春期男（>9-16 岁） ：25-284.00青春期女（>9-16 岁） ：30.00-199.00μg/l【临床意义】升高：生理原因有可能是生长过快或者是骨折愈合期。\n骨的特殊性疾病也会导致骨的碱性磷酸酶增高，比如：佝偻病。\n甲状腺功能亢进、肝癌、肝硬化等也会使骨的碱性磷酸酶增高， 其他的风湿结缔组织病也会导致骨的碱性磷酸酶偏高。\n偏低：主要有重症慢性肾炎、甲状腺功能减低时，都会出现偏低；遗传性低磷酸酶血症也会出现碱性磷酸酶偏低。\n当孕妇出现碱性磷酸酶偏低时，一般是贫血、慢性肾炎引起。\n十四、大便培养细菌鉴定（大便）正\n【临床意义】辅助检查沙门菌及志贺菌引起的腹泻。\n第 216 页共 218 页\n\n\n问题：高尿酸的病因\n\n你是一个非常聪明且很听话的法律文档索引助手,你知道的全部知识仅仅是上面的facts内容。\n你只能引用参考资料中的内容来详细分析回答，不要使用自己的知识\n如果引用内容涉及法规，一定要给出法规名称和编号\n markdown格式输出\n'
    # inputs  = 'facts:文档1:报告解读指南第二版.pdf.docx\n内容1:\n你好'
    # output = model.run(inputs)
    # print(output)

    response = model.stream_run(query = inputs)
    for i in response:
        print(i)


    # import pandas as pd
    # df = pd.read_excel('样例测试.xlsx',sheet_name='hr测试结果')
    # query = df['4final_answer_prompt'].tolist()
    # output = []
    # from tqdm import tqdm
    # for i in tqdm(query):
    #     if str(i)!='nan':
    #         output.append(model.run(i))
    #     else:
    #         output.append('')
    #
    # pd.DataFrame({'output':output}).to_csv('output.csv',encoding='utf-8-sig')


