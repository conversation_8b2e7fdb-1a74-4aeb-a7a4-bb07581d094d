from transformers import LlamaTokenizer, AutoTokenizer,LlamaTokenizerFast,LlamaConfig
import setting
import os

model_path = os.path.join(setting.MAIN_DIR,'model','CallLLM','qwen')
config = LlamaConfig.from_pretrained(model_path)
tokenizer = LlamaTokenizerFast.from_pretrained(model_path,config=config)




def qwen_len_cuter(text, query = None,max_len=4900):
    query_len = 0
    if query is not None:
        query_len = len(tokenizer.encode(query))
        # print(query_len,'query_lenquery_lenquery_lenquery_lenquery_lenquery_lenquery_len')
    ids = tokenizer.encode(text)
    token = [tokenizer._convert_id_to_token(i) for i in ids][:max_len-query_len]
    # print(len(token),'tokentokentokentokentokentokentokentokentoken')
    string = tokenizer.convert_tokens_to_string(token)[3:]
    return string


def qwen_lener(text):
    return len(tokenizer.encode(text))

if __name__=='__main__':
    # text = '我怎么知道这个是不是呢'
    # cut_text = qwen_len_cuter(text,3)
    text = "2015年6月1日，张某加入A公司，2016年3月10日，A公司与张某签订了期限为2015年6月1日起的无固定期限劳动合同书一份，合同的落款日期为2015年6月1日。张某以落款时间存疑进行了质问并录像留证，2016年3月25日，张某主动离职。2016年3月26日，张某向劳动人事争议仲裁委员会申请仲裁，要求A公司支付2015年12月19日至2016年3月9日期间未签订劳动合同的双倍工资差额。仲裁委裁决支持了张某的仲裁请求。A公司不服该裁决，依法提起诉讼。庭审中，A公司称其于2015年6月1日就与张某签订了无固定期限劳动合同，故无需支付二倍工资。对此你怎么看"
    print(qwen_lener(text))