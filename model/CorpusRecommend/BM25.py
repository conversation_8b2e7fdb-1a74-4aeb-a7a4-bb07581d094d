from multiprocessing import Pool, cpu_count

import jieba
import math
import numpy as np
import pandas as pd

# 只加浮点限制66t/s
# ES200加浮点限制89t/s     精准率99.5%
# ES50加浮点限制118t/s     精准率98.7%
# 更换tfidf召回200     143t/s    精准率98%
# 更换python版本BM25 召回200      140t/s      精准率99.6%


"""
All of these algorithms have been taken from the paper:
<PERSON><PERSON><PERSON><PERSON> et al, Improvements to BM25 and Language Models Examined

Here we implement all the BM25 variations mentioned. 
"""

class baseBM25:
    def __init__(self, corpus, tokenizer=None):
        self.corpus_size = len(corpus)
        self.avgdl = 0
        self.doc_freqs = []
        self.idf = {}
        self.doc_len = []
        self.tokenizer = tokenizer

        if tokenizer:
            corpus = self._tokenize_corpus(corpus)

        nd = self._initialize(corpus)
        self._calc_idf(nd)

    def _initialize(self, corpus):
        nd = {}  # word -> number of documents with word
        num_doc = 0
        for document in corpus:
            self.doc_len.append(len(document))
            num_doc += len(document)

            frequencies = {}
            for word in document:
                if word not in frequencies:
                    frequencies[word] = 0
                frequencies[word] += 1
            self.doc_freqs.append(frequencies)

            for word, freq in frequencies.items():
                try:
                    nd[word] += 1
                except KeyError:
                    nd[word] = 1

        self.avgdl = num_doc / self.corpus_size
        return nd

    def _tokenize_corpus(self, corpus):
        pool = Pool(cpu_count())
        tokenized_corpus = pool.map(self.tokenizer, corpus)
        return tokenized_corpus

    def _calc_idf(self, nd):
        raise NotImplementedError()

    def get_scores(self, query):
        raise NotImplementedError()

    def get_batch_scores(self, query, doc_ids):
        raise NotImplementedError()

    def get_top_n(self, query, documents, n=5):
        assert self.corpus_size == len(documents), "The documents given don't match the index corpus!"

        scores = self.get_scores(query)
        top_n = np.argsort(scores)[::-1][:n]
        scores = scores.tolist()
        scores.sort(reverse=True)
        scores = scores[:n]
        return [documents[i] for i in top_n], scores

    def get_top_threshold(self, query, documents, threshold=0.0):
        assert self.corpus_size == len(documents), "The documents given don't match the index corpus!"

        scores = self.get_scores(query)
        index_list = np.where(scores > threshold)[0]
        return [documents[i] for i in index_list], scores[index_list]

class BM25Okapi(baseBM25):
    def __init__(self, corpus, tokenizer=None, k1=1.5, b=0.75, epsilon=0.25):
        self.k1 = k1
        self.b = b
        self.epsilon = epsilon
        super().__init__(corpus, tokenizer)

    def _calc_idf(self, nd):
        """
        Calculates frequencies of terms in documents and in corpus.
        This algorithm sets a floor on the idf values to eps * average_idf
        """
        # collect idf sum to calculate an average idf for epsilon value
        idf_sum = 0
        # collect words with negative idf to set them a special epsilon value.
        # idf can be negative if word is contained in more than half of documents
        negative_idfs = []
        for word, freq in nd.items():
            idf = math.log(self.corpus_size - freq + 0.5) - math.log(freq + 0.5)
            self.idf[word] = idf
            idf_sum += idf
            if idf < 0:
                negative_idfs.append(word)
        self.average_idf = idf_sum / len(self.idf)

        eps = self.epsilon * self.average_idf
        for word in negative_idfs:
            self.idf[word] = eps

    def get_scores(self, query):
        """
        The ATIRE BM25 variant uses an idf function which uses a log(idf) score. To prevent negative idf scores,
        this algorithm also adds a floor to the idf value of epsilon.
        See [Trotman, A., X. Jia, M. Crane, Towards an Efficient and Effective Search Engine] for more info
        :param query:
        :return:
        """
        score = np.zeros(self.corpus_size)
        doc_len = np.array(self.doc_len)
        for q in query:
            q_freq = np.array([(doc.get(q) or 0) for doc in self.doc_freqs])
            score += (self.idf.get(q) or 0) * (q_freq * (self.k1 + 1) /
                                               (q_freq + self.k1 * (1 - self.b + self.b * doc_len / self.avgdl)))
        return score

    def get_batch_scores(self, query, doc_ids):
        """
        Calculate bm25 scores between query and subset of all docs
        """
        assert all(di < len(self.doc_freqs) for di in doc_ids)
        score = np.zeros(len(doc_ids))
        doc_len = np.array(self.doc_len)[doc_ids]
        for q in query:
            q_freq = np.array([(self.doc_freqs[di].get(q) or 0) for di in doc_ids])
            score += (self.idf.get(q) or 0) * (q_freq * (self.k1 + 1) /
                                               (q_freq + self.k1 * (1 - self.b + self.b * doc_len / self.avgdl)))
        return score.tolist()


class BM25model:
    def __init__(self, doc):
        self.doc = doc  # 不小写了
        self.token_doc = [jieba.lcut(i) for i in self.doc]
        self.bm25 = BM25Okapi(self.token_doc)
        self.init_FREQ = jieba.dt.FREQ.copy()
        for i in self.bm25.idf.keys():
            jieba.add_word(i)
        self.userdict = jieba.dt.FREQ.copy()
        jieba.dt.FREQ = self.init_FREQ

    def search_topn(self, sentence, topn=200, overlap_score=False):
        #加载BM25的用户分词词典
        #not for sure this kind of memories copy's problem
        if overlap_score:
            jieba.dt.FREQ = self.userdict
            tokens = jieba.lcut(sentence)
            jieba.dt.FREQ = self.init_FREQ
        else:
            tokens = jieba.lcut(sentence)

        out, scores = self.bm25.get_top_n(tokens, self.doc, n=topn)

        out = pd.DataFrame({'sentence': out})
        out['bm_score'] = scores
        out = out.sort_values(['bm_score'], ascending=False)
        return out

    def search_threshold(self, sentence, threshold=0.0, overlap_score=False):
        #加载BM25的用户分词词典
        #not for sure this kind of memories copy's problem
        if overlap_score:
            jieba.dt.FREQ = self.userdict
            tokens = jieba.lcut(sentence)
            jieba.dt.FREQ = self.init_FREQ
        else:
            tokens = jieba.lcut(sentence)

        out, scores = self.bm25.get_top_threshold(tokens, self.doc, threshold=threshold)

        out = pd.DataFrame({'sentence': out})
        out['bm_score'] = scores
        out = out.sort_values(['bm_score'], ascending=False)
        return out
