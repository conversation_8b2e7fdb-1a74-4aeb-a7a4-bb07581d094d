import datetime
import time

import pandas as pd
import pymysql
import sqlalchemy

import setting
from model.CorpusRecommend.BM25 import BM25model
from setting import logger

time.strftime('%Y-%m')


class CorpusRecommend:
    def __init__(self):
        self.train_bm = None

    @staticmethod
    def read_sql_query(sql):
        df = pd.read_sql_query(sql, con=pymysql.connect(**setting.SQL_SETTING))
        return df

    def train(self):
        msg = '训练成功'
        corpus = []
        cur_time_stamp = int(time.time())
        four_week_ago_time_stamp = cur_time_stamp - 2419200*100

        sentence_df = pd.DataFrame()
        sentence = self.read_sql_query(f'select * from t_robot_kg_resp')
        sentence_df = pd.concat([sentence_df, sentence])
        sentence_df['timestamp'] = sentence_df['created_at'].apply(lambda x: x.timestamp())

        if sentence_df.shape[0]:
            # 获取最近4周数据,包括当周
            sentence_df = sentence_df[sentence_df["timestamp"] >= four_week_ago_time_stamp]
            if sentence_df.shape[0]:
                sentence = sentence_df['query'].drop_duplicates().tolist()
                sentence = list(set([s.strip() for s in sentence]))
                begin_str = datetime.datetime.fromtimestamp(four_week_ago_time_stamp).strftime('%Y-%m-%d')
                end_str = datetime.datetime.fromtimestamp(cur_time_stamp).strftime('%Y-%m-%d')
                logger.debug(f'语料推荐训练,获取{begin_str}到{end_str}的语料,语料数{len(sentence)}')
                corpus += sentence
            else:
                logger.debug(f"最近四周没有数据")
        else:
            logger.debug(f"语料推荐SQL没有数据")

        corpus = list(set(corpus))
        if len(corpus) == 0:
            msg = '没有语料'
            corpus = ['没有语料']
        self.train_bm = BM25model(corpus)

        # 清理corpus_recommend中过期数据,只保留一个月内数据
        corpus_recommend = self.read_sql_query(f'select * from corpus_recommend')
        one_month_ago_time_stamp = cur_time_stamp - 2678400
        corpus_recommend['timestamp'] = corpus_recommend['create_time'].apply(lambda x: x.timestamp())
        corpus_recommend = corpus_recommend[corpus_recommend["timestamp"] >= one_month_ago_time_stamp]
        corpus_recommend["id"] = [str(i) for i in range(corpus_recommend.shape[0])]
        corpus_recommend = corpus_recommend.drop("timestamp", axis=1)
        # 写入数据表,如果表存在则替换
        conn = sqlalchemy.create_engine(f"mysql+pymysql://{setting.SQL_SETTING['user']}:{setting.SQL_SETTING['passwd']}@{setting.SQL_SETTING['host']}:{setting.SQL_SETTING['port']}/{setting.SQL_SETTING['db']}")
        corpus_recommend.to_sql('corpus_recommend', con=conn, if_exists='replace', index=False, dtype={'create_time': sqlalchemy.DateTime()})
        return msg

    # def predict(self, query, n):
    #     if self.train_bm is not None:
    #         out = self.train_bm.search(query, n)
    #         logger.debug(f"推荐语料:{out}")
    #         out = out[out['bm_score'] > 0]['sentence'].tolist()
    #     else:
    #         out = []
    #     return out

    def predict(self, query, n, knowledge_id=""):
        # 已录入的语料
        logger.debug(f"语料推荐,查找已录入的语料,query:{query},n:{n},knowledge_id:{knowledge_id}")
        cur_used_corpus = self.read_sql_query(f'select similar_ask from similar2knowledge_robot')
        cur_used_corpus = cur_used_corpus["similar_ask"].to_list()
        # logger.debug(f"语料推荐,已录入语料:{cur_used_corpus}")
        if len(knowledge_id):
            logger.debug(f"语料推荐,查找已推荐过的语料,query:{query},n:{n},knowledge_id:{knowledge_id}")
            corpus_recommend = self.read_sql_query(f'select * from corpus_recommend')
            new_corpus_recommend = corpus_recommend[corpus_recommend["knowledge_id"] == knowledge_id]
            new_corpus_recommend = new_corpus_recommend["corpus"].to_list()
            logger.debug(f"语料推荐,意图下已推荐过的语料:{new_corpus_recommend},query:{query},n:{n},knowledge_id:{knowledge_id}")
            cur_used_corpus = list(set(cur_used_corpus + new_corpus_recommend))
            # logger.debug(f"语料推荐,已录入语料+已推荐过语料:{cur_used_corpus}")
        else:
            corpus_recommend = None

        if self.train_bm is not None:
            out = self.train_bm.search_threshold(query, threshold=0.0)
            out = out[~out.sentence.isin(cur_used_corpus)]
            logger.debug(f"过滤后的推荐语料:{out}")
            out = out['sentence'][:n].tolist()
        else:
            out = []

        try:
            if len(out) and len(knowledge_id):
                if corpus_recommend.shape[0]:
                    last_id = int(corpus_recommend["id"].iloc[-1])
                else:
                    last_id = 0
                logger.debug(f"语料推荐,保存已推荐过的语料,旧表大小:{corpus_recommend.shape},query:{query},n:{n},knowledge_id:{knowledge_id}")
                new_data_df = pd.DataFrame({
                            "id": [str(i) for i in range(last_id+1, last_id+1+len(out))],
                            "knowledge_id": [knowledge_id]*len(out),
                            "corpus": out,
                            "create_time": [int(time.time())]*len(out)})
                new_data_df['create_time'] = pd.to_datetime(new_data_df['create_time'], unit='s')
                logger.debug(f"语料推荐,保存已推荐过的语料,追加的新表大小:{new_data_df.shape},query:{query},n:{n},knowledge_id:{knowledge_id}")
                # 写入数据表,追加方式
                conn = sqlalchemy.create_engine(f"mysql+pymysql://{setting.SQL_SETTING['user']}:{setting.SQL_SETTING['passwd']}@{setting.SQL_SETTING['host']}:{setting.SQL_SETTING['port']}/{setting.SQL_SETTING['db']}")
                new_data_df.to_sql('corpus_recommend', con=conn, if_exists='append', index=False, dtype={'create_time': sqlalchemy.DateTime()})
                logger.debug(f"语料推荐,保存已推荐过的语料成功,追加的新表大小:{new_data_df.shape},query:{query},n:{n},knowledge_id:{knowledge_id}")
        except Exception as e:
            logger.debug(f"语料推荐,保存已推荐过的语料失败,query:{query},n:{n},knowledge_id:{knowledge_id},错误:{e}")

        return out

    def period_train(self):
        try:
            if self.train_bm is None:
                self.train()
            while True:
                if time.strftime('%w') == '7':
                    self.train()
                    time.sleep(3600*24)
                else:
                    time.sleep(3600*4)
        except Exception as e:
            logger.error(f"语料推荐周期训练报错:{e}")

if __name__ == '__main__':
    search_engine = CorpusRecommend()
    search_engine.train()
    print("推荐:", search_engine.predict('个人银行', 10, knowledge_id="123123"))
    print("推荐:", search_engine.predict('机器人', 10, knowledge_id=""))
    print("推荐:", search_engine.predict('机器人', 10, knowledge_id="123123"))
    print("推荐:", search_engine.predict('机器人', 10, knowledge_id=""))
    print("推荐:", search_engine.predict('机器人', 10, knowledge_id="123123A"))
    print("推荐:", search_engine.predict('机器人', 10, knowledge_id=""))
    print("推荐:", search_engine.predict('机器人', 10, knowledge_id="123123A"))
    print("推荐:", search_engine.predict('机器人', 10, knowledge_id=""))
    print("推荐:", search_engine.predict('人', 10, knowledge_id="123123A"))
    print("推荐:", search_engine.predict('人', 10, knowledge_id=""))
    print("推荐:", search_engine.predict('人', 10, knowledge_id="123123A"))
    print("推荐:", search_engine.predict('人', 10, knowledge_id=""))
    print("wait")
    # from concurrent.futures import ThreadPoolExecutor
    # executor = ThreadPoolExecutor(2)
    # def printer(a):
    #     return a
    # feature = executor.submit(search_engine.period_train)
    # print('yes')
