import re
from setting import logger
from model.CallLLM.CallOpenAI_ultra import CallOpenAI
import time
import pandas as pd


class MyDFChat:
    def __init__(self,llm:CallOpenAI):
        self.llm = llm

    def chat(self, df_decribe,df, question):
        parse_sql,df = self.get_sql(df_decribe,df,question)
        results = []
        exec_sql = parse_sql + 'results.append(result)' if parse_sql.endswith('\n') else parse_sql + '\nresults.append(result)'
        try:
            exec(exec_sql)
            logger.info(f'执行代码{exec_sql}结果：{results}')
            result = results[0]
            if type(result)==pd.DataFrame:
                result = result.to_csv(index=False)
            elif type(result)==list:
                result = [str(i) for i in result]
                result = '\n'.join(result)
            elif type(result)==str:
                pass
        except:
            logger.error(f'执行代码出错：exec_sql：{exec_sql}，results：{results}',exc_info=True)
            result = ''
        result = str(result)
        return result,parse_sql

    def get_sql(self,df_decribe,df:pd.DataFrame,question):
        df.columns = [str(i).replace('\n', '').strip().replace(' ','') for i in df.columns]
        content = df.sample(2, random_state=2024).to_csv(header=False, index=False, sep='|')
        dtypes_str = "\n".join(["【字段名】,数据类型"]+[f"【{k}】,{v}" for k, v in df.dtypes.items()])
        prompt = """
表格名称：@df_decribe@
表头数据类型：
@dtype@
数据：
@content@        
已知pandas表格样例df如上，你可以根据表格样例推断表格的其他样本，len(df)=200，系统变量已经有df，怎么查询到相关信息用于回答用户问题:@query@
请只输python代码最终结果变量以result命名,请根据用户问句确定要返回的result类型（DataFrame,list,string）,result一定返回明细list或DataFrame，不要返回聚合结果
查询不能用==只能用str.contains,接下来你只会输出代码,不需要做任何解释和#注释,变量声明不能有空格"""
        start = time.time()

        final_input = prompt.replace("@content@", content).replace("@query@", question).replace('@dtype@', dtypes_str).replace('@df_decribe@', df_decribe)
        output = llm.run(query=final_input,llm_parameter={'temperature': 0.1})
        consume = time.time() - start

        logger.info(f'llm_input:{final_input}，llm_output:{output}，consume:{consume}')
        sql = self.get_code(output)
        if 'contains' in sql and 'na=False' not in sql:
            sql = re.sub(r"(str\.contains\()([^)]+)\)", r"\1\2, na=False)", sql) #contains要有na=False

        return sql,df


    def get_code(self,string):
        code = re.findall(r'```python\n(.*?)```', string, re.DOTALL)
        if not code:
            code = re.findall(r'```(.*?)```', string, re.DOTALL)
            if not code:
                code = [string]
        return '\n'.join(code) if code else None



# if __name__ == '__main__':
from setting import FILECHAT_MODEL_URL
base_url = FILECHAT_MODEL_URL
api_key = "EMPTY"
llm = CallOpenAI('zs-gpt', base_url=base_url, api_key=api_key)

mydfchat = MyDFChat(llm)
# df = pd.read_excel(r"C:\Users\<USER>\Desktop\rag表格测试\files\1.202304-202403争议解决部业绩表（20240521）.xlsx", sheet_name='青岛')
#
# question = '24年至今有哪些客户'
# result = model.chat(df,question)

















