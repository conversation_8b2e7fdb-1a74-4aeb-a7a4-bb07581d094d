import json
import os

import setting
from setting import logger
import re
import requests
import pandas as pd
mysql = pd.read_csv(os.path.join(setting.MAIN_DIR,'main','mysql.csv'))
mysql['姓名'] = mysql['姓名'].apply(lambda x:x.replace(' ',''))

class CriminalBot:
    def __init__(self,summary_llm):
        self.call_llm_model = summary_llm

    def get_LLM_json(self,llmoutput:str):
        try:
            llmoutput_json = json.loads(llmoutput)
        except:
            try:
                llmoutput = re.findall('\{.+\}',llmoutput.replace(' ','').replace('\n',''))[0]
                llmoutput_json = json.loads(llmoutput)
            except:
                return llmoutput
        return llmoutput_json

    def llm_rank(self,query,content):
        from model.CallLLM.qwen.qwen_token_lener import qwen_len_cuter
        #需要限制5000token
        content = qwen_len_cuter(content)

        update_info = {}
        prompt = """@doc@\n\n\n问题：@query@\n前面的文章是否适合用来分析当前问题，先给出理由，然后输出：相关/不相关"""
        prompt = prompt.replace('@doc@', content)
        prompt = prompt.replace('@query@', query)
        llm_result = self.call_llm_model.run(prompt)
        update_info['llm_rank_prompt'] = prompt
        update_info['llm_rank_result'] = llm_result

        return update_info,prompt,llm_result

    def retrieval(self,query,topk):
        import requests

        update_info = {}
        url = 'http://192.168.1.239:6011/search_faq'
        # url = 'http://192.168.1.239:6011/search_faq'
        update_info['2topk'] = topk
        # model_id =  '240320_v2_col_0320_m3_people_keyword_gpt4qpl_pos1_neg1_epoch1_d0320_pretrain_v2'
        # model_id  =  "240322_col_seg100_0322_m3_people_gpt4log_pos1_neg1_epoch1_d0322_pretrain"
        model_id = "240409_faq_doc_criminal_seg5000_0328_people_gpt4log_keywordFB_gpt4loggenN_gpt4labelseg100_manualFAQSEGTWI_pos1_neg1_epoch1_d0328_85344_pretrain"
        # or_and = query_condition_output_json['logit']
        # weight_method = 'max' if or_and=='or' else 'mean'
        # model_id = '240321_v2_col_combine_0320_m3_people_keyword_gpt4qpl_pos1_neg1_epoch1_d0320_pretrain_v2'
        post_data = {'model_id': model_id, "query": query, "top_k": topk}  # best
        # post_data = {"query": query,"field_list": conditions,"model_id": "old","is_whole": False,"top_k": topk,"weight_method": "variance","vec_type": "colbert"}
        update_info['2post_data'] = post_data
        retrieval_result = requests.post(url, data=json.dumps(post_data)).json()
        update_info['2retrieval_result'] = retrieval_result
        return update_info,retrieval_result

if __name__ == '__main__':

    agent = FileChatAgent()
    query = '我需要一位专门从事家事服务与财富管理以及不动产与基础设施领域，并且没有中国专利代理师资格的律师，这个律师是谁？'
    struce_key = ['姓名', '职务', '执业领域', '邮箱', '电话', '工作经历', '荣誉奖项', '社会职务', '教育背景', '职业资格', '工作语言', '代表业绩']
    query_condition_output_json = agent.query_condition_generate(query,struce_key)
    print(query_condition_output_json,'query_condition_output_jsonquery_condition_output_jsonquery_condition_output_json')

