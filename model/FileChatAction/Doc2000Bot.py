import json

import re
from utils.filechat_utils import get_list_of_string



class Doc2000Bot:
    def __init__(self, call_llm_model):
        self.call_llm_model = call_llm_model

    def get_final_answer_prompt(self,query,mans_df):

        fit_for_llm = '姓名:' + mans_df['names'] + '\n' + mans_df['text'] + '\n'
        fit_for_llm = '\n'.join(fit_for_llm)

        # final_answer_prompt = """@doc@\n\n问题：@query@,列举出所有满足条件的候选人,回答的时候先判断每个人符合与不符合的理由,不要提到不相关的候选人,只说正确的候选人"""
        # final_answer_prompt = """@doc@\n\n问题：@query@,列举出所有满足条件的候选人,不要提到不相关的候选人,只说正确的候选人"""
        # final_answer_prompt = """@doc@\n\n问题：@query@,列举出所有满足条件的候选人,不要提到不相关的候选人,只说正确的候选人"""
        final_answer_prompt = """@doc@

问题：@query@

请先详细分析问题以及问题中的关键词汇、法律术语以及简历中对应的关键信息，再列举出满足条件的所有候选人,不要提到不相关的候选人,请给出简历原文作为依据。  在输出候选人的理由后，请判断这个人是否符合条件。
对于你不确定或者不知道的信息，必须拒识，不要推理、编造或者按照问题修改。 如果你没办法确定候选人符合条件，请不要输出信息。


问题中会涉及到法律术语，对于你不懂的，请告知用户你不懂词的含义。
如果有多位律师符合条件，不要遗漏。


先推理再根据简历原文以及推理结果给出结论。

请使用格式：
根据简历知识库中的简历原文，.....。
所以.....


可以使用list帮助查看更轻松。"""
        final_answer_prompt = final_answer_prompt.replace('@doc@', fit_for_llm)
        final_answer_prompt = final_answer_prompt.replace('@query@', query)
        #
        # # # 回答
        # responce = call_with_messages(fit_for_llm)
        # answer_output = ''

        doc_list = final_answer_prompt.split("\n\n", 5)
        temp = ""
        for d in doc_list:
            yeji = 0
            jingli = 0
            for line in d.split("\n"):
                if (line[:5] == "工作经历:"):
                    if (jingli == 0):
                        jingli += 1
                        line = line.replace("工作经历:", "工作经历:\n")
                    else:
                        line = line[5:]
                if (line[:5] == "代表业绩:"):
                    if (yeji == 0):
                        yeji += 1
                        line = line.replace("代表业绩:", "代表业绩:\n")
                    else:
                        line = line[5:]
                temp += line + '\n'
            temp += "\n"
        final_answer_prompt = temp
        return final_answer_prompt



    def get_LLM_json(self,llmoutput:str):
        try:
            llmoutput_json = json.loads(llmoutput)
        except:
            try:
                llmoutput = re.findall('\{.+\}',llmoutput.replace(' ','').replace('\n',''))[0]
                llmoutput_json = json.loads(llmoutput)
            except:
                return llmoutput
        return llmoutput_json

    def llm_rank(self,query,content):
        from model.CallLLM.qwen.qwen_token_lener import qwen_len_cuter
        #需要限制5000token
        content = qwen_len_cuter(content)

        update_info = {}
        prompt = """@doc@\n\n\n问题：@query@\n前面的文章是否适合用来分析当前问题，先给出理由，然后输出：相关/不相关"""
        prompt = prompt.replace('@doc@', content)
        prompt = prompt.replace('@query@', query)
        llm_result = self.call_llm_model.run(prompt)
        update_info['llm_rank_prompt'] = prompt
        update_info['llm_rank_result'] = llm_result

        return update_info,prompt,llm_result

    def rewrite_query(self, query):
        #需要限制5000token
        from model.CallLLM.qwen.qwen_token_lener import qwen_len_cuter
        query = qwen_len_cuter(query)

        update_info = {}
        prompt = """你是一个擅长分析问题和使用搜索引擎的智能助手，请你分析客户问题，并思考你需要用搜索引擎检索哪些子问题。
检索的子问题必须规范，专业，不能过于口语化。
如果客户问题已经很清晰了或者你不确定应该检索哪些子问题，则可以直接参考原始问题。

客户问题：@query@

上面是客户的问题，请给出你需要搜索的子问题，用中文输出，返回json结构，如下：
["子问题1","子问题2","子问题3"]
"""
        prompt = prompt.replace('@query@', query)
        llm_output = self.call_llm_model.run(prompt)
        llm_result = get_list_of_string(llm_output)

        llm_result = list(set([q for q in llm_result if q!=query]))
        update_info['rewrite_prompt'] = prompt
        update_info['rewrite_llm_output'] = llm_output
        update_info["rewrite_result"] = llm_result

        return update_info, llm_result
    
    def retrieval(self,query,topk):
        import requests

        update_info = {}
        # url = 'http://127.0.0.1:6012/search_faq'
        url = 'http://192.168.1.239:6011/search_faq'
        update_info['2topk'] = topk
        # model_id =  '240320_v2_col_0320_m3_people_keyword_gpt4qpl_pos1_neg1_epoch1_d0320_pretrain_v2'
        # model_id  =  "240322_col_seg100_0322_m3_people_gpt4log_pos1_neg1_epoch1_d0322_pretrain"
        # model_id = "240411_laber_faq_doc_seg300_0328_people_gpt4log_keywordFB_gpt4loggenN_gpt4labelseg100_manualFAQSEGTWI_pos1_neg1_epoch1_d0328_85344_pretrain"
        model_id = "all_doc_seg2000_bge-m3"
        # model_id = '240517_all_doc_seg2000_0508'
        # model_id = "240516_all_doc_seg2000_0508"
        # model_id = "240517_all_doc_seg2000_0508"
        # or_and = query_condition_output_json['logit']
        # weight_method = 'max' if or_and=='or' else 'mean'
        # model_id = '240321_v2_col_combine_0320_m3_people_keyword_gpt4qpl_pos1_neg1_epoch1_d0320_pretrain_v2'
        post_data = {'model_id': model_id, "query": query, "top_k": topk}  # best
        # post_data = {"query": query,"field_list": conditions,"model_id": "old","is_whole": False,"top_k": topk,"weight_method": "variance","vec_type": "colbert"}
        update_info['2post_data'] = post_data
        retrieval_result = requests.post(url, data=json.dumps(post_data)).json()
        update_info['2retrieval_result'] = retrieval_result
        return update_info,retrieval_result

if __name__ == '__main__':

    agent = FileChatAgent()
    query = '我需要一位专门从事家事服务与财富管理以及不动产与基础设施领域，并且没有中国专利代理师资格的律师，这个律师是谁？'
    struce_key = ['姓名', '职务', '执业领域', '邮箱', '电话', '工作经历', '荣誉奖项', '社会职务', '教育背景', '职业资格', '工作语言', '代表业绩']
    query_condition_output_json = agent.query_condition_generate(query,struce_key)
    print(query_condition_output_json,'query_condition_output_jsonquery_condition_output_jsonquery_condition_output_json')

