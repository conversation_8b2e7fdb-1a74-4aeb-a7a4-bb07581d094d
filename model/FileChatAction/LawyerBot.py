import json
import os
import random
import time
import numpy as np
import Lev<PERSON>htein
import setting
from model.CallLLM.CallOpenAI import CallOpenAI
from model.CallLLM.CallOurModel import CallOurModel
from setting import logger
import re
import requests
import pandas as pd

class LawyerBot:
    def __init__(self,call_llm_model):
        self.call_llm_model = call_llm_model

    def chat(self,query,answer):
        logger.debug(f'query:{query}')
        logger.debug('1开始生成condition')
        # yield None, '正在生成查询条件...', []
        return_info, conditions, output_key = self.query_condition_generate_new(query)
        answer.update(return_info)
        # 手动condition
        # label_df = pd.read_csv(r"D:\develop\aicc-llm-agent\eval\lawyer_answer_check_label.csv")
        # mapper = dict(zip(label_df['session_id'],label_df['condition_label']))
        # llm_query_condition_output = eval(str(mapper[query]) if '[' in mapper[query] else str(mapper[query].split(',')))
        # query_condition_output_json = {'条件':llm_query_condition_output,'输出':llm_query_condition_output}

        yield None, '正在搜索资料...', []
        logger.debug('2开始粗排')
        # 2开始粗排
        topk = 5
        return_info, mans_df = self.retrieval(query, conditions, topk)
        answer.update(return_info)
        # logger.debug('3开始精排')
        # update_info, no_good_man, mans_df = filechatagent.llm_rerank(query,mans_df)
        # answer.update(update_info)
        # logger.debug('3精排完成')
        final_answer_prompt = self.get_final_answer_prompt(query, mans_df)


    def query_condition_generate(self,query, struct_key: list,calllmm=None):
        # query_condition_prompt = """query：@query@\n\n律师事务所系统简历中出现的字段名称范围：姓名、职务、执业领域、邮箱、电话、工作经历、荣誉奖项、社会职务、教育背景、职业资格、工作语言、代表业绩\n其中，\n职务是律师的当前的职务，不是曾经担任过的职务。代表业绩主要描述律师优秀突出的工作经历或者项目以及成就。 \n工作经历主要描述律师在各大公司任职、职位、负责的业务、项目以及成就。\n执业领域：包含律师主要处理案件所属领域。\n工作语言是工作中使用的各国语言\n职业资格主要描述已获得的资格证书，也可以证明律师的各项能力\n\n这是一个在简历系统中检索简历的任务。query由查询条件以及期待的输出结果构成。\nStep1:  将query的内容进行切分，分为简历筛选条件和期待输出两部分。期待输出不是简历的限制条件要求。\nStep2：根据简历中字段的每个维度，将query中的查询条件拆分成每个字段的具体筛选子问题。 请给出这些子问题所涉及的简历字段。\n注意：执业领域、代表业绩、工作经历、荣誉、社会职务、职业资格证书都会涉及律师所擅长的专业领域。\nStep3:  请分析根据上一步的字段筛选简历后，从query抽取出想要获取简历中的字段名称，填在期待输出字段。 当query中未明确提到具体获取的简历字段，请不要猜测，为空即可。 \n最终完整输出格式：\n{\"条件\":[\"简历字段名称1\", \"简历字段名2\"], \"输出\":[\"简历字段名称1\"]}\n\n其中，条件是query中抽取出的筛选条件。输出是query中用户想要获取的信息不是筛选的条件。\n\n请注意step1-step3都是在隐式完成，只输出json，不要输出其他内容。 """
        query_condition_prompt = """query：@query@\n律师事务所系统简历中出现的字段名称范围：姓名、职务、执业领域、邮箱、电话、工作经历、荣誉奖项、社会职务、教育背景、职业资格、工作语言、代表业绩\n这是一个在简历系统中检索简历的任务。历史职务可能在工作经历或社会职务中，服务过的公司在工作经历中，刑法学专业是教育背景，**新星、**推荐是荣誉奖项\n请你给出与query相关的查询字段，只输出sql，字段保持中文，不要输出其他说明，格式：SELECT * FROM 律师事务所系统简历 WHERE"""
        query_condition_prompt = query_condition_prompt.replace('@query@',query)
        if calllmm is not None:
            response = calllmm.run(query_condition_prompt)
        else:
            response = self.call_llm_model.run(query_condition_prompt)
            # response = self.call_llm_model.stream_run(query_condition_prompt)
        query_condition_output = response
        # query_condition_output = ''
        # for i in response:
        #     query_condition_output = i
        return query_condition_output,query_condition_prompt

    def query_condition_generate_new(self,query,calllmm=None):
        struct_key = ['姓名', '职务', '执业领域', '邮箱', '电话', '工作经历', '荣誉奖项', '社会职务', '教育背景', '职业资格', '工作语言',
                      '代表业绩']
        """条件解析"""
        return_info = {}
        # query_condition_prompt = """query：@query@\n\n律师事务所系统简历中出现的字段名称范围：姓名、职务、执业领域、邮箱、电话、工作经历、荣誉奖项、社会职务、教育背景、职业资格、工作语言、代表业绩\n其中，\n职务是律师的当前的职务，不是曾经担任过的职务。代表业绩主要描述律师优秀突出的工作经历或者项目以及成就。 \n工作经历主要描述律师在各大公司任职、职位、负责的业务、项目以及成就。\n执业领域：包含律师主要处理案件所属领域。\n工作语言是工作中使用的各国语言\n职业资格主要描述已获得的资格证书，也可以证明律师的各项能力\n\n这是一个在简历系统中检索简历的任务。query由查询条件以及期待的输出结果构成。\nStep1:  将query的内容进行切分，分为简历筛选条件和期待输出两部分。期待输出不是简历的限制条件要求。\nStep2：根据简历中字段的每个维度，将query中的查询条件拆分成每个字段的具体筛选子问题。 请给出这些子问题所涉及的简历字段。\n注意：执业领域、代表业绩、工作经历、荣誉、社会职务、职业资格证书都会涉及律师所擅长的专业领域。\nStep3:  请分析根据上一步的字段筛选简历后，从query抽取出想要获取简历中的字段名称，填在期待输出字段。 当query中未明确提到具体获取的简历字段，请不要猜测，为空即可。 \n最终完整输出格式：\n{\"条件\":[\"简历字段名称1\", \"简历字段名2\"], \"输出\":[\"简历字段名称1\"]}\n\n其中，条件是query中抽取出的筛选条件。输出是query中用户想要获取的信息不是筛选的条件。\n\n请注意step1-step3都是在隐式完成，只输出json，不要输出其他内容。 """
        query_condition_prompt = """query：@query@\n律师事务所系统简历中出现的字段名称范围：姓名、职务、执业领域、邮箱、电话、工作经历、荣誉奖项、社会职务、教育背景、职业资格、工作语言、代表业绩\n这是一个在简历系统中检索简历的任务。历史职务可能在工作经历或社会职务中，服务过的公司在工作经历中，刑法学专业是教育背景，**新星、**推荐是荣誉奖项\n请你给出与query相关的查询字段，只输出sql，字段保持中文，不要输出其他说明，格式：SELECT * FROM 律师事务所系统简历 WHERE"""
        query_condition_prompt = query_condition_prompt.replace('@query@',query)
        if calllmm is not None:
            response = calllmm.run(query_condition_prompt)
        else:
            response = self.call_llm_model.run(query_condition_prompt)
            # response = self.call_llm_model.stream_run(query_condition_prompt)
        query_condition_output = response
        # query_condition_output = ''
        # for i in response:
        #     query_condition_output = i

        #解析
        def get_condition_from_llm(content, struct_key):
            content = content.split("WHERE")[-1]
            parse_result = {'条件': [i for i in struct_key if i in content], '输出': [], 'source': content,
                            'logic': 'and' if 'and' in content.lower() else 'or'}
            return parse_result

        query_condition_output_json = get_condition_from_llm(query_condition_output, struct_key)
        conditions = query_condition_output_json['条件']
        conditions = list(set(conditions + ['工作经历', '代表业绩']))  # 必须检
        conditions = [i for i in conditions if i in struct_key]

        output_key = query_condition_output_json['输出']
        output_key = list(set(output_key) - set(conditions))
        output_key = [i for i in output_key if i in struct_key]

        return_info['1llm_query_condition_prompt'] = query_condition_prompt
        return_info['1llm_query_condition_output'] = query_condition_output

        return return_info,conditions,output_key

    def get_condition_from_llm(self,content, struct_key):
        content = content.split("WHERE")[-1]
        parse_result = {'条件': [i for i in struct_key if i in content], '输出': [], 'source': content,
                        'logic': 'and' if 'and' in content.lower() else 'or'}
        return parse_result

    def get_task(self,query):
        from model.FileChatAction.prompt import law_or_chat_prompt as task_prompt
        # from main.prompt import doc_lawyer_chat_prompt as task_prompt
        task_fit_propmt = task_prompt.replace('@query@',query)
        task_llm_output = self.call_llm_model.run(task_fit_propmt, ).lower()

        def get_task(anwser):
            task_list = []
            if "task1" in anwser:
                task_list.append("task1")
            if "task2" in anwser:
                task_list.append("task2")
            if "task3" in anwser:
                task_list.append("task3")
            if "task4" in anwser:
                task_list.append("task4")
            if "task5" in anwser:
                task_list.append("task5")

            #兜底闲聊
            if not task_list:
                if "是法律问题咨询" in anwser:
                    task_list.append("task3")
                # if "知识问答" in anwser:
                #     task_list.append("task3")
            if not task_list:
                task_list.append("task5")
            return task_list
        task_list = get_task(task_llm_output)
        update_info = {}
        update_info['0task_fit_propmt'] = task_fit_propmt
        update_info['0task_llm_output'] = task_llm_output
        update_info['0task_list'] = task_list

        return update_info, task_list

    def get_task_new(self,query):
        return_info = {}
        from main.prompt import task_prompt
        task_fit_propmt = task_prompt.replace('@query@',query)
        print(task_fit_propmt,'task_fit_propmttask_fit_propmt')
        task_llm_output = self.call_llm_model.run(task_fit_propmt).lower()

        def get_task(anwser):
            task_list = []
            if "task1" in anwser:
                task_list.append("task1")
            if "task2" in anwser:
                task_list.append("task2")
            if "task3" in anwser:
                task_list.append("task3")
            return task_list
        task_list = get_task(task_llm_output)
        return_info['0task_fit_propmt'] = task_fit_propmt
        return_info['0task_llm_output'] = task_llm_output
        return return_info,task_list


    def llm_rerank(self,query,df):

        update_info = {'3不符合候选人': [],'3llm_rank_output':'','3llm_rank_prompt':''}

        for window in range(0, len(df), 5):
            sec = df.iloc[window:window + 5]
            sec['index'] = range(1, len(sec) + 1)
            sec_candidateid_to_name = dict(zip('候选人' + sec['index'].map(str), sec['names']))

            sec_for_llm_rank = '候选人' + sec['index'].map(str) + '\n' + sec['text']
            sec_for_llm_rank = '\n'.join(sec_for_llm_rank)

            llm_rank_prompt = f"""{sec_for_llm_rank}
query：{query}
你是一位法律领域专家， 前面是多位候选律师的介绍，我们需要根据query详细分析律师的介绍排除掉不符合的律师，请你给出不符合条件的律师名字。 并将其按照不符合程度排序。可能存在多位符合条件的候选人或者没有候选人不符合条件。 我们希望有尽可能多的排除不符合条件的候选人.
json格式输出不符合候选人的id:
{{"不符合候选人":["候选人id1", "候选人id2", "候选人id3"]}}"""

            llm_rank_output = self.call_llm_model.run(llm_rank_prompt)


            update_info['3llm_rank_output'] += llm_rank_output + '\n'
            update_info['3llm_rank_prompt'] += llm_rank_prompt + '\n'

            sec_llm_rank_json = self.get_LLM_json(llm_rank_output)

            no_good_man = []
            for i in sec_candidateid_to_name:
                if i in sec_llm_rank_json:
                    no_good_man.append(sec_candidateid_to_name[i])
            update_info['3不符合候选人'] += no_good_man

        no_good_man = list(set(update_info['3不符合候选人']))
        df = df[~df['names'].isin(no_good_man)]
        df['index'] = range(1, len(df) + 1)
        update_info['3candidates'] = df['names'].tolist()
        return update_info,no_good_man,df

    def update_df_from_outputkey(self,df,output_key):
        output_key = [i for i in output_key if '姓名' != i]
        if output_key:
            output_info_supply = mysql[mysql['姓名'].isin(df['names'].tolist())]
            output_info_supply = output_info_supply[list(set(['姓名'] + output_key))]
            df = pd.merge(df, output_info_supply, left_on='names', right_on='姓名')
            for col in output_key:
                df['text'] = df['text'] + '\n' + col + '\n' + df[col]
        return df

    def retrieval(self,query:str,conditions,topk):

        return_info = {}
        url = 'http://192.168.1.239:6011/search_lawyer'

        # model_id =  '240320_v2_col_0320_m3_people_keyword_gpt4qpl_pos1_neg1_epoch1_d0320_pretrain_v2'
        # model_id  =  "240322_col_seg100_0322_m3_people_gpt4log_pos1_neg1_epoch1_d0322_pretrain"
        model_id = "240328_col_seg100_dictseg_p2_0328_people_gpt4log_keywordFB_gpt4loggenN_gpt4labelseg100_manualFAQSEGTWI_pos1_neg1_epoch1_d0328_85344_pretrain"
        # or_and = query_condition_output_json['logit']
        # weight_method = 'max' if or_and=='or' else 'mean'
        # model_id = '240321_v2_col_combine_0320_m3_people_keyword_gpt4qpl_pos1_neg1_epoch1_d0320_pretrain_v2'
        post_data = {'model_id': model_id, "query": query, "field_list": conditions, "is_whole": False, "top_k": topk,
                     "weight_method": "sort_twice1", "vec_type": "dense"}  # best
        # post_data = {"query": query,"field_list": conditions,"model_id": "old","is_whole": False,"top_k": topk,"weight_method": "variance","vec_type": "colbert"}
        retrieval_result = requests.post(url, data=json.dumps(post_data)).json()

        logger.debug('2粗排完成')
        names = [i[0].replace(' ', '') for i in retrieval_result['people']]
        text = retrieval_result['text']
        text = [['\n'.join(value) for value in json.loads(i).values()] for i in text]  # 精简text
        text = ['\n'.join(i) for i in text]  # 精简text
        df = pd.DataFrame({'names': names, 'text': text})
        df['index'] = range(1, len(df) + 1)

        return_info['2conditions'] = conditions
        return_info['2topk'] = topk
        return_info['2post_data'] = post_data
        return_info['2retrieval_result'] = retrieval_result
        return_info['3candidates'] = names
        return return_info,df

    def get_final_answer_prompt(self,query,mans_df):

        fit_for_llm = '姓名:' + mans_df['names'] + '\n' + mans_df['text'] + '\n'
        fit_for_llm = '\n'.join(fit_for_llm)

        # final_answer_prompt = """@doc@\n\n问题：@query@,列举出所有满足条件的候选人,回答的时候先判断每个人符合与不符合的理由,不要提到不相关的候选人,只说正确的候选人"""
        # final_answer_prompt = """@doc@\n\n问题：@query@,列举出所有满足条件的候选人,不要提到不相关的候选人,只说正确的候选人"""
        # final_answer_prompt = """@doc@\n\n问题：@query@,列举出所有满足条件的候选人,不要提到不相关的候选人,只说正确的候选人"""
        final_answer_prompt = """@doc@

问题：@query@

请先详细分析问题以及问题中的关键词汇、法律术语以及简历中对应的关键信息，再列举出满足条件的所有候选人,不要提到不相关的候选人,请给出简历原文作为依据。  在输出候选人的理由后，请判断这个人是否符合条件。
对于你不确定或者不知道的信息，必须拒识，不要推理、编造或者按照问题修改。 如果你没办法确定候选人符合条件，请不要输出信息。


问题中会涉及到法律术语，对于你不懂的，请告知用户你不懂词的含义。
如果有多位律师符合条件，不要遗漏。


先推理再根据简历原文以及推理结果给出结论。

请使用格式：
根据简历知识库中的简历原文，.....。
所以.....


可以使用list帮助查看更轻松。"""
        final_answer_prompt = final_answer_prompt.replace('@doc@', fit_for_llm)
        final_answer_prompt = final_answer_prompt.replace('@query@', query)
        #
        # # # 回答
        # responce = call_with_messages(fit_for_llm)
        # answer_output = ''

        doc_list = final_answer_prompt.split("\n\n", 5)
        temp = ""
        for d in doc_list:
            yeji = 0
            jingli = 0
            for line in d.split("\n"):
                if (line[:5] == "工作经历:"):
                    if (jingli == 0):
                        jingli += 1
                        line = line.replace("工作经历:", "工作经历:\n")
                    else:
                        line = line[5:]
                if (line[:5] == "代表业绩:"):
                    if (yeji == 0):
                        yeji += 1
                        line = line.replace("代表业绩:", "代表业绩:\n")
                    else:
                        line = line[5:]
                temp += line + '\n'
            temp += "\n"
        final_answer_prompt = temp
        return final_answer_prompt



    def get_LLM_json(self,llmoutput:str):
        try:
            llmoutput_json = json.loads(llmoutput)
        except:
            try:
                llmoutput = re.findall('\{.+\}',llmoutput.replace(' ','').replace('\n',''))[0]
                llmoutput_json = json.loads(llmoutput)
            except:
                return llmoutput
        return llmoutput_json

    def resume_display_process(self,text,mans_df):
        from main.lawyer_name_utils import text_to_resume_urls
        resume, hits = text_to_resume_urls(text)

        hits_man_info = mans_df[mans_df['names'].isin(hits)]
        hits_man_info = '姓名:' + hits_man_info['names'] + '\n' + hits_man_info['text'] + '\n'
        # hits_man_info = '\n'.join(hits_man_info.tolist())
        # print(hits_man_info, 'hits_man_infohits_man_info')

        all_list = [resume] + hits_man_info.tolist()
        display_resume = [(all_list[i], all_list[i + 1]) for i in range(0, len(all_list) - 1, 2)]
        if len(all_list) % 2 == 1:
            display_resume += [(all_list[-1], '')]
        display_resume = [(display_resume[0][0], None)]
        return display_resume

if __name__ == '__main__':
    # filechat_llm = CallOurModel(url='http://region-3.seetacloud.com:46181', temperature=0.1)
    filechat_llm = CallOurModel(url='http://region-3.seetacloud.com:19965', temperature=0.1)
    agent = LawyerBot(filechat_llm)
    queries = """哪些律师拥有美国的律师执业资格
请介绍陈文昊律师的业绩
哪些合伙人服务过生命科学和医疗健康行业的企业？包括哪些企业？
需要找处理房地产纠纷案件的律师，需要会俄文沟通
有并购重组方面经验律师，需要教育行业相关的，会英文沟通
请问有没有律师可以为跨国公司做集中申报的？
有哪位律师有二手车诉讼业务经验？
想找处理过离婚财产纠纷案件的律师？
有没有北大或者清华的律师，需要法学硕士及以上学历，可以提供反垄断合规咨询的
在刑事争议解决和刑事合规领域经验丰富的合伙人
叶涵律师获得过哪些行业奖项和荣誉？
请问清华大学的法学学士是否具有美国纽约州律师执业资格？
有哪些律师可以写招股书吗
曾经参与处理过民间借贷纠纷案案件的律师有哪些
个人经历中有为跨国娱乐公司提供过知识产权服务的律师
有帮助客户完成跨境知识产权许可交易的律师有哪些
律所有没有多次服务国企，能够解决投资金融法律问题的律师
有处理过对赌协议纠纷的律师吗
我们公司在美国遇到一些法律纠纷，咱们有没有拥有美国律师执业资格的律师
我需要寻找在处理非法集资案件方面有经验的律师。
我需要寻找在处理非法集资案件方面有经验的律师。
能够处理国内重大食品安全问题纠纷的律师，需要之前有处理经验的""".split('\n')
    queries = [' ']
    for query in queries:
        # query = '起草辩护词需要哪些内容？'
        print(query)
        update_info,task_list= agent.get_task(query)
        print(update_info['0task_llm_output'])

        print('-----------------')
