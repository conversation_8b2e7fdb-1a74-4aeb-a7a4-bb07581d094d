import numpy as np
import hnswlib
from sklearn.metrics.pairwise import normalize


class HNSWModel:
    def __init__(self, documents=None, embeddings=None):
        self.ef_construction = 200
        self.M = 16
        self.ef = 10000
        self.space = "cosine"

        embeddings = normalize(embeddings)
        self.documents = documents
        num_elements, dim = embeddings.shape
        ids = np.arange(num_elements)

        # Declaring index
        self.p = hnswlib.Index(space=self.space, dim=dim)  # possible options are l2, cosine or ip

        # Initializing index - the maximum number of elements should be known beforehand
        self.p.init_index(max_elements=num_elements, ef_construction=self.ef_construction, M=self.M)

        self.p.add_items(embeddings, ids)
        self.p.set_ef(self.ef)

    def search(self, query, top_k=200, return_emb=False):
        """
        问句索引
        """
        query = normalize(query)
        labels, distances = self.p.knn_query(query, k=top_k)
        docs = [self.documents[i] for i in labels[0]]
        # if return_emb:
        embeddings = np.array(self.p.get_items(labels[0]))
        embeddings = embeddings.astype(np.float16)
        embeddings = normalize(embeddings)
        return docs, embeddings, distances
        # else:
        #     return docs, distances
