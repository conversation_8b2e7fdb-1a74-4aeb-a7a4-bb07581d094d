# 













# 依赖安装

## python依赖

```
pip install -r requirements.txt -i https://pypi.douban.com/simple/
```

## gcc

```
sudo yum install -y gcc gcc-c++ make automake
```

## Cmake

```
wget https://cmake.org/files/v3.21/cmake-3.21.0-linux-x86_64.tar.gz
sudo tar -zxvf cmake-3.21.3.tar.gz
cd cmake-3.21.3.tar.gz
sudo ./bootstrap
sudo gmake
sudo gmake install
```

## boost

 ```
 yum install boost
 yum install boost-devel
 yum install boost-doc
 ```

## kenlm

```
wget -O - https://kheafield.com/code/kenlm.tar.gz |tar xz
mkdir kenlm/build
cd kenlm/build
cmake ..
make -j2
```

## python-devel

```
yum install python-devel -y
```

## python Kenlm

```
pip install https://github.com/kpu/kenlm/archive/master.zip
```
