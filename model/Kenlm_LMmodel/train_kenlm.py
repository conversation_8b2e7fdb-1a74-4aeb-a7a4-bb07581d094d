import os
import setting
from setting import logger
from model.Corrector import Corrector



def train_kenlm(input_text_path,arpa_output_path,klm_output_path):
    cmd1 = f'{setting.KENLM_PATH}/lmplz -o 2 -S 3% --verbose_header --discount_fallback --text {input_text_path} --arpa {arpa_output_path}'
    code1 = os.system(cmd1)

    # 转换arpa成klm
    cmd2 = f'{setting.KENLM_PATH}/build_binary {arpa_output_path} {klm_output_path}'
    code2 = os.system(cmd2)
    if code1!=0 or code2!=0:
        logger.debug(f"code1{code1} code2{code2}" + "|||" + f"cmd1:{cmd1} - cmd2{cmd2}")

    assert code1 == code2 == 0, 'kenlm训练失败!'



def save_kenlm_text(texts,save_dir):
    c = Corrector()
    with open(save_dir, 'w', encoding='utf8') as f:
        for text in texts:
            split_text = c.split_2_short_text(text)
            for j in split_text:
                j = j[0]
                j = ' '.join(list(j))
                f.write(j + '\n')
        f.close()