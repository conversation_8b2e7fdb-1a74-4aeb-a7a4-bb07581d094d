import jieba
from sklearn.feature_extraction.text import TfidfVectorizer






class TFIDFModel:

    def __init__(self,corpus:list,ngram = (2,3)):
        self.corpus = corpus
        self.tfidf_model = self.train(corpus,ngram)

    def train(self,train_texts,ngram = (2,3)):
        """
        训练 TF-IDF 模型
        :param train_texts: 训练文本列表，格式为 ["text1", "text2", ...]
        :return: 训练好的 TF-IDF 模型和特征名字
        """
        train_texts = [' '.join(jieba.lcut(i)) for i in train_texts]
        # 初始化 TF-IDF 向量化器，可自定义参数
        tfidf = TfidfVectorizer(
            max_features=1000,  # 最大特征数
            ngram_range=ngram  # 包含 1-grams 和 2-grams
        )

        # 训练模型并转换训练数据
        tfidf.fit(train_texts)
        return tfidf

    def predict(self, new_texts, top_n=5, idf_weight=1):  # 新增idf_weight参数
        new_texts = [' '.join(jieba.lcut(i)) for i in new_texts]
        tf_matrix = self.tfidf_model.transform(new_texts)
        idf = self.tfidf_model.idf_

        try:
            feature_names = self.tfidf_model.get_feature_names_out()
        except:
            feature_names = self.tfidf_model.get_feature_names()

        keywords_list = []

        for doc_idx in range(tf_matrix.shape[0]):
            doc_vector = tf_matrix[doc_idx].toarray().flatten()

            # 修改点：对IDF进行指数加权
            tfidf_scores = doc_vector * (idf ** idf_weight)  # 增加idf的权重

            sorted_indices = tfidf_scores.argsort()[::-1]

            keywords = []
            for idx in sorted_indices:
                if tfidf_scores[idx] > 0:
                    ngram_terms = feature_names[idx].split(' ')
                    # 检查所有相邻词是否重复（针对任何长度的N-gram）
                    has_consecutive_duplicates = any(
                        ngram_terms[i] == ngram_terms[i + 1] for i in range(len(ngram_terms) - 1))
                    if not has_consecutive_duplicates:
                        keywords.append(feature_names[idx])
                if len(keywords) >= top_n:
                    break

            keywords_list.append(keywords)

        return keywords_list


# 示例用法
if __name__ == "__main__":
    # 示例训练数据（替换为你的文本）
    train_corpus = [
        "Machine learning is the study of computer algorithms that improve automatically through experience.",
        "Natural language processing enables computers to understand human language.",
        "Deep learning uses neural networks with multiple layers.",
        "Artificial intelligence is transforming modern industries."
    ]

    # 示例新文本（替换为你的目标文本）
    new_texts = [
        "Advanced machine learning techniques boost AI applications.",
        "Neural networks in deep learning process complex patterns."
    ]

    # 训练 TF-IDF 模型
    model = TFIDFModel(train_corpus)
    model.predict(new_texts)




