from model.CallLLM.CallOpenAI import CallOpenAI
from module.Prompt.prompt import get_language_judge_prompt


class LanguageJudgeGPT:
    def __init__(self):
        self.call_openai_model = CallOpenAI()

    def language_judge(self, text, lang):
        """
        text: 文本
        lang: 语言,string,例如:中文简体、中文繁体、粤语、英文
        """
        prompt_judge = get_language_judge_prompt(text=text, lang=lang)
        judge_result = self.call_openai_model.run(user_prompt=prompt_judge, retry_times=3)
        if "Y" in judge_result:
            return True
        else:
            return False

if __name__ == "__main__":
    judge_model = LanguageJudgeGPT()

    for t, l in [["从我的微众银行卡的余额转出100到我尾号1201的招商银行贷款卡上", "简体中文"],
                 ["从我的微众银行卡的余额转出100到我尾号1201的招商银行贷款卡上", "繁体中文"],
                 ["从我的微众银行卡的余额转出100到我尾号1201的招商银行贷款卡上", "粤语"],
                 ["从我的微众银行卡的余额转出100到我尾号1201的招商银行贷款卡上", "英语"],
                 ["Transfer to myself", "简体中文"],
                 ["Transfer to myself", "繁体中文"],
                 ["Transfer to myself", "粤语"],
                 ["Transfer to myself", "英语"],
                 ["如果你需要我嘅时候，随时揾我，唔好俾我以为自己係个闲人。", "简体中文"],
                 ["如果你需要我嘅时候，随时揾我，唔好俾我以为自己係个闲人。", "繁体中文"],
                 ["如果你需要我嘅时候，随时揾我，唔好俾我以为自己係个闲人。", "粤语"],
                 ["如果你需要我嘅时候，随时揾我，唔好俾我以为自己係个闲人。", "英语"]]:
        if judge_model.language_judge(t, l):
            print(f"{l}:{t}")
