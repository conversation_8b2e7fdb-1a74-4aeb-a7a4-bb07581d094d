import os
import re

from setting import MAIN_DIR,logger
from utils.my_utils import remove_symbol
from data.sim_tra_yue_char_set.zh_wiki import zh2Hant
from utils.my_utils import contains_chinese, contains_english


class LanguageJudgeRegular:
    def __init__(self):
        self.yue_char = set()
        self.chi_tra_char = set()
        self.chi_sim_char = set()
        with open(os.path.join(MAIN_DIR, "data/sim_tra_yue_char_set/suppchara.txt"), "r", encoding="utf-8") as f:
            for line in f:
                line = line.split("\t")
                line = [l.strip() for l in line if len(l.strip())]
                if len(line) == 2 and len(line[1]) == 1:
                    self.yue_char.add(line[1])
        self.zh2Hant = zh2Hant
        for k, v in zh2Hant.items():
            k = k.strip()
            v = v.strip()
            if len(k) == 1:
                self.chi_sim_char.add(k)
            if len(v) == 1 and k != v:
                self.chi_tra_char.add(v)
        self.char_set_dict = {
            "简体中文": self.chi_sim_char,
            "繁体中文": self.chi_tra_char,
            "粤语": self.yue_char
        }
        self.yue_char_reg = re.compile('|'.join(self.yue_char))
        self.en_char_reg = re.compile('[a-zA-Z]')

    def language_judge(self, text, lang):
        """
        text: 文本
        lang: 语言,string,例如:中文简体、中文繁体、粤语、英文
        """
        num_only = re.findall("^(\d*)$", text)
        if num_only and len(num_only):
            # 纯数字不预测语种，统一返回True
            return True

        en_num_only = re.findall("^([a-zA-Z\d]*)$", text)
        if en_num_only and len(en_num_only):
            # 只判断纯粹是英文和数字组成的
            num_en_char = 0
            num_digit_char = 0
            for c in text:
                if c in "0123456789":
                    num_digit_char += 1
                else:
                    num_en_char += 1
            if num_digit_char > num_en_char:
                return True
            else:
                if lang == "英语":
                    return True
                else:
                    return False

        have_chinese = contains_chinese(text)
        have_english = contains_english(text)
        if lang == "英语":
            if have_english and not have_chinese:
                return True
            else:
                return False
        else:
            if not have_chinese:
                return False

        if lang == "繁体中文":
            for c in text:
                if c in self.chi_tra_char:
                    return True
                if c in self.zh2Hant and self.zh2Hant[c] != c:
                    return False
            return True
        if lang == "粤语":
            for c in text:
                if c in self.yue_char:
                    return True
            return False
        if lang == "简体中文":
            for c in text:
                if c in self.chi_tra_char:
                    return False
            return have_chinese
        return True

    def language_judge_use_rate(self,text):
        text = remove_symbol(text).replace(' ','')
        len_text = len(text)
        yue_rate = len(self.yue_char_reg.findall(text)) / len_text
        en_rate = len(self.en_char_reg.findall(text)) / len_text
        logger.info(f'text:{text},yue_rate:{yue_rate},en_rate:{en_rate}')
        if yue_rate>=0.7:
            return '粤语'
        if en_rate>=0.7:
            return '英文'
        else:
            return '中文'

if __name__ == "__main__":
    judge_model = LanguageJudgeRegular()

    # for t_, l_ in [["从我的微众银行卡的余额转出100到我尾号1201的招商银行贷款卡上", "简体中文"],
    #                ["从我的微众银行卡的余额转出100到我尾号1201的招商银行贷款卡上", "繁体中文"],
    #                ["从我的微众银行卡的余额转出100到我尾号1201的招商银行贷款卡上", "粤语"],
    #                ["从我的微众银行卡的余额转出100到我尾号1201的招商银行贷款卡上", "英语"],
    #                ["Transfer to myself", "简体中文"],
    #                ["Transfer to myself", "繁体中文"],
    #                ["Transfer to myself", "粤语"],
    #                ["Transfer to myself", "英语"],
    #                ["如果你需要我嘅时候，随时揾我，唔好俾我以为自己係个闲人。", "简体中文"],
    #                ["如果你需要我嘅时候，随时揾我，唔好俾我以为自己係个闲人。", "繁体中文"],
    #                ["如果你需要我嘅时候，随时揾我，唔好俾我以为自己係个闲人。", "粤语"],
    #                ["如果你需要我嘅时候，随时揾我，唔好俾我以为自己係个闲人。", "英语"],
    #                ["流程配置页面中有哪些模块", "繁体中文"],
    #                ["註冊申請實體資料有何手續？", "简体中文"],
    #                ["法人", "繁体中文"]]:
    #     if judge_model.language_judge(t_, l_):
    #         print(f"{l_}:{t_}")

    text = '咩呀en'
    result = judge_model.language_judge_use_rate(text)
    print(result)
