from nltk.lm import KneserNeyInterpolated
from nltk.lm.preprocessing import padded_everygram_pipeline


def nltk_ngram_train(texts):
    """
    texts: list    [句子1,句子2]   无需分词
    """
    ngram = 2
    train, vocab = padded_everygram_pipeline(ngram, texts)
    model = KneserNeyInterpolated(ngram)
    # model = MLE(ngram)
    model.fit(train, vocab)
    return model


if __name__=='__main__':
    model = nltk_ngram_train(['你好','我很好!'])