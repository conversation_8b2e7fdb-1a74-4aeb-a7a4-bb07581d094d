# -*- coding: utf-8 -*-

import numpy as np
import tensorflow as tf
from transformers.models.bert.modeling_tf_bert import TFBertModel, get_initializer


class RDrop(tf.keras.Model):
    def __init__(self, model_path, config, tokenizer, num_class, max_len=128):
        super().__init__()
        self.max_len = max_len

        self.tokenizer = tokenizer
        self.config = config
        self.bert = TFBertModel.from_pretrained(model_path, from_pt=False, config=self.config)
        self.dense = tf.keras.layers.Dense(units=num_class, activation="softmax", kernel_initializer=get_initializer())

    def call(self, inputs, **kwargs):
        input_ids = inputs["input_ids"]
        token_type_ids = inputs.get("token_type_ids", None)
        attention_mask = inputs.get("attention_mask", None)

        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            **kwargs
        )
        return self.dense(outputs[1])

    def get_data(self, texts, max_len, return_tensor=False):
        if isinstance(texts, str):
            texts = [texts]
        data = {
            "input_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "token_type_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "attention_mask": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
        }
        for i, text in enumerate(texts):
            inputs = self.tokenizer.encode_plus(text, add_special_tokens=True, max_length=max_len, padding="max_length")
            data["input_ids"][i, :] = inputs["input_ids"]
            data["token_type_ids"][i, :] = inputs["token_type_ids"]
            data["attention_mask"][i, :] = inputs["attention_mask"]

        if return_tensor:
            data["input_ids"] = tf.convert_to_tensor(data["input_ids"])
            data["token_type_ids"] = tf.convert_to_tensor(data["token_type_ids"])
            data["attention_mask"] = tf.convert_to_tensor(data["attention_mask"])
        return data

class RDropLoss(tf.keras.losses.Loss):
    def __init__(self, alpha=4, **kwargs):
        super(RDropLoss, self).__init__(**kwargs)
        self.alpha = alpha

    def call(self, y_true, y_pred):
        # y_true = K.reshape(y_true, K.shape(y_pred)[:-1])
        # y_true = K.cast(y_true, 'int32')
        loss1 = tf.reduce_mean(tf.losses.sparse_categorical_crossentropy(y_true=y_true, y_pred=y_pred))
        loss2 = tf.losses.kld(y_pred[::2], y_pred[1::2]) + tf.losses.kld(y_pred[1::2], y_pred[::2])
        return loss1 + tf.reduce_mean(loss2) / 4 * self.alpha

    def get_config(self):
        return super(RDropLoss, self).get_config()