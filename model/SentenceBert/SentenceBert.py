# -*- coding: utf-8 -*-

import tensorflow as tf
from transformers import BertConfig
from transformers.models.bert.modeling_tf_bert import TFBertModel


class SentenceBert(tf.keras.Model):
    def __init__(self, model_path, emb_layer, output_type="cosine", max_len=128):
        """
        output_type: "cosine"、"dense"
        emb_layer: [int, list, str]
            int: 输出哪一层的 embedding, 如 1，2
            list: 输出哪几层的 embedding 取均值, 如 [1, 2]
            str: cls 表示区 cls 的 embedding
        """
        super().__init__()
        self.max_len = max_len
        self.output_type = output_type
        if isinstance(emb_layer, int):
            self.emb_layer = [emb_layer]
        else:
            self.emb_layer = emb_layer

        self.config = BertConfig.from_pretrained(model_path)
        self.config.output_hidden_states = True
        self.bert = TFBertModel.from_pretrained(model_path, from_pt=False, config=self.config)
        self.dense1 = tf.keras.layers.Dense(units=20, name="output_dense1", activation="tanh")
        self.dense2 = tf.keras.layers.Dense(units=2, name="output_dense2", activation="softmax")

    def call(self, inputs, **kwargs):
        """
        用于微调、训练
        inputs: dict ["input_ids_1", "token_type_ids_1", "attention_mask_1"]
        """
        input_ids_1 = inputs["input_ids_1"]
        token_type_ids_1 = inputs.get("token_type_ids_1", None)
        attention_mask_1 = inputs.get("attention_mask_1", None)
        input_ids_2 = inputs["input_ids_2"]
        token_type_ids_2 = inputs.get("token_type_ids_2", None)
        attention_mask_2 = inputs.get("attention_mask_2", None)

        emb_1 = self.get_encoder_layer(input_ids=input_ids_1, attention_mask=attention_mask_1, token_type_ids=token_type_ids_1, **kwargs)  # [batch, dim]
        emb_2 = self.get_encoder_layer(input_ids=input_ids_2, attention_mask=attention_mask_2, token_type_ids=token_type_ids_2, **kwargs)  # [batch, dim]

        if self.output_type == "dense":
            emb_concat = tf.concat([emb_1, emb_2], axis=1)
            return self.dense2(self.dense1(emb_concat, **kwargs), **kwargs)
        elif self.output_type == "cosine":
            prob = (-tf.keras.losses.cosine_similarity(emb_1, emb_2) + 1) / 2
            prob = tf.reshape(prob, shape=(-1, 1))
            return tf.concat([1-prob, prob], axis=-1)

    def get_encoder_layer(self, input_ids, attention_mask, token_type_ids, **kwargs):
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            output_hidden_states=True,
            **kwargs
        )
        hidden_states = outputs[2]

        if isinstance(self.emb_layer, str):
            emb = hidden_states[-1][:, 0, :]
        elif isinstance(self.emb_layer, list):
            emb = None
            for layer in self.emb_layer:
                emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
            emb = emb / len(self.emb_layer)
            emb = tf.reduce_mean(emb, axis=1)
        else:
            raise Exception(f"{self.__class__.__name__} 参数出错: emb_layer {self.emb_layer}")
        return emb


if __name__ == "__main__":
    from transformers import BertTokenizer
    from sklearn.metrics.pairwise import cosine_similarity
    import setting

    tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
    texts1 = ["有什么办法可以测试文本相似度？", "NLP是什么意思"]
    texts2 = ["文本相似度的测量方法", "这是一段测试代码"]
    inputs_1 = tokenizer.batch_encode_plus(texts1, add_special_tokens=True,
                                           max_length=setting.SENT_BERT_TEXT_PAIR_SIM_MAX_LEN,
                                           padding="max_length", return_tensors="tf")
    inputs_2 = tokenizer.batch_encode_plus(texts2, add_special_tokens=True,
                                           max_length=setting.SENT_BERT_TEXT_PAIR_SIM_MAX_LEN,
                                           padding="max_length", return_tensors="tf")

    # cls + dense
    model = SentenceBert(model_path=setting.PRETRAIN_BERT_DIR, emb_layer="cls", output_type="dense", max_len=setting.SENT_BERT_TEXT_PAIR_SIM_MAX_LEN)
    emb_1 = model.get_encoder_layer(**inputs_1).numpy()
    emb_2 = model.get_encoder_layer(**inputs_2).numpy()
    print(cosine_similarity(emb_1, emb_2))

    # [1, 4] + dense
    model = SentenceBert(model_path=setting.PRETRAIN_BERT_DIR, emb_layer=[1, -1], output_type="dense", max_len=setting.SENT_BERT_TEXT_PAIR_SIM_MAX_LEN)
    emb_1 = model.get_encoder_layer(**inputs_1).numpy()
    emb_2 = model.get_encoder_layer(**inputs_2).numpy()
    print(cosine_similarity(emb_1, emb_2))

    # cls + cosine
    model = SentenceBert(model_path=setting.PRETRAIN_BERT_DIR, emb_layer="cls", output_type="cosine", max_len=setting.SENT_BERT_TEXT_PAIR_SIM_MAX_LEN)
    emb_1 = model.get_encoder_layer(**inputs_1).numpy()
    emb_2 = model.get_encoder_layer(**inputs_2).numpy()
    print(cosine_similarity(emb_1, emb_2))

    # [1, 4] + cosine
    model = SentenceBert(model_path=setting.PRETRAIN_BERT_DIR, emb_layer=[1, -1], output_type="cosine", max_len=setting.SENT_BERT_TEXT_PAIR_SIM_MAX_LEN)
    emb_1 = model.get_encoder_layer(**inputs_1).numpy()
    emb_2 = model.get_encoder_layer(**inputs_2).numpy()
    print(cosine_similarity(emb_1, emb_2))
