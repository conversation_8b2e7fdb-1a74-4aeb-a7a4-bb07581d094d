# -*- coding: utf-8 -*-

import numpy as np
import tensorflow as tf
from transformers.models.bert.modeling_tf_bert import TFBertModel
from utils.onnx_utils import inference_with_onnx


class SimCSE(tf.keras.Model):
    def __init__(self, config, tokenizer, model_path=None):
        super().__init__()
        self.config = config
        self.config.output_hidden_states = True
        self.tokenizer = tokenizer
        self.max_len = config.max_len
        if isinstance(config.emb_layer, int):
            self.emb_layer = [config.emb_layer]
        else:
            self.emb_layer = config.emb_layer
        if model_path:
            self.bert = TFBertModel.from_pretrained(model_path, from_pt=False, config=self.config)
        else:
            self.bert = TFBertModel(config=self.config)
        self.session = None

    def call(self, inputs, **kwargs):
        input_ids = inputs["input_ids"]
        token_type_ids = inputs.get("token_type_ids", None)
        attention_mask = inputs.get("attention_mask", None)
        emb = self.get_encoder_layer(input_ids=input_ids, token_type_ids=token_type_ids, attention_mask=attention_mask, **kwargs)  # [batch, dim]
        return emb

    def get_encoder_layer(self, input_ids, attention_mask, token_type_ids, **kwargs):
        if self.session:
            inputs = {
                'input_ids': input_ids,
                'token_type_ids': token_type_ids,
                'attention_mask': attention_mask,
                'output_hidden_states': True
            }
            outputs = inference_with_onnx(self.session, inputs)
            hidden_states = outputs[:-2]
        else:
            outputs = self.bert(
                input_ids=input_ids,
                attention_mask=attention_mask,
                token_type_ids=token_type_ids,
                output_hidden_states=True,
                **kwargs
            )
            hidden_states = outputs[2]

        if isinstance(self.emb_layer, str):
            emb = hidden_states[-1][:, 0, :]
        elif isinstance(self.emb_layer, list):
            emb = None
            for layer in self.emb_layer:
                emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
            emb = emb / len(self.emb_layer)
            emb = tf.reduce_mean(emb, axis=1)
        else:
            raise Exception(f"{self.__class__.__name__} 参数出错: emb_layer {self.emb_layer}")
        return emb

    def get_data(self, texts, max_len, return_tensor=False):
        if isinstance(texts, str):
            texts = [texts]
        data = {
            "input_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "token_type_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "attention_mask": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
        }
        for i, text in enumerate(texts):
            inputs = self.tokenizer.encode_plus(text, add_special_tokens=True,
                                           max_length=max_len,
                                           truncation=True,
                                           padding="max_length")
            data["input_ids"][i, :] = inputs["input_ids"]
            data["token_type_ids"][i, :] = inputs["token_type_ids"]
            data["attention_mask"][i, :] = inputs["attention_mask"]

        if return_tensor:
            data["input_ids"] = tf.convert_to_tensor(data["input_ids"])
            data["token_type_ids"] = tf.convert_to_tensor(data["token_type_ids"])
            data["attention_mask"] = tf.convert_to_tensor(data["attention_mask"])
        return data

class SimLoss2N(tf.keras.losses.Loss):
    def __init__(self, **kwargs):
        super(SimLoss2N, self).__init__(**kwargs)

    def call(self, y_true, y_pred):
        """
        一个 batch 的句子 [b1, b2, b3, b4, ...], 每两个是语义相同的 b1 和 b2 相同, b3 和 b4 相同
        y_true: 无用全0, 要在call中重新计算,计算得到 [batch, batch] 的矩阵
        y_pred: [batch, dim] 每个句子的 embeddign
        """
        # 构造标签
        idxs = tf.range(0, tf.shape(y_pred)[0])
        idxs_1 = idxs[None, :]
        idxs_2 = (idxs + 1 - idxs % 2 * 2)[:, None]
        y_true = tf.equal(idxs_1, idxs_2)
        y_true = tf.cast(y_true, tf.float32)

        # 计算相似度
        y_pred = tf.math.l2_normalize(y_pred, axis=1)
        similarities = tf.matmul(y_pred, y_pred, transpose_b=True)
        similarities = similarities - tf.eye(tf.shape(y_pred)[0]) * 1e12
        similarities = similarities * 20
        loss = tf.keras.losses.categorical_crossentropy(y_true, similarities, from_logits=True)
        return tf.reduce_mean(loss)

    def get_config(self):
        return super(SimLoss2N, self).get_config()

class SimLossN(tf.keras.losses.Loss):
    def __init__(self, **kwargs):
        super(SimLossN, self).__init__(**kwargs)

    def call(self, y_true, y_pred):
        """
        一个 batch (2n个) 的句子 [a1, a2, a3, a4, ..., an, b1, b2, b3, b4, ..., bn], ai 和 bi 是相同语义的
        y_true: 无用全0, 在call中重新计算, [0, 1, 2, 3, ..., n-1]
        y_pred: [2n, dim] 每个句子的 embeddign
        """
        # 构造标签
        n = tf.shape(y_pred)[0] // 2
        y_true = tf.range(0, n)

        # 计算相似度
        y_pred = tf.math.l2_normalize(y_pred, axis=1)
        similarities = tf.matmul(y_pred[0::2], y_pred[1::2], transpose_b=True)
        similarities = similarities * 20
        loss = tf.keras.losses.sparse_categorical_crossentropy(y_true, similarities, from_logits=True)
        return tf.reduce_mean(loss)

    def get_config(self):
        return super(SimLossN, self).get_config()

class SimMetric2N(tf.keras.metrics.Metric):
    def __init__(self, metric_type="acc", name='sim_acc', threshold=0.7, **kwargs):
        super(SimMetric2N, self).__init__(name=name, **kwargs)
        self.metric_type = metric_type
        self.threshold = threshold
        self.tp = tf.metrics.TruePositives(thresholds=self.threshold)
        self.fp = tf.metrics.FalsePositives(thresholds=self.threshold)
        self.tn = tf.metrics.TrueNegatives(thresholds=self.threshold)
        self.fn = tf.metrics.FalseNegatives(thresholds=self.threshold)
        self.auc = tf.metrics.AUC()

    def update_state(self, y_true, y_pred, sample_weight=None):
        # 构造标签
        idxs = tf.range(0, tf.shape(y_pred)[0])
        idxs_1 = idxs[None, :]
        idxs_2 = (idxs + 1 - idxs % 2 * 2)[:, None]
        y_true = tf.equal(idxs_1, idxs_2)
        y_true = tf.cast(y_true, tf.int32)

        # 计算相似度
        y_pred = tf.math.l2_normalize(y_pred, axis=1)
        similarities = tf.matmul(y_pred, y_pred, transpose_b=True)
        # similarities = similarities - tf.eye(tf.shape(y_pred)[0]) * 1e12
        # similarities = similarities * 4
        # y_pred = tf.nn.sigmoid(similarities)
        similarities = similarities - tf.eye(tf.shape(y_pred)[0]) * 2
        y_pred = tf.clip_by_value((similarities+1)/2, clip_value_min=0, clip_value_max=1)

        self.auc.update_state(y_true=y_true, y_pred=y_pred)
        self.tp.update_state(y_true=y_true, y_pred=y_pred)
        self.fp.update_state(y_true=y_true, y_pred=y_pred)
        self.tn.update_state(y_true=y_true, y_pred=y_pred)
        self.fn.update_state(y_true=y_true, y_pred=y_pred)

    def result(self):
        tp = self.tp.result()
        fp = self.fp.result()
        tn = self.tn.result()
        fn = self.fn.result()
        auc = self.auc.result()

        if self.metric_type == "auc":
            return auc
        elif self.metric_type == "acc":
            return (tp+tn)/(tp+fp+tn+fn)
        elif self.metric_type == "precision":
            return tp/(tp+fp)
        elif self.metric_type == "recall":
            return tp/(tp+fn)
        else:
            p = tp/(tp+fp)
            r = tp/(tp+fn)
            return 2*p*r/(p+r)

    def reset_states(self):
        self.tp.reset_states()
        self.fp.reset_states()
        self.tn.reset_states()
        self.fn.reset_states()
        self.auc.reset_states()

class SimMetricN(tf.keras.metrics.Metric):
    def __init__(self, metric_type="acc", name='sim_acc', threshold=0.7, **kwargs):
        super(SimMetricN, self).__init__(name=name, **kwargs)
        self.metric_type = metric_type
        self.threshold = threshold
        self.tp = tf.metrics.TruePositives(thresholds=self.threshold)
        self.fp = tf.metrics.FalsePositives(thresholds=self.threshold)
        self.tn = tf.metrics.TrueNegatives(thresholds=self.threshold)
        self.fn = tf.metrics.FalseNegatives(thresholds=self.threshold)
        self.auc = tf.metrics.AUC()

    def update_state(self, y_true, y_pred, sample_weight=None):
        # 构造标签
        n = tf.shape(y_pred)[0] // 2
        y_true = tf.eye(n)

        # 计算相似度
        y_pred = tf.math.l2_normalize(y_pred, axis=1)
        similarities = tf.matmul(y_pred[0::2], y_pred[1::2], transpose_b=True)
        y_pred = tf.clip_by_value((similarities+1)/2, clip_value_min=0, clip_value_max=1)

        self.auc.update_state(y_true=y_true, y_pred=y_pred)
        self.tp.update_state(y_true=y_true, y_pred=y_pred)
        self.fp.update_state(y_true=y_true, y_pred=y_pred)
        self.tn.update_state(y_true=y_true, y_pred=y_pred)
        self.fn.update_state(y_true=y_true, y_pred=y_pred)

    def result(self):
        tp = self.tp.result()
        fp = self.fp.result()
        tn = self.tn.result()
        fn = self.fn.result()
        auc = self.auc.result()

        if self.metric_type == "auc":
            return auc
        elif self.metric_type == "acc":
            return (tp+tn)/(tp+fp+tn+fn)
        elif self.metric_type == "precision":
            return tp/(tp+fp)
        elif self.metric_type == "recall":
            return tp/(tp+fn)
        else:
            p = tp/(tp+fp)
            r = tp/(tp+fn)
            return 2*p*r/(p+r)

    def reset_states(self):
        self.tp.reset_states()
        self.fp.reset_states()
        self.tn.reset_states()
        self.fn.reset_states()
        self.auc.reset_states()