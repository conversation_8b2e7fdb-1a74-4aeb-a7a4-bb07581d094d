# -*- coding: utf-8 -*-

import os

import numpy as np
from transformers import BertTokenizer, BertConfig
from transformers.models.bert.modeling_tf_bert import TFBertModel
from tqdm import tqdm

import setting

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

class BertTextRepresentation:
    def __init__(self, model_path, emb_layer, max_len=128):
        """
        model_path: Bert 预训练模型路径
        max_len: 最大长度
        gpu_device: str, "-1" 表示不用 gpu, "0", "1" 表示用第 0 或 1 gpu
        emb_layer: [int, list, str]
            int: 输出哪一层的 embedding, 如 1，2
            list: 输出哪几层的 embedding 取均值, 如 [1, 2]
            str: cls 表示区 cls 的 embedding
        """
        self.config = BertConfig.from_pretrained(model_path)
        self.tokenizer = BertTokenizer.from_pretrained(model_path)
        self.max_len = max_len
        if isinstance(emb_layer, int):
            self.emb_layer = [emb_layer]
        else:
            self.emb_layer = emb_layer
        self.bert = TFBertModel.from_pretrained(model_path, from_pt=False, config=self.config)

    def encode_sentences(self, text_list, batch_size=32):
        embeddings = list()

        for start_idx in tqdm(range(0, len(text_list), batch_size),desc='BertTextRepresentation'):
            end_idx = min(start_idx+batch_size, len(text_list))
            inputs = self.tokenizer.batch_encode_plus(
                text_list[start_idx:end_idx],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                truncation=True,
                return_tensors="tf")

            outputs = self.bert(
                input_ids=inputs["input_ids"],
                attention_mask=inputs["attention_mask"],
                token_type_ids=inputs["token_type_ids"],
                output_hidden_states=True
            )
            hidden_states = outputs[2]

            if isinstance(self.emb_layer, str):
                emb = hidden_states[-1][:, 0, :]
            elif isinstance(self.emb_layer, list):
                emb = None
                for layer in self.emb_layer:
                    emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
                emb = emb / len(self.emb_layer)
                emb = tf.reduce_mean(emb, axis=1)
            else:
                raise Exception(f"{self.__class__.__name__} 参数出错: emb_layer {self.emb_layer}")
            embeddings.append(emb.numpy())

        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings


if __name__ == "__main__":
    from sklearn.metrics.pairwise import cosine_similarity

    text_list = ["万用金款项仅限个人消费，不得用于购房>、理财、股票银证转账、生产经营等投资类领域、也不得偿还债务或通过第三方支付平台转入非消费领域。",
                 "北京时间6月30日03:00，欧洲杯1/8决赛最后一场，由E组第1的瑞典对阵C组第3的乌克兰。津琴科首开纪录，福斯贝里扳平后在13分钟内2次中柱，"]
    # text_list = ["万用金款项仅限个人消费，不得用于购房>、理财、股票银证转账、生产经营等投资类领域、也不得偿还债务或通过第三方支付平台转入非消费领域。",
    #              "万用金款项仅限个人消费，不得用于购房>、理财、 账、生产经营等投资类领域、也不 还债务或通过第三方支付平台转入非消费领域。"]

    # cls
    text_representaion_model = BertTextRepresentation(model_path=setting.PRETRAIN_BERT_DIR, emb_layer="cls", max_len=128)
    embedding = text_representaion_model.encode_sentences(text_list=text_list)
    print(cosine_similarity(embedding[0, None, :], embedding[1, None, :]))

    # -1
    text_representaion_model = BertTextRepresentation(model_path=setting.PRETRAIN_BERT_DIR, emb_layer=-1, max_len=128)
    embedding = text_representaion_model.encode_sentences(text_list=text_list)
    print(cosine_similarity(embedding[0, None, :], embedding[1, None, :]))

    # [1, 4]
    text_representaion_model = BertTextRepresentation(model_path=setting.PRETRAIN_BERT_DIR, emb_layer=[1, 4], max_len=128)
    embedding = text_representaion_model.encode_sentences(text_list=text_list)
    print(cosine_similarity(embedding[0, None, :], embedding[1, None, :]))