from model.TraAndSim.zh_wiki import zh2Hans, zh2Hant

class TraAndSim(object):
    def __init__(self):
        max_len = max([len(k) for k in zh2Hans.keys()])
        self.zh2Hans = [{} for _ in range(max_len+1)]
        for k, v in zh2Hans.items():
            self.zh2Hans[len(k)][k] = v

        max_len = max([len(k) for k in zh2Hant.keys()])
        self.zh2Hant = [{} for _ in range(max_len + 1)]
        for k, v in zh2Hant.items():
            self.zh2Hant[len(k)][k] = v

    def tra_or_sim(self, text, target="sim"):
        """
        target: sim简体中文,tra繁体中文
        """
        if target == "sim":
            trans_dict = self.zh2Hans
        else:
            trans_dict = self.zh2Hant

        start_idx = 0
        end_idx = len(text)
        new_text = []
        while start_idx < end_idx:
            find_word = False
            for cur_len in range(len(trans_dict)-1, 0, -1):
                if start_idx + cur_len <= end_idx:
                    if text[start_idx:start_idx+cur_len] in trans_dict[cur_len]:
                        find_word = True
                        new_text.append(trans_dict[cur_len].get(text[start_idx:start_idx+cur_len]))
                        start_idx = start_idx + cur_len
                    if find_word:
                        break
            if not find_word:
                new_text.append(text[start_idx])
                start_idx += 1
        return "".join(new_text)


if __name__ == "__main__":
    model = TraAndSim()
    sim_text = "未接通的错误码"
    tra_text = model.tra_or_sim(sim_text, target="tra")
    print(tra_text)
    new_sim_text = model.tra_or_sim(tra_text, target="sim")
    print(new_sim_text)
    new_sim_text = model.tra_or_sim(sim_text, target="sim")
    print(new_sim_text)
