import time

from module.Prompt.prompt import get_translate_prompt
from model.CallLLM.CallOpenAI import CallOpenAI
from model.TraAndSim.TraAndSim import TraAndSim
import json
from setting import logger


class TranslateGPT:
    def __init__(self):
        self.tra_and_sim = TraAndSim()
        self.call_openai_model = CallOpenAI()

    def translate_text(self, text_list, source_lang, target_langs, parallel=True):
        """
        text_list: 待翻译文本列表
        source_lang: 文本源语言,string,例如:中文简体、中文繁体、粤语、英文
        target_lang: 文本目标语言,list,例如:中文简体、中文繁体、粤语、英文
        """
        start_time = time.time()
        all_translate_list = []

        for target_lang in target_langs:
            if source_lang == target_lang:
                all_translate_list.extend(text_list)
                continue
            if source_lang == "简体中文" and target_lang == "繁体中文":
                trans_result = [self.tra_and_sim.tra_or_sim(t_, target="tra") for t_ in text_list]
                for i in range(len(text_list)):
                    logger.debug(f"翻译{source_lang}-{target_lang}:{text_list[i]}==>{trans_result[i]}")
                all_translate_list.extend(trans_result)
                continue
            if source_lang == "繁体中文" and target_lang == "简体中文":
                trans_result = [self.tra_and_sim.tra_or_sim(t_, target="sim") for t_ in text_list]
                for i in range(len(text_list)):
                    logger.debug(f"翻译{source_lang}-{target_lang}:{text_list[i]}==>{trans_result[i]}")
                all_translate_list.extend(trans_result)
                continue

            def llm_assert_func(temp_string):
                try:
                    temp = json.loads(temp_string)
                    assert isinstance(temp, dict) and "translate" in temp
                    return True
                except:
                    return False

            if parallel:
                translate_user_prompt_list = []
                for text in text_list:
                    prompt_translate = get_translate_prompt(text=text, source_lang=source_lang, target_lang=target_lang)
                    translate_user_prompt_list.append(prompt_translate)
                translate_list_dict = self.call_openai_model.run_parallel(user_prompt_list=translate_user_prompt_list, retry_times=10, assert_func=llm_assert_func, n_threads=3)
                translate_list = []
                for t_l_d in translate_list_dict:
                    try:
                        t_l_d_dict = json.loads(t_l_d)
                        translate_list.append(t_l_d_dict["translate"])
                    except:
                        translate_list.append(t_l_d)
                if target_lang == "繁体中文":
                    translate_list = [self.tra_and_sim.tra_or_sim(t_, target="tra") for t_ in translate_list]
                if target_lang == "简体中文":
                    translate_list = [self.tra_and_sim.tra_or_sim(t_, target="sim") for t_ in translate_list]
                for i in range(len(text_list)):
                    logger.debug(f"翻译{source_lang}-{target_lang}:{text_list[i]}==>{translate_list[i]}")
            else:
                translate_list = []
                for text in text_list:
                    prompt_translate = get_translate_prompt(text=text, source_lang=source_lang, target_lang=target_lang)
                    translate_text = self.call_openai_model.run(user_prompt=prompt_translate, retry_times=3, assert_func=llm_assert_func)
                    if translate_text is None or len(translate_text.strip()) == 0:
                        translate_list.append(text)
                    else:
                        try:
                            translate_text_json = json.loads(translate_text)
                            translate_list.append(translate_text_json["translate"])
                        except:
                            translate_list.append(translate_text)
                    logger.debug(f"翻译{source_lang}-{target_lang}:{text}==>{translate_list[-1]}")
                if target_lang == "繁体中文":
                    translate_list = [self.tra_and_sim.tra_or_sim(t_, target="tra") for t_ in translate_list]
                if target_lang == "简体中文":
                    translate_list = [self.tra_and_sim.tra_or_sim(t_, target="sim") for t_ in translate_list]
            all_translate_list.extend(translate_list)
        total_time = time.time() - start_time

        all_translate_list = [t if isinstance(t, str) else str(t) for t in all_translate_list]  # 有些文本可能是字典类型
        logger.debug(f"翻译完成,总耗时:{total_time},原始文本数:{len(text_list)},翻译后文本数:{len(all_translate_list)},平均每条文本耗时:{total_time/len(all_translate_list)}")
        return all_translate_list

    def translate_text_parallel(self, text_list, source_lang, target_langs):
        """
        text_list: 待翻译文本列表
        source_lang: 文本源语言,string,例如:中文简体、中文繁体、粤语、英文
        target_lang: 文本目标语言,string,例如:中文简体、中文繁体、粤语、英文
        """
        start_time = time.time()
        all_translate_list = []

        for target_lang in target_langs:
            if source_lang == target_lang:
                all_translate_list.extend(text_list)
                continue
            if source_lang == "简体中文" and target_lang == "繁体中文":
                all_translate_list.extend([self.tra_and_sim.tra_or_sim(t_, target="tra") for t_ in text_list])
                continue
            if source_lang == "繁体中文" and target_lang == "简体中文":
                all_translate_list.extend([self.tra_and_sim.tra_or_sim(t_, target="sim") for t_ in text_list])
                continue

            def llm_assert_func(temp_string):
                try:
                    temp = json.loads(temp_string)
                    assert isinstance(temp, dict) and "translate" in temp
                    return True
                except:
                    return False

            translate_list = []
            translate_user_prompt_list = []
            for text in text_list:
                prompt_translate = get_translate_prompt(text=text, source_lang=source_lang, target_lang=target_lang)
                translate_user_prompt_list.append(prompt_translate)
            translate_list = self.call_openai_model.run_parallel(user_prompt_list=translate_user_prompt_list, retry_times=3, assert_func=llm_assert_func, n_threads=3)
            logger.debug(f"翻译{source_lang}-{target_lang}:{text}==>{translate_list[-1]}")
            all_translate_list.extend(translate_list)

        total_time = time.time() - start_time
        logger.debug(f"翻译完成,总耗时:{total_time},原始文本数:{len(text_list)},翻译后文本数:{len(all_translate_list)},平均每条文本耗时:{total_time/len(all_translate_list)}")
        return all_translate_list

if __name__ == "__main__":
    translate_model = TranslateGPT()

    text_list_1 = ["从我的微众银行卡的余额转出100到我尾号1201的招商银行贷款卡上", "转账给我自己", "我要去微众银行"]

    start_main = time.time()
    translate_list_1 = translate_model.translate_text(text_list=text_list_1, source_lang="简体中文", target_langs=["简体中文", "繁体中文", "英文", "粤语"], parallel=False)
    for t in translate_list_1:
        print(t)
    print(f"非并发耗时:{time.time()-start_main}")

    start_main = time.time()
    translate_list_2 = translate_model.translate_text(text_list=text_list_1, source_lang="简体中文", target_langs=["简体中文", "繁体中文", "英文", "粤语"], parallel=True)
    for t in translate_list_2:
        print(t)
    print(f"并发耗时:{time.time()-start_main}")
    print("wait")
