# -*- coding: utf-8 -*-
import json
import os
import pickle

import numpy as np

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from setting import logger
from transformers import BertConfig, BertTokenizer
from transformers.models.bert import TFBertForSequenceClassification
from utils.my_utils import remove_symbol
from utils.onnx_utils import create_session, convert_tf2onnx, inference_with_onnx
from model.TraAndSim.TraAndSim import TraAndSim


class YesOrNoModel:
    def __init__(self):
        self.models = dict()
        self.model_id = "model"
        self.use_onnx = False
        self.tar_and_sim = TraAndSim()
        # 1218最好版本
        self.load_model(self.model_id, os.path.join(setting.MAIN_DIR, "model/YesOrNoModel/model/YesOrNo_231226_m1_our_gpt_log_qa_replace_zh2yue_unrelate_answer_posneganswer_norepeat_nodirty_sim_e2/BertClassifier"))

    def load_model(self, model_id, save_dir):
        try:
            h5_model_path = os.path.join(save_dir, "tf_model.h5")
            if not os.path.exists(h5_model_path):
                raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: 模型文件不存在!")

            tokenizer = BertTokenizer.from_pretrained(save_dir)

            if not self.use_onnx:
                # 不用onnx
                config = BertConfig.from_pretrained(save_dir)
                model = TFBertForSequenceClassification.from_pretrained(
                    h5_model_path, config=config, from_pt=False
                )
                inputs = self.get_data(texts=["测试一下"], tokenizer=tokenizer)
                model(inputs)
            else:
                # onnx
                onnx_model_path = os.path.join(save_dir, "tf_model.onnx")
                if not os.path.exists(onnx_model_path):
                    config = BertConfig.from_pretrained(save_dir)
                    model = TFBertForSequenceClassification.from_pretrained(
                        h5_model_path, config=config, from_pt=False
                    )
                    input_signature = [tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='input_ids'),
                                       tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='token_type_ids'),
                                       tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='attention_mask')]
                    convert_tf2onnx(model, onnx_model_path, input_signature=input_signature, enable_overwrite=True)
                model = create_session(onnx_model_path=onnx_model_path, intra_op_num_threads=2)
                model.use_onnx = True

            with open(os.path.join(save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)
            with open(os.path.join(save_dir, "labelid2id.json"), "r", encoding="utf-8") as f:
                labelid2id = json.load(fp=f)
            with open(os.path.join(save_dir, "labelid2title.json"), "r", encoding="utf-8") as f:
                labelid2title = json.load(fp=f)
            id2labelid = {}
            for key, value in labelid2id.items():
                id2labelid[value] = key

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['model'] = model
            self.models[model_id]['id2labelid'] = id2labelid
            self.models[model_id]['labelid2id'] = labelid2id
            self.models[model_id]['labelid2title'] = labelid2title
            self.models[model_id]['text2item_cm'] = text2item_cm

            self.predict(question="你是不是他", answer="不是", label_ids=None)
            logger.debug(f"模型自测完成 - model_id: {model_id}")
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}", exc_info=True)
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, question, answer, label_ids=None, topk=1):
        result = []
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": ""
        }

        query = f"问:{question}\n答:{answer}"
        query = self.tar_and_sim.tra_or_sim(query, target="sim")
        if isinstance(query, str):
            querys = [query]
        else:
            querys = query

        tokenizer = self.models[self.model_id]['tokenizer']
        model = self.models[self.model_id]['model']
        id2labelid = self.models[self.model_id]['id2labelid']
        labelid2id = self.models[self.model_id]['labelid2id']
        labelid2title = self.models[self.model_id]['labelid2title']
        text2item_cm = self.models[self.model_id]['text2item_cm']

        if label_ids is None or len(label_ids) == 0:
            label_ids_index = list(labelid2id.values())
        else:
            label_ids_index = [labelid2id[l_i] for l_i in label_ids if l_i in labelid2id]

        if len(label_ids_index) == 0:  # 传入的labelid都不在模型内则不使用labelid
            label_ids_index = list(labelid2id.values())

        fully_match_query_indexs = {}
        for fully_match_query_index, query in enumerate(querys):
            query_ = remove_symbol(query)
            res = {"label_id": 0, "label_title": 0, "score": 0, "margin_score": 0}
            if query_ in text2item_cm:
                labelid = text2item_cm[query_]
                if label_ids is None or labelid in label_ids or len(label_ids) == 0:
                    res["label_id"] = labelid
                    res["label_title"] = labelid2title[labelid]
                    res["score"] = 1
                    res["margin_score"] = 0
                    fully_match_query_indexs[fully_match_query_index] = res
                    result.append(res)
        if len(result) == len(querys):
            return result, error_dict
        result = []

        all_logits = []
        for i in range(0, len(querys), setting.BERT_CLASSIFICATION_BATCH_SIZE):
            batch_query = querys[i:i+setting.BERT_CLASSIFICATION_BATCH_SIZE]
            inputs = self.get_data(batch_query, tokenizer)
            if hasattr(model, 'use_onnx'):
                logits = inference_with_onnx(model, inputs)[5]
            else:
                pred = model.predict(inputs)
                logits = pred.logits
            all_logits.extend(list(logits))

        for logits in all_logits:
            logits = logits.reshape(1, -1)
            for i in range(logits.shape[1]):
                if i not in label_ids_index:
                    logits[0, i] = -1e10
            prob = tf.nn.softmax(logits, axis=-1).numpy()[0, :]
            index = np.argsort(prob)

            if topk == 0:
                index = index[::-1]
            else:
                index = index[-topk:][::-1]

            for i, idx in enumerate(index):
                if idx not in label_ids_index:
                    continue
                res = {"label_id": 0, "label_title": 0, "score": 0, "margin_score": 0}
                label_id = id2labelid[idx]
                label_title = labelid2title[label_id]
                score = prob[idx]
                if prob.shape[0] <= 1:
                    margin_score = score
                elif i < len(index) - 1:
                    margin_score = score - prob[index[i+1]]
                else:
                    margin_score = 0
                res["label_id"] = label_id
                res["label_title"] = label_title
                res["score"] = score
                res["margin_score"] = margin_score
                result.append(res)

        for index, v in fully_match_query_indexs.items():  # 完全匹配的句子得分为1
            result[index] = v
        return result, error_dict

    @staticmethod
    def get_data(texts, tokenizer):
        input_ids = np.zeros(shape=(len(texts), setting.BERT_CLASSIFICATION_MAX_LEN), dtype=np.int32)
        token_type_ids = np.zeros(shape=(len(texts), setting.BERT_CLASSIFICATION_MAX_LEN), dtype=np.int32)
        attention_mask = np.zeros(shape=(len(texts), setting.BERT_CLASSIFICATION_MAX_LEN), dtype=np.int32)

        for i, text in enumerate(texts):
            inputs_ = tokenizer.encode_plus(text, padding='max_length', truncation=True, max_length=setting.BERT_CLASSIFICATION_MAX_LEN)
            input_ids[i, :] = inputs_["input_ids"]
            attention_mask[i, :] = inputs_["attention_mask"]
            token_type_ids[i, :] = inputs_["token_type_ids"]

        return {
            'input_ids': input_ids,
            'token_type_ids': token_type_ids,
            'attention_mask': attention_mask
        }


if __name__ == "__main__":
    main_model = YesOrNoModel()

    # 预测
    main_texts = [[f"你今年的税费交了吗？", "我交了"],
                  [f"你今年的税费交了吗？", "我没交"],
                  ["你今年的税费交了吗？", "我有牌照"],
                  ["你今年的税费交了吗？", "我没有牌照"],
                  ["需不需要支付?", "我有牌照"],
                  ["你是否有过写代码的经验？", "我有牌照"],
                  ["你今年的税费交了吗？", "有没交税费"],
                  ["你今年的税费交了吗？", "仲没交税费"],
                  ["你今年的税费交了吗？", "冇交税费"],
                  ["有没有牌照？", "搞錯我說我沒有牌照"]]
    for main_q, main_a in main_texts:
        print(f"问:{main_q}\n答:{main_a}")
        result = main_model.predict(question=main_q, answer=main_a, label_ids=[])
        print(result)
        # print("wait")
    print("wait")