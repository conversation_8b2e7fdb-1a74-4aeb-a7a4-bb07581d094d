import json

import gradio as gr
import mdtex2html

from model.YesOrNoModel.YesOrNoModel import YesOrNoModel

yes_or_no_model = YesOrNoModel()
save_file = open("./log_data/data.jsonl", "a", encoding="utf-8")

def postprocess(self, y):
    if y is None:
        return []
    for i, (message, response) in enumerate(y):
        y[i] = (
            None if message is None else mdtex2html.convert((message)),
            None if response is None else mdtex2html.convert(response),
        )
    return y


def parse_text(text):
    """copy from https://github.com/GaiZhenbiao/ChuanhuChatGPT/"""
    lines = text.split("\n")
    lines = [line for line in lines if line != ""]
    count = 0
    for i, line in enumerate(lines):
        if "```" in line:
            count += 1
            items = line.split('`')
            if count % 2 == 1:
                lines[i] = f'<pre><code class="language-{items[-1]}">'
            else:
                lines[i] = f'<br></code></pre>'
        else:
            if i > 0:
                if count % 2 == 1:
                    line = line.replace("`", "\`")
                    line = line.replace("<", "&lt;")
                    line = line.replace(">", "&gt;")
                    line = line.replace(" ", "&nbsp;")
                    line = line.replace("*", "&ast;")
                    line = line.replace("_", "&lowbar;")
                    line = line.replace("-", "&#45;")
                    line = line.replace(".", "&#46;")
                    line = line.replace("!", "&#33;")
                    line = line.replace("(", "&#40;")
                    line = line.replace(")", "&#41;")
                    line = line.replace("$", "&#36;")
                lines[i] = "<br>" + line
    text = "\n".join(lines)
    print("text: ", text)
    return text


gr.Chatbot.postprocess = postprocess


def clear_session(sission):
    return "", "", None


def parse_gpt(question, answer):
    result = yes_or_no_model.predict(question=question, answer=answer)
    if int(result[0][0]["label_id"]) == 1:
        result_str = "否定"
    else:
        result_str = "肯定"
    save_file.write(json.dumps({"question": question, "answer": answer, "pred": result_str}, ensure_ascii=False) + '\n')
    save_file.flush()
    print(question, answer, result_str)
    return result_str

if __name__ == "__main__":
    block = gr.Blocks()
    with block as demo:
        gr.Markdown("""<h1><center>肯定否定意图</center></h1>""")

        # 抽取问答对
        gr.Markdown("## 肯定否定意图")
        with gr.Row():
            with gr.Column(scale=3):
                question_textbox = gr.components.Textbox(label="问题").style(height=200)
                answer_textbox = gr.components.Textbox(label="回答").style(height=200)
                send = gr.Button("🚀 预测")
            with gr.Column(scale=2):
                result_textbox = gr.components.Textbox(label="预测结果").style(height=500)
            send.click(parse_gpt, inputs=[question_textbox, answer_textbox], outputs=[result_textbox], show_progress=True)

    demo.queue(concurrency_count=3) \
        .launch(server_name='0.0.0.0',
                # ip for listening, 0.0.0.0 for every inbound traffic, 127.0.0.1 for local inbound
                server_port=7870,  # the port for listening
                show_api=False,  # if display the api document
                share=True,  # if register a public url
                inbrowser=False,  # if browser would be open automatically
                enable_queue=True)
