# -*- coding: utf-8 -*-
import json
import os
import pickle
import time

import numpy as np

import setting
from database.REDIS import REDIS

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

import jieba
from setting import logger
from transformers import BertConfig, BertTokenizer
from transformers.models.bert import TFBertForSequenceClassification
from sklearn.model_selection import train_test_split
from module.Classification.BertClassifier.DataGenerator import DataGenerator
from utils.my_utils import MyEncoder, remove_symbol
from sklearn.feature_extraction.text import CountVectorizer, TfidfTransformer
from utils.onnx_utils import create_session, convert_tf2onnx,inference_with_onnx


class BertClassifier:
    def __init__(self):
        self.models = dict()
        self.adjusted_ratio = 1
        self.redis = REDIS()
        self.ratio_name = "adjusted_ratio_" + str(self.__class__.__name__)

    def build_model(self, text):
        pass

    def train(self, model_id, data, save_dir):
        start_time = time.time()
        estimated_train_time = self.train_time(data, get_baseline=True)[0]
        logger.debug(f"预估训练耗时：{round(estimated_train_time * self.adjusted_ratio,2)}秒,即{round(estimated_train_time * self.adjusted_ratio / 60, 3)}分钟，使用上一次预估耗时的调整比例值为：{self.adjusted_ratio}")
        logger.debug(f"准备开始训练 - model_id: {model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载数据、预训练模型
        tokenizer = BertTokenizer.from_pretrained(setting.classification_PRETRAIN_BERT_VOCAB)
        data_train, len_train, data_valid, len_valid, labelid2id, labelid2title, text2item_cm = \
            self.get_data_generator(data=data, tokenizer=tokenizer)
        config = BertConfig.from_pretrained(setting.classification_PRETRAIN_BERT_CONFIG, num_labels=len(labelid2id))
        model = TFBertForSequenceClassification.from_pretrained(
            setting.classification_PRETRAIN_BERT_MODEL_TF, config=config, from_pt=False
        )

        id2labelid = {}
        for key, value in labelid2id.items():
            id2labelid[value] = key
        logger.debug(f"数据加载完成 - 训练集数据量: {len_train}, 验证集数据量: {len_valid}")

        # 训练模型
        model.save_pretrained(save_dir)
        model_file = os.path.join(save_dir, 'tf_model.h5')

        opt = tf.keras.optimizers.Adam(learning_rate=setting.BERT_CLASSIFICATION_LEARNING_RATE, epsilon=1e-08)
        loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
        metrics = [tf.keras.metrics.SparseCategoricalAccuracy("accuracy")]
        model.compile(optimizer=opt, loss=loss, metrics=metrics)
        callbacks = [
            tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_accuracy', save_best_only=True, save_weights_only=True),
            # tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=10),
            # tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=5, verbose=0), # 当 patience 次迭代损失未改善，Keras停止训练
        ]
        logger.debug(f"开始训练 - model_id: {model_id}")
        history = model.fit(
            data_train,
            epochs=setting.BERT_CLASSIFICATION_EPOCHS,
            validation_data=data_valid,
            callbacks=callbacks
        )
        #onnx部分
        model.load_weights(os.path.join(save_dir, 'tf_model.h5'))
        onnx_model_path = os.path.join(save_dir, "tf_model.onnx")
        input_signature = [tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='input_ids'),
                           tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='token_type_ids'),
                           tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='attention_mask')]
        convert_tf2onnx(model, onnx_model_path, input_signature=input_signature, enable_overwrite=True)

        # 保存模型
        tokenizer.save_pretrained(save_dir)
        config.save_pretrained(save_dir)
        with open(os.path.join(save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "labelid2id.json"), "w", encoding="utf-8") as f:
            json.dump(labelid2id, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, "labelid2title.json"), "w", encoding="utf-8") as f:
            json.dump(labelid2title, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, 'train_history.json'), 'w', encoding='utf-8') as f:
            json.dump(history.history, fp=f, cls=MyEncoder, ensure_ascii=False, indent=2)

        mixed = self.puzzl_label(model_id, save_dir, data)

        # self.models[model_id] = dict()
        # self.models[model_id]['tokenizer'] = tokenizer
        # self.models[model_id]['model'] = model
        # self.models[model_id]["labelid2id"] = labelid2id
        # self.models[model_id]["id2labelid"] = id2labelid
        # self.models[model_id]["labelid2title"] = labelid2title
        # self.models[model_id]["text2item_cm"] = text2item_cm

        score = np.max(history.history["val_accuracy"])

        real_train_time = time.time() - start_time
        self.adjusted_ratio = round(real_train_time / estimated_train_time, 4)

        # 比例值写入redis
        self.redis.set_data(self.ratio_name, str(self.adjusted_ratio))
        logger.debug(f"修正后预估耗时：{round(estimated_train_time * self.adjusted_ratio,2)}秒,即{round(estimated_train_time * self.adjusted_ratio / 60, 3)}分钟，更新预估耗时的调整比例值为：{self.adjusted_ratio}")
        logger.debug(f"训练成功 - model_id:{model_id}, 训练得分:{score}, 耗时:{time.time() - start_time}s,数据量:{len_train + len_valid}, batch:{setting.BERT_CLASSIFICATION_BATCH_SIZE}, epoch:{setting.BERT_CLASSIFICATION_EPOCHS}")

        return score, mixed


    def puzzl_label(self, model_id, save_dir, data):
        start = time.time()
        texts = []
        labelids = []
        belong_konwledge_mapper = {}
        for data_dict in data:
            labelid = data_dict["labelId"]
            for t in data_dict["labelData"].strip().split("||"):
                if len(t.strip()):
                    texts.append(t.strip())
                    labelids.append(labelid)
            belong_konwledge_mapper[labelid] = '知识' if data_dict.get('isKnowledge',False) else '流程'
        print(f"puzzl 数据量:{len(texts)}, time:{time.time()-start}")

        self.load_model(model_id=model_id, save_dir=save_dir)
        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        id2labelid = self.models[model_id]['id2labelid']
        labelid2title = self.models[model_id]['labelid2title']

        inputs = self.get_data(texts=texts, tokenizer=tokenizer)
        if hasattr(model,'use_onnx'):
            logits = []
            for i in range(0, len(texts), setting.BERT_CLASSIFICATION_BATCH_SIZE):
                batch_query = texts[i:i + setting.BERT_CLASSIFICATION_BATCH_SIZE]
                inputs = self.get_data(batch_query, tokenizer)
                if hasattr(model, 'use_onnx'):
                    logit = inference_with_onnx(model, inputs)[0]
                else:
                    pred = model.predict(inputs)
                    logit = pred.logits
                logits.extend(list(logit))
            logits = tf.convert_to_tensor(logits)
        else:
            pred = model.predict(inputs, batch_size=setting.BERT_CLASSIFICATION_BATCH_SIZE)
            logits = pred.logits
        print(f"puzzl 预测完成, pred:{logits.shape}, time:{time.time()-start}")

        prob = tf.nn.softmax(logits, axis=-1).numpy()
        error_dict = {}
        for i in range(prob.shape[0]):
            index = np.argmax(prob[i, :])
            true_label_id = labelids[i]
            pred_label_id = id2labelid[index]
            if true_label_id == pred_label_id:
                continue
            key = (true_label_id, pred_label_id)
            if key not in error_dict:
                error_dict[key] = []
            error_dict[key].append(texts[i])
        self.offline_model(model_id=model_id)

        mixed = []
        error_text_num = 0
        for key, value in error_dict.items():
            if len(value) >= 1:
                error_text_num += len(value)
                mixed.append({"label": key[0], "pred_label": key[1],
                              "label_name": labelid2title[key[0]], "pred_label_name": labelid2title[key[1]],
                              'label_belong': belong_konwledge_mapper[key[0]],'pred_label_belong': belong_konwledge_mapper[key[1]],
                              "texts":value, "num_text": len(value)})
        mixed.sort(key=lambda x: -len(x["texts"]))
        print(f"puzzl 整理结束, mixed len:{len(mixed)}, error_text_num:{error_text_num}, time:{time.time()-start}")

        # TFIDF 抽关键词
        corpos = [' '.join(jieba.lcut(text)) for text in texts]
        vectorizer = CountVectorizer(token_pattern=r"(?u)\b\w+\b")
        transformer = TfidfTransformer()
        tfidf = transformer.fit_transform(vectorizer.fit_transform(corpos))
        labelids = np.array(labelids)
        labelid2tfidfvec = {}
        for i in id2labelid.values():
            if i in labelids:
                vec = np.mean(tfidf[labelids == i], axis=0)
                labelid2tfidfvec[i] = vec.__array__()[0, :]
            else:
                labelid2tfidfvec[i] = np.zeros(shape=(len(vectorizer.vocabulary_)))
        for mixed_dict in mixed:
            pred_tfidf = labelid2tfidfvec[mixed_dict["pred_label"]]
            text_tfidf = transformer.transform(
                vectorizer.transform([' '.join(jieba.lcut(t)) for t in mixed_dict["texts"]]))
            text_tfidf = np.mean(text_tfidf, axis=0).__array__()[0, :]
            pred_tfidf = text_tfidf * pred_tfidf
            index = pred_tfidf.argsort()[-5:][::-1]
            key_words = [vectorizer.get_feature_names()[i] for i in index]
            mixed_dict["keywords"] = key_words
        print(f"puzzl 抽取关键词结束, 单词数:{len(vectorizer.get_feature_names())}, time:{time.time()-start}")

        return mixed

    def load_model(self, model_id, save_dir):
        try:
            h5_model_path = os.path.join(save_dir, "tf_model.h5")
            if not os.path.exists(h5_model_path):
                raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: 模型文件不存在!")

            tokenizer = BertTokenizer.from_pretrained(save_dir)
            #onnx
            onnx_model_path = os.path.join(save_dir, "tf_model.onnx")
            if not os.path.exists(onnx_model_path):
                config = BertConfig.from_pretrained(save_dir)
                model = TFBertForSequenceClassification.from_pretrained(
                    h5_model_path, config=config, from_pt=False
                )
                input_signature = [tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='input_ids'),
                                   tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='token_type_ids'),
                                   tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='attention_mask')]
                convert_tf2onnx(model, onnx_model_path, input_signature=input_signature, enable_overwrite=True)
            model = create_session(onnx_model_path=onnx_model_path, intra_op_num_threads=2)
            model.use_onnx = True
            with open(os.path.join(save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)
            with open(os.path.join(save_dir, "labelid2id.json"), "r", encoding="utf-8") as f:
                labelid2id = json.load(fp=f)
            with open(os.path.join(save_dir, "labelid2title.json"), "r", encoding="utf-8") as f:
                labelid2title = json.load(fp=f)
            id2labelid = {}
            for key, value in labelid2id.items():
                id2labelid[value] = key

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['model'] = model
            self.models[model_id]['id2labelid'] = id2labelid
            self.models[model_id]['labelid2id'] = labelid2id
            self.models[model_id]['labelid2title'] = labelid2title
            self.models[model_id]['text2item_cm'] = text2item_cm

            self.predict(model_id=model_id, query="测试一下", labelIds=None)
            logger.debug(f"模型自测完成 - model_id: {model_id}")
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}",exc_info=True)
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, query, labelIds=None, topk=1):
        result = []
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": ""
        }

        if isinstance(query, str):
            querys = [query]
        else:
            querys = query

        if model_id not in self.models:
            try:
                is_exist = self.check_model_file_exist(model_id=model_id)
                if is_exist:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91024"
                    error_dict["error_msg"] = "预测错误: 模型未加载，请重新上线"
                else:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91025"
                    error_dict["error_msg"] = "预测错误: 模型未加载，模型文件缺失，请重新训练"
            except Exception as ee:
                error_dict["error_type"] = 0
                error_dict["error_code"] = "NLU91017"
                error_dict["error_msg"] = f"预测错误: 模型未加载, {ee}"
            logger.error(f"预测失败 [{model_id}], 错误信息: {error_dict['error_msg']}")
            return result, error_dict

        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        id2labelid = self.models[model_id]['id2labelid']
        labelid2id = self.models[model_id]['labelid2id']
        labelid2title = self.models[model_id]['labelid2title']
        text2item_cm = self.models[model_id]['text2item_cm']

        if labelIds is None or len(labelIds) == 0:
            labelIds_index = list(labelid2id.values())
        else:
            labelIds_index = [labelid2id[l] for l in labelIds if l in labelid2id]

        if len(labelIds_index)==0: #传入的labelid都不在模型内则不使用labelid
            labelIds_index = list(labelid2id.values())

        fully_match_query_indexs = {}
        for fully_match_query_index,query in enumerate(querys):
            query_ = remove_symbol(query)
            res = {"label_id": 0, "label_title": 0, "score": 0, "margin_score": 0}
            if query_ in text2item_cm:
                labelid = text2item_cm[query_]
                if labelIds is None or labelid in labelIds or labelIds==[]:
                    res["label_id"] = labelid
                    res["label_title"] = labelid2title[labelid]
                    res["score"] = 1
                    res["margin_score"] = 0
                    fully_match_query_indexs[fully_match_query_index] = res
                    result.append(res)
        if len(result) == len(querys):
            return result, error_dict
        result = []

        all_logits = []
        for i in range(0,len(querys),setting.BERT_CLASSIFICATION_BATCH_SIZE):
            batch_query = querys[i:i+setting.BERT_CLASSIFICATION_BATCH_SIZE]
            inputs = self.get_data(batch_query, tokenizer)
            if hasattr(model,'use_onnx'):
                logits = inference_with_onnx(model, inputs)[0]
            else:
                pred = model.predict(inputs)
                logits = pred.logits
            all_logits.extend(list(logits))

        for logits in all_logits:
            logits = logits.reshape(1,-1)
            for i in range(logits.shape[1]):
                if i not in labelIds_index:
                    logits[0, i] = -1e10
            prob = tf.nn.softmax(logits, axis=-1).numpy()[0, :]
            index = np.argsort(prob)

            if topk == 0:
                index = index[::-1]
            else:
                index = index[-(topk):][::-1]

            for i, idx in enumerate(index):
                if idx not in labelIds_index:
                    continue
                res = {"label_id": 0, "label_title": 0, "score": 0, "margin_score": 0}
                label_id = id2labelid[idx]
                label_title = labelid2title[label_id]
                score = prob[idx]
                if prob.shape[0] <= 1:
                    margin_score = score
                elif i < len(index) - 1:
                    margin_score = score - prob[index[i+1]]
                else:
                    margin_score = 0
                res["label_id"] = label_id
                res["label_title"] = label_title
                res["score"] = score
                res["margin_score"] = margin_score
                result.append(res)

        for index, v in fully_match_query_indexs.items(): #完全匹配的句子得分为1
            result[index] = v
        return result, error_dict

    def labeling(self, model_id, texts, text_ids=None):
        labeling_result = []

        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        id2labelid = self.models[model_id]['id2labelid']
        labelid2id = self.models[model_id]['labelid2id']
        labelid2title = self.models[model_id]['labelid2title']

        inputs = self.get_data(texts=texts, tokenizer=tokenizer)
        if model.use_onnx:
            logits = inference_with_onnx(model, inputs)[0]
        else:
            pred = model.predict(inputs, batch_size=8)
            logits = pred.logits

        prob = tf.nn.softmax(logits, axis=-1).numpy()

        for i in range(prob.shape[0]):
            index = np.argsort(prob[i, :])
            label_id = id2labelid[index[-1]]
            label_title = labelid2title[label_id]
            score = prob[i, index[-1]]
            if prob.shape[1] <= 1:
                margin_score = score
            else:
                margin_score = prob[i, index[-1]] - prob[i, index[-2]]
            result = {
                "text": texts[i],
                "id": text_ids[i] if text_ids is not None else None,
                "label": label_id,
                "score": score,
                "margin_score": margin_score
            }
            labeling_result.append(result)
        return labeling_result

    def check_model_file_exist(self, model_id):
        model_file_exist = True
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")

        if not os.path.exists(os.path.join(save_dir, "tf_model.h5")):
            model_file_exist = False
        if not os.path.exists(os.path.join(save_dir, "text2item_cm.pkl")):
            model_file_exist = False
        if not os.path.exists(os.path.join(save_dir, "labelid2id.json")):
            model_file_exist = False
        if not os.path.exists(os.path.join(save_dir, "labelid2title.json")):
            model_file_exist = False
        if not model_file_exist:
            return False

        try:
            tokenizer = BertTokenizer.from_pretrained(save_dir)
            config = BertConfig.from_pretrained(save_dir)
        except:
            model_file_exist = False
        return model_file_exist

    @staticmethod
    def get_data(texts, tokenizer):
        input_ids = np.zeros(shape=(len(texts), setting.BERT_CLASSIFICATION_MAX_LEN), dtype=np.int32)
        token_type_ids = np.zeros(shape=(len(texts), setting.BERT_CLASSIFICATION_MAX_LEN), dtype=np.int32)
        attention_mask = np.zeros(shape=(len(texts), setting.BERT_CLASSIFICATION_MAX_LEN), dtype=np.int32)

        for i, text in enumerate(texts):
            inputs_ = tokenizer.encode_plus(text, padding='max_length', truncation=True, max_length=setting.BERT_CLASSIFICATION_MAX_LEN)
            input_ids[i, :] = inputs_["input_ids"]
            attention_mask[i, :] = inputs_["attention_mask"]
            token_type_ids[i, :] = inputs_["token_type_ids"]

        return {
            'input_ids': input_ids,
            'token_type_ids': token_type_ids,
            'attention_mask': attention_mask
        }

    @staticmethod
    def get_data_generator(data, tokenizer=None):
        # 整理数据, id 表示训练用的 id (0, 1, 2), labelid 表示传入的意图 id
        id2text_list = {}
        labelid2id = {}
        labelid2title = {}
        text2item_cm = dict() # 精确匹配字典

        for data_dict in data:
            title = data_dict['title'].strip()
            labelid = data_dict["labelId"]
            texts = data_dict["labelData"].strip()

            labelid2title[labelid] = title
            if labelid not in labelid2id.keys():
                labelid2id[labelid] = len(labelid2id)
            train_id = labelid2id[labelid]

            if train_id not in id2text_list.keys():
                id2text_list[train_id] = []

            if len(title):
                id2text_list[train_id].append(title)
            title_strip = remove_symbol(title)
            if len(title_strip):
                text2item_cm[title_strip] = labelid

            for text in texts.split("||"):
                text_strip = text.strip()
                if len(text_strip):
                    id2text_list[train_id].append(text_strip)
                text_strip = remove_symbol(text_strip)
                if len(text_strip):
                    text2item_cm[text_strip] = labelid

        # 划分数据集
        id2text_list_train = {}
        id2text_list_valid = {}
        num_train_text = 0
        num_valid_text = 0
        for train_id, texts in id2text_list.items():
            if len(texts) <= 0:
                continue
            elif len(texts) == 1:
                id2text_list_train[train_id] = texts
                num_train_text += len(texts)
            else:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                id2text_list_train[train_id] = texts_train
                id2text_list_valid[train_id] = texts_valid
                num_train_text += len(texts_train)
                num_valid_text += len(texts_valid)

        data_gen_train = DataGenerator(id2text_list=id2text_list_train, tokenizer=tokenizer)
        data_gen_valid = DataGenerator(id2text_list=id2text_list_valid, tokenizer=tokenizer)

        return data_gen_train, num_train_text, data_gen_valid, num_valid_text, labelid2id, labelid2title, text2item_cm

    def train_time(self, data, get_baseline=False):
        # ratio = 0.03121488325633662
        num_text = 0
        for data_dict in data:
            num_text += 1
            texts = data_dict["labelData"].strip()
            num_text += len(texts.split("||"))

        if not get_baseline:
            if self.redis.get_keys(self.ratio_name):
                self.adjusted_ratio = float(self.redis.get_data(self.ratio_name))
                logger.debug(f"读取redis的{self.ratio_name}值为：{self.adjusted_ratio}")

        estimated_time = num_text * setting.BERT_CLASSIFICATION_EPOCHS  #基准参考值
        adjusted_estimated_time = num_text * setting.BERT_CLASSIFICATION_EPOCHS * self.adjusted_ratio

        return estimated_time, adjusted_estimated_time
        # 耗时:123.26945972442627, 数据量:1656, batch:64, epoch:2
        # 耗时:946.5079221725464, 数据量:16018, batch:64, epoch:2
        # 耗时:1528.0756196975708, 数据量:25817, batch:64, epoch:2
        # ratio = 1528.0756196975708/25817/2 = 0.029594368433543225
        # ratio = 946.5079221725464/16018/2 = 0.029545134291813786
        # ratio = 123.26945972442627/1656/2 = 0.037219039771867836

    # def my_test(self, model_id, data):
    #     random.seed(setting.SEED)
    #     # 划分数据集
    #     text_list_valid = []
    #     for data_dict in data:
    #         norm_query = data_dict['title']
    #         texts = set()
    #         texts.add(norm_query)
    #         for text in data_dict['labelData'].split("||"):
    #             if len(text.strip()):
    #                 texts.add(text.strip())
    #         texts = list(texts)
    #         if len(texts) > 1:
    #             texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
    #             text_list_valid.append(texts_valid)
    #
    #     # 测试准确率，正负样本 1:1
    #     acc, total = 0, 0
    #     pbar = tqdm(enumerate(text_list_valid))
    #     for i, text_list in pbar:
    #         pbar.set_description('测试进度')
    #         for text1 in text_list:
    #             # 正样本
    #             text2 = random.choice(text_list)
    #             score = self.predict(model_id=model_id, text_1=text1, text_2=text2)[0][0]
    #             total += 1
    #             if score >= setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD:
    #                 acc += 1
    #
    #             # 负样本
    #             j = random.randint(0, len(text_list_valid)-1)
    #             while j == i:
    #                 j = random.randint(0, len(text_list_valid)-1)
    #             text2 = random.choice(text_list_valid[j])
    #             score = self.predict(model_id=model_id, text_1=text1, text_2=text2)[0][0]
    #             total += 1
    #             if score < setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD:
    #                 acc += 1
    #             acc_dict = {f'acc': acc / total}
    #             pbar.set_postfix(acc_dict)
    #     print(f"准确率: {acc/total}")


if __name__ == "__main__":
    import pandas as pd
    model = BertClassifier()
    Redis = REDIS()

    model_id = f"9527"
    data_key = "faq_temp_data"
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), BertClassifier.__name__)

    # 训练
    start = time.time()
    data = Redis.get_data(data_key)[:100]
    model.train_time(data)
    score, mixed = model.train(model_id=model_id, data=data, save_dir=save_dir)
    print(f"训练得分: {score}, 耗时: {time.time()-start}")

    mixed = model.puzzl_label(model_id, save_dir, data)
    df = pd.DataFrame(mixed)
    df.to_csv("./语料优化建议.csv", index=False, encoding="utf-8-sig")


    # # 优化建议
    # data = Redis.get_data(data_key)
    # mixed = model.puzzl_label(model_id=model_id, data=data, save_dir=save_dir)
    # print(mixed)
    #
    # # 加载微调过的模型
    # model.load_model(model_id=model_id, save_dir=save_dir)
    #
    # # 预测
    # texts = ['你能告诉我什么时候能查到我快速赎回的余额理财到账的钱吗',
    #          '快速赎回的余额理财我什么时候能收到钱',
    #          'IE浏览器登录个人网银显示空白页如何处理',
    #          '数字牛熊证介绍',
    #          'ATM扫码取款支持怎样的账户啊',
    #          "请问余额理财赎回到账时间"]
    # for text in texts:
    #     print(f"\n{text}")
    #     print(model.predict(model_id=model_id, query=text, labelIds=[]))
    #
    # model.labeling(model_id=model_id, texts=["IE浏览器登录个人网银显示空白页如何处理"]*100, text_ids=["123"]*100)
