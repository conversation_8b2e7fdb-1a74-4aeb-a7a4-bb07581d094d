# -*- coding: UTF-8 -*-

import math
import os
import random

import numpy as np

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from tensorflow import keras
from database.REDIS import REDIS


class DataGenerator(keras.utils.Sequence):
    def __init__(self, id2text_list, tokenizer):
        self.id2text_list = id2text_list
        self.features = []
        for train_id, texts in id2text_list.items():
            for text in texts:
                self.features.append((text, train_id))
        self.tokenizer = tokenizer
        self.batch_size = min(len(self.features), setting.BERT_CLASSIFICATION_BATCH_SIZE)
        self.num_batches = math.ceil(len(self.features) / self.batch_size)
        random.seed(setting.SEED)

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return self.num_batches

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        start = index * self.batch_size
        end = min((index+1)*self.batch_size, len(self.features))
        X, Y = self.__data_generation(self.features[start:end])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        random.shuffle(self.features)
        pass

    def __data_generation(self, features):
        """
        生成一个 batch 的数据。
        """
        input_ids = np.zeros(shape=(len(features), setting.BERT_CLASSIFICATION_MAX_LEN), dtype=np.int32)
        token_type_ids = np.zeros(shape=(len(features), setting.BERT_CLASSIFICATION_MAX_LEN), dtype=np.int32)
        attention_mask = np.zeros(shape=(len(features), setting.BERT_CLASSIFICATION_MAX_LEN), dtype=np.int32)
        labels = np.zeros(shape=(len(features), 1))

        for i, (text, train_id) in enumerate(features):
            inputs_ = self.tokenizer.encode_plus(text, padding='max_length', truncation=True, max_length=setting.BERT_CLASSIFICATION_MAX_LEN)
            input_ids[i, :] = inputs_["input_ids"]
            attention_mask[i, :] = inputs_["attention_mask"]
            token_type_ids[i, :] = inputs_["token_type_ids"]
            labels[i, 0] = train_id

        return (
            {
                'input_ids': input_ids,
                'token_type_ids': token_type_ids,
                'attention_mask': attention_mask
            },
            labels
        )


if __name__ == '__main__':
    from sklearn.model_selection import train_test_split
    from transformers import BertTokenizer

    data_key = "outcall_test_data"
    R = REDIS()
    data = R.get_data(data_key)

    # 整理数据, id 表示训练用的 id (0-1), labelid 表示传入的意图 id
    id2text_list = {}
    labelid2id = {}
    labelid2title = {}

    for data_dict in data:
        title = data_dict['title']
        labelid = data_dict["labelId"]
        texts = data_dict["labelData"]

        labelid2title[labelid] = title
        if labelid not in labelid2id.keys():
            labelid2id[labelid] = len(labelid2id)
        train_id = labelid2id[labelid]

        if train_id not in id2text_list.keys():
            id2text_list[train_id] = []

        for text in texts.split("||"):
            text_strip = text.strip()
            if len(text_strip):
                id2text_list[train_id].append(text_strip)

    # 划分数据集
    id2text_list_train = {}
    id2text_list_valid = {}
    num_train_text = 0
    num_valid_text = 0
    for train_id, texts in id2text_list.items():
        if len(texts) <= 0:
            continue
        elif len(texts) == 1:
            id2text_list_train[train_id] = texts
            num_train_text += len(texts)
        else:
            texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
            id2text_list_train[train_id] = texts_train
            id2text_list_valid[train_id] = texts_valid
            num_train_text += len(texts_train)
            num_valid_text += len(texts_valid)

    tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_VOCAB)
    train_gen = DataGenerator(id2text_list=id2text_list_train, tokenizer=tokenizer)
    valid_gen = DataGenerator(id2text_list=id2text_list_valid, tokenizer=tokenizer)

    for d in train_gen:
        print(d)

