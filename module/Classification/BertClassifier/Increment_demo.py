# -*- coding: utf-8 -*-
import json
import os
import random

import numpy as np

import setting
from database.REDIS import REDIS

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from transformers import BertConfig, BertTokenizer
from transformers.models.bert import TFBertForSequenceClassification
from sklearn.model_selection import train_test_split
from module.Classification.BertClassifier.DataGenerator import DataGenerator
from utils.my_utils import remove_symbol


def get_data_generator(data, tokenizer=None):
    id2text_list = {}
    labelid2id = {}
    labelid2title = {}
    text2item_cm = dict()  # 精确匹配字典

    for data_dict in data:
        title = data_dict['title'].strip()
        labelid = data_dict["labelId"]
        texts = data_dict["labelData"].strip()

        labelid2title[labelid] = title
        if labelid not in labelid2id.keys():
            labelid2id[labelid] = len(labelid2id)
        train_id = labelid2id[labelid]

        if train_id not in id2text_list.keys():
            id2text_list[train_id] = []

        if len(title):
            id2text_list[train_id].append(title)
        title_strip = remove_symbol(title)
        if len(title_strip):
            text2item_cm[title_strip] = labelid

        for text in texts.split("||"):
            text_strip = text.strip()
            if len(text_strip):
                id2text_list[train_id].append(text_strip)
            text_strip = remove_symbol(text_strip)
            if len(text_strip):
                text2item_cm[text_strip] = labelid

    # 划分数据集
    id2text_list_train = {}
    id2text_list_valid = {}
    num_train_text = 0
    num_valid_text = 0
    for train_id, texts in id2text_list.items():
        if len(texts) <= 0:
            continue
        elif len(texts) == 1:
            id2text_list_train[train_id] = texts
            num_train_text += len(texts)
        else:
            texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
            id2text_list_train[train_id] = texts_train
            id2text_list_valid[train_id] = texts_valid
            num_train_text += len(texts_train)
            num_valid_text += len(texts_valid)

    data_gen_train = DataGenerator(id2text_list=id2text_list_train, tokenizer=tokenizer)
    data_gen_valid = DataGenerator(id2text_list=id2text_list_valid, tokenizer=tokenizer)

    all_train_ids = list(id2text_list_valid.keys())
    random.shuffle(all_train_ids)
    data_gen_train_first_dict = {}
    data_gen_valid_first_dict = {}
    for first_train_ratio in [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]:
        first_train_id = all_train_ids[:int(len(all_train_ids)*first_train_ratio)]
        id2text_list_train_first = {}
        id2text_list_valid_first = {}
        for f_id in first_train_id:
            id2text_list_train_first[f_id] = id2text_list_train[f_id]
            id2text_list_valid_first[f_id] = id2text_list_valid[f_id]
        data_gen_train_first = DataGenerator(id2text_list=id2text_list_train_first, tokenizer=tokenizer)
        data_gen_valid_first = DataGenerator(id2text_list=id2text_list_valid_first, tokenizer=tokenizer)
        data_gen_train_first_dict[first_train_ratio] = data_gen_train_first
        data_gen_valid_first_dict[first_train_ratio] = data_gen_valid_first

    return data_gen_train, num_train_text, data_gen_valid, num_valid_text, labelid2id, labelid2title, text2item_cm, data_gen_train_first_dict, data_gen_valid_first_dict

def train(data_train, data_valid, labelid2id):
    model_id = f"0812_test_all"
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), "BertClassifier")
    os.makedirs(save_dir, exist_ok=True)
    config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_CONFIG, num_labels=len(labelid2id))
    model = TFBertForSequenceClassification.from_pretrained(setting.PRETRAIN_BERT_MODEL_TF, config=config, from_pt=False)
    id2labelid = {}
    for key, value in labelid2id.items():
        id2labelid[value] = key

    # 训练模型
    model.save_pretrained(save_dir)
    model_file = os.path.join(save_dir, 'tf_model.h5')
    opt = tf.keras.optimizers.Adam(learning_rate=setting.BERT_CLASSIFICATION_LEARNING_RATE, epsilon=1e-08)
    loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
    metrics = [tf.keras.metrics.SparseCategoricalAccuracy("accuracy")]
    model.compile(optimizer=opt, loss=loss, metrics=metrics)
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_accuracy', save_best_only=True, save_weights_only=True),
        # tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=10),
        # tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=5, verbose=0), # 当 patience 次迭代损失未改善，Keras停止训练
    ]
    history = model.fit(
        data_train,
        epochs=setting.BERT_CLASSIFICATION_EPOCHS,
        validation_data=data_valid,
        callbacks=callbacks
    )
    return np.max(history.history["val_accuracy"])


def increment_train(data_train, data_valid, labelid2id, data_gen_train_first, data_gen_valid_first):
    model_id = f"0812_test_inc"
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), "BertClassifier")
    os.makedirs(save_dir, exist_ok=True)
    config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_CONFIG, num_labels=len(labelid2id))
    model = TFBertForSequenceClassification.from_pretrained(setting.PRETRAIN_BERT_MODEL_TF, config=config, from_pt=False)
    id2labelid = {}
    for key, value in labelid2id.items():
        id2labelid[value] = key

    # 训练模型
    model.save_pretrained(save_dir)
    model_file = os.path.join(save_dir, 'tf_model.h5')
    opt = tf.keras.optimizers.Adam(learning_rate=setting.BERT_CLASSIFICATION_LEARNING_RATE, epsilon=1e-08)
    loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
    metrics = [tf.keras.metrics.SparseCategoricalAccuracy("accuracy")]
    model.compile(optimizer=opt, loss=loss, metrics=metrics)
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_accuracy', save_best_only=True, save_weights_only=True),
        # tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=10),
        # tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=5, verbose=0), # 当 patience 次迭代损失未改善，Keras停止训练
    ]
    history = model.fit(
        data_gen_train_first,
        epochs=setting.BERT_CLASSIFICATION_EPOCHS,
        validation_data=data_gen_valid_first,
        callbacks=callbacks
    )
    first_score = np.max(history.history["val_accuracy"])
    epoch_scores = []

    model.bert.embeddings.trainable = False
    model.bert.encoder.trainable = False
    model.bert.pooler.trainable = False
    history = model.fit(
        data_train,
        epochs=20,
        validation_data=data_valid,
        callbacks=callbacks
    )
    for i in range(20):
        epoch_scores.append(np.max(history.history["val_accuracy"][:i+1]))
    return first_score, epoch_scores

if __name__ == "__main__":
    Redis = REDIS()

    # 训练数据
    data_key = "faq_model1_all"
    data = Redis.get_data(data_key)
    tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_VOCAB)
    data_train, len_train, data_valid, len_valid, labelid2id, labelid2title, text2item_cm, data_gen_train_first_dict, data_gen_valid_first_dict = get_data_generator(data=data, tokenizer=tokenizer)
    
    # 全量训练
    all_score = train(data_train=data_train, data_valid=data_valid, labelid2id=labelid2id)

    # 增量训练
    increment_score_dict = {}
    for first_train_ratio in data_gen_train_first_dict.keys():
        print(f"开始增量训练:{first_train_ratio}")
        first_score, epoch_scores = increment_train(data_train, data_valid, labelid2id, data_gen_train_first_dict[first_train_ratio], data_gen_valid_first_dict[first_train_ratio])
        increment_score_dict[first_train_ratio] = [first_score, epoch_scores]

    result = {"all_score": all_score, "increment_score_dict": increment_score_dict}
    with open("./Increment_demo_result.json", "w", encoding="utf-8") as f:
        json.dump(result, f)
    print("wait")
