# -*- coding: utf-8 -*-
import json
import os
import pickle

import numpy as np

import setting
from database.REDIS import REDIS

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from setting import logger
from transformers import BertTokenizer, BertConfig
from sklearn.model_selection import train_test_split
from module.Classification.RDrop.DataGenerator import DataGenerator
from utils.my_utils import MyEncoder, remove_symbol
from model.RDrop.RDrop import RDrop, RDropLoss


class RDropClassifier:
    def __init__(self):
        self.models = dict()

    def build_model(self, text):
        pass

    def train(self, model_id, data, save_dir):
        logger.debug(f"准备开始训练 - model_id: {model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载数据、预训练模型
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        data_train, len_train, data_valid, len_valid, labelid2id, labelid2title, text2item_cm = \
            self.get_data_generator(data=data, tokenizer=tokenizer)
        id2labelid = {}
        for key, value in labelid2id.items():
            id2labelid[value] = key

        config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR, num_labels=len(labelid2id))
        config.attention_probs_dropout_prob = setting.RDROP_CLASSIFICATION_DROP_RATE
        config.hidden_dropout_prob = setting.RDROP_CLASSIFICATION_DROP_RATE
        model_config = {
            "model_path":setting.PRETRAIN_BERT_DIR,
            "num_class":len(labelid2id),
            "max_len": setting.RDROP_CLASSIFICATION_MAX_LEN
        }
        model = RDrop(config=config, tokenizer=tokenizer, **model_config)
        logger.debug(f"数据加载完成 - 训练集数据量: {len_train}, 验证集数据量: {len_valid}")

        # 训练模型
        model_file = os.path.join(save_dir, 'best_model.ckpt')
        model.save_weights(model_file)

        opt = tf.keras.optimizers.Adam(learning_rate=setting.RDROP_CLASSIFICATION_LEARNING_RATE, epsilon=1e-08)
        loss = RDropLoss(alpha=setting.RDROP_CLASSIFICATION_ALPHA)
        metrics = [tf.keras.metrics.SparseCategoricalAccuracy("accuracy")]
        model.compile(optimizer=opt, loss=loss, metrics=metrics)
        callbacks = [
            tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_accuracy', save_best_only=True),
            tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=10),
            tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=5, verbose=0), # 当 patience 次迭代损失未改善，Keras停止训练
        ]

        logger.debug(f"开始训练 - model_id: {model_id}")
        history = model.fit(
            data_train,
            epochs=setting.RDROP_CLASSIFICATION_EPOCHS,
            validation_data=data_valid,
            callbacks=callbacks
        )

        # 保存模型
        tokenizer.save_pretrained(save_dir)
        config.save_pretrained(save_dir)
        with open(os.path.join(save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "labelid2title.json"), "w", encoding="utf-8") as f:
            json.dump(labelid2title, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, 'train_history.json'), 'w', encoding='utf-8') as f:
            json.dump(history.history, fp=f, cls=MyEncoder, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, 'model_config.json'), 'w', encoding='utf-8') as f:
            json.dump(model_config, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, "labelid2id.json"), "w", encoding="utf-8") as f:
            json.dump(labelid2id, fp=f, ensure_ascii=False, indent=2)

        # self.models[model_id] = dict()
        # self.models[model_id]['tokenizer'] = tokenizer
        # self.models[model_id]['model'] = model
        # self.models[model_id]["labelid2title"] = labelid2title
        # self.models[model_id]["text2item_cm"] = text2item_cm
        # self.models[model_id]["labelid2id"] = labelid2id
        # self.models[model_id]["id2labelid"] = id2labelid

        logger.debug(f"训练成功 - model_id: {model_id}")

        return np.max(history.history["val_accuracy"])

    def load_model(self, model_id, save_dir):
        try:
            tokenizer = BertTokenizer.from_pretrained(save_dir)
            config = BertConfig.from_pretrained(save_dir)

            with open(os.path.join(save_dir, 'model_config.json'), 'r', encoding='utf-8') as f:
                model_config = json.load(f)
                model_config["model_path"] = setting.PRETRAIN_BERT_DIR
            model = RDrop(config=config, tokenizer=tokenizer, **model_config)
            model.load_weights(os.path.join(save_dir, 'best_model.ckpt')).expect_partial()

            with open(os.path.join(save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)
            with open(os.path.join(save_dir, "labelid2title.json"), "r", encoding="utf-8") as f:
                labelid2title = json.load(fp=f)
            with open(os.path.join(save_dir, "labelid2id.json"), "r", encoding="utf-8") as f:
                labelid2id = json.load(fp=f)
            id2labelid = {}
            for key, value in labelid2id.items():
                id2labelid[value] = key

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['model'] = model
            self.models[model_id]['labelid2title'] = labelid2title
            self.models[model_id]['text2item_cm'] = text2item_cm
            self.models[model_id]['id2labelid'] = id2labelid
            self.models[model_id]['labelid2id'] = labelid2id
            self.predict(model_id=model_id, query="测试一下", labelIds=[])
            logger.debug(f"模型自测完成 - model_id: {model_id}")
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, query, labelIds):
        if model_id not in self.models:
            logger.error(f"预测错误, 模型未加载 - model_id: {model_id}, text: {query}")
            raise Exception(f"预测错误, 模型未加载 - model_id: {model_id}")

        res = {"label_id": 0, "label_title": 0, "score" : 0, "margin_score": 0}
        if query is None or query == "":
            return res

        model = self.models[model_id]['model']
        labelid2title = self.models[model_id]['labelid2title']
        text2item_cm = self.models[model_id]['text2item_cm']
        labelid2id = self.models[model_id]['labelid2id']
        id2labelid = self.models[model_id]['id2labelid']

        query_ = remove_symbol(query)
        if query_ in text2item_cm:
            labelid = text2item_cm[query_]
            res["label_id"] = labelid
            res["label_title"] = labelid2title[labelid]
            res["score"] = 1
            res["margin_score"] = 0
            return res

        if labelIds is None or len(labelIds) == 0:
            labelIds_index = list(labelid2id.values())
        else:
            labelIds_index = [labelid2id[l] for l in labelIds]

        inputs = model.get_data(query, max_len=setting.RDROP_CLASSIFICATION_MAX_LEN)
        logits = model.predict(inputs)
        for i in range(logits.shape[1]):
            if i not in labelIds_index:
                logits[0, i] = -10e10
        prob = logits[0, :]
        index = np.argsort(prob)

        label_id = id2labelid[index[-1]]
        label_title = labelid2title[label_id]
        score = prob[index[-1]]
        if prob.shape[0] <= 1:
            margin_score = score
        else:
            margin_score = prob[index[-1]] - prob[index[-2]]
        res["label_id"] = label_id
        res["label_title"] = label_title
        res["score"] = score
        res["margin_score"] = margin_score
        return res

    def labeling(self, model_id, texts, text_ids=None):
        labeling_result = []

        model = self.models[model_id]['model']
        id2labelid = self.models[model_id]['id2labelid']

        inputs = model.get_data(texts, max_len=setting.RDROP_CLASSIFICATION_MAX_LEN)
        prob = model.predict(inputs, batch_size=setting.RDROP_CLASSIFICATION_BATCH_SIZE)

        for i in range(prob.shape[0]):
            index = np.argsort(prob[i, :])
            label_id = id2labelid[index[-1]]
            score = prob[i, index[-1]]
            if prob.shape[1] <= 1:
                margin_score = score
            else:
                margin_score = prob[i, index[-1]] - prob[i, index[-2]]
            result = {
                "text": texts[i],
                "id": text_ids[i] if text_ids is not None else None,
                "label": label_id,
                "score": score,
                "margin_score": margin_score
            }
            labeling_result.append(result)
        return labeling_result

    @staticmethod
    def get_data_generator(data, tokenizer=None):
        # 整理数据, id 表示训练用的 id (0, 1, 2), labelid 表示传入的意图 id
        id2text_list = {}
        labelid2id = {}
        labelid2title = {}
        text2item_cm = dict() # 精确匹配字典

        for data_dict in data:
            title = data_dict['title']
            labelid = data_dict["labelId"]
            texts = data_dict["labelData"]

            labelid2title[labelid] = title
            if labelid not in labelid2id.keys():
                labelid2id[labelid] = len(labelid2id)
            train_id = labelid2id[labelid]

            if train_id not in id2text_list.keys():
                id2text_list[train_id] = []

            for text in texts.split("||"):
                text_strip = text.strip()
                if len(text_strip):
                    id2text_list[train_id].append(text_strip)
                text_strip = remove_symbol(text_strip)
                if len(text_strip):
                    text2item_cm[text_strip] = labelid

        # 划分数据集
        id2text_list_train = {}
        id2text_list_valid = {}
        num_train_text = 0
        num_valid_text = 0
        for train_id, texts in id2text_list.items():
            if len(texts) <= 0:
                continue
            elif len(texts) == 1:
                id2text_list_train[train_id] = texts
                num_train_text += len(texts)
            else:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                id2text_list_train[train_id] = texts_train
                id2text_list_valid[train_id] = texts_valid
                num_train_text += len(texts_train)
                num_valid_text += len(texts_valid)

        data_gen_train = DataGenerator(id2text_list=id2text_list_train, tokenizer=tokenizer)
        data_gen_valid = DataGenerator(id2text_list=id2text_list_valid, tokenizer=tokenizer)

        return data_gen_train, num_train_text, data_gen_valid, num_valid_text, labelid2id, labelid2title, text2item_cm

    # def my_test(self, model_id, data):
    #     random.seed(setting.SEED)
    #     # 划分数据集
    #     text_list_valid = []
    #     for data_dict in data:
    #         norm_query = data_dict['title']
    #         texts = set()
    #         texts.add(norm_query)
    #         for text in data_dict['labelData'].split("||"):
    #             if len(text.strip()):
    #                 texts.add(text.strip())
    #         texts = list(texts)
    #         if len(texts) > 1:
    #             texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
    #             text_list_valid.append(texts_valid)
    #
    #     # 测试准确率，正负样本 1:1
    #     acc, total = 0, 0
    #     pbar = tqdm(enumerate(text_list_valid))
    #     for i, text_list in pbar:
    #         pbar.set_description('测试进度')
    #         for text1 in text_list:
    #             # 正样本
    #             text2 = random.choice(text_list)
    #             score = self.predict(model_id=model_id, text_1=text1, text_2=text2)[0][0]
    #             total += 1
    #             if score >= setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD:
    #                 acc += 1
    #
    #             # 负样本
    #             j = random.randint(0, len(text_list_valid)-1)
    #             while j == i:
    #                 j = random.randint(0, len(text_list_valid)-1)
    #             text2 = random.choice(text_list_valid[j])
    #             score = self.predict(model_id=model_id, text_1=text1, text_2=text2)[0][0]
    #             total += 1
    #             if score < setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD:
    #                 acc += 1
    #             acc_dict = {f'acc': acc / total}
    #             pbar.set_postfix(acc_dict)
    #     print(f"准确率: {acc/total}")


if __name__ == "__main__":
    model = RDropClassifier()
    Redis = REDIS()

    model_id = f"model1"
    data_key = "faq_model1_all"
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), RDropClassifier.__name__)

    # 训练
    data = Redis.get_data(data_key)
    score = model.train(model_id=model_id, data=data, save_dir=save_dir)
    print(f"训练得分: {score}")

    # 加载微调过的模型
    model.load_model(model_id=model_id, save_dir=save_dir)

    # 预测
    texts = ['你能告诉我什么时候能查到我快速赎回的余额理财到账的钱吗',
             '快速赎回的余额理财我什么时候能收到钱',
             'IE浏览器登录个人网银显示空白页如何处理',
             '数字牛熊证介绍',
             'ATM扫码取款支持怎样的账户啊',
             "请问余额理财赎回到账时间"]
    for text in texts:
        print(f"\n{text}")
        print(model.predict(model_id=model_id, query=text, labelIds=[]))

    print(model.labeling(model_id=model_id, texts=["IE浏览器登录个人网银显示空白页如何处理"]*100, text_ids=["123"]*100))