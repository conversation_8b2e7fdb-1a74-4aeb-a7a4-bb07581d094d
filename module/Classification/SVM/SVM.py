import jieba
import time
import numpy as np
import os
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.feature_extraction.text import TfidfTransformer
from sklearn.model_selection import cross_val_predict
from sklearn.svm import SVC
from cleanlab.pruning import get_noise_indices

from database.REDIS import REDIS


def get_error_labels():
    start = time.time()
    redis = REDIS()
    data = redis.get_data(key="faq_model1_all")

    labelid2id = {}
    id2labelid = {}
    id2text = {}
    all_text = []
    Y = []


    for data_dict in data:
        labelid = data_dict["labelId"]
        title = data_dict['title'].strip()
        texts = data_dict["labelData"].strip()

        if labelid not in labelid2id:
            labelid2id[labelid] = len(labelid2id)
            id2labelid[len(id2labelid)] = labelid

        all_text.append(title.strip())
        Y.append(labelid2id[labelid])
        id2text[labelid2id[labelid]] = title.strip()

        for text in texts.split("||"):
            all_text.append(text.strip())
            Y.append(labelid2id[labelid])

    # 训练 Tf-idf 模型
    corpos = [' '.join(jieba.cut(text)) for text in all_text]
    vectorizer = CountVectorizer(token_pattern=r"(?u)\b\w+\b")
    transformer = TfidfTransformer()
    X = transformer.fit_transform(vectorizer.fit_transform(corpos))

    # 交叉验证 predict
    if os.path.exists("./p.npy"):
        p = np.load("p.npy")
    else:
        clf = SVC(kernel='linear', C=1, random_state=0, probability=True)
        p = cross_val_predict(clf, X, Y, n_jobs=5, cv=5, method="predict_proba")
        np.save("p.npy", p)
    print(f"耗时: {time.time()-start}")
    return p, Y, all_text, id2text

def get_noise():
    start = time.time()
    p, Y, all_text, id2text = get_error_labels()
    ordered_label_errors = get_noise_indices(s=Y, psx=p, sorted_index_method='normalized_margin')
    for idx in ordered_label_errors:
        print(f"true: {Y[idx]} {all_text[idx]}, pred: {np.argmax(p[idx])} {id2text[np.argmax(p[idx])]}")
    print(f"耗时: {time.time()-start}")
    print("wait")

if __name__ == "__main__":
    get_error_labels() # 耗时: 1286.064088344574
    # get_noise() # 耗时: 50.06057786941528