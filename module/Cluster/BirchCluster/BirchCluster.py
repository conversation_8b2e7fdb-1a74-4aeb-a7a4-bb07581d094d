import time

import jieba
import numpy as np
from sklearn.cluster import Birch
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.feature_extraction.text import TfidfTransformer
from sklearn.metrics import f1_score
from sklearn.metrics import silhouette_score

import setting
from model.TextRepresentationPretrainBert.BertTextRepresentation import BertTextRepresentation
from setting import logger


def compute_kernel_bias(vecs, dim):
    """
    vecs = n*d
    计算kernel和bias
    最后的变换：y = (x + bias).dot(kernel)
    """
    mu = vecs.mean(axis=0, keepdims=True)
    cov = np.cov(vecs.T)
    u, s, vh = np.linalg.svd(cov)
    W = np.dot(u, np.diag(1 / np.sqrt(s)))
    return W[:, :dim], -mu

def transform_and_normalize(vecs, kernel=None, bias=None):
    """应用变换，然后标准化
    """
    if not (kernel is None or bias is None):
        vecs = (vecs + bias).dot(kernel)
    return vecs / (vecs**2).sum(axis=1, keepdims=True)**0.5

class BirchCluster:
    def __init__(self):
        self.model = BertTextRepresentation(model_path=setting.PRETRAIN_BERT_DIR,
                                            emb_layer=setting.BIRCH_CLUSTER_EMB_LAYER,
                                            max_len=setting.BIRCH_CLUSTER_MAX_LEN)

    def get_embedding(self, text_list):
        logger.debug(f"开始获取 Embedding - 数据量: {len(text_list)}")
        if setting.BIRCH_CLUSTER_EMB_TYPE == "bert":
            embeddings = self.model.encode_sentences(text_list=text_list, batch_size=setting.BIRCH_CLUSTER_BATCH_SIZE)
            if setting.BIRCH_CLUSTER_USE_WHITE:
                kernel, bias = compute_kernel_bias(embeddings, setting.BIRCH_CLUSTER_WHITE_DIM)
                embeddings = transform_and_normalize(embeddings, kernel, bias)
        else:
            corpus = []
            for t in text_list:
                output = ' '.join(jieba.lcut(t))
                corpus.append(output.strip())
            vectorizer = CountVectorizer()
            transformer = TfidfTransformer()
            embeddings = transformer.fit_transform(vectorizer.fit_transform(corpus))
        return embeddings

    def predict(self, text_list, n_cluster=10):
        if text_list is None or len(text_list) == 0:
            return [], []

        embeddings = self.get_embedding(text_list=text_list)
        cluster_ids = Birch(n_clusters=n_cluster).fit_predict(embeddings)
        cluster_text_dict = dict()
        for i, p in enumerate(cluster_ids):
            if p not in cluster_text_dict:
                cluster_text_dict[p] = []
            cluster_text_dict[p].append(text_list[i])
        cluster_text_list = []
        for v in cluster_text_dict.values():
            cluster_text_list.append(v)

        return cluster_text_list, cluster_ids

    def predict_with_grid_search(self, text_list):
        if text_list is None or len(text_list) == 0:
            return [], [], -1

        logger.debug(f"开始获取 Embedding - 数据量: {len(text_list)}")
        embeddings = self.get_embedding(text_list=text_list)

        # 搜索 cluster num
        max_cluster_num = min(max(len(text_list) // 5, 3), 1000)
        min_cluster_num = 2
        cluster_delta = max((max_cluster_num - min_cluster_num) // 10, 1)

        best_cluster_num = min_cluster_num
        best_score = -1e10
        best_cluster_ids = None

        cluster_num = min_cluster_num
        while cluster_num <= max_cluster_num:
            cluster_ids = Birch(n_clusters=cluster_num).fit_predict(embeddings)
            try:
                score = silhouette_score(X=embeddings, labels=cluster_ids)
            except:
                score = -1

            if score > best_score:
                best_score = score
                best_cluster_ids = cluster_ids
                best_cluster_num = cluster_num
                logger.debug(f"找到更好的聚类结果 - 簇个数:{best_cluster_num}, pred簇个数:{np.max(best_cluster_ids)+1}, 轮廓系数:{best_score}")
            cluster_num += cluster_delta

        cluster_text_dict = dict()
        for i, p in enumerate(best_cluster_ids):
            if p not in cluster_text_dict:
                cluster_text_dict[p] = []
            cluster_text_dict[p].append(text_list[i])
        cluster_text_list = []
        for v in cluster_text_dict.values():
            cluster_text_list.append(v)

        return cluster_text_list, best_cluster_ids, best_score

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        pass

    def load_model(self, model_id, save_dir):
        pass

    def offline_model(self, model_id):
        pass

    def get_data(self, data):
        pass

    @staticmethod
    def get_data_generator(data, tokenizer=None):
        pass

    def gridsearch_test(self, data):
        # 读取数据
        text_list = []
        label_list = []
        for i, d in enumerate(data):
            title = d["title"].strip()
            if len(title):
                text_list.append(title)
                label_list.append(i)
            for t in d["labelData"].strip().split('||'):
                t = t.strip()
                if len(t):
                    text_list.append(t)
                    label_list.append(i)

        # 聚类
        start = time.time()
        cluster_text_list, cluster_ids, score = self.predict_with_grid_search(text_list=text_list)
        print(f"预测完成时间:{time.time()-start}")

        # F1
        start = time.time()
        A = np.tile(np.reshape(label_list, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(label_list, (1, -1)), [len(label_list), 1])
        true_labels = np.array(A==B, dtype=np.int32)
        true_labels[np.eye(true_labels.shape[0], dtype=np.bool8)] = 0

        A = np.tile(np.reshape(cluster_ids, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(cluster_ids, (1, -1)), [len(label_list), 1])
        pred_labels = np.array(A==B, dtype=np.int32)
        pred_labels[np.eye(pred_labels.shape[0], dtype=np.bool8)] = 0

        f1 = f1_score(y_true=true_labels.reshape(-1), y_pred=pred_labels.reshape(-1))
        print(f"轮廓系数: {score}, 簇个数:{len(cluster_text_list)}, f1: {f1}, 计算 F1 时间:{time.time()-start}")


if __name__ == "__main__":
    from database.REDIS import REDIS

    redis = REDIS()
    model = BirchCluster()

    # # 预测
    # text_list = redis.get_data("cluster_huawei_all")
    # cluster_text_list, cluster_ids, score = model.predict_with_grid_search(text_list=text_list)

    # 网格搜索测试
    data = redis.get_data("faq_model1_all")
    model.gridsearch_test(data)

    input("w")
