# -*- coding: UTF-8 -*-

import math
import random

import numpy as np
from tensorflow import keras

import setting


class DataGenerator(keras.utils.Sequence):
    def __init__(self, text_list, tokenizer):
        self.text_list = text_list
        self.tokenizer = tokenizer
        self.batch_size = setting.SCCL_CLUSTER_BATCH_SIZE
        self.num_batches = math.ceil(len(text_list) / self.batch_size)
        random.seed(setting.SEED)

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return self.num_batches

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        start = index * self.batch_size
        end = min(len(self.text_list), (index+1)*self.batch_size)
        X, Y = self.__data_generation(self.text_list[start:end])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        random.shuffle(self.text_list)

    def __data_generation(self, text_list):
        """
        生成 cur_id 的数据。
        """
        input_ids = np.zeros(shape=(self.batch_size * 2, setting.SCCL_CLUSTER_MAX_LEN), dtype=np.int32)
        token_type_ids = np.zeros(shape=(self.batch_size * 2, setting.SCCL_CLUSTER_MAX_LEN), dtype=np.int32)
        attention_mask = np.zeros(shape=(self.batch_size * 2, setting.SCCL_CLUSTER_MAX_LEN), dtype=np.int32)
        labels = np.zeros(shape=(self.batch_size * 2, 1))
        index = 0

        for text in text_list:
            for _ in range(2):
                # 每一个 text 复制两份，作为正样本
                inputs = self.tokenizer.encode_plus(text, add_special_tokens=True,
                                                    max_length=setting.SCCL_CLUSTER_MAX_LEN,
                                                    truncation=True,
                                                    padding="max_length")
                input_ids[index, :] = inputs["input_ids"]
                attention_mask[index, :] = inputs["attention_mask"]
                token_type_ids[index, :] = inputs["token_type_ids"]
                index += 1

        return (
            {
                'input_ids': input_ids,
                'token_type_ids': token_type_ids,
                'attention_mask': attention_mask
            },
            labels
        )