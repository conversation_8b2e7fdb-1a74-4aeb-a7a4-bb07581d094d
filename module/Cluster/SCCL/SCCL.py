# -*- coding: utf-8 -*-

import numpy as np
import tensorflow as tf


class SCCL(tf.keras.Model):
    def __init__(self, config, tokenizer, bert, cluster_centers=None):
        super().__init__()
        self.config = config
        self.config.output_hidden_states = True
        self.tokenizer = tokenizer
        self.max_len = config.max_len
        self.alpha = config.alpha
        self.temperature = config.temperature
        self.batch_size = config.batch_size
        if isinstance(config.emb_layer, int):
            self.emb_layer = [config.emb_layer]
        else:
            self.emb_layer = config.emb_layer
        self.bert = bert
        self.cluster_centers = tf.Variable(cluster_centers, trainable=True, name='cluster_centers')

    def call(self, inputs, **kwargs):
        input_ids = inputs["input_ids"]
        token_type_ids = inputs.get("token_type_ids", None)
        attention_mask = inputs.get("attention_mask", None)
        emb = self.get_encoder_layer(input_ids=input_ids, token_type_ids=token_type_ids, attention_mask=attention_mask, **kwargs)  # [batch, dim]

        # contrastive loss, 一个 batch 的句子 [b1, b2, b3, b4, ...], 每两个是语义相同的 b1 和 b2 相同, b3 和 b4 相同
        idxs = tf.range(0, tf.shape(emb)[0])
        idxs_1 = idxs[None, :]
        idxs_2 = (idxs + 1 - idxs % 2 * 2)[:, None]
        y_true = tf.equal(idxs_1, idxs_2)
        y_true = tf.cast(y_true, tf.float32)

        emb_norm = tf.math.l2_normalize(emb, axis=1)
        similarities = tf.matmul(emb_norm, emb_norm, transpose_b=True)
        similarities = similarities - tf.eye(tf.shape(emb_norm)[0]) * 1e12
        similarities = similarities * self.temperature
        contrastive_loss = tf.keras.losses.categorical_crossentropy(y_true, similarities, from_logits=True)

        # cluster loss
        cluster_prob = self.get_cluster_prob(embeddings=emb) # [batch, cluster]
        cluster_prob_target = (cluster_prob ** 2) / (tf.reduce_sum(cluster_prob, axis=0) + 1e-9) # [batch, cluster]
        cluster_prob_target = tf.transpose(tf.transpose(cluster_prob_target) / tf.reduce_sum(cluster_prob_target, 1))
        cluster_loss = tf.keras.losses.kl_divergence(y_true=cluster_prob_target, y_pred=cluster_prob)

        # 相加
        contrastive_loss = tf.reduce_mean(contrastive_loss)
        cluster_loss = tf.reduce_mean(cluster_loss)
        loss = contrastive_loss + cluster_loss
        return loss

    def get_encoder_layer(self, input_ids, attention_mask, token_type_ids, **kwargs):
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            output_hidden_states=True,
            **kwargs
        )
        hidden_states = outputs[2]

        if isinstance(self.emb_layer, str):
            emb = hidden_states[-1][:, 0, :]
        elif isinstance(self.emb_layer, list):
            emb = None
            for layer in self.emb_layer:
                emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
            emb = emb / len(self.emb_layer)
            emb = tf.reduce_mean(emb, axis=1)
        else:
            raise Exception(f"{self.__class__.__name__} 参数出错: emb_layer {self.emb_layer}")
        return emb

    def get_cluster_prob(self, embeddings):
        embeddings_expand = tf.expand_dims(embeddings, 1)
        norm_squared = tf.reduce_sum((embeddings_expand - self.cluster_centers) ** 2, 2)
        numerator = 1.0 / (1.0 + (norm_squared / self.alpha))
        power = float(self.alpha + 1) / 2
        numerator = numerator ** power
        return numerator / tf.reduce_sum(numerator, axis=1, keepdims=True)

    def get_cluster_ids(self, text_list):
        cluster_ids = []
        for start_idx in range(0, len(text_list), self.batch_size):
            end_idx = min(start_idx+self.batch_size, len(text_list))
            inputs = self.tokenizer.batch_encode_plus(
                text_list[start_idx:end_idx],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                truncation=True,
                return_tensors="tf")
            emb = self.get_encoder_layer(**inputs)
            prob = self.get_cluster_prob(emb.numpy())
            cluster_ids.extend(list(np.argmax(prob, axis=1)))
        return cluster_ids

    def get_data(self, texts, max_len, return_tensor=False):
        if isinstance(texts, str):
            texts = [texts]
        data = {
            "input_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "token_type_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "attention_mask": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
        }
        for i, text in enumerate(texts):
            inputs = self.tokenizer.encode_plus(text, add_special_tokens=True,
                                           max_length=max_len,
                                           truncation=True,
                                           padding="max_length")
            data["input_ids"][i, :] = inputs["input_ids"]
            data["token_type_ids"][i, :] = inputs["token_type_ids"]
            data["attention_mask"][i, :] = inputs["attention_mask"]

        if return_tensor:
            data["input_ids"] = tf.convert_to_tensor(data["input_ids"])
            data["token_type_ids"] = tf.convert_to_tensor(data["token_type_ids"])
            data["attention_mask"] = tf.convert_to_tensor(data["attention_mask"])
        return data

def loss_func(y_true, y_pred):
    return y_pred