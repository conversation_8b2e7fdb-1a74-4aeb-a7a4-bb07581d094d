# -*- coding: UTF-8 -*-

import json
import os
import time

import numpy as np

import setting

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.cluster import KMeans

from module.Cluster.SCCL.DataGenerator import DataGenerator
from module.Cluster.SCCL.SCCL import SCCL, loss_func
from setting import logger
from utils.my_utils import MyEncoder
from sklearn.metrics import silhouette_score, f1_score
from model.TextRepresentationPretrainBert.BertTextRepresentation import BertTextRepresentation
from transformers import BertTokenizer, BertConfig
from transformers.models.bert.modeling_tf_bert import TFBertModel


class SCCLCluster:
    def __init__(self):
        self.sbert = BertTextRepresentation(model_path=setting.PRETRAIN_BERT_DIR,
                                            emb_layer=setting.SCCL_CLUSTER_EMB_LAYER,
                                            max_len=setting.SCCL_CLUSTER_MAX_LEN)

    def build_model(self):
        pass

    def predict(self, text_list, n_cluster=10):
        if text_list is None or len(text_list) == 0:
            return [], []

        logger.debug(f"开始获取初始簇中心 - 数据量: {len(text_list)}")
        embeddings = self.sbert.encode_sentences(text_list=text_list, batch_size=setting.SCCL_CLUSTER_BATCH_SIZE)
        kmeans_model = KMeans(n_clusters=n_cluster)
        kmeans_model.fit_predict(embeddings)
        cluster_centers = kmeans_model.cluster_centers_

        logger.debug(f"创建 SCCL 模型 - 簇中心数组: {cluster_centers.shape}")
        config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
        config.emb_layer = setting.SCCL_CLUSTER_EMB_LAYER
        config.max_len = setting.SCCL_CLUSTER_MAX_LEN
        config.alpha = 1
        config.temperature = 5
        config.batch_size = setting.SCCL_CLUSTER_BATCH_SIZE
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        bert = TFBertModel.from_pretrained(setting.PRETRAIN_BERT_DIR, from_pt=False, config=config)
        sccl = SCCL(config=config, tokenizer=tokenizer, bert=bert, cluster_centers=cluster_centers)

        logger.debug(f"开始训练 SCCL 模型")
        data_gen = DataGenerator(text_list=text_list.copy(), tokenizer=tokenizer)
        opt = tf.keras.optimizers.Adam(learning_rate=setting.SCCL_CLUSTER_LEARNING_RATE, epsilon=1e-08)
        sccl.compile(optimizer=opt, loss=loss_func)
        save_dir = "./tmp"
        os.makedirs(save_dir, exist_ok=True)
        model_file = os.path.join(save_dir, 'best_model.ckpt')
        sccl.save_weights(model_file)
        callbacks = [
            tf.keras.callbacks.ModelCheckpoint(model_file, monitor='loss', save_best_only=True),
            # tf.keras.callbacks.ReduceLROnPlateau(monitor='loss', factor=0.2, patience=3),
            # tf.keras.callbacks.EarlyStopping(monitor='loss', patience=3, verbose=0), # 当 patience 次迭代损失未改善，Keras停止训练
        ]
        history = sccl.fit(
            data_gen,
            epochs=setting.SCCL_CLUSTER_EPOCHS,
            callbacks=callbacks
        )
        with open(os.path.join(save_dir, 'train_history.json'), 'w', encoding='utf-8') as f:
            json.dump(history.history, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

        logger.debug(f"训练结束, 开始聚类")
        sccl.load_weights(model_file).expect_partial()
        cluster_ids = sccl.get_cluster_ids(text_list=text_list)
        cluster_text_dict = dict()
        for i, p in enumerate(cluster_ids):
            if p not in cluster_text_dict:
                cluster_text_dict[p] = []
            cluster_text_dict[p].append(text_list[i])
        cluster_text_list = []
        for v in cluster_text_dict.values():
            cluster_text_list.append(v)
        try:
            score = silhouette_score(X=embeddings, labels=cluster_ids, metric="cosine")
        except:
            score = -1
        return cluster_text_list, cluster_ids, score

    def predict_with_grid_search(self, text_list):
        if text_list is None or len(text_list) == 0:
            return [], [], -1

        # 搜索 cluster num
        max_cluster_num = 992 # min(max(len(text_list) // 5, 3), 1000)
        min_cluster_num = 992 # 2
        cluster_delta = max((max_cluster_num - min_cluster_num) // 10, 1)

        best_cluster_num = min_cluster_num
        best_score = -1e10
        best_cluster_ids = None

        cluster_num = min_cluster_num
        while cluster_num <= max_cluster_num:
            cluster_text_list, cluster_ids, score = self.predict(text_list=text_list, n_cluster=cluster_num)
            if score > best_score:
                best_score = score
                best_cluster_ids = cluster_ids
                best_cluster_num = cluster_num
                logger.debug(f"找到更好的聚类结果 - 簇个数:{best_cluster_num}, pred簇个数:{np.max(best_cluster_ids)+1}, 轮廓系数:{best_score}")
            cluster_num += cluster_delta

        cluster_text_dict = dict()
        for i, p in enumerate(best_cluster_ids):
            if p not in cluster_text_dict:
                cluster_text_dict[p] = []
            cluster_text_dict[p].append(text_list[i])
        cluster_text_list = []
        for v in cluster_text_dict.values():
            cluster_text_list.append(v)

        return cluster_text_list, best_cluster_ids, best_score

    def gridsearch_test(self, data):
        # 读取数据
        text_list = []
        label_list = []
        for i, d in enumerate(data):
            title = d["title"].strip()
            if len(title):
                text_list.append(title)
                label_list.append(i)
            for t in d["labelData"].strip().split('||'):
                t = t.strip()
                if len(t):
                    text_list.append(t)
                    label_list.append(i)

        # 聚类
        start = time.time()
        cluster_text_list, cluster_ids, score = self.predict_with_grid_search(text_list=text_list)
        print(f"预测完成时间:{time.time()-start}")

        # F1
        start = time.time()
        A = np.tile(np.reshape(label_list, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(label_list, (1, -1)), [len(label_list), 1])
        true_labels = np.array(A==B, dtype=np.int32)
        true_labels[np.eye(true_labels.shape[0], dtype=np.bool8)] = 0

        A = np.tile(np.reshape(cluster_ids, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(cluster_ids, (1, -1)), [len(label_list), 1])
        pred_labels = np.array(A==B, dtype=np.int32)
        pred_labels[np.eye(pred_labels.shape[0], dtype=np.bool8)] = 0

        f1 = f1_score(y_true=true_labels.reshape(-1), y_pred=pred_labels.reshape(-1))
        print(f"轮廓系数: {score}, 簇个数:{len(cluster_text_list)}, f1: {f1}, 计算 F1 时间:{time.time()-start}")


if __name__ == "__main__":
    from database.REDIS import REDIS

    redis = REDIS()
    model = SCCLCluster()

    # # 预测
    # text_list = redis.get_data("cluster_huawei_all")
    # cluster_text_list, cluster_ids = model.predict(text_list=text_list, n_cluster=139)

    # 网格搜索测试
    data = redis.get_data("faq_model1_all")
    model.gridsearch_test(data)

    input("w")