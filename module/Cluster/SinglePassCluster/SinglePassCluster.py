import jieba
import numpy as np
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.feature_extraction.text import TfidfTransformer
from sklearn.metrics import f1_score
from sklearn.metrics import silhouette_score
from sklearn.metrics.pairwise import cosine_similarity
import time

import setting
from model.TextRepresentationPretrainBert.BertTextRepresentation import BertTextRepresentation
from setting import logger


def compute_kernel_bias(vecs, dim):
    """
    vecs = n*d
    计算kernel和bias
    最后的变换：y = (x + bias).dot(kernel)
    """
    mu = vecs.mean(axis=0, keepdims=True)
    cov = np.cov(vecs.T)
    u, s, vh = np.linalg.svd(cov)
    W = np.dot(u, np.diag(1 / np.sqrt(s)))
    return W[:, :dim], -mu

def transform_and_normalize(vecs, kernel=None, bias=None):
    """应用变换，然后标准化
    """
    if not (kernel is None or bias is None):
        vecs = (vecs + bias).dot(kernel)
    return vecs / (vecs**2).sum(axis=1, keepdims=True)**0.5

class SinglePassCluster:
    def __init__(self):
        self.model = BertTextRepresentation(model_path=f'{setting.MAIN_DIR}/pretrain_model/roberta_intent_anto_num_en_0712',
                                            emb_layer=setting.SINGLE_PASS_CLUSTER_EMB_LAYER,
                                            max_len=setting.SINGLE_PASS_CLUSTER_MAX_LEN)

    def get_embedding(self, text_list):
        logger.debug(f"开始获取 Embedding - 数据量: {len(text_list)}")
        if setting.SINGLE_PASS_CLUSTER_EMB_TYPE == "bert":
            embeddings = self.model.encode_sentences(text_list=text_list, batch_size=setting.SINGLE_PASS_CLUSTER_BATCH_SIZE)
            if setting.SINGLE_PASS_CLUSTER_USE_WHITE:
                kernel, bias = compute_kernel_bias(embeddings, setting.SINGLE_PASS_CLUSTER_WHITE_DIM)
                embeddings = transform_and_normalize(embeddings, kernel, bias)
        else:
            corpus = []
            for t in text_list:
                output = ' '.join(jieba.lcut(t))
                corpus.append(output.strip())
            vectorizer = CountVectorizer()
            transformer = TfidfTransformer()
            embeddings = transformer.fit_transform(vectorizer.fit_transform(corpus))

        return embeddings

    def predict(self, text_list, embeddings=None, threshold=0.37):
        if text_list is None or len(text_list) == 0:
            return [], [], -1

        if embeddings is None:
            embeddings = self.get_embedding(text_list=text_list)
        text_num = embeddings.shape[0]

        # 第一次聚类
        logger.debug(f"开始第一次聚类, threshold: {threshold}")
        cluster_ids = []
        cluster_id_count = dict()
        max_cluster_id = 0

        for i in range(text_num):
            if i == 0:
                max_cluster_id += 1
                cluster_ids.append(max_cluster_id)
                cluster_id_count[max_cluster_id] = 1
                continue

            sim_score = cosine_similarity(embeddings[:i, :], embeddings[i, :].reshape(1, -1))[:, 0]
            max_idx = np.argmax(sim_score)
            if sim_score[max_idx] < threshold:
                max_cluster_id += 1
                cluster_ids.append(max_cluster_id)
                cluster_id_count[max_cluster_id] = cluster_id_count.get(max_cluster_id, 0) + 1
            else:
                cluster_ids.append(cluster_ids[max_idx])
                cluster_id_count[cluster_ids[max_idx]] = cluster_id_count.get(cluster_ids[max_idx], 0) + 1

        logger.debug(f"第一次聚类 - 簇个数:{max_cluster_id}")

        # 第二次聚类
        logger.debug(f"开始第二次聚类, threshold: {threshold}")
        need_re_cluster_idx = []
        min_count = len(text_list) // max_cluster_id // 2
        for idx, cur_id in enumerate(cluster_ids):
            if cluster_id_count[cur_id] < min_count:
                need_re_cluster_idx.append(idx)

        while len(need_re_cluster_idx):
            cur_idx = need_re_cluster_idx[0]

            sim_score = cosine_similarity(embeddings, embeddings[cur_idx, :].reshape(1, -1))[:, 0]
            for idx in need_re_cluster_idx:
                sim_score[idx] = -1e10

            max_idx = np.argmax(sim_score)
            if sim_score[max_idx] < threshold:
                max_cluster_id += 1
                cluster_ids[cur_idx] = max_cluster_id
            else:
                cluster_ids[cur_idx] = cluster_ids[max_idx]

            need_re_cluster_idx.pop(0)

        # 返回结果
        cluster_text_dict = {}
        for i, text in enumerate(text_list):
            if cluster_ids[i] not in cluster_text_dict:
                cluster_text_dict[cluster_ids[i]] = []
            cluster_text_dict[cluster_ids[i]].append(text)
        cluster_text_list = []
        for v in cluster_text_dict.values():
            cluster_text_list.append(v)

        try:
            score = silhouette_score(X=embeddings, labels=cluster_ids, metric="cosine")
        except:
            score = -1
        logger.debug(f"聚类成功 - 簇个数:{len(cluster_text_list)}, 轮廓系数:{score}")
        return cluster_text_list, cluster_ids, score

    def predict_with_grid_search(self, text_list, low_threshold=setting.SINGLE_PASS_CLUSTER_LOW_THRESHOLD,
                                 high_threshold=setting.SINGLE_PASS_CLUSTER_HIGH_THRESHOLD):
        if text_list is None or len(text_list) == 0:
            return [], [], -1
        if len(text_list) == 1:
            return [text_list], [1], -1

        embeddings = self.get_embedding(text_list=text_list)

        # 网格搜索
        best_score = -1e10
        best_cluster_ids = []
        best_cluster_text_list = []
        threshold = low_threshold
        best_threshold = low_threshold

        while threshold <= high_threshold:
            cluster_text_list, cluster_ids, score = self.predict(text_list=text_list,
                                                                 embeddings=embeddings,
                                                                 threshold=threshold)
            cluster_num = len(cluster_text_list)
            avg_num = np.sum([len(i) for i in cluster_text_list]) / cluster_num

            if best_score <= -1 or (score > best_score and avg_num >= 5 and cluster_num <= 1000):
                best_score = score
                best_cluster_ids = cluster_ids
                best_cluster_text_list = cluster_text_list
                best_threshold = threshold
                logger.debug(f"找到更好的 threshold - threshold: {threshold}, cluster_num: {len(cluster_text_list)}, score: {score}")

            if cluster_num > 1000 or avg_num < 3:
                break
            threshold += 0.01

        return best_cluster_text_list, best_cluster_ids, best_threshold

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        pass

    def load_model(self, model_id, save_dir):
        pass

    def offline_model(self, model_id):
        pass

    def get_data(self, data):
        pass

    @staticmethod
    def get_data_generator(data, tokenizer=None):
        pass

    def supervise_test(self, data):
        # 读取数据
        text_list = []
        label_list = []
        for i, d in enumerate(data):
            title = d["title"].strip()
            if len(title):
                text_list.append(title)
                label_list.append(i)
            for t in d["labelData"].strip().split('||'):
                t = t.strip()
                if len(t):
                    text_list.append(t)
                    label_list.append(i)

        # 获取 bert embedding
        embeddings = self.get_embedding(text_list=text_list)

        # 搜索 threshold
        threshold = 0.3
        best_threshold = threshold
        best_f1 = -1e10
        while threshold <= 1:
            cluster_text_list, cluster_ids, score = self.predict(text_list=text_list, embeddings=embeddings, threshold=threshold)

            # F1
            A = np.tile(np.reshape(label_list, (-1, 1)), [1, len(label_list)])
            B = np.tile(np.reshape(label_list, (1, -1)), [len(label_list), 1])
            true_labels = np.array(A==B, dtype=np.int32)
            true_labels[np.eye(true_labels.shape[0], dtype=np.bool8)] = 0

            A = np.tile(np.reshape(cluster_ids, (-1, 1)), [1, len(label_list)])
            B = np.tile(np.reshape(cluster_ids, (1, -1)), [len(label_list), 1])
            pred_labels = np.array(A==B, dtype=np.int32)
            pred_labels[np.eye(pred_labels.shape[0], dtype=np.bool8)] = 0

            f1 = f1_score(y_true=true_labels.reshape(-1), y_pred=pred_labels.reshape(-1))
            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold

            print(f"threshold: {threshold}, f1: {f1}, 轮廓系数: {score}")
            threshold += 0.01
        print(f"best threshold: {best_threshold}, best f1: {best_f1}")

    def gridsearch_test(self, data):
        # 读取数据
        text_list = []
        label_list = []
        for i, d in enumerate(data):
            title = d["title"].strip()
            if len(title):
                text_list.append(title)
                label_list.append(i)
            for t in d["labelData"].strip().split('||'):
                t = t.strip()
                if len(t):
                    text_list.append(t)
                    label_list.append(i)

        # 聚类
        start = time.time()
        cluster_text_list, cluster_ids, threshold = self.predict_with_grid_search(text_list=text_list, low_threshold=0.3, high_threshold=1)
        print(f"预测完成时间:{time.time()-start}")

        # F1
        start = time.time()
        A = np.tile(np.reshape(label_list, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(label_list, (1, -1)), [len(label_list), 1])
        true_labels = np.array(A==B, dtype=np.int32)
        true_labels[np.eye(true_labels.shape[0], dtype=np.bool8)] = 0

        A = np.tile(np.reshape(cluster_ids, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(cluster_ids, (1, -1)), [len(label_list), 1])
        pred_labels = np.array(A==B, dtype=np.int32)
        pred_labels[np.eye(pred_labels.shape[0], dtype=np.bool8)] = 0

        f1 = f1_score(y_true=true_labels.reshape(-1), y_pred=pred_labels.reshape(-1))
        print(f"threshold: {threshold}, 簇个数:{len(cluster_text_list)}, f1: {f1}, 计算 F1 时间:{time.time()-start}")


if __name__ == "__main__":
    from database.REDIS import REDIS

    redis = REDIS()
    model = SinglePassCluster()

    # # 预测
    # text_list = redis.get_data("cluster_huawei_all")
    # cluster_text_list, cluster_ids, score = model.predict(text_list=text_list, threshold=setting.SINGLE_PASS_CLUSTER_THRESHOLD)

    # # 网格搜索预测
    # text_list = redis.get_data("cluster_huawei_all")
    # best_cluster_text_list, best_cluster_ids, best_threshold = model.predict_with_grid_search(text_list=text_list)

    # # 有监督测试
    # data = redis.get_data("faq_huawei_all")
    # model.supervise_test(data)

    # 网格搜索测试
    data = redis.get_data("faq_model1_all")
    model.gridsearch_test(data)

    input("w")

"""
聚类两次
huawei notwhite notscale(-1,1) cls  best threshold: 0.9600000000000004, best f1: 0.07851072440307567
huawei notwhite scale(0,1) cls  best threshold: 0.9800000000000004, best f1: 0.07851072440307567
huawei white notscale(-1,1) cls  best threshold: 0.5, best f1: 0.10728862973760932
huawei white scale(0,1) cls  best threshold: 0.6800000000000002, best f1: 0.20756302521008405
"""