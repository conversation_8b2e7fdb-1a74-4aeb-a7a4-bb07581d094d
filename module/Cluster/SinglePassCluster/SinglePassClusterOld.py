import jieba
import numpy as np
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.feature_extraction.text import TfidfTransformer
from sklearn.metrics import f1_score
from sklearn.metrics import silhouette_score
from sklearn.metrics.pairwise import cosine_similarity
import time

import setting
from model.TextRepresentationPretrainBert.BertTextRepresentation import BertTextRepresentation
from setting import logger


def compute_kernel_bias(vecs, dim):
    """
    vecs = n*d
    计算kernel和bias
    最后的变换：y = (x + bias).dot(kernel)
    """
    mu = vecs.mean(axis=0, keepdims=True)
    cov = np.cov(vecs.T)
    u, s, vh = np.linalg.svd(cov)
    W = np.dot(u, np.diag(1 / np.sqrt(s)))
    return W[:, :dim], -mu

def transform_and_normalize(vecs, kernel=None, bias=None):
    """应用变换，然后标准化
    """
    if not (kernel is None or bias is None):
        vecs = (vecs + bias).dot(kernel)
    return vecs / (vecs**2).sum(axis=1, keepdims=True)**0.5

class SinglePassCluster:
    def __init__(self):
        self.model = BertTextRepresentation(model_path=f'{setting.MAIN_DIR}/pretrain_model/roberta_intent_anto_num_en_0712',
                                            emb_layer=setting.SINGLE_PASS_CLUSTER_EMB_LAYER,
                                            max_len=setting.SINGLE_PASS_CLUSTER_MAX_LEN)

    def get_embedding_sim_score(self, text_list, emb="bert"):
        logger.debug(f"开始获取 Embedding - 数据量: {len(text_list)}")
        if emb == "bert":
            embeddings = self.model.encode_sentences(text_list=text_list, batch_size=setting.SINGLE_PASS_CLUSTER_BATCH_SIZE)
            if setting.SINGLE_PASS_CLUSTER_USE_WHITE:
                kernel, bias = compute_kernel_bias(embeddings, setting.SINGLE_PASS_CLUSTER_WHITE_DIM)
                embeddings = transform_and_normalize(embeddings, kernel, bias)
        else:
            corpus = []
            for t in text_list:
                output = ' '.join(jieba.lcut(t))
                corpus.append(output.strip())
            vectorizer = CountVectorizer()
            transformer = TfidfTransformer()
            embeddings = transformer.fit_transform(vectorizer.fit_transform(corpus))

        sim_score_matric = cosine_similarity(embeddings, embeddings)
        # sim_score_matric = np.clip((sim_score_matric + 1) / 2, a_min=0, a_max=1)
        sim_score_matric = np.clip(sim_score_matric, a_min=0, a_max=1)
        np.fill_diagonal(sim_score_matric, 0)

        return embeddings, sim_score_matric

    def predict(self, text_list, embeddings=None, sim_score_matric=None, threshold=setting.SINGLE_PASS_CLUSTER_THRESHOLD):
        if text_list is None or len(text_list) == 0:
            return [], [], -1

        if embeddings is None or sim_score_matric is None:
            if len(text_list) > 100:
                embeddings, sim_score_matric = self.get_embedding_sim_score(text_list=text_list, emb="bert")
            else:
                embeddings, sim_score_matric = self.get_embedding_sim_score(text_list=text_list, emb="tfidf")

        # 第一次聚类
        logger.debug(f"开始第一次聚类, threshold: {threshold}")
        cluster_ids = []
        cluster_id_count = dict()
        max_cluster_id = 0

        for i in range(sim_score_matric.shape[0]):
            if i == 0:
                max_cluster_id += 1
                cluster_ids.append(max_cluster_id)
                cluster_id_count[max_cluster_id] = 1
                continue

            max_idx = np.argmax(sim_score_matric[i, :i])
            if sim_score_matric[i, max_idx] < threshold:
                max_cluster_id += 1
                cluster_ids.append(max_cluster_id)
                cluster_id_count[max_cluster_id] = cluster_id_count.get(max_cluster_id, 0) + 1
            else:
                cluster_ids.append(cluster_ids[max_idx])
                cluster_id_count[cluster_ids[max_idx]] = cluster_id_count.get(cluster_ids[max_idx], 0) + 1

        cluster_id_count = list(cluster_id_count.items())
        cluster_id_count.sort(key=lambda x: x[1])
        logger.debug(f"第一次聚类 - 簇个数:{max_cluster_id}")

        # 第二次聚类
        logger.debug(f"开始第二次聚类, threshold: {threshold}")
        need_re_cluster_ids = set()
        min_count = max(len(text_list) // max_cluster_id // 2, 2)

        for cur_id, cur_id_count in cluster_id_count:
            if cur_id_count < min_count:
                need_re_cluster_ids.add(cur_id)

        sim_score_matric_copy = sim_score_matric.copy()
        for i, cur_id in enumerate(cluster_ids):
            if cur_id in need_re_cluster_ids:
                sim_score_matric_copy[:, i] = -1e10
                cluster_ids[i] = -1

        for i, cur_id in enumerate(cluster_ids):
            if cur_id != -1:
                continue
            max_idx = np.argmax(sim_score_matric_copy[i, :])
            if sim_score_matric_copy[i, max_idx] < threshold:
                max_cluster_id += 1
                cluster_ids[i] = max_cluster_id
            else:
                assert cluster_ids[max_idx] != -1
                cluster_ids[i] = cluster_ids[max_idx]
            sim_score_matric_copy[:, i] = sim_score_matric[:, i]

        # 返回结果
        cluster_text_dict = {}
        for i, text in enumerate(text_list):
            if cluster_ids[i] not in cluster_text_dict:
                cluster_text_dict[cluster_ids[i]] = []
            cluster_text_dict[cluster_ids[i]].append(text)
        cluster_text_list = []
        for v in cluster_text_dict.values():
            cluster_text_list.append(v)

        try:
            np.fill_diagonal(sim_score_matric, 1)
            score = silhouette_score(X=1-sim_score_matric, labels=cluster_ids, metric="precomputed")
        except:
            score = -1
        logger.debug(f"聚类成功 - 簇个数:{len(cluster_text_list)}, 轮廓系数:{score}")
        return cluster_text_list, cluster_ids, score

    def predict_with_threshold_search(self, text_list):
        if text_list is None or len(text_list) == 0:
            return [], [], -1

        if len(text_list) > 100:
            embeddings, sim_score_matric = self.get_embedding_sim_score(text_list=text_list, emb="bert")
        else:
            embeddings, sim_score_matric = self.get_embedding_sim_score(text_list=text_list, emb="tfidf")

        # 网格搜索
        best_score = -1e10
        best_cluster_ids = []
        best_cluster_text_list = []
        threshold = 0.3
        best_threshold = threshold

        while threshold <= 1:
            cluster_text_list, cluster_ids, score = self.predict(text_list=text_list,
                                                                 embeddings=embeddings,
                                                                 sim_score_matric=sim_score_matric,
                                                                 threshold=threshold)
            cluster_num = len(cluster_text_list)
            avg_num = np.sum([len(i) for i in cluster_text_list]) / cluster_num

            if best_score <= -1 or (score > best_score and avg_num >= 5 and cluster_num <= 1000):
                best_score = score
                best_cluster_ids = cluster_ids
                best_cluster_text_list = cluster_text_list
                best_threshold = threshold
                logger.debug(f"找到更好的 threshold - threshold: {threshold}, cluster_num: {len(cluster_text_list)}, score: {score}")

            if cluster_num > 1000 or avg_num < 3:
                break
            threshold += 0.01

        return best_cluster_text_list, best_cluster_ids, best_threshold

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        pass

    def load_model(self, model_id, save_dir):
        pass

    def offline_model(self, model_id):
        pass

    def get_data(self, data):
        pass

    @staticmethod
    def get_data_generator(data, tokenizer=None):
        pass

    def supervise_test(self, data):
        # 读取数据
        text_list = []
        label_list = []
        for i, d in enumerate(data):
            title = d["title"].strip()
            if len(title):
                text_list.append(title)
                label_list.append(i)
            for t in d["labelData"].strip().split('||'):
                t = t.strip()
                if len(t):
                    text_list.append(t)
                    label_list.append(i)

        # 获取 bert embedding
        embeddings, sim_score_matric = self.get_embedding_sim_score(text_list=text_list, emb="bert")
        # embeddings, sim_score_matric = self.get_embedding_sim_score(text_list=text_list, emb="tfidf")

        # 搜索 threshold
        threshold = 0.3
        best_threshold = threshold
        best_f1 = -1e10
        while threshold <= 1:
            cluster_text_list, cluster_ids, score = self.predict(text_list=text_list, embeddings=embeddings, sim_score_matric=sim_score_matric, threshold=threshold)

            # F1
            A = np.tile(np.reshape(label_list, (-1, 1)), [1, len(label_list)])
            B = np.tile(np.reshape(label_list, (1, -1)), [len(label_list), 1])
            true_labels = np.array(A==B, dtype=np.int32)
            true_labels[np.eye(true_labels.shape[0], dtype=np.bool8)] = 0

            A = np.tile(np.reshape(cluster_ids, (-1, 1)), [1, len(label_list)])
            B = np.tile(np.reshape(cluster_ids, (1, -1)), [len(label_list), 1])
            pred_labels = np.array(A==B, dtype=np.int32)
            pred_labels[np.eye(pred_labels.shape[0], dtype=np.bool8)] = 0

            f1 = f1_score(y_true=true_labels.reshape(-1), y_pred=pred_labels.reshape(-1))
            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold

            print(f"threshold: {threshold}, f1: {f1}, 轮廓系数: {score}")
            threshold += 0.01
        print(f"best threshold: {best_threshold}, best f1: {best_f1}")

    def gridsearch_test(self, data):
        # 读取数据
        text_list = []
        label_list = []
        for i, d in enumerate(data):
            title = d["title"].strip()
            if len(title):
                text_list.append(title)
                label_list.append(i)
            for t in d["labelData"].strip().split('||'):
                t = t.strip()
                if len(t):
                    text_list.append(t)
                    label_list.append(i)

        # 聚类
        start = time.time()
        cluster_text_list, cluster_ids, threshold = self.predict_with_threshold_search(text_list=text_list)

        # F1
        A = np.tile(np.reshape(label_list, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(label_list, (1, -1)), [len(label_list), 1])
        true_labels = np.array(A==B, dtype=np.int32)
        true_labels[np.eye(true_labels.shape[0], dtype=np.bool8)] = 0

        A = np.tile(np.reshape(cluster_ids, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(cluster_ids, (1, -1)), [len(label_list), 1])
        pred_labels = np.array(A==B, dtype=np.int32)
        pred_labels[np.eye(pred_labels.shape[0], dtype=np.bool8)] = 0

        f1 = f1_score(y_true=true_labels.reshape(-1), y_pred=pred_labels.reshape(-1))
        print(f"threshold: {threshold}, f1: {f1}, time:{time.time()-start}")


if __name__ == "__main__":
    from database.REDIS import REDIS

    redis = REDIS()
    model = SinglePassCluster()

    # # 预测
    # text_list = redis.get_data("cluster_huawei_all")
    # cluster_text_list, cluster_ids, score = model.predict(text_list=text_list, threshold=setting.SINGLE_PASS_CLUSTER_THRESHOLD)
    #
    # # 网格搜索预测
    # best_cluster_text_list, best_cluster_ids = model.predict_with_threshold_search(text_list=text_list)

    # 有监督测试
    data = redis.get_data("faq_model1_all")
    model.supervise_test(data)

    # # 网格搜索测试
    # data = redis.get_data("faq_huawei_all")
    # model.gridsearch_test(data)

    input("w")

"""
聚类两次
huawei notwhite notscale(-1,1) cls  best threshold: 0.9600000000000004, best f1: 0.07851072440307567
huawei notwhite scale(0,1) cls  best threshold: 0.9800000000000004, best f1: 0.07851072440307567
huawei white notscale(-1,1) cls  best threshold: 0.5, best f1: 0.10728862973760932
huawei white scale(0,1) cls  best threshold: 0.6800000000000002, best f1: 0.20756302521008405
"""