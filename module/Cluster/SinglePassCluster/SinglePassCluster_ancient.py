import itertools
import time
import os
os.environ['MKL_NUM_THREADS']='10'

import jieba
import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.feature_extraction.text import TfidfTransformer
from sklearn.metrics import f1_score
from sklearn.metrics import silhouette_score
from sklearn.metrics.pairwise import cosine_similarity

import setting
from database.REDIS import REDIS
redis = REDIS()
from model.TextRepresentationPretrainBert.BertTextRepresentation import BertTextRepresentation
from setting import logger



def compute_kernel_bias(vecs, dim):
    """
    vecs = n*d
    计算kernel和bias
    最后的变换：y = (x + bias).dot(kernel)
    """
    mu = vecs.mean(axis=0, keepdims=True)
    cov = np.cov(vecs.T)
    u, s, vh = np.linalg.svd(cov)
    W = np.dot(u, np.diag(1 / np.sqrt(s)))
    return W[:, :dim], -mu

def transform_and_normalize(vecs, kernel=None, bias=None):
    """应用变换，然后标准化
    """
    if not (kernel is None or bias is None):
        vecs = (vecs + bias).dot(kernel)
    return vecs / (vecs**2).sum(axis=1, keepdims=True)**0.5

class SinglePassCluster:
    def __init__(self):
        self.model_trans = BertTextRepresentation(model_path=f'{setting.MAIN_DIR}/pretrain_model/roberta_intent_tupu_antoyue_num_en_trassim_1030',
                                            emb_layer=setting.SINGLE_PASS_CLUSTER_EMB_LAYER,
                                            max_len=setting.SINGLE_PASS_CLUSTER_MAX_LEN)
        self.model_simple = BertTextRepresentation(model_path=f'{setting.MAIN_DIR}/pretrain_model/roberta_intent_anto_num_en_0712',
                                            emb_layer=setting.SINGLE_PASS_CLUSTER_EMB_LAYER,
                                            max_len=setting.SINGLE_PASS_CLUSTER_MAX_LEN)

    def get_embedding(self, text_list,lang='tra'):
        logger.debug(f"开始获取 Embedding - 数据量: {len(text_list)}")
        if setting.SINGLE_PASS_CLUSTER_EMB_TYPE == "bert":
            if lang=='tra':
                logger.debug('使用繁体模型')
                embeddings = self.model_trans.encode_sentences(text_list=text_list, batch_size=setting.SINGLE_PASS_CLUSTER_BATCH_SIZE)
            else:
                logger.debug('使用简体模型')
                embeddings = self.model_simple.encode_sentences(text_list=text_list, batch_size=setting.SINGLE_PASS_CLUSTER_BATCH_SIZE)
            if setting.SINGLE_PASS_CLUSTER_USE_WHITE:
                kernel, bias = compute_kernel_bias(embeddings, setting.SINGLE_PASS_CLUSTER_WHITE_DIM)
                embeddings = transform_and_normalize(embeddings, kernel, bias)
        else:
            corpus = []
            for t in text_list:
                output = ' '.join(jieba.lcut(t))
                corpus.append(output.strip())
            vectorizer = CountVectorizer()
            transformer = TfidfTransformer()
            embeddings = transformer.fit_transform(vectorizer.fit_transform(corpus))
        return embeddings

    def predict(self,text_list,lang='sim'):
        if not text_list:
            return [], [], ''
        all_data = text_list
        embedding = self.get_embedding(all_data,lang=lang)
        # embedding = np.random.rand(200000,312)
        text_lists = all_data
        questions = all_data
        question_std = all_data
        def single_pass(sen_vec, sim_threshold):
            global text_vec
            global topic_serial
            global topic_cnt
            global error_topics
            global error_index

            # 向量归一化
            sen_vec = sen_vec * (1.0 / np.linalg.norm(sen_vec))
            if topic_cnt == 0:  # 第1次送入的文本
                # 添加文本向量
                text_vec = sen_vec
                # 话题数量+1
                topic_cnt += 1
                # 分配话题编号，话题编号从1开始
                topic_serial = [topic_cnt]
            else:  # 第2次及之后送入的文本
                # 文本逐一与已有的话题中的各文本进行相似度计算
                # sim_vec = tf.matmul(sen_vec, text_vec, transpose_b=True)
                sim_vec = np.dot(sen_vec, text_vec.T)
                # sim_vec = [cosine_similarity(sen_vec, y) for y in text_vec]
                # 将统计为error_topics位置改为0
                if error_index:
                    for i in error_index:
                        sim_vec[i] = 0.
                # 获取最大相似度值
                max_value = np.max(sim_vec)
                # 获取最大相似度值的文本所对应的话题编号
                topic_ser = topic_serial[np.argmax(sim_vec)]

                # 第二轮循环时，跳过已排除的差分类
                # print("topic_ser", topic_ser, "max_value", max_value)
                # 添加文本向量
                text_vec = np.vstack([text_vec, sen_vec])
                # 分配话题编号
                if max_value >= sim_threshold:
                    # 将文本聚合到该最大相似度的话题中
                    topic_serial.append(topic_ser)
                else:
                    # 否则新建话题，将文本聚合到该话题中
                    # 话题数量+1
                    topic_cnt += 1
                    # 将新增的话题编号（也就是增加话题后的话题数量）分配给当前文本
                    topic_serial.append(topic_cnt)

        # single-pass
        # 以文本在文本集中的顺序列出的文本向量矩阵（用300维向量表示）
        # text_vec = None
        # # 以文本在文本集中的顺序列出的话题序号列表
        # topic_serial = None
        # # 话题数量
        # topic_cnt = 0


        def matirc(text_lists,base_line, steps, save=False):
            scores = []
            scores_ = []
            base_lines = []
            return_for_boc = []
            for step in range(steps):
                try:
                    global text_vec
                    global topic_serial
                    global topic_cnt
                    global topic_vec
                    global topic_serial_cnt
                    global error_topics
                    global error_index

                    text_vec = None
                    # 以文本在文本集中的顺序列出的话题序号列表
                    topic_serial = None
                    # 聚类中心向量
                    topic_vec = None
                    # 各类的已匹配文档数量
                    topic_serial_cnt = None
                    # 话题数量
                    topic_cnt = 0
                    # 差的话题，只含有一个文本，仅用于第二次循环
                    error_topics = None
                    # 差的话题对应索引，只含有一个文本，仅用于第二次循环
                    error_index = None

                    baseline = base_line + step * 0.01
                    # 排序并计算分数
                    for i in range(len(embedding)):
                        single_pass(embedding[i], baseline)

                    topic_serial_new = []
                    topic_serial_new_long = []
                    text_vec_new = None
                    text_vec_new_long = None
                    topic_list_new = []
                    topic_list_new_long = []

                    error_list = []
                    error_topics = []
                    text_lists_new = []
                    text_lists_new_long = []
                    max_topic = max(topic_serial)
                    topic_serial = np.array(topic_serial)
                    text_lists = np.array(text_lists)
                    for i in np.arange(1, max_topic + 1):
                        topic_index = np.where(topic_serial == i)[0]  # 同一主题的index

                        if len(topic_index) >= 6:
                            topic_list_new_long.append(i)
                            topic_serial_new_long += list(topic_serial[topic_index])
                            text_lists_new_long += list(text_lists[topic_index])
                            if text_vec_new_long is None:
                                text_vec_new_long = text_vec[topic_index]
                            else:
                                text_vec_new_long = np.vstack([text_vec_new_long, text_vec[topic_index]])
                        elif len(topic_index) == 1:
                            error_list.append(text_lists[topic_index][0])
                            error_topics.append(i)
                        else:
                            topic_list_new.append(i)
                            topic_serial_new += list(topic_serial[topic_index])
                            text_lists_new += list(text_lists[topic_index])
                            if text_vec_new is None:
                                text_vec_new = text_vec[topic_index]
                            else:
                                text_vec_new = np.vstack([text_vec_new, text_vec[topic_index]])

                    increase_rate = 1.06
                    print('increase_rate: ', increase_rate)
                    baseline1 = base_line * increase_rate

                    topic_serial_new_long = np.array(topic_serial_new_long)
                    text_lists_new_long = np.array(text_lists_new_long)
                    topic_list_new_long = np.array(topic_list_new_long)
                    for topic in topic_list_new_long:
                        index = np.where(topic_serial_new_long == topic)[0]
                        texts = text_lists_new_long[index]
                        vectors = text_vec_new_long[index]
                        topic_serial = None
                        text_vec = None
                        topic_cnt = 0
                        for i in range(len(vectors)):
                            single_pass(vectors[i], baseline1)

                        texts = np.array(texts)
                        topic_serial = np.array(topic_serial)
                        for i in range(1, max(topic_serial) + 1):
                            index = np.where(topic_serial == i)[0]
                            if len(index) > 1:
                                text_lists_new += list(texts[index])
                                max_topic += 1
                                topic_list_new.append(max_topic)
                                topic_serial_new += [max_topic for j in range(len(index))]
                                text_vec_new = np.vstack([text_vec_new, text_vec[index]])
                            else:
                                error_list.append(texts[index][0])

                    topic_serial = topic_serial_new
                    text_vec = text_vec_new
                    topic_cnt = max_topic

                    error_text = [text.split('_')[0] for text in error_list]
                    error_embedding = self.get_embedding(error_text,lang=lang)

                    # 对错误的位置进行负分处理，用于第二轮
                    error_index = []
                    for i in range(len(topic_serial)):
                        if topic_serial[i] in error_topics:
                            error_index.append(i)

                    decay_rate = 0.76
                    baseline2 = baseline * decay_rate
                    for i in range(len(error_embedding)):
                        single_pass(error_embedding[i], baseline2)
                    sorted_key_words = sorted(zip(topic_serial, text_lists_new + error_list), key=lambda x: x[0])

                    return_for_boc = []
                    result = []
                    result_list = []
                    error_list2 = []
                    for i in np.arange(1, max(topic_serial) + 1):
                        if i in topic_list_new:
                            topic_list = [key_word for topic_ser, key_word in sorted_key_words if topic_ser == i]
                            # print("cluster", i, end='\t')
                            # print(u'||'.join(topic_list))
                            if len(topic_list) > 1:
                                result_list.append(topic_list)
                                result.append((i, u'||'.join(topic_list)))
                                return_for_boc.append(topic_list)
                            else:
                                error_list2.append(topic_list[0])


                    if error_list2 != []:
                        error_text = [text.split('_')[0] for text in error_list2]
                        error_embedding = self.get_embedding(error_text,lang=lang)
                        baseline3 = baseline2 * decay_rate
                        text_vec = None
                        # 以文本在文本集中的顺序列出的话题序号列表
                        topic_serial = None
                        # 聚类中心向量
                        topic_vec = None
                        # 各类的已匹配文档数量
                        topic_serial_cnt = None
                        # 话题数量
                        topic_cnt = 0
                        # 错误位置
                        error_index = None

                        for i in range(len(error_embedding)):
                            single_pass(error_embedding[i], baseline3)
                        sorted_key_words = sorted(zip(topic_serial, error_list2), key=lambda x: x[0])

                        for i in np.arange(1, max(topic_serial) + 1):
                            topic_list = [key_word for topic_ser, key_word in sorted_key_words if topic_ser == i]
                            # print("cluster", i, end='\t')
                            # print(u'||'.join(topic_list))
                            return_for_boc.append(topic_list)
                            result.append((i, u'||'.join(topic_list)))
                            result_list.append(topic_list)

                    if save:
                        result = pd.DataFrame(result)
                        result.columns = ['cluster', 'topics']


                    # 评价, 候选问是否和对应标准问在一起，代表绝对准确率
                    base_lines.append(baseline)
                except:
                    logger.debug('asd',exc_info=True)
                    scores.append((0, 0, 0, 0, 0))
                    scores_.append(0)
                    base_lines.append(0)
                    print(step)
                    return_for_boc = [[i] for i in text_lists]

            #把漏了的补上
            import itertools
            add = list(set(text_lists) - set(list(itertools.chain.from_iterable(return_for_boc))))
            if add:
                return_for_boc += [add]
            return base_lines, 0,return_for_boc


        steps = 1
        base_line = 0.8
        best_baseline, scores,return_for_boc = matirc(text_lists,base_line, steps, save=True)

        return return_for_boc,[],scores

    def _predict(self, text_list, embeddings=None, threshold=0.37):
        if text_list is None or len(text_list) == 0:
            return [], [], -1

        if embeddings is None:
            embeddings = self.get_embedding(text_list=text_list)
        text_num = embeddings.shape[0]

        # 第一次聚类
        logger.debug(f"开始第一次聚类, threshold: {threshold}")
        cluster_ids = []
        cluster_id_count = dict()
        max_cluster_id = 0

        for i in range(text_num):
            if i == 0:
                max_cluster_id += 1
                cluster_ids.append(max_cluster_id)
                cluster_id_count[max_cluster_id] = 1
                continue



            sim_score = cosine_similarity(embeddings[:i, :], embeddings[i, :].reshape(1, -1))[:, 0]
            max_idx = np.argmax(sim_score)
            if sim_score[max_idx] < threshold:
                max_cluster_id += 1
                cluster_ids.append(max_cluster_id)
                cluster_id_count[max_cluster_id] = cluster_id_count.get(max_cluster_id, 0) + 1
            else:
                cluster_ids.append(cluster_ids[max_idx])
                cluster_id_count[cluster_ids[max_idx]] = cluster_id_count.get(cluster_ids[max_idx], 0) + 1

        logger.debug(f"第一次聚类 - 簇个数:{max_cluster_id}")

        # 第二次聚类
        logger.debug(f"开始第二次聚类, threshold: {threshold}")
        need_re_cluster_idx = []
        min_count = len(text_list) // max_cluster_id // 2
        for idx, cur_id in enumerate(cluster_ids):
            if cluster_id_count[cur_id] < min_count:
                need_re_cluster_idx.append(idx)

        while len(need_re_cluster_idx):
            cur_idx = need_re_cluster_idx[0]

            sim_score = cosine_similarity(embeddings, embeddings[cur_idx, :].reshape(1, -1))[:, 0]
            for idx in need_re_cluster_idx:
                sim_score[idx] = -1e10

            max_idx = np.argmax(sim_score)
            if sim_score[max_idx] < threshold:
                max_cluster_id += 1
                cluster_ids[cur_idx] = max_cluster_id
            else:
                cluster_ids[cur_idx] = cluster_ids[max_idx]

            need_re_cluster_idx.pop(0)

        # 返回结果
        cluster_text_dict = {}
        for i, text in enumerate(text_list):
            if cluster_ids[i] not in cluster_text_dict:
                cluster_text_dict[cluster_ids[i]] = []
            cluster_text_dict[cluster_ids[i]].append(text)
        cluster_text_list = []
        for v in cluster_text_dict.values():
            cluster_text_list.append(v)

        try:
            score = silhouette_score(X=embeddings, labels=cluster_ids, metric="cosine")
        except:
            score = -1
        logger.debug(f"聚类成功 - 簇个数:{len(cluster_text_list)}, 轮廓系数:{score}")
        return cluster_text_list, cluster_ids, score


    def predict_with_grid_search(self, text_list, low_threshold=setting.SINGLE_PASS_CLUSTER_LOW_THRESHOLD,
                                 high_threshold=setting.SINGLE_PASS_CLUSTER_HIGH_THRESHOLD):
        if text_list is None or len(text_list) == 0:
            return [], [], -1
        if len(text_list) == 1:
            return [text_list], [1], -1

        embeddings = self.get_embedding(text_list=text_list)

        # 网格搜索
        best_score = -1e10
        best_cluster_ids = []
        best_cluster_text_list = []
        threshold = low_threshold
        best_threshold = low_threshold

        while threshold <= high_threshold:
            cluster_text_list, cluster_ids, score = self._predict(text_list=text_list,
                                                                 embeddings=embeddings,
                                                                 threshold=threshold)
            cluster_num = len(cluster_text_list)
            avg_num = np.sum([len(i) for i in cluster_text_list]) / cluster_num
            print({'cluster_num':cluster_num,'avg_num':avg_num,'score':score})
            if best_score <= -1 or (score > best_score and avg_num >= 5 and cluster_num <= 1000):
                best_score = score
                best_cluster_ids = cluster_ids
                best_cluster_text_list = cluster_text_list
                best_threshold = threshold
                logger.debug(f"找到更好的 threshold - threshold: {threshold}, cluster_num: {len(cluster_text_list)}, score: {score}")

            if cluster_num > 1000 or avg_num < 3:
                break
            threshold += 0.01

        return best_cluster_text_list, best_cluster_ids, best_threshold

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        pass

    def load_model(self, model_id, save_dir):
        pass

    def offline_model(self, model_id):
        pass

    def get_data(self, data):
        pass

    @staticmethod
    def get_data_generator(data, tokenizer=None):
        pass

    def supervise_test(self, data):
        # 读取数据
        text_list = []
        label_list = []
        for i, d in enumerate(data):
            title = d["title"].strip()
            if len(title):
                text_list.append(title)
                label_list.append(i)
            for t in d["labelData"].strip().split('||'):
                t = t.strip()
                if len(t):
                    text_list.append(t)
                    label_list.append(i)

        # 获取 bert embedding
        embeddings = self.get_embedding(text_list=text_list)

        # 搜索 threshold
        threshold = 0.3
        best_threshold = threshold
        best_f1 = -1e10
        while threshold <= 1:
            cluster_text_list, cluster_ids, score = self._predict(text_list=text_list, embeddings=embeddings, threshold=threshold)

            # F1
            A = np.tile(np.reshape(label_list, (-1, 1)), [1, len(label_list)])
            B = np.tile(np.reshape(label_list, (1, -1)), [len(label_list), 1])
            true_labels = np.array(A == B, dtype=np.int32)
            true_labels[np.eye(true_labels.shape[0], dtype=np.bool8)] = 0

            A = np.tile(np.reshape(cluster_ids, (-1, 1)), [1, len(label_list)])
            B = np.tile(np.reshape(cluster_ids, (1, -1)), [len(label_list), 1])
            pred_labels = np.array(A == B, dtype=np.int32)
            pred_labels[np.eye(pred_labels.shape[0], dtype=np.bool8)] = 0

            f1 = f1_score(y_true=true_labels.reshape(-1), y_pred=pred_labels.reshape(-1))
            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold

            print(f"threshold: {threshold}, f1: {f1}, 轮廓系数: {score}")
            threshold += 0.01
        print(f"best threshold: {best_threshold}, best f1: {best_f1}")

    def gridsearch_test(self, data):
        # 读取数据
        text_list = []
        label_list = []
        for i, d in enumerate(data):
            title = d["title"].strip()
            if len(title):
                text_list.append(title)
                label_list.append(i)
            for t in d["labelData"].strip().split('||'):
                t = t.strip()
                if len(t):
                    text_list.append(t)
                    label_list.append(i)

        # 聚类
        start = time.time()
        cluster_text_list, cluster_ids, threshold = self.predict_with_grid_search(text_list=text_list, low_threshold=0.3, high_threshold=1)
        print(f"预测完成时间:{time.time()-start}")

        # F1
        start = time.time()
        A = np.tile(np.reshape(label_list, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(label_list, (1, -1)), [len(label_list), 1])
        true_labels = np.array(A == B, dtype=np.int32)
        true_labels[np.eye(true_labels.shape[0], dtype=np.bool8)] = 0

        A = np.tile(np.reshape(cluster_ids, (-1, 1)), [1, len(label_list)])
        B = np.tile(np.reshape(cluster_ids, (1, -1)), [len(label_list), 1])
        pred_labels = np.array(A == B, dtype=np.int32)
        pred_labels[np.eye(pred_labels.shape[0], dtype=np.bool8)] = 0

        f1 = f1_score(y_true=true_labels.reshape(-1), y_pred=pred_labels.reshape(-1))
        print(f"threshold: {threshold}, 簇个数:{len(cluster_text_list)}, f1: {f1}, 计算 F1 时间:{time.time()-start}")

    def redis_clutser_to_sql(self,data_key):
        try:
            logger.debug(f'SinglePassCluster开始聚类data_key:{data_key}')
            data = redis.get_data(data_key)
            df = pd.DataFrame(data)
            df['session_time'] = pd.to_datetime(df['session_time'])
            cluster_text_list, cluster_ids, _ = model.predict(df['text'].tolist())
            final_output = []
            mapper = {'FAQ': 0, '闲聊': 1, '意图': 2}
            for index, i in enumerate(cluster_text_list):
                sec = df[df['text'].isin(i)]
                item = {}
                import json
                from datetime import datetime
                # item['id'] = index
                item['query_content'] = i[0]
                item['relation_knowledge_type'] = mapper[sec['relation_type'].value_counts().index[0]]
                item['relation_intention_id'] = sec['relation_intention_id'].value_counts().index[0]
                item['relation_intention'] = sec['relation_intention'].value_counts().index[0]
                item['query_count'] = sec['session_id'].unique().shape[0]
                item['session_time'] = sec['session_time'].min()
                item['text_id'] = json.dumps(sec['text_id'].tolist(), ensure_ascii=False)
                item['detail'] = json.dumps(sec['text'].tolist(), ensure_ascii=False)
                item['tenant_id'] = data_key.split('_')[1]
                item['create_time'] = datetime.now()
                final_output.append(item)
                # 反写问句详情页

                text_id = str(tuple(sec['text_id'].tolist()))
                text_id = text_id[:-2] + text_id[-2:].replace(',', '')
                mysql.execute_sql('aicc', f'update t_robot_none_match set class_id={index} where id in {text_id}')

            final_df = pd.concat([pd.DataFrame(i, index=[0]) for i in final_output])
            mysql.execute_sql('aicc', 'truncate table t_robot_none_match_nlp')
            mysql.to_sql(final_df, 'aicc', 't_robot_none_match_nlp', if_exists='append')
        except:
            logger.error(f'SinglePassCluster聚类失败报错,data_key:{data_key}')








if __name__ == "__main__":
    # from database.MYSQL import MYSQL
    # mysql = MYSQL(**setting.SQL_SETTING)
    model = SinglePassCluster()
    cluster_text_list, cluster_ids, _ = model.predict(['你好','没事','啊实打实的'])
    is_tans = [language_judge_regular.language_judge(text=i, lang='繁体中文') for i in ['你好','没事','啊实打实的']]

    # a = open(r"C:\Users\<USER>\Desktop\remark_2.txt", 'r', encoding='utf-8').read().split('\n')
    # df = pd.read_excel(r"D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2023-10\呼入明细_1698723923005.xlsx")
    # a = df['用户'].dropna().tolist()
    # a = [i for i in a if i]
    # a = list(set(a))
    # cluster_text_list, cluster_ids, _ = model.predict(a)
    # print(len(cluster_text_list),'cluster_text_listcluster_text_listcluster_text_listcluster_text_list')
    # output = []
    # for index,i in enumerate(cluster_text_list):
    #     item = {'text':i,'class':[index]*len(i)}
    #     output.append(item)
    # import pandas as pd
    # result = pd.concat([pd.DataFrame(i) for i in output])
    # text = result[result['text'].str.contains('普通')]['text'].tolist()
    # result.to_csv(r"C:\Users\<USER>\Desktop\output.csv",encoding='utf-8-sig')


    # emb = model.get_embedding(text)
    #
    # m = cosine_similarity(emb,emb)
    # corrmat = pd.DataFrame(m,columns=text,index=text)
    # import os
    # import pandas as pd
    # import numpy as np
    # from tqdm import tqdm
    #
    # import matplotlib.pyplot as plt
    # import seaborn as sns
    #
    # # matplotlib.use('Agg')
    # colors = sns.color_palette(["#FF005D", "#0085B6", "#0BB4C1", "#00D49D", "#FEDF03"])
    # plt.rcParams['axes.labelweight'] = 'bold'
    # plt.rcParams['font.sans-serif'] = ['SimHei']
    # plt.rcParams['axes.unicode_minus'] = False
    # plt.style.use("fivethirtyeight")  # plt.style.available?
    # plt.rcParams['figure.dpi'] = 250
    #
    # plt.subplots(figsize=(15, 20))
    # sns.heatmap(corrmat, vmax=1, vmin=0.5, square=True, xticklabels=True, yticklabels=True)
    # plt.title('各变量相关热力图')
    # # plt.tight_layout()
    # plt.yticks(fontsize=10)
    # plt.xticks(fontsize=10)
    # plt.savefig(r"C:\Users\<USER>\Desktop\123.jpg",dpi=300)


    #
    # model = SinglePassCluster()
    # key = 'cluster_397907891_1688723039885'
    # data = redis.get_data(key)
    # df = pd.DataFrame(data)
    # df['session_time'] = pd.to_datetime(df['session_time'],unit='ms')
    # cluster_text_list, cluster_ids, _ = model.predict(df['text'].tolist())
    # final_output = []
    # mapper = {'FAQ':0,'闲聊':1,'意图':2}
    # for index,i in enumerate(cluster_text_list):
    #     sec = df[df['text'].isin(i)]
    #     item = {}
    #     import json
    #     from datetime import datetime
    #     # item['id'] = index
    #     item['query_content'] = i[0]
    #     item['relation_knowledge_type'] = mapper[sec['relation_type'].value_counts().index[0]]
    #     item['relation_intention_id'] = sec['relation_intention_id'].value_counts().index[0]
    #     item['relation_intention'] = sec['relation_intention'].value_counts().index[0]
    #     item['query_count'] = sec['session_id'].unique().shape[0]
    #     item['session_time'] = sec['session_time'].min()
    #     item['text_id'] = json.dumps(sec['text_id'].tolist(), ensure_ascii=False)
    #     item['detail'] = json.dumps(sec['text'].tolist(), ensure_ascii=False)
    #     item['tenant_id'] = key.split('_')[1]
    #     item['create_time'] = datetime.now()
    #     final_output.append(item)
    #     #反写问句详情页
    #
    #     text_id = str(tuple(sec['text_id'].tolist()))
    #     text_id = text_id[:-2] + text_id[-2:].replace(',', '')
    #     mysql.execute_sql('aicc',f'update t_robot_none_match set class_id={index} where id in {text_id}')
    #
    # final_df = pd.concat([pd.DataFrame(i,index=[0]) for i in final_output])
    # mysql.execute_sql('aicc','truncate table t_robot_none_match_nlp')
    # mysql.to_sql(final_df,'aicc','t_robot_none_match_nlp',if_exists='append')

    # pd.DataFrame({'类别':cluster_text_list}).to_excel(r'D:\work\jupyter_notebook\other\杂事\浙江电信录音用户问题聚类\src\result\电商聚类\singpass_result.xlsx')
    # print('singlepass')
    # for i in cluster_text_list:
    #     print(len(i),i)
















    # kmeans
    # print('kmeans')
    # emb = model.get_embedding(text_list)
    # emb = pd.DataFrame(emb,index=text_list)
    # from sklearn.cluster import KMeans
    # iteration = 500
    # model = KMeans(n_clusters=5, n_jobs=4, max_iter=iteration)
    # model.fit(emb)
    #
    # r1 = pd.Series(model.labels_).value_counts()
    # r2 = pd.DataFrame(model.cluster_centers_)
    #
    # r = pd.concat([r2, r1], axis=1)
    # r.columns = list(emb.columns) + [u'class']
    #
    # res = pd.concat([emb, pd.Series(model.labels_, index=emb.index)], axis=1)
    # res.columns = list(emb.columns) + [u'class']
    #
    # for i in res['class'].unique():
    #     print(res[res['class']==i].index.tolist())
    #
    # #层次积累
    # print('层次聚类')
    # from sklearn.cluster import AgglomerativeClustering
    #
    # model = AgglomerativeClustering(n_clusters=None,distance_threshold=5, linkage='ward')
    # model.fit(emb)
    #
    #
    # res = pd.concat([emb, pd.Series(model.labels_, index = emb.index)], axis = 1)
    # res.columns = list(emb.columns) + [u'class']
    # for i in res['class'].unique():
    #     print(res[res['class']==i].index.tolist())

