import os
import pickle
import time
from collections import Counter

import jieba

import setting
from database.REDIS import REDIS
from setting import logger


class NGramContextInput:
    def __init__(self):
        self.n_grame = setting.NGRAM_CONTEXT_INPUT_N
        self.return_n = setting.NGRAM_CONTEXT_RETURN_N
        self.models = dict()
        self.stopwords = set()
        with open(setting.STOP_WORDS_FILE, "r") as f:
            for word in f:
                word = word.strip()
                if len(word) <= 1:
                    self.stopwords.add(word)
        self.symbols = '，|。|？|！|：|；|,|\.|\?|!|:|;'

    def load_model(self, model_id, save_dir):
        try:
            with open(os.path.join(save_dir, "n_gram_dicts.pkl"), 'rb') as f:
                n_gram_dicts = pickle.load(f)
            with open(os.path.join(save_dir, "reverse_sort_dict.pkl"), 'rb') as f:
                reverse_sort_dict = pickle.load(f)
            with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
                all_text = pickle.load(f)
            with open(os.path.join(save_dir, "vocabs_freq.pkl"), 'rb') as f:
                vocabs_freq = pickle.load(f)

            self.models[model_id] = dict()
            self.models[model_id]['n_gram_dicts'] = n_gram_dicts
            self.models[model_id]["reverse_sort_dict"] = reverse_sort_dict
            self.models[model_id]["all_text"] = all_text
            self.models[model_id]["vocabs_freq"] = vocabs_freq

        except Exception as e:
            logger.error(f"模型加载失败 [{model_id}], 错误信息: {e}")
            raise Exception(f"模型加载失败 [{model_id}], 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"模型下线成功 [{model_id}]")

    def train(self, model_id, data, save_dir):
        start = time.time()
        os.makedirs(save_dir, exist_ok=True)

        # 整理数据
        start2 = time.time()
        text2norm_id = dict()
        norm_id2norm_text = dict()
        for data_dict in data:
            norm_query = data_dict['title'].strip().lower()
            norm_query_id = data_dict['labelId']
            norm_id2norm_text[norm_query_id] = norm_query
            text2norm_id[norm_query] = norm_query_id
            for text in data_dict["labelData"].split("||"):
                text = text.strip().lower()
                if len(text):
                    text2norm_id[text] = norm_query_id
        all_text = list(text2norm_id.keys())
        logger.debug(f"读取训练数据结束,model_id:{model_id},数据量:{len(all_text)},耗时:{time.time()-start2}")

        # 分词
        start2 = time.time()
        vocabs_freq = Counter()
        all_text_words = [[]] * len(all_text)
        for i, text in enumerate(all_text):
            all_text_words[i] = [word for word in jieba.lcut(text) if word not in self.stopwords and word not in self.symbols]
            vocabs_freq.update(all_text_words[i])
        logger.debug(f"分词结束,model_id:{model_id},耗时:{time.time()-start2}")

        # 生成 n_gram 字典
        start2 = time.time()
        n_gram_dicts = []
        for n in range(1, self.n_grame+1):
            cur_dict = dict()
            for words in all_text_words:
                words_copy = words.copy()
                while len(words_copy) > n+3: # 起码剩三个word
                    n_gram_text = ''.join(words_copy[:n])
                    if n_gram_text not in cur_dict:
                        cur_dict[n_gram_text] = Counter()

                    next_word_list = []
                    start_idx = n
                    for end_idx in range(n+3, len(words_copy)):
                        next_word_list.append(''.join(words_copy[start_idx:end_idx]))
                    cur_dict[n_gram_text].update(next_word_list)

                    words_copy.pop(0)

            for key in cur_dict.keys():
                cur_dict[key] = cur_dict[key].most_common()
            n_gram_dicts.append(cur_dict)
        logger.debug(f"生成n-gram字典结束,model_id:{model_id},耗时:{time.time()-start2}")

        # 生成倒排索引
        start2 = time.time()
        reverse_sort_dict = {}
        for i, words in enumerate(all_text_words):
            for word in words:
                if len(word) > 1:
                    if word not in reverse_sort_dict:
                        reverse_sort_dict[word] = Counter()
                    reverse_sort_dict[word].update([i])
        logger.debug(f"生成倒排索引结束,model_id:{model_id},耗时:{time.time()-start2}")

        # 保存模型
        with open(os.path.join(save_dir, "n_gram_dicts.pkl"), 'wb') as f:
            pickle.dump(n_gram_dicts, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "reverse_sort_dict.pkl"), 'wb') as f:
            pickle.dump(reverse_sort_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "vocabs_freq.pkl"), 'wb') as f:
            pickle.dump(vocabs_freq, f, pickle.HIGHEST_PROTOCOL)
        self.models[model_id] = dict()
        self.models[model_id]['n_gram_dicts'] = n_gram_dicts
        self.models[model_id]["reverse_sort_dict"] = reverse_sort_dict
        self.models[model_id]["all_text"] = all_text
        self.models[model_id]["vocabs_freq"] = vocabs_freq

        logger.debug(f"训练完成,model_id:{model_id},耗时:{time.time()-start}")

    def incremental_train(self, model_id, data, save_dir):
        pass

    def predict(self, model_id, query):
        if model_id not in self.models:
            logger.error(f"预测失败 [{model_id}], 错误信息: 模型未加载")
            raise Exception(f"预测失败 [{model_id}], 错误信息: 模型未加载")

        n_gram_dicts = self.models[model_id]['n_gram_dicts']
        reverse_sort_dict = self.models[model_id]['reverse_sort_dict']
        all_text = self.models[model_id]['all_text']
        vocabs_freq = self.models[model_id]["vocabs_freq"]

        all_words = jieba.lcut(query)
        query_words = all_words[-self.n_grame:]
        result = {"words": [], "query": []}

        # n-gram
        for n in range(min(len(query_words), self.n_grame), 0, -1):
            n_gram_text = ''.join(query_words[-n:])
            result["words"] = result["words"] + n_gram_dicts[n-1].get(n_gram_text, [])
        result["words"] = result["words"][:self.return_n]
        result["words"] = [query+r[0] for r in result["words"]]

        # 倒排
        all_words = [(word, vocabs_freq[word]) for word in all_words if len(word) > 1  and vocabs_freq[word]]
        all_words.sort(key=lambda x: x[0])

        counter = Counter()
        for word, word_freq in all_words:
            if word not in reverse_sort_dict:
                continue

            if len(counter):
                cur_counter = reverse_sort_dict[word]
                new_counter = counter.copy()
                for key in cur_counter.keys():
                    if key in new_counter:
                        new_counter[key] += cur_counter[key]
                for key in list(new_counter.keys()):
                    if key not in cur_counter:
                        new_counter.pop(key)

                if len(new_counter) >= setting.NGRAM_CONTEXT_RETURN_N:
                    counter = new_counter
                else:
                    for key in new_counter.keys():
                        counter[key] = new_counter[key]
            else:
                counter = reverse_sort_dict[word]

            if len(counter) <= setting.NGRAM_CONTEXT_RETURN_N:
                break

        reverse_sort = counter.most_common(setting.NGRAM_CONTEXT_RETURN_N)
        result["query"] = [all_text[i] for (i, _) in reverse_sort]

        return result

    def jieba_cut(self, text):
        return [word for word in jieba.lcut(text) if word not in self.stopwords and word not in self.symbols]


if __name__ == '__main__':
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    ranker = NGramContextInput()
    R = REDIS()

    # 训练 faq 模型
    model_id = "model1"
    save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{NGramContextInput.__name__}/")
    data_faq = R.get_data(f'faq_model1_all')
    ranker.train(model_id=model_id, data=data_faq, save_dir=save_dir)

    # 加载模型
    ranker.load_model(model_id=model_id, save_dir=save_dir)

    # 预测
    query_list = ['请问余额理财赎回', '用IE登录个人网银']
    for query in query_list:
        result = ranker.predict(model_id=model_id, query=query)
        print(f'\n查询句子: {query}')
        for key, value in result.items():
            print(f"{key}: {value}")

    while True:
        s = input("输入:")
        result = ranker.predict(model_id=model_id, query=s)
        for key, value in result.items():
            print(f"{key}: {value}")
