# -*- coding: UTF-8 -*-

import os
# os.environ['CUDA_VISIBLE_DEVICES'] = "0"
import random
import numpy as np
import string
from tensorflow import keras


class DataGenerator(keras.utils.Sequence):
    def __init__(self, text_list, teacher_tokenizer, student_tokenizer, teacher_model, batch_num_samples=64, max_len=128):
        self.text_list = text_list
        self.idx_list = [i for i in range(len(text_list))]
        self.teacher_tokenizer = teacher_tokenizer
        self.student_tokenizer = student_tokenizer
        self.teacher_model = teacher_model
        self.batch_num_samples = min(len(text_list), batch_num_samples)
        self.max_len = max_len
        self.num_text = np.sum([len(t) for t in self.text_list])
        self.num_batches = max(len(self.idx_list)//self.batch_num_samples, 1)
        self.item_idx_list = [i for i in range(self.num_batches)]
        random.shuffle(self.item_idx_list)
        self.en_digits = string.digits+string.ascii_lowercase+string.ascii_uppercase
        self.zh_words = set()
        with open("/data/cbk/aicc-nlu/data/中文词库/webdict_with_freq.txt", "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip().split(" ")[0]
                if len(line) <= 5:
                    self.zh_words.add(line)
        self.zh_words = list(self.zh_words)

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return self.num_batches

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        true_index = self.item_idx_list[index]
        X, Y = self.__data_generation(self.idx_list[true_index*self.batch_num_samples:(true_index+1)*self.batch_num_samples])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        random.shuffle(self.item_idx_list)

    def __data_generation(self, idx_list):
        """
        生成 cur_id 的数据。
        """
        input_ids = np.zeros(shape=(len(idx_list)*2, self.max_len), dtype=np.int32)
        token_type_ids = np.zeros(shape=(len(idx_list)*2, self.max_len), dtype=np.int32)
        attention_mask = np.zeros(shape=(len(idx_list)*2, self.max_len), dtype=np.int32)
        input_ids_teacher = np.zeros(shape=(len(idx_list)*2, self.max_len), dtype=np.int32)
        token_type_ids_teacher = np.zeros(shape=(len(idx_list)*2, self.max_len), dtype=np.int32)
        attention_mask_teacher = np.zeros(shape=(len(idx_list)*2, self.max_len), dtype=np.int32)
        labels = np.zeros(shape=(len(idx_list)*2, 1))


        for i, idx in enumerate(idx_list):
            for j in range(2):
                # 一个 idx 采样两个样本，作为正样本
                if j < len(self.text_list[idx]):
                    text = self.text_list[idx][j]
                else:
                    text = self.text_list[idx][0]
                    temp_idx = random.randint(int(len(text)*0.4), int(len(text)*0.6))
                    text = text[temp_idx:] + text[:temp_idx]
                text = self.esimcse_repeat_word(text)

                inputs = self.student_tokenizer.encode_plus(text, add_special_tokens=True, max_length=self.max_len, truncation=True, padding="max_length")
                input_ids[i*2+j, :] = inputs["input_ids"]
                attention_mask[i*2+j, :] = inputs["attention_mask"]
                token_type_ids[i*2+j, :] = inputs["token_type_ids"]
                inputs = self.teacher_tokenizer.encode_plus(text, add_special_tokens=True, max_length=self.max_len, truncation=True, padding="max_length")
                input_ids_teacher[i * 2 + j, :] = inputs["input_ids"]
                attention_mask_teacher[i * 2 + j, :] = inputs["attention_mask"]
                token_type_ids_teacher[i * 2 + j, :] = inputs["token_type_ids"]

        outputs = self.teacher_model(input_ids=input_ids_teacher, attention_mask=attention_mask_teacher, token_type_ids=token_type_ids_teacher, output_attentions=True, output_hidden_states=True, training=False)
        return (
            {
                'input_ids': input_ids,
                'token_type_ids': token_type_ids,
                'attention_mask': attention_mask,
                'teacher_outputs': outputs
            },
            labels
        )

    @staticmethod
    def esimcse_repeat_word(text):
        if len(text) < 2:
            return text
        actual_len = len(text)
        dup_len = random.randint(a=0, b=max(1, int(0.15 * actual_len)))
        dup_word_index = random.sample(list(range(1, actual_len)), k=dup_len)
        dup_text = ''
        for index, word in enumerate(text):
            dup_text += word
            if index in dup_word_index:
                dup_text += word
        return dup_text

if __name__ == '__main__':
    import json
    from transformers.models.bert import BertConfig, BertTokenizer, TFBertModel
    from sklearn.model_selection import train_test_split

    TEACHER_MODEL_CONFIG_PATH = "/data/cbk/NLP_Model/pretrain_base_arccse2n_arcTrue_tri(replace)False_esimcse_shuffle(b_t)_batch64_epoch1_allsts_ourdata/ArcCSEPreRankPretrain"
    TEACHER_MODEL_PATH = "/data/cbk/NLP_Model/pretrain_base_arccse2n_arcTrue_tri(replace)False_esimcse_shuffle(b_t)_batch64_epoch1_allsts_ourdata/ArcCSEPreRankPretrain/save_pretrained"

    with open("/data/cbk/aicc-nlu/data/语义匹配/all_sts_pretrain_data_with_our_data_aug_1213.json", "r", encoding="utf-8") as f:
        data_list = json.load(f)
    text_list = []
    for d in data_list:
        text_list.append(d["title"])
        if len(text_list) >= 200:
            break
    train_text_list, test_text_list = train_test_split(text_list, test_size=0.2)
    teacher_config = BertConfig.from_pretrained(TEACHER_MODEL_CONFIG_PATH)
    teacher_tokenizer = BertTokenizer.from_pretrained(TEACHER_MODEL_CONFIG_PATH)
    teacher_model = TFBertModel.from_pretrained(TEACHER_MODEL_PATH)

    data_generator = DataGenerator(text_list=text_list, tokenizer=teacher_tokenizer, teacher_model=teacher_model, batch_num_samples=64, max_len=128)

    for d in data_generator:
        print("wait")
        break
