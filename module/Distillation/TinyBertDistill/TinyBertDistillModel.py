#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:TinyBertDistillModel.py
@time:2022/12/14
"""

import os
# os.environ['CUDA_VISIBLE_DEVICES'] = "0"
import numpy as np
import math
import tensorflow as tf
from transformers.models.bert.modeling_tf_bert import TFBertModel


class TinyBertDistillModel(tf.keras.Model):
    def __init__(self, config, tokenizer, emb_layer, teacher_model=None, max_len=128, use_attn_loss=True, use_pooler_loss=True, use_emb_loss=True):
        super(TinyBertDistillModel, self).__init__()
        self.config = config
        self.tokenizer = tokenizer
        self.bert = TFBertModel(config=self.config)
        self.max_len = max_len
        self.emb_layer = emb_layer
        self.use_attn_loss = use_attn_loss
        self.use_pooler_loss = use_pooler_loss
        self.use_emb_loss = use_emb_loss
        self.temperature = 1
        self.teacher_model = teacher_model
        if self.teacher_model.config.vocab_size != self.config.vocab_size:
            self.use_emb_loss = False
        self.hidden_dense = tf.keras.layers.Dense(teacher_model.config.hidden_size)
        self.pooler_dense = tf.keras.layers.Dense(teacher_model.config.hidden_size)
        self.token_emb_dense = tf.keras.layers.Dense(teacher_model.config.hidden_size)
        self.pos_emb_dense = tf.keras.layers.Dense(teacher_model.config.hidden_size)
        self.token_type_emb_dense = tf.keras.layers.Dense(teacher_model.config.hidden_size)
        # inputs = self.get_data(["测试一下"], max_len=self.max_len)
        # self.bert(inputs)
        # self.bert.bert.embeddings.set_weights(teacher_model.bert.embeddings.get_weights())

    def call(self, inputs, **kwargs):
        input_ids = inputs["input_ids"]
        token_type_ids = inputs.get("token_type_ids", None)
        attention_mask = inputs.get("attention_mask", None)
        teacher_outputs = inputs.get("teacher_outputs", None)
        student_outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            output_attentions=True,
            output_hidden_states=True,
            **kwargs
        )
        student_outputs.fit_hidden_states = [self.hidden_dense(h) for h in student_outputs.hidden_states]
        student_outputs.fit_pooler_output = self.pooler_dense(student_outputs.pooler_output)
        if teacher_outputs is None:
            return student_outputs

        teacher_layer_num = len(teacher_outputs.attentions)
        student_layer_num = len(student_outputs.attentions)
        assert teacher_layer_num % student_layer_num == 0
        layers_per_block = int(teacher_layer_num / student_layer_num)

        """ 测试用 """
        # for p in [
        #     ["初始化模型", "/data/cbk/NLP_Model/distill_init/ArcCSEPreRankPretrain"],
        #     ["task only mse", "/data/cbk/NLP_Model/distill_0104_taskonlymse/ArcCSEPreRankPretrain"],
        #     ["task only mse e=10 d=10000", "/data/cbk/NLP_Model/pretrain_base_arccsen_arcTrue_tri(replace)False_esimcse_shuffle(b_t)_batch64_epoch1_allsts_ourdata_auggpt5000/TinyBertDistillModel_taskonlymse_epoch10_10000"],
        #     ["task only ce t=1", "/data/cbk/NLP_Model/distill_0111_taskonlyce_t1/ArcCSEPreRankPretrain"]
        # ]:
        #     self.bert.load_weights(os.path.join(p[1], 'best_model.h5'))
        #     student_outputs = self.bert(
        #         input_ids=input_ids,
        #         attention_mask=attention_mask,
        #         token_type_ids=token_type_ids,
        #         output_attentions=True,
        #         output_hidden_states=True,
        #         **kwargs
        #     )
        #
        #     student_embedding = self.get_encoder_layer(student_outputs)
        #     teacher_embedding = self.get_encoder_layer(teacher_outputs)
        #     student_embedding = tf.math.l2_normalize(student_embedding, axis=1)
        #     teacher_embedding = tf.math.l2_normalize(teacher_embedding, axis=1)
        #     student_cosine = tf.matmul(student_embedding, student_embedding, transpose_b=True)
        #     teacher_cosine = tf.matmul(teacher_embedding, teacher_embedding, transpose_b=True)
        #     student_cosine = student_cosine - tf.eye(tf.shape(student_cosine)[0]) * 1e12
        #     teacher_cosine = teacher_cosine - tf.eye(tf.shape(teacher_cosine)[0]) * 1e12
        #     ce_loss_t_1 = soft_cross_entropy(student_cosine/1.0, teacher_cosine/1.0)
        #     ce_loss_t_02 = soft_cross_entropy(student_cosine/0.2, teacher_cosine/0.2)
        #     mse_loss = tf.reduce_mean(tf.keras.losses.mean_squared_error(student_cosine, teacher_cosine))
        #     print(f"|{p[0]}|{ce_loss_t_1}|{ce_loss_t_02}|{mse_loss}|")
        #
        #     # temp = 1
        #     # student_likelihood = tf.nn.log_softmax(student_cosine/temp, -1)
        #     # targets_prob = tf.nn.softmax(teacher_cosine/temp, -1)
        #     # print(tf.reduce_mean(- targets_prob * student_likelihood))
        #     # print(tf.reduce_mean(tf.keras.losses.mean_squared_error(student_cosine, teacher_cosine)))
        #     # student_likelihood = tf.nn.log_softmax(teacher_cosine/temp, -1)
        #     # targets_prob = tf.nn.softmax(teacher_cosine/temp, -1)
        #     # print(tf.reduce_mean(- targets_prob * student_likelihood))
        #     # print(tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_cosine, teacher_cosine)))
        #     # A = tf.nn.softmax(teacher_cosine/temp, -1).numpy()
        #     # B = tf.nn.softmax(teacher_cosine[:6, :6]/temp, -1).numpy()
        #     # C = tf.nn.softmax(student_cosine/temp, -1).numpy()
        #     # D = tf.nn.softmax(student_cosine[:6, :6]/temp, -1).numpy()
        # print("wait")
        """ 测试用 """

        att_loss = None
        if self.use_attn_loss:
            new_teacher_attentions = [teacher_outputs.attentions[i * layers_per_block + layers_per_block - 1] for i in range(student_layer_num)]
            for student_att, teacher_att in zip(student_outputs.attentions, new_teacher_attentions):
                A = tf.keras.losses.mean_squared_error(teacher_att, student_att)
                A = tf.reduce_mean(A, 1)
                A = tf.reduce_sum(A, -1)
                mse = tf.reduce_sum(A / tf.cast(tf.reduce_sum(attention_mask, -1), tf.float32))
                if att_loss is None:
                    att_loss = mse
                    # att_loss = tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_att, student_att))
                else:
                    att_loss += mse
                    # att_loss += tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_att, student_att))

        # hidden_loss = None
        # new_teacher_hidden = [teacher_outputs.hidden_states[i * layers_per_block] for i in range(student_layer_num + 1)]
        # for student_hidden, teacher_hidden in zip(student_outputs.fit_hidden_states, new_teacher_hidden):
        #     if hidden_loss is None:
        #         hidden_loss = tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_hidden, student_hidden))
        #     else:
        #         hidden_loss += tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_hidden, student_hidden))

        def soft_cross_entropy(predicts, targets):
            student_likelihood = tf.nn.log_softmax(predicts, -1)
            targets_prob = tf.nn.softmax(targets, -1)
            return tf.reduce_mean(- targets_prob * student_likelihood)

        student_embedding = self.get_encoder_layer(student_outputs)
        teacher_embedding = self.get_encoder_layer(teacher_outputs)
        student_embedding = tf.math.l2_normalize(student_embedding, axis=1)
        teacher_embedding = tf.math.l2_normalize(teacher_embedding, axis=1)
        student_cosine = tf.matmul(student_embedding, student_embedding, transpose_b=True)
        teacher_cosine = tf.matmul(teacher_embedding, teacher_embedding, transpose_b=True)
        student_cosine = student_cosine - tf.eye(tf.shape(student_cosine)[0]) * 1e12
        teacher_cosine = teacher_cosine - tf.eye(tf.shape(teacher_cosine)[0]) * 1e12
        # task_loss = soft_cross_entropy(student_cosine/self.temperature, teacher_cosine/self.temperature)
        task_loss = tf.reduce_mean(tf.keras.losses.mean_squared_error(student_cosine, teacher_cosine))

        # pooler_loss = tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_outputs.pooler_output, student_outputs.fit_pooler_output))
        #
        # stu_token_emb = self.token_emb_dense(self.bert.bert.embeddings.weights[0])
        # stu_token_type_emb = self.token_type_emb_dense(self.bert.bert.embeddings.weights[1])
        # stu_pos_emb = self.pos_emb_dense(self.bert.bert.embeddings.weights[2])
        # emb_loss = tf.reduce_mean(tf.keras.losses.mean_squared_error(stu_token_emb, self.teacher_model.bert.embeddings.weights[0]))
        # emb_loss += tf.reduce_mean(tf.keras.losses.mean_squared_error(stu_token_type_emb, self.teacher_model.bert.embeddings.weights[1]))
        # emb_loss += tf.reduce_mean(tf.keras.losses.mean_squared_error(stu_pos_emb, self.teacher_model.bert.embeddings.weights[2]))

        # print(hidden_loss.numpy(), task_loss.numpy(), att_loss.numpy(), pooler_loss.numpy(), emb_loss.numpy())
        # loss = hidden_loss + task_loss
        loss = task_loss
        if self.use_attn_loss:
            loss += att_loss
        # if self.use_pooler_loss:
        #     loss += pooler_loss
        # if self.use_emb_loss:
        #     loss += emb_loss

        return loss

    def get_encoder_layer(self, outputs):
        hidden_states = outputs.hidden_states
        if isinstance(self.emb_layer, str):
            emb = hidden_states[-1][:, 0, :]
        elif isinstance(self.emb_layer, list):
            emb = None
            for layer in self.emb_layer:
                emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
            emb = emb / len(self.emb_layer)
            emb = tf.reduce_mean(emb, axis=1)
        else:
            raise Exception(f"{self.__class__.__name__} 参数出错: emb_layer {self.emb_layer}")
        return emb

    def get_data(self, texts, max_len, return_tensor=False):
        if isinstance(texts, str):
            texts = [texts]
        data = {
            "input_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "token_type_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "attention_mask": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
        }
        for i, text in enumerate(texts):
            inputs = self.tokenizer.encode_plus(text, add_special_tokens=True, max_length=max_len, truncation=True, padding="max_length")
            data["input_ids"][i, :] = inputs["input_ids"]
            data["token_type_ids"][i, :] = inputs["token_type_ids"]
            data["attention_mask"][i, :] = inputs["attention_mask"]

        if return_tensor:
            data["input_ids"] = tf.convert_to_tensor(data["input_ids"])
            data["token_type_ids"] = tf.convert_to_tensor(data["token_type_ids"])
            data["attention_mask"] = tf.convert_to_tensor(data["attention_mask"])
        return data


class TinyBertDistillLoss(tf.keras.losses.Loss):
    def __init__(self, **kwargs):
        super(TinyBertDistillLoss, self).__init__(**kwargs)

    def call(self, y_true, y_pred):
        return y_pred

    def get_config(self):
        return super(TinyBertDistillLoss, self).get_config()


if __name__ == "__main__":
    from transformers.models.bert import BertConfig, BertTokenizer
    import setting

    config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
    tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
    model = TinyBertDistillModel(config=config, tokenizer=tokenizer, teacher_model=None)
    data = model.get_data("你好啊", max_len=128)
    model(data)
    print("wait")
