#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:TinyBertDistillModel.py
@time:2022/12/14
"""

import os
os.environ['CUDA_VISIBLE_DEVICES'] = "0"
import numpy as np
import math
import tensorflow as tf
from transformers.models.bert.modeling_tf_bert import TFBertModel


class TinyBertDistillModel(tf.keras.Model):
    def __init__(self, config, tokenizer, teacher_model=None, max_len=128):
        super(TinyBertDistillModel, self).__init__()
        self.config = config
        self.tokenizer = tokenizer
        self.bert = TFBertModel(config=self.config)
        self.max_len = max_len
        self.teacher_model = teacher_model
        self.fit_dense = tf.keras.layers.Dense(teacher_model.config.hidden_size)
        # inputs = self.get_data(["测试一下"], max_len=self.max_len)
        # self.bert(inputs)
        # self.bert.bert.embeddings.set_weights(teacher_model.bert.embeddings.get_weights())

    def call(self, inputs, **kwargs):
        input_ids = inputs["input_ids"]
        token_type_ids = inputs.get("token_type_ids", None)
        attention_mask = inputs.get("attention_mask", None)
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            output_attentions=True,
            output_hidden_states=True,
            **kwargs
        )
        outputs.fit_hidden_states = [self.fit_dense(h) for h in outputs.hidden_states]
        return outputs

    def get_data(self, texts, max_len, return_tensor=False):
        if isinstance(texts, str):
            texts = [texts]
        data = {
            "input_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "token_type_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "attention_mask": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
        }
        for i, text in enumerate(texts):
            inputs = self.tokenizer.encode_plus(text, add_special_tokens=True, max_length=max_len, truncation=True, padding="max_length")
            data["input_ids"][i, :] = inputs["input_ids"]
            data["token_type_ids"][i, :] = inputs["token_type_ids"]
            data["attention_mask"][i, :] = inputs["attention_mask"]

        if return_tensor:
            data["input_ids"] = tf.convert_to_tensor(data["input_ids"])
            data["token_type_ids"] = tf.convert_to_tensor(data["token_type_ids"])
            data["attention_mask"] = tf.convert_to_tensor(data["attention_mask"])
        return data


class TinyBertDistillLoss(tf.keras.losses.Loss):
    def __init__(self, use_attn_loss=True, **kwargs):
        super(TinyBertDistillLoss, self).__init__(**kwargs)
        self.use_attn_loss = use_attn_loss

    def call(self, y_true, y_pred):
        teacher_layer_num = len(y_true.attentions)
        student_layer_num = len(y_pred.attentions)
        assert teacher_layer_num % student_layer_num == 0
        layers_per_block = int(teacher_layer_num / student_layer_num)

        if self.use_attn_loss:
            att_loss = None
            new_teacher_attentions = [y_true.attentions[i * layers_per_block + layers_per_block - 1] for i in range(student_layer_num)]
            for student_att, teacher_att in zip(y_pred.attentions, new_teacher_attentions):
                # student_att = tf.clip_by_value(student_att, clip_value_min=0, clip_value_max=1)
                # teacher_att = tf.clip_by_value(teacher_att, clip_value_min=0, clip_value_max=1)
                if att_loss is None:
                    att_loss = tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_att, student_att))
                else:
                    att_loss += tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_att, student_att))

        hidden_loss = None
        new_teacher_hidden = [y_true.hidden_states[i * layers_per_block] for i in range(student_layer_num + 1)]
        for student_hidden, teacher_hidden in zip(y_pred.fit_hidden_states, new_teacher_hidden):
            if hidden_loss is None:
                hidden_loss = tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_hidden, student_hidden))
            else:
                hidden_loss += tf.reduce_mean(tf.keras.losses.mean_squared_error(teacher_hidden, student_hidden))

        if self.use_attn_loss:
            loss = att_loss + hidden_loss
        else:
            loss = hidden_loss
        return loss

    def get_config(self):
        return super(TinyBertDistillLoss, self).get_config()


if __name__ == "__main__":
    from transformers.models.bert import BertConfig, BertTokenizer
    import setting

    config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
    tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
    model = TinyBertDistillModel(config=config, tokenizer=tokenizer, teacher_model=None)
    data = model.get_data("你好啊", max_len=128)
    model(data)
    print("wait")
