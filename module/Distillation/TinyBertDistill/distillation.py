#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:distillation.py
@time:2022/12/14
"""

import os
os.environ['CUDA_VISIBLE_DEVICES'] = "-1"

import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

import json
import numpy as np
from tqdm import tqdm
from transformers.models.bert import BertConfig, BertTokenizer, TFBertModel
from module.Distillation.TinyBertDistill.TinyBertDistillModel import TinyBertDistillModel, TinyBertDistillLoss
from module.Distillation.TinyBertDistill.DataGenerator import DataGenerator
from sklearn.model_selection import train_test_split
from utils.my_utils import complete_match_text

TEACHER_MODEL_CONFIG_PATH = "/data/cbk/aicc-nlu/pretrain_model/roberta_chinese_clue_base"
TEACHER_MODEL_PATH = "/data/cbk/NLP_Model/pretrain_base_arccsen_arcTrue_tri(replace)False_esimcse_shuffle(b_t)_batch64_epoch1_allsts_ourdata_auggpt5000/ArcCSEPreRankPretrain"
STUDENT_MODEL_CONFIG_PATH = "/data/cbk/aicc-nlu/pretrain_model/roberta_chinese_clue_base_distill_tiny"
SAVE_MODEL_PATH = "/".join(TEACHER_MODEL_CONFIG_PATH.split("/")[:-1]) + "/test"
os.makedirs(SAVE_MODEL_PATH, exist_ok=True)
LEARNING_RATE = 5e-5
MAX_LEN = 128
EPOCHS = 1
BATCH_SAMPLES = 64
USE_ATTN_LOSS = True

teacher_config = BertConfig.from_pretrained(TEACHER_MODEL_CONFIG_PATH)
teacher_tokenizer = BertTokenizer.from_pretrained(TEACHER_MODEL_CONFIG_PATH)
teacher_model = TFBertModel.from_pretrained(TEACHER_MODEL_PATH, from_pt=False, config=teacher_config)
student_config = BertConfig.from_pretrained(STUDENT_MODEL_CONFIG_PATH)
student_tokenizer = BertTokenizer.from_pretrained(STUDENT_MODEL_CONFIG_PATH)
student_model = TinyBertDistillModel(config=student_config, tokenizer=student_tokenizer, emb_layer=[1, -1], teacher_model=teacher_model, max_len=MAX_LEN, use_attn_loss=True,  use_pooler_loss=True, use_emb_loss=True)
test_inputs = student_model.get_data("测试一下", max_len=MAX_LEN)
student_model(test_inputs)
teacher_model(test_inputs)
student_model.bert.load_weights(os.path.join("/data/cbk/NLP_Model/distill_init/ArcCSEPreRankPretrain", 'best_model.h5'))

with open("/data/cbk/aicc-nlu/data/语义匹配/all_sts_pretrain_data_with_our_data_aug_1213.json", "r", encoding="utf-8") as f:
    data_list = json.load(f)

def get_data_generator(data, teacher_tokenizer=None, teacher_model=None,):
    # 整理数据
    text2norm_id = dict()
    norm_id2norm_text = dict()
    text2item_cm = dict()  # 精确匹配字典
    text_list_train = []
    text_list_valid = []

    for data_dict in tqdm(data):
        norm_query = data_dict['title'].strip().lower()
        norm_query_id = data_dict['labelId']
        texts = set()
        texts.add(norm_query)

        norm_id2norm_text[norm_query_id] = norm_query
        text2norm_id[norm_query] = norm_query_id
        norm_query_ = complete_match_text(norm_query)
        if len(norm_query_):
            text2item_cm[norm_query_] = [norm_query, norm_query_id]

        for text in data_dict["labelData"].split("||"):
            text = text.strip().lower()
            if len(text):
                texts.add(text)
                text2norm_id[text] = norm_query_id
            text = complete_match_text(text)
            if len(text):
                text2item_cm[text] = [norm_query, norm_query_id]

        texts = list(texts)
        if len(texts) <= 0:
            continue
        else:
            text_list_train.append(texts)

    num_valid_text = np.sum([len(l) for l in text_list_valid])
    data_gen_train = DataGenerator(text_list=text_list_train, teacher_tokenizer=teacher_tokenizer, student_tokenizer=student_tokenizer, teacher_model=teacher_model, batch_num_samples=BATCH_SAMPLES, max_len=MAX_LEN)
    data_gen_valid = DataGenerator(text_list=text_list_valid, teacher_tokenizer=teacher_tokenizer, student_tokenizer=student_tokenizer, teacher_model=teacher_model, batch_num_samples=BATCH_SAMPLES, max_len=MAX_LEN) if num_valid_text else None
    return data_gen_train, data_gen_valid

train_data_generator, test_data_generator = get_data_generator(data=data_list, teacher_tokenizer=teacher_tokenizer, teacher_model=teacher_model)

for i in range(100):
    inputs, labels = train_data_generator[i]
    for j in range(5):
        tokens = teacher_tokenizer.convert_ids_to_tokens(inputs["input_ids"][j])
        tokens = [c for c in tokens if "]" not in c]
        print("".join(tokens))
    loss = TinyBertDistillLoss()
    outputs = student_model(inputs)
    loss(labels, outputs)
    print("wait")


# new_inputs = {"input_ids": inputs["input_ids"], "token_type_ids": inputs["token_type_ids"], "attention_mask": inputs["attention_mask"]}
# A = teacher_model(new_inputs, output_hidden_states=True)
# A_emb = get_encoder_layer(A)
# A_emb = tf.math.l2_normalize(A_emb, axis=1)
# teacher_cosine = tf.matmul(A_emb, A_emb, transpose_b=True)
# for i in range(5):
#     tokens = teacher_tokenizer.convert_ids_to_tokens(inputs["input_ids"][i])
#     tokens = [c for c in tokens if "]" not in c]
#     print("".join(tokens))
#
# def get_encoder_layer(outputs):
#     emb_layer = [1, -1]
#     hidden_states = outputs[2]
#     if isinstance(emb_layer, str):
#         emb = hidden_states[-1][:, 0, :]
#     elif isinstance(emb_layer, list):
#         emb = None
#         for layer in emb_layer:
#             emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
#         emb = emb / len(emb_layer)
#         emb = tf.reduce_mean(emb, axis=1)
#     return emb

# # 训练模型
# opt = tf.keras.optimizers.Adam(learning_rate=LEARNING_RATE, epsilon=1e-08)
# loss = TinyBertDistillLoss(name="tiny_bert_distill_loss")
# student_model.compile(optimizer=opt, loss=loss)
# model_file = os.path.join(SAVE_MODEL_PATH, 'tf_model.h5')
# student_model.save_weights(model_file)
# callbacks = [
#     tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_loss', save_best_only=True, save_weights_only=True),
#     tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=3),
#     tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=3, verbose=0)
# ]
# print(f"开始训练")
# history = student_model.fit(
#     train_data_generator,
#     epochs=EPOCHS,
#     validation_data=test_data_generator,
#     callbacks=callbacks
# )
#
# student_model.bert.save_pretrained(SAVE_MODEL_PATH)
# student_config.save_pretrained(SAVE_MODEL_PATH)
# student_tokenizer.save_pretrained(SAVE_MODEL_PATH)
#
# print("wait")
