
import math
import random
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
import numpy as np
from tensorflow import keras


class DataGenerator(keras.utils.Sequence):
    def __init__(self, docs,labels, tokenizer, batch_size, max_length):
        self.docs = docs
        self.labels = labels
        self.features = []
        for doc, label in zip(docs,labels):
            self.features.append((doc, label))
        self.tokenizer = tokenizer
        self.batch_size = min(len(self.features), batch_size)
        self.max_length = max_length
        self.num_batches = math.ceil(len(self.features) / self.batch_size)

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return self.num_batches

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        start = index * self.batch_size
        end = min((index+1)*self.batch_size, len(self.features))
        X, Y = self.__data_generation(self.features[start:end])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        # random.shuffle(self.features)
        pass

    def __data_generation(self, features):
        """
        生成一个 batch 的数据。
        """
        final_label = []
        final_inputs = []
        final_inputs_dict = {}
        for i, (doc, label) in enumerate(features):
            inputs = self.tokenizer.batch_encode_plus(doc,return_tensors='tf',max_length=self.max_length,truncation=True, padding="max_length")
            final_inputs.append(inputs)
            final_label.append(label)
        final_inputs_dict['input_ids'] = np.concatenate([i['input_ids'] for i in final_inputs],axis=0)
        final_inputs_dict['token_type_ids'] = np.concatenate([i['token_type_ids'] for i in final_inputs],axis=0)
        final_inputs_dict['attention_mask'] = np.concatenate([i['attention_mask'] for i in final_inputs],axis=0)
        final_label = np.array(final_label)
        return (
            final_inputs_dict,
            final_label
        )

if __name__=='__main__':
    from transformers import BertTokenizer
    tokenizer = BertTokenizer.from_pretrained(r'D:\develop\aicc-nlp-nlu\pretrain_model\roberta_chinese_clue_tiny_anto')
    data = DataGenerator([['你好','啊啊']]*128,[[0,1]]*128,tokenizer,32,128)
    get = data.__getitem__(0)
    print(get[0]['attention_mask'].shape)
    print(get[0]['input_ids'].shape)