import itertools
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
from tqdm import tqdm
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)


from model.BertEmbeddingFitLSTM.BertEmbeddingFitLSTM import BertEmbeddingF<PERSON>LSTM
from module.DocLevelClassification.DataGenerator import DataGenerator
import numpy as np


from module.DocLevelClassification.metrics import custom_f1

class DocLevelClassification:
        def __init__(self,docs,labels,label_unique,is_train=True):
            self.docs = docs
            self.labels = labels
            self.label_unique = label_unique
            self.mask_label = max(self.label_unique) + 1
            self.label_unique.append(self.mask_label)
            self.model = BertEmbeddingFitLSTM(len(self.label_unique))
            if is_train:
                self.train_data, self.vaild_data = self.get_data_generator(self.docs, self.labels)
            self.all_data = self.get_data_generator(self.docs, self.labels, False)

        def train(self):
            optimizer = tf.keras.optimizers.Adam(learning_rate=8e-5, epsilon=1e-08)
            self.model.compile(loss=self.loss_func, metrics=[self.F1_score,self.masked_accuracy], optimizer=optimizer)  #optimizer adam

            model_save_path = os.path.join(self.model.__class__.__name__ , 'tf_model.h5')
            os.makedirs(self.model.__class__.__name__ ,exist_ok=True)
            earlystop = tf.keras.callbacks.EarlyStopping(monitor='val_F1_score',patience=5,mode='max')
            checkpoint = tf.keras.callbacks.ModelCheckpoint(filepath=model_save_path,
                                                        monitor='val_F1_score', mode='max' ,save_best_only=True,save_weights_only=True)
            callback = [earlystop,checkpoint]

            history = self.model.fit(self.train_data,epochs=50,
                      validation_data=self.vaild_data,
                      callbacks=callback,workers=30,use_multiprocessing = False) #batch_size=BATCH_SIZE
            self.model.built = True
            self.model.load_weights(model_save_path)
            score = np.max(history.history["val_masked_accuracy"])
            return score

        def masked_accuracy(self, label, predict):
            mask = tf.math.logical_not(tf.math.equal(label, self.mask_label))
            y_pred = tf.math.argmax(predict, axis=-1)
            y_true = tf.cast(label, dtype=y_pred.dtype)
            acc = tf.math.equal(y_true, y_pred)
            acc = tf.cast(acc, dtype=tf.float32)
            mask = tf.cast(mask, dtype=acc.dtype)
            mask_count = tf.reduce_sum(mask)
            acc *= mask
            acc_count = tf.reduce_sum(acc)
            return acc_count / mask_count

        def F1_score(self,label, predict):
            # label = label.reshape(-1, 1)
            # predict = predict.reshape(-1, 1)
            predict = tf.math.argmax(predict, axis=-1)
            label = tf.reshape(label,(-1, 1))
            predict = tf.reshape(predict,(-1, 1))
            index = tf.where(label < self.mask_label)
            # 去掉要mask的地方
            all_f1 = []
            for i in range(self.mask_label):
                y_true = tf.gather_nd(label, index)
                y_pred = tf.gather_nd(predict, index)
                y_true = tf.where(y_true == i, y_true, 0)
                y_true = tf.where(y_true != i, y_true, 1)

                y_pred = tf.where(y_pred == i, y_pred, 0)
                y_pred = tf.where(y_pred != i, y_pred, 1)
                f1_score = custom_f1(y_true, y_pred)
                # if tf.math.reduce_sum(tf.cast(y_true, dtype=tf.int32))==float(0):
                #     f1_score = 1
                all_f1.append(f1_score)
            all_f1 = tf.stack(all_f1)
            all_f1 = all_f1.mean()
            # all_f1 = tf.stack(all_f1).shape[0]
            return all_f1

        def loss_func(self, label, predict):
            loss_object = tf.keras.losses.SparseCategoricalCrossentropy(
                # from_logits=True,  # 未经过softmax的结果
                reduction='none')  # 禁止求和计算，这一步在自定义的loss中做

            mask = tf.math.logical_not(tf.math.equal(label, self.mask_label))  # 将y_true 中所有为0的找出来，标记为False
            loss_ = loss_object(label, predict)
            mask = tf.cast(mask, dtype=loss_.dtype)  # 将前面统计的是否零转换成1，0的矩阵
            loss_ *= mask  # 将正常计算的loss加上mask的权重，就剔除了padding 0的影响
            return tf.reduce_sum(loss_) / tf.reduce_sum(mask)  # 最后将loss求平均

        def get_data_generator(self,docs,labels,train=True):
            """
            :param docs:
            :param labels:
            :param doc_max_len:
            :return:
            """
            use_docs,use_labels = [],[]
            for index,doc,label in zip(range(len(docs)),docs,labels):
                doc = (doc+['000']*self.model.doc_max_len)[:self.model.doc_max_len]
                label = (label+[self.mask_label]*self.model.doc_max_len)[:self.model.doc_max_len]
                use_docs.append(doc)
                use_labels.append(label)
            if train:
                assert len(docs) == len(labels)
                from sklearn.model_selection import train_test_split
                train_docs,test_docs,train_labels,test_labels = train_test_split(use_docs,use_labels,test_size=0.2, random_state=2023)
                data_gen_train = DataGenerator(docs=train_docs,labels=train_labels ,
                                               tokenizer=self.model.tokenizer,max_length=self.model.sentence_max_len,batch_size=16)
                data_gen_valid = DataGenerator(docs=test_docs,labels=test_labels,
                                               tokenizer=self.model.tokenizer,max_length=self.model.sentence_max_len,batch_size=64)
                return data_gen_train,data_gen_valid
            else:
                all_data = DataGenerator(docs=use_docs,labels=use_labels,
                                         tokenizer=self.model.tokenizer,max_length=self.model.sentence_max_len,batch_size=64)
                return all_data

        def load_model(self,path):
            optimizer = tf.keras.optimizers.Adam(learning_rate=8e-5, epsilon=1e-08)
            self.model.compile(loss=self.loss_func, metrics=[self.masked_accuracy], optimizer=optimizer)  #optimizer adam
            self.model.built = True
            model_save_path = os.path.join(path, 'tf_model.h5')
            # model_save_path = os.path.join(self.model.__class__.__name__, 'tf_model.h5')
            self.model.load_weights(model_save_path)


if __name__=='__main__':
    import pandas as pd
    import re
    train_data_name = '5W_train.csv'
    if not os.path.exists(train_data_name):
        df = pd.read_csv(r"../../../data/wiki/wiki.csv")
        df.columns = ['text', 'label']
        df['text'] = df['text'].apply(lambda x: re.sub(' |=|\n|】|【', '', x))
        df.loc[df['label'] == 1, 'filename'] = df.loc[df['label'] == 1, 'text']
        df['filename'] = df['filename'].fillna(method='pad')
        df = df[df['text'] != '']
        df = df[df['label'] != 6]
        # df.loc[df[df['label']>2].index,'label'] = 0

        # use_filename = list(set(df['filename'].tolist()))[0:10000*30]
        # df.index = df['filename'].tolist()
        # df = df.loc[use_filename]

        split_mark = '||$$||'
        df['text'] = df['text'].map(str) + split_mark
        df['label'] = df['label'].map(str) + split_mark
        check = df.groupby('filename').sum()
        # check['len'] = check['text'].apply(lambda x:len(x.split(split_mark)))

        docs = [[i for i in j.split(split_mark) if i] for j in check['text']]
        labels = [[i for i in j.split(split_mark) if i] for j in check['label']]
        for i in range(len(docs)):
            assert len(docs[i])==len(labels[i])

        rate = [(pd.Series(i).value_counts() / len(i)).to_dict() for i in labels]
        rate_df = pd.DataFrame(rate).fillna(0)
        check.index = range(len(check))
        check = pd.concat([check, rate_df], axis=1)
        check = check[(check['4'] == 0)&(check['5'] == 0)]
        # check = check[check['0']>0.7]

        docs = [[i for i in j.split(split_mark) if i] for j in check['text']]
        labels = [[i for i in j.split(split_mark) if i] for j in check['label']]
        for i in range(len(docs)):
            assert len(docs[i])==len(labels[i])


        pd.DataFrame({'docs':docs,'labels':labels}).to_csv(train_data_name,encoding='utf-8-sig')
    else:
        train = pd.read_csv(train_data_name)
        docs,labels = train['docs'].tolist(),train['labels'].tolist()
        docs = [eval(i) for i in docs]
        labels = [eval(i) for i in labels]

    labels = [[int(i) for i in j] for j in labels]
    unique_label = list(set(list(itertools.chain.from_iterable(labels))))
    unique_label = [int(i) for i in unique_label]
    print('数据处理完成,开始训练!')
    unique_label += [5]
    model = DocLevelClassification(docs,labels,unique_label[:6],True)
    # score = model.train()
    # model.load_model(model.model.__class__.__name__)
    model.load_model('best_lstm_tiny_5Wdata')
    len_count = pd.Series([len(i) for i in docs])

    # labels[10856][0:10]
    # docs[10856][0:10]


    # labels = [[int(i) for i in j if j != 0] for j in labels]
    # c = [index for index,i in enumerate(labels) if '2, 4' in str(i)]
    # print(len(c))

    # len([i for i in labels if 2 in i])
    # len([i for i in labels if 3 in i])
    # len([i for i in labels if 4 in i])
    # len([i for i in labels if 5 in i])


    #检查
    # check_data = model.vaild_data
    # output = model.model.predict(check_data).argmax(-1)
    # df = pd.DataFrame(check_data.features,columns = ['text','label'])
    # df['predict'] = output.tolist()
    # check = []
    # for i in range(len(df)):
    #     sec = df.iloc[i].to_dict()
    #     sec = pd.DataFrame(sec)
    #     sec['filename'] = i
    #     check.append(sec)
    # check = pd.concat(check)
    # check = check[check['label']!=model.mask_label]
    # acc = check[check['label']==check['predict']].shape[0]/check.shape[0]
    # from sklearn.metrics import classification_report
    # score_info = classification_report(y_true=check['label'].tolist(), y_pred=check['predict'].tolist(), output_dict=True)
    # from pprint import pprint
    # pprint(score_info)
    #
    # error = check[check['label']!=check['predict']]
    # error['filename'].value_counts()
    # error = check[check['filename']==2548]



    # #test
    df = pd.read_excel(r"../../../data/test.xlsx",index_col=0)
    df['filename'] = df['filename'].fillna(method='pad')
    df = df[df['predict']!=-1]
    # df['predict'] = df['predict'].map({3:0,2:1,0:0,1:1})
    df = df[df['no_replace_string'].notnull()]
    docs = []
    labels = []
    for filename in df['filename'].unique():
        sec = df[df['filename']==filename]
        doc = [i.replace(':','').replace('：','') for i in sec['raw_string'].tolist()]
        label = sec['predict'].tolist()
        docs.append(doc)
        labels.append(label)
    check_data = DocLevelClassification(docs,labels,[0,1],False).all_data
    output = model.model.predict(check_data).argmax(-1)
    df = pd.DataFrame(check_data.features, columns=['text', 'label'])
    df['predict'] = output.tolist()
    check = []
    for i in range(len(df)):
        sec = df.iloc[i].to_dict()
        sec = pd.DataFrame(sec)
        sec['filename'] = i
        check.append(sec)
    check = pd.concat(check)
    check = check[check['text'] != '000']
    acc = check[check['label'] == check['predict']].shape[0] / check.shape[0]
    from sklearn.metrics import classification_report
    score_info = classification_report(y_true=check['label'].tolist(), y_pred=check['predict'].tolist(),output_dict=True)
    from pprint import pprint
    pprint(score_info)