import os
import pandas as pd
from tqdm import tqdm
from transformers import <PERSON><PERSON><PERSON><PERSON><PERSON>,Bert<PERSON>oken<PERSON>,TFBertForSequenceClassification

class Only_classification:
    def __init__(self,model_path):
        # self.model = SeqClassification()
        self.config = BertConfig.from_pretrained(model_path)
        self.config.num_labels = 2
        self.model = TFBertForSequenceClassification.from_pretrained(model_path,config=self.config)
        self.tokenizer = BertTokenizer.from_pretrained(model_path)

    def predict(self,questions,output_file=False):
        preds = []
        for index in tqdm(range(0,len(questions),64)):
            input_data = self.tokenizer.batch_encode_plus(questions[index:index+64],return_tensors='tf',max_length=64,truncation=True,padding=True)
            pred = self.model(**input_data)[0].numpy().argmax(-1).tolist()
            preds.extend(pred)
        return preds


if __name__=='__main__':
    from torch.utils.data import DataLoader
    predictor = Only_classification(r'D:\develop\knowlege_extract_docx\model\model_save')
    df = pd.read_csv(r'data/zhidao.csv').iloc[0:100,:]
    question = df['question'].drop_duplicates().tolist()
    answer = df['answer'].drop_duplicates().tolist()

    question_output = predictor.predict(question)
    answer_output = predictor.predict(answer)
    q_df = pd.DataFrame({'x':question,'y':question_output})
    a_df = pd.DataFrame({'x':answer,'y':answer_output})
    concat = pd.concat([q_df,a_df])
