import tensorflow as tf
import tensorflow.keras.backend as K

def custom_f1(y_true, y_pred):
    def recall_m(y_true, y_pred):
        TP = K.sum(K.round(K.clip(y_true * y_pred, 0, 1)))
        Positives = K.sum(K.round(K.clip(y_true, 0, 1)))

        recall = TP / (Positives+K.epsilon())
        return recall


    def precision_m(y_true, y_pred):
        TP = K.sum(K.round(K.clip(y_true * y_pred, 0, 1)))
        Pred_Positives = K.sum(K.round(K.clip(y_pred, 0, 1)))

        precision = TP / (Pred_Positives+K.epsilon())
        return precision

    precision, recall = precision_m(y_true, y_pred), recall_m(y_true, y_pred)

    return 2*((precision*recall)/(precision+recall+K.epsilon()))


if __name__=='__main__':
    import tensorflow as tf
    from tensorflow.python.ops.numpy_ops import np_config
    np_config.enable_numpy_behavior()
    mask_label = 4
    label = tf.convert_to_tensor([[0, 2, 1, 2, 4, 4],[0, 1, 2, 2, 4, 4]])
    predict = tf.convert_to_tensor([[0, 1, 4, 4, 4, 4],[0, 1, 2, 2, 4, 4]])
    label = label.reshape(-1, 1)
    predict = predict.reshape(-1, 1)
    index = tf.where(label < mask_label)
    #去掉要mask的地方
    all_f1 = []
    for i in range(mask_label):
        y_true = tf.gather_nd(label, index)
        y_true = tf.where(y_true == i, y_true, 0)
        y_true = tf.where(y_true != i, y_true, 1)

        y_pred = tf.gather_nd(predict, index)
        y_pred = tf.where(y_pred == i, y_pred, 0)
        y_pred = tf.where(y_pred != i, y_pred, 1)
        f1_score = custom_f1(y_true,y_pred)
        all_f1.append(f1_score)
    all_f1 = tf.stack(all_f1).mean()


