import json
import os
import random
import time
import numpy as np
import <PERSON><PERSON><PERSON><PERSON>
import setting
from model.CallLLM.CallOpenAI import Call<PERSON>penA<PERSON>
from model.CallLLM.CallOurModel import CallOurModel
from database.REDIS import REDIS
from setting import logger
from utils.my_utils import <PERSON><PERSON><PERSON><PERSON>, check_model_status
import re
from module.Exam.ComposeExam.PointAnalysisPrompt import default_point_analysis_prompt, default_question_generate_prompt
from urllib.parse import unquote
from module.Exam.ComposeExam.ComposeExamPrompt import default_extract_qa_prompt, default_check_qa_prompt, \
    default_extract_qa_option_prompt, default_assert_func_extract_qa, default_assert_func_check_qa, get_extract_qa_result, default_assert_func_extract_point_our, default_assert_func_gen_sim_question

class ComposeExam:
    def __init__(self):
        if setting.EXAM_MODEL == "gpt3.5":
            self.call_llm_model = CallOpenAI(use_azure=True)
        elif setting.EXAM_MODEL == "gpt4":
            self.call_llm_model = CallOpenAI(use_azure=False, model_name="gpt-4-0613")
        else:
            self.call_llm_model = CallOurModel(url=setting.OUR_MODEL_URL)
        self.redis = REDIS()

        # prompt
        self.extract_qa_prompt = default_extract_qa_prompt
        self.extract_qa_option_prompt = default_extract_qa_option_prompt
        self.check_qa_prompt = default_check_qa_prompt
        self.assert_func_extract_qa = default_assert_func_extract_qa
        self.assert_func_extract_point = default_assert_func_extract_point_our
        self.assert_func_gen_sim_question = default_assert_func_gen_sim_question
        self.assert_func_check_qa = default_assert_func_check_qa

        # 0-单选 1-多选 2-判断 3-填空 4-简答
        self.q_type = {
            "2": ["有判断题，必须使用陈述句", {"题型": "判断题", "难度": "简单", "题目": "", "答案": "正确/错误", "答案解析": ""}],
            "3": ["有填空题（用“_____”），如果咨询请求 是填空题记得一定要用“_____”符号表示要填空的关键内容和知识点，其它题型不要这个符号",
                    {"题型": "填空题", "难度": "困难", "题目": "", "答案": ["", ""], "答案解析": ""}],
            "1": ["有多选题，必须使用陈述句",
                    {"题型": "多选题", "难度": "中等", "题目": "", "选项": ["A. XXX", "B. XXX", "C. XXX", "D. XXX"], "答案": ["", ""],
                     "答案解析": ""}],
            "4": ["有简答题，必须使用陈述句", {"题型": "简答题", "难度": "中等", "题目": "", "答案": "", "答案解析": ""}],
            "0": ["有单选题，必须使用陈述句",
                    {"题型": "单选题", "难度": "中等", "题目": "", "选项": ["A. XXX", "B. XXX", "C. XXX", "D. XXX"], "答案": "",
                     "答案解析": ""}],
        }

    def train(self, model_id, filenames, contents, save_dir, need_check, prompt_data_key_list=None, question_types=[], other_model_id="", other_model_type="", enterprise_id=""):
        code = 0
        msg = "成功"
        start_time = time.time()
        min_seg_len = 500  # 200 500
        max_seg_len = 1000  # 500 1000
        seg_span_len = 300  # 100 300
        extract_question_dict = {}

        other_model_dict = {
            "other_model_id": other_model_id,
            "other_model_type": other_model_type,
            "model_id": model_id,
            "enterprise_id": enterprise_id
        }

        try:
            os.makedirs(save_dir, exist_ok=True)
            if not prompt_data_key_list:
                prompt_data_key_list = [""] * len(filenames)
            for i in range(len(filenames)):
                filename = filenames[i]
                file_extension = filename.split(".")[-1]  # 提取文件名后缀
                filename = unquote(filename.split("/")[-1].split(".")[0])[:51]
                try:
                    if len(prompt_data_key_list[i].strip()):
                        prompt_dict = self.redis.get_data(prompt_data_key_list[i])
                        assert isinstance(prompt_dict, dict)
                    else:
                        prompt_dict = {}
                except Exception as ee:
                    prompt_dict = {}
                    logger.warning(f"读取prompt_data失败,使用默认值:{ee}")
                # if len(prompt_data_key_list[i].strip()):
                #     prompt_dict = self.redis.get_data(prompt_data_key_list[i])
                # else:
                #     prompt_dict = {}
                logger.warning(prompt_dict)
                extract_qa_prompt = prompt_dict.get("extract_qa_prompt", "").strip()
                extract_qa_option = prompt_dict.get("extract_qa_option", "").strip()
                check_qa_prompt = prompt_dict.get("check_qa_prompt", "").strip()
                # 一问一答，答案要点
                p_extract_qa_prompt = prompt_dict.get("p_extract_qa_prompt", "").strip()  # 抽一问一答 promt
                extract_point_prompt = prompt_dict.get("extract_point_prompt", "").strip()  # 考点拆分考点总结 promt
                check_point_prompt = prompt_dict.get("check_point_prompt", "").strip()  # 删除一问一答错题prompt
                question_generate_prompt = prompt_dict.get("question_generate_prompt", "").strip()  # 相似问 prompt

                if "p" in question_types:
                    self.extract_qa_prompt = default_extract_qa_prompt if len(p_extract_qa_prompt) == 0 else p_extract_qa_prompt
                    self.check_qa_prompt = default_check_qa_prompt if len(check_qa_prompt) == 0 else check_qa_prompt
                    self.point_analysis_prompt = default_point_analysis_prompt if len(extract_point_prompt) == 0 else extract_point_prompt
                    self.question_generate_prompt = default_question_generate_prompt if len(question_generate_prompt) == 0 else question_generate_prompt
                else:
                    self.extract_qa_prompt = default_extract_qa_prompt if len(extract_qa_prompt) == 0 else extract_qa_prompt
                    self.check_qa_prompt = default_check_qa_prompt if len(check_point_prompt) == 0 else check_point_prompt
                self.extract_qa_option_prompt = default_extract_qa_option_prompt

                extract_question_dict[filename] = []
                content = contents[i]

                # content里面的内容如果太长则分段
                new_content = []
                for c in content:
                    while len(c) >= seg_span_len:
                        idx = c.find("\n", seg_span_len)
                        if idx == -1:
                            idx = seg_span_len
                        new_content.append(c[:idx].strip())
                        c = c[idx:]
                    if len(c):
                        new_content.append(c.strip())
                content = new_content
                        
                all_idx_begin = []
                total_length = 0
                for t in content:
                    total_length += len(t)
                    if len(all_idx_begin):
                        all_idx_begin.append(all_idx_begin[-1] + len(t))
                    else:
                        all_idx_begin.append(len(t))
                all_idx_begin = [t // seg_span_len for t in all_idx_begin]

                seg_begin_idx_list = []
                for ii in range(len(all_idx_begin)):
                    if ii == 0:
                        seg_begin_idx_list.append(ii)
                    elif all_idx_begin[ii] != all_idx_begin[ii - 1]:
                        seg_begin_idx_list.append(ii)

                all_segs = []
                for start_idx in seg_begin_idx_list:
                    find_text = False
                    for end_idx in range(start_idx + 1, len(content) + 1):
                        seg = "\n".join(content[start_idx:end_idx])
                        if max_seg_len >= len(seg) >= min_seg_len:
                            all_segs.append(seg)
                            find_text = True
                            break
                        elif len(seg) > max_seg_len:
                            all_segs.append("\n".join(content[start_idx:max(start_idx+1, end_idx-1)]))
                            find_text = True
                            break
                    if not find_text:
                        new_start_idx = start_idx
                        while new_start_idx > 0 and len("\n".join(content[new_start_idx:])) < min_seg_len:
                            new_start_idx -= 1
                        all_segs.append("\n".join(content[new_start_idx:]))
                if len([s[:max_seg_len] for s in all_segs if len(s) >= 100]):
                    all_segs = [s[:max_seg_len] for s in all_segs if len(s) >= 100]
                else:
                    all_segs = [s[:max_seg_len] for s in all_segs if len(s) >= 1]
                xlsx_head = ""

                for j, seg in enumerate(all_segs):
                    if not check_model_status(model_id):
                        return 1, "模型已停止"
                    page_start_time = time.time()
                    if file_extension == "xlsx":  # 每页增加表头
                        if j == 0:
                            xlsx_head = seg.split("\n")[0] + "\n"
                        else:
                            seg = xlsx_head + seg
                    logger.debug(f"{model_id}抽取文档{i + 1}/{len(filenames)},{j + 1}/{len(all_segs)},{filename},{seg}")
                    option_prompt_str = self.extract_qa_option_prompt.replace("@option@", extract_qa_option) if len(extract_qa_option) else ""
                    final_user_prompt = self.extract_qa_prompt.replace("@seg_doc@", seg).replace("@option_prompt@", option_prompt_str)
                    final_user_prompt = final_user_prompt.replace("@doc_name@", filename)  # 文档名替换
                    # 题型选择
                    question_types_1 = ""
                    question_types_2 = ""
                    if question_types:
                        for question in question_types:
                            if question == "p":
                                continue
                            question_types_1 += "," + self.q_type[question][0] if question_types_1 else \
                            self.q_type[question][0]
                            question_types_2 += ",\n" + json.dumps(self.q_type[question][1],
                                                                   ensure_ascii=False) if question_types_2 else json.dumps(
                                self.q_type[question][1], ensure_ascii=False)

                    else:
                        question_types_1 = "多样（如填空题（用“_____”）、单选题、多选题、问答题、判断题（必须使用陈述句）），如果咨询请求 是填空题记得一定要用“_____”符号表示要填空的关键内容和知识点，其它题型不要这个符号"
                        question_types_2 = ",\n".join([json.dumps(v[1], ensure_ascii=False) for v in self.q_type.values()])

                    final_user_prompt = final_user_prompt.replace("@question_types_1@", question_types_1).replace("@question_types_2@", question_types_2)
                    res = self.call_llm_model.run(user_prompt=final_user_prompt, retry_times=3, assert_func=self.assert_func_extract_qa, other_model_dict=other_model_dict)
                    res_list = get_extract_qa_result(res)

                    # 新增简答题答案要点
                    if "p" in question_types:
                        logger.warning(res_list)
                        for r_d in res_list:
                            if r_d.get('题型') == '简答题':
                                answer = "".join(r_d.get("答案", ""))
                                question = "".join(r_d.get("题目", ""))
                                answer_prompt = self.point_analysis_prompt.replace("@answer@", answer)
                                point_res = self.call_llm_model.run(user_prompt=answer_prompt, retry_times=3, assert_func=self.assert_func_extract_point, other_model_dict=other_model_dict)
                                question_prompt = self.question_generate_prompt.replace("@question@", question)
                                question_res = self.call_llm_model.run(user_prompt=question_prompt, retry_times=3, assert_func=self.assert_func_gen_sim_question, other_model_dict=other_model_dict)
                                if isinstance(question_res, list):
                                    question_res = question_res[0]
                                if isinstance(point_res, list):
                                    point_res = point_res[0]
                                point_res_list = get_extract_qa_result(point_res)
                                question_res_list = get_extract_qa_result(question_res)
                                logger.debug(f"model_id:{model_id},point_res_list:{point_res_list}")
                                logger.debug(f"model_id:{model_id},question_res_list:{question_res_list}")

                                try:
                                    if isinstance(question_res_list, list):
                                        question_res_list = question_res_list[0]
                                    question_res_list = question_res_list["questions"]
                                    assert isinstance(question_res_list, list)
                                except:
                                    # 兼容报错的情况
                                    question_res_list = []
                                try:
                                    if isinstance(point_res_list, list):
                                        point_res_list = point_res_list[0]
                                    split_answer_list = point_res_list["split"]
                                    assert isinstance(split_answer_list, list)
                                    split_point_list = point_res_list["point"]
                                    assert isinstance(split_point_list, list)
                                    assert len(split_answer_list) == len(split_point_list) and len(split_answer_list) > 0
                                except:
                                    # 兼容报错的情况
                                    split_answer_list = [answer]
                                    split_point_list = [answer]

                                r_d["相似题目"] = question_res_list
                                r_d["相似题目"].append(question)
                                r_d["question_prompt"] = question_prompt
                                r_d["question_res"] = question_res
                                r_d["答案"] = [s_.replace("||", "") for s_ in split_answer_list if s_]
                                r_d["答案要点"] = [s_.replace("||", "")[:10] for s_ in split_point_list if s_]
                                r_d["point_prompt"] = answer_prompt
                                r_d["point_res"] = point_res
                    if not isinstance(res_list, list):
                        logger.warning(f"抽题返回结果有问题:@@@{res}@@@")
                        continue
                    for r_d in res_list:
                        extract_question_dict[filename].append({
                            "题型": r_d.get("题型", []),
                            "难度": r_d.get("难度", []),
                            "题目": r_d.get("题目", []),
                            "相似题目": r_d.get("相似题目", []),
                            "选项": r_d.get("选项", ""),
                            "答案": r_d.get("答案", ""),
                            "答案要点": r_d.get("答案要点", ""),
                            "答案解析": r_d.get("答案解析", ""),
                            "文档": seg,
                            "prompt": final_user_prompt,
                            "question_prompt": r_d.get("question_prompt", ""),
                            "point_prompt": r_d.get("point_prompt", ""),
                            "question_res": r_d.get("question_res", ""),
                            "point_res": r_d.get("point_res", ""),
                            "llm_output": res
                        })

                    # 实时更新训练时间
                    page_end_time = time.time() - page_start_time
                    train_status = eval(self.redis.get_data(key=f"train_state_{model_id}", need_json=False))
                    # 预估时间
                    except_time = int(page_end_time * (len(all_segs) - j - 1))
                    train_status["task_time"] = train_status["total_time"] = min(except_time, train_status["task_time"])
                    self.redis.set_data(f"train_state_{model_id}", str(train_status), need_json=False)

            with open(os.path.join(save_dir, 'extract_question_dict_ori.json'), 'w', encoding='utf-8') as f:
                json.dump(extract_question_dict, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)

            extract_question_dict = self.remove_error_qa(extract_question_dict, need_check, question_types, other_model_dict)
            extract_question_dict = self.extact_repeat(extract_question_dict)

            #
            if "p" in question_types:
                for questions in extract_question_dict.values():
                    for question in questions:
                        question["答案"] = question["答案"].split("\n") if isinstance(question["答案"], str) else question["答案"]

            # 答案限定500字以内
            extract_question_dict = self.character_limits(extract_question_dict)

            with open(os.path.join(save_dir, 'extract_question_dict.json'), 'w', encoding='utf-8') as f:
                json.dump(extract_question_dict, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)
            # if need_check:
            #     extract_question_dict = self.remove_error_qa(extract_question_dict)
            #     extract_question_dict = self.extact_repeat(extract_question_dict)
            #     with open(os.path.join(save_dir, 'extract_question_dict.json'), 'w', encoding='utf-8') as f:
            #         json.dump(extract_question_dict, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)
            # else:
            #     with open(os.path.join(save_dir, 'extract_question_dict.json'), 'w', encoding='utf-8') as f:
            #         json.dump(extract_question_dict, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)

        except Exception as e:
            logger.error(f'抽题报错,model_id: {model_id},{e}', exc_info=True)
            code = 1
            msg = f'抽题报错:{e}'
        return code, msg

    def clean_cloze_question(self, data):
        re_result = re.findall("(_+)", data["题目"])
        if len(re_result) == 0:
            return data, True
        if not isinstance(data["答案"], list):
            data["答案"] = [data["答案"]]
        if len(re_result) == len(data["答案"]):
            return data, False
        if len(re_result) == 1:
            data["答案"] = [",".join(data["答案"])]
            return data, False
        else:
            return data, True

    def clean_judge_question(self, data):
        replace_dict = {
            "正确": "正确", "t": "正确", "对": "正确", "true": "正确", "真": "正确",
            "错误": "错误", "f": "错误", "错": "错误", "false": "错误", "假": "错误"
        }
        if data["答案"].lower() in replace_dict:
            data["答案"] = replace_dict[data["答案"].lower()]
            return data, False
        else:
            logger.debug(f"判断题答案标准化缺失,{data['答案'].lower()},{replace_dict}")
            return data, True

    def clean_single_choice_question(self, data):
        try:
            # 处理选项
            option_prefix = ["A. ", "B. ", "C. ", "D. "]
            if not isinstance(data["选项"], list):
                return data, True
            answer_in_option_list = []
            option_set = set()
            for i, option in enumerate(data["选项"]):
                res = re.findall(r"^([ABCDabcd]{1}[\.]{0,1}[\ ]{0,1})", option)
                assert len(res) <= 1
                if len(res) == 1:
                    data["选项"][i] = option_prefix[i] + option[len(res[0]):]
                    option_set.add(option[len(res[0]):])
                else:
                    data["选项"][i] = option_prefix[i] + option
                    option_set.add(option)
                if data["答案"] in data["选项"][i]:
                    answer_in_option_list.append("ABCD"[i])

            # 处理答案
            answer = [c for c in "ABCD" if c in data["答案"].upper()]

            if len(option_set) != len(data["选项"]):
                # 如果选项有重复，返回错误
                return data, True
            if len(answer) == 1:
                data["答案"] = answer[0]
            else:
                if len(answer_in_option_list) == 1:
                    data["答案"] = answer_in_option_list[0]
                else:
                    return data, True
            return data, False
        except Exception as e:
            logger.debug(f"单选题目错误\n{data}\n报错:{e}")
            return data, True

    def clean_multi_choice_question(self, data):
        try:
            # 处理选项
            option_prefix = ["A. ", "B. ", "C. ", "D. ", "E. ", "F. ", "G. ", "H. "]
            if not isinstance(data["选项"], list):
                return data, True
            option_set = set()
            for i, option in enumerate(data["选项"]):
                res = re.findall(r"^([ABCDEFGHabcdefgh]{1}[\.]{0,1}[\ ]{0,1})", option)
                assert len(res) <= 1
                if len(res) == 1:
                    data["选项"][i] = option_prefix[i] + option[len(res[0]):]
                    option_set.add(option[len(res[0]):])
                else:
                    data["选项"][i] = option_prefix[i] + option
                    option_set.add(option)

            # 处理答案
            if isinstance(data["答案"], list):
                data["答案"] = "".join(data["答案"]).upper()
                answer = [c for c in "ABCDEFGH"[:len(data["选项"])] if c in data["答案"]]
            else:
                answer = [c for c in "ABCDEFGH"[:len(data["选项"])] if c in data["答案"].upper()]

            if len(option_set) != len(data["选项"]):
                # 如果选项有重复，返回错误
                return data, True
            if len(answer) <= 1:
                return data, True
            else:
                data["答案"] = "".join(answer)
            return data, False
        except Exception as e:
            logger.debug(f"多选题目错误\n{data}\n报错:{e}")
            return data, True

    def remove_error_qa(self, data, need_check, question_types, other_model_dict={}):
        error_num = 0
        model_id = other_model_dict.get("model_id", "")
        for i, filename in enumerate(data.keys()):
            for j, d in enumerate(data[filename]):
                if not check_model_status(model_id):
                    logger.error(f"[{model_id}] 模型已停止")
                    return data
                logger.debug(f"检查文档{i+1}/{len(data.keys())},{j+1}/{len(data[filename])},{filename}")
                try:
                    # 题型编号 0-单选 1-多选 2-判断 3-填空 4-简答
                    if d["题型"] == "填空题":
                        data[filename][j], is_error = self.clean_cloze_question(d)
                        d = data[filename][j]
                        if is_error:
                            d["error_prompt"] = f"规则判断"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [False, d["答案"]]
                        elif "3" not in question_types:
                            d["error_prompt"] = f"不需要的题型"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [False, d["答案"]]
                        elif not need_check:
                            d["error_prompt"] = f"无需检查题目正确性"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [True, d["答案"]]
                        else:
                            question_str = f"填空题\n题目:{d['题目']}"
                            answer_str = f"输入填空题空格___中应该填写的内容"
                            final_prompt = self.check_qa_prompt.replace("@document@", d["文档"]).replace("@question@", question_str).replace("@answer_format@", answer_str)
                            res = self.call_llm_model.run(user_prompt=final_prompt, retry_times=3, assert_func=self.assert_func_check_qa, other_model_dict=other_model_dict)
                            try:
                                d["error_prompt"] = final_prompt
                                d["error_prompt_output"] = res
                                res = json.loads(res.strip())
                                d_answer = ",".join(d["答案"])
                                d["题目正误"] = [
                                    Levenshtein.ratio(res["answer"].strip().lower(), d_answer.lower()) > 0.2,
                                    res["answer"]
                                ]
                            except Exception as t_e:
                                d["error_prompt"] = final_prompt
                                d["error_prompt_output"] = res
                                d["题目正误"] = [True, f"检查报错,默认为True,{t_e}"]
                                logger.error(t_e, exc_info=True)
                    elif d["题型"] == "判断题":
                        data[filename][j], is_error = self.clean_judge_question(d)
                        d = data[filename][j]
                        if is_error:
                            d["error_prompt"] = f"规则判断"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [False, d["答案"]]
                        elif "2" not in question_types:
                            d["error_prompt"] = f"不需要的题型"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [False, d["答案"]]
                        elif not need_check:
                            d["error_prompt"] = f"无需检查题目正确性"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [True, d["答案"]]
                        else:
                            judge_dict = {"正确": True, "t": True, "对": True, "true": True, "真": True, "错误": False, "f": False,
                                          "错": False, "false": False, "假": False}
                            question_str = f"判断题\n题目:{d['题目']}"
                            answer_str = f"输入判断题的答案，T or F"
                            final_prompt = self.check_qa_prompt.replace("@document@", d["文档"]).replace("@question@", question_str).\
                                replace("@answer_format@", answer_str)
                            res = self.call_llm_model.run(user_prompt=final_prompt, retry_times=3, assert_func=self.assert_func_check_qa, other_model_dict=other_model_dict)
                            try:
                                d["error_prompt"] = final_prompt
                                d["error_prompt_output"] = res
                                res = json.loads(res.strip())
                                res_answer = "T" if "T" in res["answer"] else "F"
                                if d["答案"].lower() not in judge_dict:
                                    logger.debug(f"判断题答案标准化缺失,{d['答案'].lower()},{judge_dict}")
                                d["题目正误"] = [judge_dict.get(d["答案"].lower(), "") == judge_dict.get(res_answer.lower(), "X"), res["answer"]]
                            except Exception as t_e:
                                d["error_prompt"] = final_prompt
                                d["error_prompt_output"] = res
                                d["题目正误"] = [True, f"检查报错,默认为True,{t_e}"]
                    elif d["题型"] == "单选题":
                        data[filename][j], is_error = self.clean_single_choice_question(d)
                        d = data[filename][j]
                        if is_error:
                            d["error_prompt"] = f"规则判断"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [False, d["答案"]]
                        elif "0" not in question_types:
                            d["error_prompt"] = f"不需要的题型"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [False, d["答案"]]
                        elif not need_check:
                            d["error_prompt"] = f"无需检查题目正确性"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [True, d["答案"]]
                        else:
                            question_str = f"单选题\n题目:{d['题目']}\n选项:\n{d['选项']}"
                            answer_str = f"输入单选题的答案，填A、B、C或D，单选题只有一个答案"
                            final_prompt = self.check_qa_prompt.replace("@document@", d["文档"]).replace("@question@", question_str).\
                                replace("@answer_format@", answer_str)
                            res = self.call_llm_model.run(user_prompt=final_prompt, retry_times=3, assert_func=self.assert_func_check_qa, other_model_dict=other_model_dict)
                            try:
                                d["error_prompt"] = final_prompt
                                d["error_prompt_output"] = res
                                res = json.loads(res.strip())
                                d["题目正误"] = [d["答案"].lower in res["answer"][:2].lower(), res["answer"]]
                            except Exception as t_e:
                                d["error_prompt"] = final_prompt
                                d["error_prompt_output"] = res
                                d["题目正误"] = [True, f"检查报错,默认为True,{t_e}"]
                                logger.error(t_e, exc_info=True)
                    elif d["题型"] == "多选题":
                        data[filename][j], is_error = self.clean_multi_choice_question(d)
                        d = data[filename][j]
                        if is_error:
                            d["error_prompt"] = f"规则判断"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [False, d["答案"]]
                        elif "1" not in question_types:
                            d["error_prompt"] = f"不需要的题型"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [False, d["答案"]]
                        elif not need_check:
                            d["error_prompt"] = f"无需检查题目正确性"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [True, d["答案"]]
                        else:
                            question_str = f"多选题\n题目:{d['题目']}\n选项:\n{d['选项']}"
                            answer_str = f"输入多选题的答案，填ABCD，多选题可以有多个答案，例如AC,BCD"
                            final_prompt = self.check_qa_prompt.replace("@document@", d["文档"]).replace("@question@", question_str).replace(
                                "@answer_format@", answer_str)
                            res = self.call_llm_model.run(user_prompt=final_prompt, retry_times=3, assert_func=self.assert_func_check_qa, other_model_dict=other_model_dict)
                            d["error_prompt"] = final_prompt
                            d["error_prompt_output"] = res
                            res = json.loads(res.strip())
                            try:
                                res_answer_set = set([c.upper() for c in res["answer"] if c.upper() in "ABCDEFGH"])
                                d_answer_set = set([c for c in d["答案"] if c in "ABCDEFGH"])
                                if setting.EXAM_MODEL == "our":
                                    d["题目正误"] = [True, res["answer"]]
                                else:
                                    if len(res_answer_set) != len(d_answer_set) or len(res_answer_set.difference(d_answer_set)):
                                        d["题目正误"] = [False, res["answer"]]
                                    else:
                                        d["题目正误"] = [True, res["answer"]]
                            except Exception as t_e:
                                d["error_prompt"] = final_prompt
                                d["error_prompt_output"] = res
                                d["题目正误"] = [True, f"检查报错,默认为True,{t_e}"]
                    elif d["题型"] == "简答题":
                        question_str = f"简答题\n题目:{d['题目']}\n学生回答:\n{d['答案']}"
                        answer_str = f"判断学生回答是否正确，填T或者F"
                        final_prompt = self.check_qa_prompt.replace("@document@", d["文档"]).replace("@question@", question_str).replace(
                            "@answer_format@", answer_str)
                        if "4" not in question_types or "选项A" in d["答案要点"] or not d["答案"]:
                            d["error_prompt"] = f"不需要的题型"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [False, d["答案"]]
                        elif not need_check:
                            d["error_prompt"] = f"无需检查题目正确性"
                            d["error_prompt_output"] = ""
                            d["题目正误"] = [True, d["答案"]]
                        else:
                            res = self.call_llm_model.run(user_prompt=final_prompt, retry_times=3, assert_func=self.assert_func_check_qa, other_model_dict=other_model_dict)
                            try:
                                d["error_prompt"] = final_prompt
                                d["error_prompt_output"] = res
                                res = json.loads(res.strip())
                                res_answer = "T" if "T" in res["answer"] else "F"
                                # d["题目正误"] = [res_answer == "T", res["answer"]]
                                d["题目正误"] = [True, res["answer"]]
                            except Exception as t_e:
                                d["error_prompt"] = final_prompt
                                d["error_prompt_output"] = res
                                d["题目正误"] = [True, f"检查报错,默认为True,{t_e}"]
                    else:
                        d["error_prompt"] = ""
                        d["error_prompt_output"] = ""
                        d["题目正误"] = [False, f"题型有误:{d['题型']}"]
                        d["题型"] = "简答题"
                    data[filename][j] = d
                except Exception as e:
                    logger.error(f"检查题目报错:{e}")
                    d["error_prompt"] = f"检查题目报错:{e}"
                    d["error_prompt_output"] = ""
                    d["题目正误"] = [True, f"{e}"]
                    data[filename][j] = d
                error_num += 0 if d["题目正误"][0] else 1
            logger.debug(f"抽题错误率:{setting.EXAM_MODEL},错误率:{error_num/max(len(data[filename]), 1)}")
        return data

    def extact_repeat(self, data):
        min_num = 1

        for i, filename in enumerate(data.keys()):
            logger.debug(f"题目去重{i}/{len(data.keys())},{filename}")
            try:
                question_num_dict = {"填空题": 0, "判断题": 0, "单选题": 0, "多选题": 0, "简答题": 0}
                for j, d in enumerate(data[filename]):
                    if d["题型"] == "单选题":
                        d["str"] = d["题目"] + "\n".join(d["选项"])
                    elif d["题型"] == "多选题":
                        d["str"] = d["题目"] + "\n".join(d["选项"])
                    elif d["题型"] == "填空题":
                        d["str"] = d["题目"] + ",".join(d["答案"])
                    elif d["题型"] == "判断题":
                        d["str"] = d["题目"]
                    elif d["题型"] == "简答题":
                        d["str"] = d["题目"] + ",".join(d["答案"])
                    else:
                        raise Exception(f"ERROR 题型:{d['题型']}")
                    question_num_dict[d["题型"]] += 1

                for j, d in enumerate(data[filename]):
                    if d["题目正误"][0]:  # or question_num_dict[d["题型"]] < min_num:
                        d["repeat"] = False
                        d["repeat_score"] = -1
                        d["repeat_str"] = ""
                    else:
                        question_num_dict[d["题型"]] -= 1
                        d["repeat"] = True
                        d["repeat_score"] = 0
                        d["repeat_str"] = ""

                scores = np.zeros(shape=(len(data[filename]), len(data[filename])))
                for m in range(len(data[filename])):
                    for n in range(m + 1, len(data[filename])):
                        if data[filename][m]["repeat"] or data[filename][n]["repeat"]:
                            continue
                        score = Levenshtein.ratio(data[filename][m]["str"], data[filename][n]["str"])
                        scores[m][n] = score
                        scores[n][m] = score

                while True:
                    values = [v for v in question_num_dict.values() if v > min_num]
                    if len(values) == 0:
                        break

                    max_score = np.max(scores)
                    if max_score < 0.6:
                        break
                    idx_list = np.where(scores == max_score)
                    data_idx_1 = idx_list[0][0]
                    data_idx_2 = idx_list[0][1]
                    q_type_1 = data[filename][data_idx_1]["题型"]
                    q_type_2 = data[filename][data_idx_2]["题型"]
                    if question_num_dict[q_type_1] <= min_num and question_num_dict[q_type_2] <= min_num:
                        if max_score > 0.9:
                            # 过于相似，依旧要删
                            if random.random() > 0.5:
                                question_num_dict[q_type_1] -= 1
                                data_idx = data_idx_1
                                repeat_str_data_idx = data_idx_2
                            else:
                                question_num_dict[q_type_2] -= 1
                                data_idx = data_idx_2
                                repeat_str_data_idx = data_idx_1
                            data[filename][data_idx]["repeat"] = True
                            data[filename][data_idx]["repeat_score"] = max_score
                            data[filename][data_idx]["repeat_str"] = data[filename][repeat_str_data_idx]["str"]
                            scores[:, data_idx] = 0
                            scores[data_idx, :] = 0
                        else:
                            # 不是过于相似，可以都保留
                            data[filename][data_idx_1]["repeat"] = False
                            data[filename][data_idx_2]["repeat"] = False
                            scores[:, data_idx_1] = 0
                            scores[:, data_idx_2] = 0
                            scores[data_idx_1, :] = 0
                            scores[data_idx_2, :] = 0
                    else:
                        if question_num_dict[q_type_1] > question_num_dict[q_type_2]:
                            question_num_dict[q_type_1] -= 1
                            data_idx = data_idx_1
                            repeat_str_data_idx = data_idx_2
                        else:
                            question_num_dict[q_type_2] -= 1
                            data_idx = data_idx_2
                            repeat_str_data_idx = data_idx_1
                        data[filename][data_idx]["repeat"] = True
                        data[filename][data_idx]["repeat_score"] = max_score
                        data[filename][data_idx]["repeat_str"] = data[filename][repeat_str_data_idx]["str"]
                        scores[:, data_idx] = 0
                        scores[data_idx, :] = 0
            except Exception as e:
                logger.error(f"题目去重报错:{e}", exc_info=True)
        return data

    def query(self, model_id, save_dir):
        query_result = {"code": 0, "msg": "成功", "finish": 0, "single_choice": [], "multi_choice": [],
                        "cloze_question": [], "judge_question": [], "simple_question": []}
        try:
            if not os.path.exists(os.path.join(save_dir, "extract_question_dict.json")):
                return query_result
            with open(os.path.join(save_dir, 'extract_question_dict.json'), 'r', encoding='utf-8') as f:
                extract_question_dict = json.load(f)
                for filename, file_question_list in extract_question_dict.items():
                    for q in file_question_list:
                        if q.get("repeat", False):
                            continue
                        for temp_key in ["prompt", "repeat", "str", "llm_output", "error_prompt", "error_prompt_output", "题目正误"]:
                            if temp_key in q:
                                q.pop(temp_key)
                        if q["题型"] == "单选题":
                            query_result["single_choice"].append(q)
                        elif q["题型"] == "多选题":
                            query_result["multi_choice"].append(q)
                        elif q["题型"] == "填空题":
                            if not isinstance(q["答案"], list):
                                q["答案"] = [q["答案"]]
                            query_result["cloze_question"].append(q)
                        elif q["题型"] == "判断题":
                            query_result["judge_question"].append(q)
                        elif q["题型"] == "简答题":
                            query_result["simple_question"].append(q)
                query_result["finish"] = 1
        except Exception as e:
            query_result["code"] = 1
            query_result["msg"] = f"查询报错:{e}"
        return query_result

    def query_test(self, model_id, save_dir):
        query_result = {"code": 0, "msg": "成功", "finish": 0, "prompt": [], "llm_output": []}
        try:
            if not os.path.exists(os.path.join(save_dir, "extract_question_dict.json")):
                return query_result
            with open(os.path.join(save_dir, 'extract_question_dict.json'), 'r', encoding='utf-8') as f:
                extract_question_dict = json.load(f)
                for filename, file_question_list in extract_question_dict.items():
                    for q in file_question_list:
                        if q["prompt"] in query_result["prompt"] and q["llm_output"] in query_result["llm_output"]:
                            continue
                        query_result["prompt"].append(q["prompt"])
                        query_result["llm_output"].append(q["llm_output"])
                query_result["finish"] = 1
        except Exception as e:
            query_result["code"] = 1
            query_result["msg"] = f"查询报错:{e}"
        return query_result

    def character_limits(self, question_dict):
        for questions in question_dict.values():
            for question in questions:
                if isinstance(question["答案"], str):
                    if len(question["答案"]) > 500:
                        logger.warning(f"答案超过500字str:{question['答案']}")
                        question["答案"] = question["答案"][:500]
                elif isinstance(question["答案"], list):
                    total_word = "-!".join(question["答案"])
                    if len(total_word) > 500:
                        logger.warning(f"答案超过500字list:{question['答案']}")
                        tmp_question = total_word[:500]
                        question["答案"] = tmp_question.split("-!")

                if question.get("答案要点", ""):
                    question["答案要点"] = question["答案要点"][:len(question["答案"])]

        return question_dict


if __name__ == '__main__':
    from utils.filechat_utils import ultra_extract

    doc = ultra_extract(r"/data/cbk/NLP_Model/1732954420331184128/files/12081044_1702003505190.pptx")
    # model_id = "test_exam"
    model = ComposeExam()

    # temp_data = {
    #   "题型": "单选题",
    #   "难度": "简单",
    #   "题目": "猛犸电动车的特点有哪些？",
    #   "选项": [
    #     "A. 可以充电可以换电",
    #     "B. 可以实现手机操控、智能防盗、实时定位等功能",
    #     "C. 搭载汽车级的21700三元锂电池",
    #     "D. 推出买车租电模式"
    #   ],
    #   "答案": "A",
    #   "答案解析": "",
    #   "文档": "猛犸是什么品牌？好像没听过。\n答：我们是对标小牛和九号的新兴互联网品牌。我们公司总部在深圳宝安兴东，\n工厂在江西萍乡。猛犸电动车是定位中高端可以换电的智能电动车，是解决用户\n充电难、充电慢、充电烦现状的产品，我们的换电功能是充电的消费升级，更省\n时、方便、安全。\n<docx.table.table object at 0x7f72ad6ba4d0>\n你们的车有什么特点，比别家的车好在哪里？\n答：（1）可以充电可以换电，5 秒钟换一颗电池，非常方便；\n（2）猛犸电动车是一款智能车，可以实现手机操控、智能防盗、实时定位等功\n能；\n（3）猛犸电动车搭载的是汽车级的 21700 三元锂电池，电池终身质保，不用担 心电池的损耗和折旧；\n（4）推出买车租电模式，相比于整车购买模式，入手门槛更低，先用更低的价\n格购买车架，电池采用租用的方式，按月扣除能源服务费，获得电池使用的权益\n和能源服务的权益。\n什么是换电？电池怎么换？\n答：您的电动车在没电情况下，可以打开猛犸出行手机 app 查看附近换电站，\n导航到换电站点，扫码取出一颗新电池，再把您的旧电池放进换电柜里，换电非\n常方便，5 秒钟可以完成。\n换电站普及吗，在哪里换？",
    #   "prompt": "\"请你从以下提供的文本文档中,生成不同题型的题目,包括填空题、判断题、单选题、多选题和简答题。\\r\\n生成的题目必须与文本文档的内容高度相关并且具有代表性，题目数量可以超过5个，题目应尽可能多并且全面覆盖文档内容。\\r\\n题目可以选择三种难度:简单、中等、困难，请确保难度分配均匀合理。\\r\\n请用以下格式呈现，题目信息保存在dict里，题目保存在list里:\\r\\n[\\r\\n{\\\"题型\\\":\\\"填空题\\\",\\\"难度\\\":\\\"\\\",\\\"题目\\\":\\\"\\\",\\\"答案\\\":\\\"\\\"},\\r\\n{\\\"题型\\\":\\\"判断题\\\",\\\"难度\\\":\\\"\\\",\\\"题目\\\":\\\"\\\",\\\"答案\\\":\\\"\\\", \\\"答案解析\\\": \\\"填答案的详细解析步骤\\\"},\\r\\n{\\\"题型\\\":\\\"单选题\\\",\\\"难度\\\":\\\"\\\",\\\"题目\\\":\\\"\\\",\\\"选项\\\":[\\\"A. XXX\\\", \\\"B. XXX\\\", \\\"C. XXX\\\", \\\"D. XXX\\\"], \\\"答案\\\":\\\"A\\\", \\\"答案解析\\\": \\\"填答案的详细解析步骤\\\"},\\r\\n{\\\"题型\\\":\\\"多选题\\\",\\\"难度\\\":\\\"\\\",\\\"题目\\\":\\\"\\\",\\\"选项\\\":[\\\"A. XXX\\\", \\\"B. XXX\\\", \\\"C. XXX\\\", \\\"D. XXX\\\"], \\\"答案\\\":\\\"AB\\\", \\\"答案解析\\\": \\\"填答案的详细解析步骤\\\"},\\r\\n{\\\"题型\\\":\\\"简答题\\\",\\\"难度\\\":\\\"\\\",\\\"题目\\\":\\\"\\\",\\\"答案\\\":\\\"\\\"}\\r\\n]\\r\\n----------\\r\\n文本文档内容：\\\\n猛犸是什么品牌？好像没听过。\n答：我们是对标小牛和九号的新兴互联网品牌。我们公司总部在深圳宝安兴东，\n工厂在江西萍乡。猛犸电动车是定位中高端可以换电的智能电动车，是解决用户\n充电难、充电慢、充电烦现状的产品，我们的换电功能是充电的消费升级，更省\n时、方便、安全。\n<docx.table.table object at 0x7f72ad6ba4d0>\n你们的车有什么特点，比别家的车好在哪里？\n答：（1）可以充电可以换电，5 秒钟换一颗电池，非常方便；\n（2）猛犸电动车是一款智能车，可以实现手机操控、智能防盗、实时定位等功\n能；\n（3）猛犸电动车搭载的是汽车级的 21700 三元锂电池，电池终身质保，不用担 心电池的损耗和折旧；\n（4）推出买车租电模式，相比于整车购买模式，入手门槛更低，先用更低的价\n格购买车架，电池采用租用的方式，按月扣除能源服务费，获得电池使用的权益\n和能源服务的权益。\n什么是换电？电池怎么换？\n答：您的电动车在没电情况下，可以打开猛犸出行手机 app 查看附近换电站，\n导航到换电站点，扫码取出一颗新电池，再把您的旧电池放进换电柜里，换电非\n常方便，5 秒钟可以完成。\n换电站普及吗，在哪里换？\"",
    #   "error_prompt": "检查题目报错:'in <string>' requires string as left operand, not builtin_function_or_method",
    # }
    # clean_data_1, is_error_1 = model.clean_multi_choice_question(temp_data)
    # print("wait")
    #
    # save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), model.__class__.__name__)
    # model.train(model_id, ['高尿酸_small.txt'], [doc], save_dir)
    model_id = "1732935871495766016"
    save_dir = "/data/cbk/NLP_Model/1732935871495766016/ComposeExam"
    q_result = model.query(model_id, save_dir)
    print("wait")
