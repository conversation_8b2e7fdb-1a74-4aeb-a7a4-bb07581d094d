import json
import re

import setting
from utils.my_utils import get_list_of_string, get_dict_of_string
from model.CallLLM.CallOpenAI import CallOpenAI
from model.CallLLM.CallOurModel import CallOurModel

default_extract_qa_prompt_gpt35 = """
文本文档内容：\n@seg_doc@
请你从以上提供的文本文档中,生成不同题型的题目,题型@question_types_1@
生成的题目必须与文本文档的内容高度相关并且具有代表性，题目数量可以超过5个，题目应尽可能多并且全面覆盖文档内容。
题目可以选择三种难度:简单、中等、困难，请确保难度分配均匀合理。
请用以下格式呈现，题目信息保存在dict里，题目保存在list里:
[
@question_types_2@
]
----------

@option_prompt@"""

# default_extract_qa_prompt_our = """文档：@seg_doc@
#
# 请你立足以上内容，出5个有难度的题目让学员回答，以用来考核学员对培训文档的了解程度。每个题目可以涵盖文本文档的不同章节。
# 题目内容可以在原文或原文题基础上作变化，用自己的语言去组织以考察学生对文档的深入了解；
#
# 要求：题目要有一定的复杂度和难度，这样回复需要多步推理或结合文中多处信息抽取才能得到，但答案不要超出文章内容，不要编造
# 题目要像考试题的风格，直接出题就好，Be truthful。
# 每个题目的开头要各不相同，它们之间是相互独立的。
# 题目长短要不一，咨询的题型多样（如填空题（用“_____”）、单选题、多选题、问答题、判断题（必须使用陈述句,要求正误判断的请求要以错的、带有迷惑性的居多）），如果咨询请求是填空题记得一定要用“_____”符号表示要填空的关键内容和知识点，其它题型不要这个符号
# 记住：你要丰富题目的细节，使之看起来和文本有一定的表述差异；每题40-200字不等
# 每种题型的格式：
# {"题型":"判断题","难度":"简单","题目":"","答案":"正确/错误", "答案解析": "填答案的详细解析步骤"},
# {"题型":"填空题","难度":"困难","题目":"","答案":["",""], "答案解析": "填答案的详细解析步骤"},
# {"题型":"多选题","难度":"中等","题目":"","选项":["A. XXX", "B. XXX", "C. XXX", "D. XXX"], "答案":["",""], "答案解析": "填答案的详细解析步骤"},
# {"题型":"简答题","难度":"中等","题目":"","答案":"","答案解析": "填答案的详细解析步骤"},
# {"题型":"单选题","难度":"中等","题目":"","选项":["A. XXX", "B. XXX", "C. XXX", "D. XXX"], "答案":"A", "答案解析": "填答案的详细解析步骤"},
# json输出"""

default_extract_qa_prompt_our = """
文档名：《@doc_name@》
@seg_doc@

请你立足以上内容，出5个有难度的题目让学员回答，以用来考核学员对培训文档的了解程度。每个题目可以涵盖文本文档的不同章节。
题目内容可以在原文或原文题基础上作变化，用自己的语言去组织以考察学生对文档的深入了解；

要求：题目要有一定的复杂度和难度，这样回复需要多步推理或结合文中多处信息抽取才能得到，但答案不要超出文章内容，不要编造
题目要像考试题的风格，直接出题就好，Be truthful。
每个题目的开头要各不相同，它们之间是相互独立的。
题目长短要不一，咨询的题型@question_types_1@
记住：你要丰富题目的细节，使之看起来和文本有一定的表述差异；每题40-200字不等。
@option_prompt@题型的格式示例：
@question_types_2@
json输出，答案解析是填答案的详细解析步骤"""

default_extract_qa_option_prompt = """生成题目要求:@option@
----------

"""

default_check_qa_prompt = """参考资料:
@document@
----------

当前问题：
@question@

这是一项文档测试任务，请从参考资料找到正确的文档内容并回答问题。
输出json格式: {"answer": "@answer_format@"}
"""


def get_extract_qa_result(res_string):
    try:
        temp = get_list_of_string(res_string)
        temp_dict = get_dict_of_string(res_string)
        if "questions" in res_string:
            if temp_dict and isinstance(temp_dict, dict):
                return [temp_dict]
            if temp and isinstance(temp, list):
                return temp
        else:
            if temp and isinstance(temp, list):
                return temp
            if temp_dict and isinstance(temp_dict, dict):
                return [temp_dict]
        raise Exception("解析失败")
        # temp = json.loads(res_string.strip())
        # if isinstance(temp, dict):
        #     temp = [temp]
    except:
        temp = []
        if not isinstance(res_string, str):
            return temp
        for i, line in enumerate(res_string.split("\n")):
            line = line.replace("'", '"')
            setting.logger.debug(f"解析过程:{i},{line}")
            line = line.strip()
            while len(line) and line[-1] != "}":
                line = line[:-1]
            while len(line) and line[0] != "{":
                line = line[1:]
            if len(line):
                try:
                    temp.append(json.loads(line))
                except:
                    #再次补充尝试
                    try:
                        find_json = re.findall(""".*({\"split\".*})""", line)
                        if len(find_json):
                            temp.append(json.loads(find_json[-1]))
                    except:
                        pass
    return temp

def default_assert_func_extract_qa_gpt35(res_string):
    try:
        temp = get_extract_qa_result(res_string)
        setting.logger.debug(f"解析结果:{temp}")
        assert isinstance(temp, list) and len(temp)
        for i, t_ in enumerate(temp):
            setting.logger.debug(f"{i}:{t_}")
            assert "题型" in t_
            assert "难度" in t_
            assert "题目" in t_
            assert "答案" in t_
            if t_["题型"] == "填空题":
                assert "_" in t_["题目"]
                # assert isinstance(t_["答案"], list)
            if t_["题型"] == "单选题":
                assert isinstance(t_["选项"], list)
            if t_["题型"] == "多选题":
                assert isinstance(t_["选项"], list)
        return True
    except Exception as e:
        setting.logger.debug(f"assert_func不符合:{res_string},{e}")
        return False

def default_assert_func_extract_qa_our(res_string, model_id=""):
    try:
        temp = get_extract_qa_result(res_string)
        setting.logger.debug(f"{model_id} 解析结果:{temp}")
        assert "填答案的详细解析步骤" not in res_string
        assert isinstance(temp, list) and len(temp)
        for i, t_ in enumerate(temp):
            setting.logger.debug(f"{i}:{t_}")
            assert "题型" in t_
            assert "难度" in t_
            assert "题目" in t_
            assert "答案" in t_
            if t_["题型"] == "填空题":
                assert "_" in t_["题目"]
            if t_["题型"] == "单选题":
                assert isinstance(t_["选项"], list)
            if t_["题型"] == "多选题":
                assert isinstance(t_["选项"], list)
            if t_["题型"] == "简答题" and t_.get("答案要点", ""):
                if isinstance(t_.get("答案", ""), list):
                    t_["答案"] = "\n".join([f"{t_}" for t_ in t_["答案"]])
                assert len(t_.get("答案", "").split("||")) == len(t_.get("答案要点", ""))
        return True
    except Exception as e:
        setting.logger.debug(f"assert_func不符合:{res_string},{e}")
        return False


def default_assert_func_extract_point_our(res_string, model_id=""):
    try:
        temp = get_extract_qa_result(res_string)
        setting.logger.debug(f"{model_id} 解析结果:{temp}")
        if isinstance(temp, list):
            temp = temp[0]
        assert isinstance(temp, dict) and len(temp)
        assert isinstance(temp["split"], list) and isinstance(temp["point"], list) and len(temp["split"]) == len(temp["point"])
        assert 0 < len(temp["split"]) <= 10
        return True
    except Exception as e:
        setting.logger.debug(f"assert_func不符合:{res_string},{e}")
        return False

def default_assert_func_gen_sim_question(res_string, model_id=""):
    try:
        temp = get_extract_qa_result(res_string)
        setting.logger.debug(f"{model_id} 解析结果:{temp}")
        if isinstance(temp, list):
            temp = temp[0]
        assert isinstance(temp, dict) and len(temp)
        assert isinstance(temp["questions"], list) and len(temp["questions"]) > 0
        return True
    except Exception as e:
        setting.logger.debug(f"assert_func不符合:{res_string},{e}")
        return False

def default_assert_func_check_qa(res_string):
    try:
        temp = json.loads(res_string.strip())
        assert isinstance(temp, dict)
        assert "answer" in temp
        return True
    except Exception as e:
        print(f"{res_string}, {e}")
        return False


if setting.EXAM_MODEL == "our":
    default_extract_qa_prompt = default_extract_qa_prompt_our
    default_assert_func_extract_qa = default_assert_func_extract_qa_our
else:
    default_extract_qa_prompt = default_extract_qa_prompt_gpt35
    default_assert_func_extract_qa = default_assert_func_extract_qa_gpt35
