default_point_analysis_prompt = """

请把原始答案切分为若干段，分段必须有逻辑，然后总结每一段的的回答要点。
注意分段后原始答案的顺序不能变，同时不能遗漏、删减、或者增加原始答案内容。
回答段落要点必须简洁，不能超过6个字。
请直接返回json结构：
{"spilt": ["段落1", "段落2", ..., "段落N"], "point": ["段落1要点", "段落2要点", ..., "段落N要点"]}

以下是一些例子：
答案：【你好女士，请问有什么可以帮到你】
返回：{"split": ["你好女士，", "请问有什么可以帮到你"], "point": ["礼貌问候", "询问需求"]}
答案：【在我国刑法中，男性和女性的最低刑事年龄是相同的，都是14岁。这意味着，无论男性还是女性，只有达到14岁，才能对自己的行为承担刑事责任。】
返回：{"split": ["在我国刑法中，男性和女性的最低刑事年龄是相同的，都是14岁。", "这意味着，无论男性还是女性，只有达到14岁，才能对自己的行为承担刑事责任。"], "point": ["刑法最低年龄", "刑法只看年龄"]}
答案：【歇业制度：或者称为市场主体休眠制度，作为一项全新的制度，需要从商事登记、劳动管理等角度加以规范，应当防止市场主体对歇业制度的滥用，避免侵害员工和相关方权益的情况发生。】
返回：{"split": ["歇业制度：或者称为市场主体休眠制度，作为一项全新的制度，", "需要从商事登记、劳动管理等角度加以规范，", "应当防止市场主体对歇业制度的滥用，避免侵害员工和相关方权益的情况发生。"], "point": ["介绍歇业制度", "防范角度", "避免侵害各利益"]}


答：【@answer@】

"""

default_question_generate_prompt = """
问题：@question@
请为上面的问题生成3个不同的相似问，生成的相似问必须和原始问题意思相同，但是表达方式上要多样化。
直接返回json结构
{"questions": ["相似问1", "相似问2", "相似问3"]}
"""

