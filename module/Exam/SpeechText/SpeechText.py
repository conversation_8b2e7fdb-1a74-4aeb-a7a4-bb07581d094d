import json
import os
import time

import requests
import re
import setting
from database.REDIS import RED<PERSON>
from model.CallLLM.CallOpenAI import <PERSON><PERSON>penA<PERSON>
from model.CallLLM.CallOurModel import CallOurModel
from module.Exam.SpeechText.SpeechTextPrompt import default_ppt_start_prompt, default_ppt_prompt, \
    default_ppt_option_prompt, default_compress_text_prompt, default_image_to_text_prompt, default_ppt_start_gen_new_prompt, \
    default_ppt_gen_new_prompt, default_ppt_task_to_option, use_image_text_only, default_ppt_task_prompt
from setting import logger
from utils.filechat_utils import ToBase64
from utils.my_utils import MyEncoder, remove_front_end_symbol_2, check_model_status
from module.img2text.img2text import send_image_to_openai_api, file_to_base64_resample


class SpeechText:
    def __init__(self):
        if setting.EXAM_MODEL == "gpt3.5":
            self.call_llm_model = CallOpenAI(use_azure=True)
        elif setting.EXAM_MODEL == "gpt4":
            self.call_llm_model = CallOpenAI(use_azure=True, model_name="gpt-4")
        else:
            self.call_llm_model = CallOurModel(url=setting.OUR_MODEL_URL)
        self.redis = REDIS()
        # prompt
        self.ppt_start_prompt = default_ppt_start_prompt
        self.ppt_prompt = default_ppt_prompt
        self.ppt_option_prompt = default_ppt_option_prompt
        self.compress_text_prompt = default_compress_text_prompt
        self.assert_func = None
        self.task_type_to_task = {
            0: "重新生成",
            1: "改写",
            2: "缩写",
            3: "扩写"
        }

    def train(self, model_id, filenames, contents, image_list, save_dir, prompt_data_key_list=None, other_model_id="", other_model_type="", enterprise_id=""):
        code = 0
        msg = "成功"
        start_time = time.time()
        ppt_speech_text_dict = {}

        other_model_dict = {
            "other_model_id": other_model_id,
            "other_model_type": other_model_type,
            "model_id": model_id,
            "enterprise_id": enterprise_id
        }
        try:
            os.makedirs(save_dir, exist_ok=True)
            if not prompt_data_key_list:
                prompt_data_key_list = [""] * len(filenames)
            for i in range(len(filenames)):
                filename = filenames[i]
                try:
                    if len(prompt_data_key_list[i].strip()):
                        prompt_dict = self.redis.get_data(prompt_data_key_list[i])
                        assert isinstance(prompt_dict, dict)
                    else:
                        prompt_dict = {}
                except Exception as ee:
                    prompt_dict = {}
                    logger.warning(f"读取prompt_data失败,使用默认值:{ee}")
                ppt_start_prompt = prompt_dict.get("ppt_start_prompt", "").strip()
                ppt_prompt = prompt_dict.get("ppt_prompt", "").strip()
                ppt_option = prompt_dict.get("ppt_option", "").strip()
                self.ppt_start_prompt = default_ppt_start_prompt if len(ppt_start_prompt) == 0 else ppt_start_prompt
                self.ppt_prompt = default_ppt_prompt if len(ppt_prompt) == 0 else ppt_prompt
                self.ppt_option_prompt = default_ppt_option_prompt
                ppt_option = self.ppt_option_prompt.replace("@PPT生成要求@", ppt_option) if len(ppt_option) else ""

                all_page_texts = contents[i]

                # 图片转文字
                if len(image_list[i]) != len(all_page_texts):
                    logger.debug(f"{model_id},{filename},未传图片,不用进行图片抽取")
                    ppt_speech_text_dict[filename] = [{"page": page_i, "page_text": page_t, "image_text": "", "image_to_text_prompt": ""} for page_i, page_t in enumerate(all_page_texts)]
                else:
                    logger.debug(f"{model_id},{filename},开始进行图片抽取")
                    ppt_speech_text_dict[filename] = []
                    for page_i, page_t in enumerate(all_page_texts):
                        # page_start_time = time.time()
                        if not check_model_status(model_id):
                            return 1, "模型已停止", {filename: {}}
                        image_path = image_list[i][page_i]
                        image_prompt = default_image_to_text_prompt.replace("@ppt当前页内容@", "\n".join(page_t))
                        image_text = self.call_image_to_text(image_path, image_prompt)
                        logger.debug(f"{model_id},{filename},图片抽取{page_i+1}/{len(all_page_texts)},结果:{image_text}")
                        ppt_speech_text_dict[filename].append({
                            "page": page_i, "page_text": page_t, "image_text": image_text,
                            "image_to_text_prompt": image_prompt
                        })
                        # 实时更新训练时间
                        train_status = eval(self.redis.get_data(key=f"train_state_{model_id}", need_json=False))
                        # page_end_time = time.time() - page_start_time
                        # except_time = int(page_end_time * (len(all_page_texts) - page_i - 1)) + (len(all_page_texts) * 300)
                        train_status["task_time"] = train_status["total_time"] = min(train_status["task_time"]-50, train_status["task_time"])
                        self.redis.set_data(f"train_state_{model_id}", str(train_status), need_json=False)

                # 压缩文本
                compress_text_list = []
                for ppt_speech_text_page in ppt_speech_text_dict[filename]:
                    if not check_model_status(model_id):
                        return 1, "模型已停止", {filename: {}}
                    page_i = ppt_speech_text_page["page"]
                    page_t = ppt_speech_text_page["page_text"]
                    image_text = ppt_speech_text_page["image_text"]
                    if len(image_text) and use_image_text_only:
                        ppt_str_cur = image_text
                    else:
                        ppt_str_cur = "\n".join(page_t) + "\n" + image_text
                    compress_result, compress_prompt = self.compress_text(ppt_str_cur, other_model_dict)
                    ppt_speech_text_dict[filename][page_i]["compress_prompt"] = compress_prompt
                    ppt_speech_text_dict[filename][page_i]["compress_result"] = compress_result
                    logger.debug(f"{model_id},{filename},压缩文本{page_i+1}/{len(all_page_texts)},结果:{compress_result}")
                    compress_text_list.append(compress_result)

                for ppt_speech_text_page in ppt_speech_text_dict[filename]:
                    if not check_model_status(model_id):
                        return 1, "模型已停止", {filename: {}}
                    page_start_time = time.time()
                    page_i = ppt_speech_text_page["page"]
                    page_t = ppt_speech_text_page["page_text"]
                    image_text = ppt_speech_text_page["image_text"]
                    if len(image_text) and use_image_text_only:
                        ppt_str_cur = image_text
                    else:
                        ppt_str_cur = "\n".join(page_t) + "\n" + image_text
                    if page_i == 0:
                        ppt_str_last_title = "无内容"
                    else:
                        ppt_str_last_title = compress_text_list[page_i-1]
                    if page_i == len(all_page_texts) - 1:
                        ppt_str_next_title = "无内容"
                    else:
                        ppt_str_next_title = compress_text_list[page_i+1]

                    # prompt
                    if page_i == 0:
                        final_ppt_prompt = self.ppt_start_prompt.replace("@PPT生成要求@", ppt_option).replace("@ppt下一页摘要@", ppt_str_next_title).replace("@ppt当前页内容@", ppt_str_cur)
                    else:
                        final_ppt_prompt = self.ppt_prompt.replace("@PPT上一页页码@", f"{page_i}").replace("@ppt上一页摘要@", ppt_str_last_title).replace("@PPT当前页页码@", f"{page_i+1}").\
                            replace("@PPT下一页页码@", f"{page_i+2}").replace("@ppt下一页摘要@", ppt_str_next_title).replace("@PPT生成要求@", ppt_option).replace("@ppt当前页内容@", ppt_str_cur)
                    res = self.call_llm_model.run(user_prompt=final_ppt_prompt, retry_times=3, other_model_dict=other_model_dict)
                    ppt_speech_text_dict[filename][page_i]["ppt_prompt"] = final_ppt_prompt
                    ppt_speech_text_dict[filename][page_i]["ori_result"] = res
                    ppt_speech_text_dict[filename][page_i]["result"] = self.clean_res(res, page_i == 0, page_i == len(all_page_texts)-1)
                    logger.debug(f"{model_id},{filename},生成讲稿{page_i+1}/{len(all_page_texts)},结果:{ppt_speech_text_dict[filename][page_i]['result']}")
                    # 实时更新训练时间
                    train_status = eval(self.redis.get_data(key=f"train_state_{model_id}", need_json=False))
                    page_end_time = time.time() - page_start_time
                    except_time = int(page_end_time * (len(all_page_texts) - page_i - 1))
                    train_status["task_time"] = train_status["total_time"] = min(except_time, train_status["task_time"])
                    self.redis.set_data(f"train_state_{model_id}", str(train_status), need_json=False)

            with open(os.path.join(save_dir, 'ppt_speech_text_dict.json'), 'w', encoding='utf-8') as f:
                json.dump(ppt_speech_text_dict, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)
        except Exception as e:
            logger.error(f'抽题报错,model_id{model_id},{e}', exc_info=True)
            code = 1
            msg = f'抽题报错:{e}'
        return code, msg, ppt_speech_text_dict

    def compress_text(self, ppt_str, other_model_dict=None):
        prompt = self.compress_text_prompt.replace("@ppt当前页内容@", ppt_str)
        res = self.call_llm_model.run(user_prompt=prompt, retry_times=3, other_model_dict=other_model_dict)
        return res, prompt

    def query(self, model_id, save_dir):
        query_result = {"code": 0, "msg": "成功", "finish": 0, "ppt": []}
        try:
            if not os.path.exists(os.path.join(save_dir, "ppt_speech_text_dict.json")):
                return query_result
            with open(os.path.join(save_dir, 'ppt_speech_text_dict.json'), 'r', encoding='utf-8') as f:
                ppt_speech_text_dict = json.load(f)
                for filename, file_ppt_list in ppt_speech_text_dict.items():
                    for q in file_ppt_list:
                        # java tts 不支持\n
                        query_result["ppt"].append({"page": q["page"], "content": q["result"].replace("\n", "")})
                query_result["finish"] = 1
        except Exception as e:
            query_result["code"] = 1
            query_result["msg"] = f"查询报错:{e}"
        return query_result

    def clean_res(self, res, is_start, is_end):
        for last_str in ["【结束语/致谢语】", "【衔接语（下一页）】", "结束语/致谢语", "衔接语（下一页）"]:
            if last_str in res:
                res = res[:res.find(last_str)]

        for start_str in ["【开场+首页演讲稿的内容】", "【当前演讲稿的内容】", "开场+首页演讲稿的内容", "当前演讲稿的内容"]:
            if start_str in res:
                res = res[res.find(start_str)+len(start_str):]
        res = res[res.find("】") + 1:].strip()
        res = remove_front_end_symbol_2(res, need_lower=False)
        res = res.replace("\r", "").strip()  # \r字符在写入ppt的时候会乱码
        return res
    
    def clean_res_task(self, res, task_type):
        task_name = self.task_type_to_task[task_type]
        if task_name in res:
            regex = f"({self.task_type_to_task[task_type]}.{{0,5}}[:：]{{1}})"
            match_text = re.findall(regex, res)
            print(match_text)
            if len(match_text):
                res = res[res.find(match_text[0])+len(match_text[0]):]
            else:
                res = res[res.find(task_name)+len(task_name):]
        return res

    def single_page_gen(self, all_page_texts, model_id, page, image_path, task_type, old_text, prompt_data_key, save_dir, other_model_dict={}):
        redis_key = f"single_page_gen_result_{model_id}"
        if len(prompt_data_key.strip()):
            prompt_dict = self.redis.get_data(prompt_data_key)
        else:
            prompt_dict = {}
        ppt_start_prompt = prompt_dict.get("ppt_start_prompt", "").strip()
        ppt_prompt = prompt_dict.get("ppt_prompt", "").strip()
        ppt_option = prompt_dict.get("ppt_option", "").strip()
        self.ppt_start_prompt = default_ppt_start_prompt if len(ppt_start_prompt) == 0 else ppt_start_prompt
        self.ppt_prompt = default_ppt_prompt if len(ppt_prompt) == 0 else ppt_prompt
        self.ppt_option_prompt = default_ppt_option_prompt
        ppt_option = self.ppt_option_prompt.replace("@PPT生成要求@", ppt_option) if len(ppt_option) else ""

        ppt_speech_text_dict = {"page": page, "all_page_texts": all_page_texts}

        if task_type == 0:
            # 重新生成才需要考虑ppt原始内容
            # 压缩前一页内容
            logger.debug(f"{model_id},PPT单页讲稿,准备压缩前一页")
            if page > 0:
                ppt_str = "\n".join(all_page_texts[page-1])
                ppt_str_last_title, ppt_str_last_compress_prompt = self.compress_text(ppt_str, other_model_dict)
            else:
                ppt_str_last_title = "无内容"
                ppt_str_last_compress_prompt = ""
            ppt_speech_text_dict["ppt_str_last_title"] = ppt_str_last_title
            ppt_speech_text_dict["ppt_str_last_compress_prompt"] = ppt_str_last_compress_prompt
            logger.debug(f"{model_id},PPT单页讲稿,成功压缩前一页,ppt_str_last_title:{ppt_str_last_title},ppt_str_last_compress_prompt:{ppt_str_last_compress_prompt}")

            # 压缩下一页内容
            logger.debug(f"{model_id},PPT单页讲稿,准备压缩下一页")
            if page < len(all_page_texts)-1:
                ppt_str = "\n".join(all_page_texts[page+1])
                ppt_str_next_title, ppt_str_next_compress_prompt = self.compress_text(ppt_str, other_model_dict)
            else:
                ppt_str_next_title = "无内容"
                ppt_str_next_compress_prompt = ""
            ppt_speech_text_dict["ppt_str_next_title"] = ppt_str_next_title
            ppt_speech_text_dict["ppt_str_next_compress_prompt"] = ppt_str_next_compress_prompt
            logger.debug(f"{model_id},PPT单页讲稿,成功压缩下一页,ppt_str_next_title:{ppt_str_next_title},ppt_str_last_compress_prompt:{ppt_str_next_compress_prompt}")

            # 当前页PPT图片抽取
            if image_path:
                image_text = self.call_image_to_text(image_path, default_image_to_text_prompt)
                logger.debug(f"{model_id},PPT单页讲稿,图片抽取结果:{image_text}")
            else:
                image_text = ""
                logger.debug(f"{model_id},PPT单页讲稿,未传图片,无需进行图片抽取")

            # 生成讲稿
            logger.debug(f"{model_id},PPT单页讲稿,准备生成讲稿,task_type:{task_type},task:{self.task_type_to_task[task_type]},old_text:{old_text}")
            ppt_str_cur = "\n".join(all_page_texts[page]) + "\n" + image_text
            if task_type == 0:
                if page == 0:
                    final_ppt_prompt = self.ppt_start_prompt.replace("@PPT生成要求@", ppt_option).replace("@ppt下一页摘要@", ppt_str_next_title).replace("@ppt当前页内容@", ppt_str_cur)
                else:
                    final_ppt_prompt = self.ppt_prompt.replace("@PPT上一页页码@", f"{page}").replace("@ppt上一页摘要@", ppt_str_last_title).replace("@PPT当前页页码@", f"{page+1}"). \
                        replace("@PPT下一页页码@", f"{page + 2}").replace("@ppt下一页摘要@", ppt_str_next_title).replace("@PPT生成要求@", ppt_option).replace("@ppt当前页内容@", ppt_str_cur)
            else:
                if page == 0:
                    final_ppt_prompt = default_ppt_start_gen_new_prompt.replace("@PPT生成要求@", ppt_option).replace("@ppt下一页摘要@", ppt_str_next_title).replace("@ppt当前页内容@", ppt_str_cur).\
                        replace("@ppt当前页演讲稿内容@", old_text).replace("@TASK_TYPE@", self.task_type_to_task[task_type]).replace("@TASK_TYPE_OPTION@", default_ppt_task_to_option[self.task_type_to_task[task_type]])
                else:
                    final_ppt_prompt = default_ppt_gen_new_prompt.replace("@PPT上一页页码@", f"{page}").replace("@ppt上一页摘要@", ppt_str_last_title).replace("@PPT当前页页码@", f"{page+1}"). \
                        replace("@PPT下一页页码@", f"{page + 2}").replace("@ppt下一页摘要@", ppt_str_next_title).replace("@PPT生成要求@", ppt_option).replace("@ppt当前页内容@", ppt_str_cur).\
                        replace("@ppt当前页演讲稿内容@", old_text).replace("@TASK_TYPE@", self.task_type_to_task[task_type]).replace("@TASK_TYPE_OPTION@", default_ppt_task_to_option[self.task_type_to_task[task_type]])
        else:
            final_ppt_prompt = default_ppt_task_prompt[self.task_type_to_task[task_type]].replace("@query@", old_text)
        res = self.call_llm_model.run(user_prompt=final_ppt_prompt, retry_times=3, other_model_dict=other_model_dict)
        ppt_speech_text_dict["task_type"] = task_type
        ppt_speech_text_dict["task_type_text"] = self.task_type_to_task[task_type]
        ppt_speech_text_dict["ppt_prompt"] = final_ppt_prompt
        ppt_speech_text_dict["ori_result"] = res
        if task_type == 0:
            clean_res = self.clean_res(res, page == 0, page == len(all_page_texts)-1)
        else:
            clean_res = self.clean_res_task(res, task_type)
        ppt_speech_text_dict["result"] = clean_res
        with open(os.path.join(save_dir, 'ppt_speech_text_dict.json'), 'w', encoding='utf-8') as f:
            json.dump(ppt_speech_text_dict, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)
        logger.debug(f"{model_id},PPT单页讲稿,成功生成讲稿,res:{res}")
        logger.debug(f"{model_id},PPT单页讲稿,成功生成讲稿,clean_res:{clean_res}")
        if isinstance(clean_res, str) and len(clean_res.strip()):
            single_page_gen_data = {
                'code': 0,
                'msg': '成功',
                'finish': 1,
                'content': clean_res,
                "model_id": model_id
            }
        else:
            single_page_gen_data = {
                'code': 1,
                'msg': '失败',
                'finish': 1,
                'content': "",
                "model_id": model_id
            }
        self.redis.set_data(redis_key, single_page_gen_data)
        return single_page_gen_data

    @staticmethod
    def call_image_to_text(image_path, prompt):
        try:
            img_base64 = file_to_base64_resample(image_path)
            file_name = image_path.split("/")[-1]
            # data = {'image': img_base64, 'image_name': f"{int(time.time())}_{file_name}", "text": prompt}
            response = send_image_to_openai_api(img_base64)
            # response = requests.post(setting.IMAGE_TO_TEXT_URL, data=data, timeout=600)
            logger.debug(f"图片抽取返回:{response}")
            # response = json.loads(response.text.strip())
            return response
        except Exception as e:
            logger.warning(f"调用图片抽取报错,{e}")
            return ""


if __name__ == '__main__':
    from utils.filechat_utils import ppt_text_extract_page

    content = ppt_text_extract_page(r"/data/cbk/kaopei_llm/data/test_file/元宇宙简介概述-经典PPT-.pptx")
    model_id = "1735224571495940096"
    model = SpeechText()

    s = """ -【开场+首页演讲稿的内容】：各位好，感谢大家能在百忙之中抽出时间参加这次分享。【当前演讲稿的内容】今天我们将一起探讨一些关于我们在日常工作中可能会遇到的问题，比如：发版之后我该做什么？如何提高代码质量？怎么样才算测试通过？如何高效
协同？什么时候提测？如何分解任务？如何编写报告？发版材料应该填写什么？团队如何协作？系统出了问题找谁处理？多套产品模块如何共存？代码如何适应多套环境？定制化代码怎么管理？这些问题，我们将通过今天的分享，一起找出解决方法，希望能对大家
在工作中有所帮助。\r\n-【衔接语（下一页）】：下面，我们将进一步深入讨论这些问题，并找出解决方案。请大家跟我一起，用心投入到这个过程中，让我们一起探索，一起学习。\r\n-【结束语/致谢语】：在此，我再次感谢大家的参与，希望我们的分享能对>你们有所帮助。在接下来的时间里，我们将一起探讨如何更好地解决我们在工作中可能遇到的问题。"""
    t = model.clean_res(s, is_start=False, is_end=False)
    print(t)
    print("wait")
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), model.__class__.__name__)
    model.train(model_id, ['元宇宙简介概述-经典PPT-.pptx'], [content], save_dir)
    q_result = model.query(model_id, save_dir)
    print("wait")
