import setting

default_ppt_start_prompt_gpt35 = """请你根据我提供的ppt资料,帮忙写一段当前ppt首页的演讲的开场部分，作为完整演讲稿的一部分。
这里，一个当前页的演讲稿包括：1.开场 + 2.当前演讲稿的内容 + 3.衔接语（下一页）。其中衔接语是可选的，可以不生成，并不重要，第2部分的内容才是重点。
注意：你要说中文；
要求：仅有标题或字数少的ppt页要简练（如几句话）说明，请只围绕首页ppt的内容说；字数50字以内
@PPT生成要求@

当前页：PPT第1页
下一页：PPT第2页【@ppt下一页摘要@】

----------
首页ppt内容如下：
@ppt当前页内容@

----------
输出的内容与结构部分如下，每个部分的开头也要有下面的前缀说明：
-【开场+首页演讲稿的内容】：
-【衔接语（下一页）】：
-【结束语/致谢语】：
这个也可以
"""

default_ppt_prompt_gpt35 = """请你根据我提供的ppt资料,帮忙写一段当前ppt页面的演讲稿，作为完整演讲稿的一部分。
当前演讲稿主要是对当前ppt的介绍，介绍完即可结束，停止输出。
这里，一个当前页的演讲稿包括：1.衔接语（上一页）+ 2.当前演讲稿的内容 + 3.衔接语（下一页）。其中衔接语是可选的，可以不生成，并不重要，第2部分的内容才是重点。

上一页：PPT第@PPT上一页页码@页【@ppt上一页摘要@】
当前页：PPT第@PPT当前页页码@页
下一页：PPT第@PPT下一页页码@页【@ppt下一页摘要@】
说中文；当前页说完后，你马上你就要讲下一页，你会一次演讲说完所有几十页的ppt
@PPT生成要求@

----------
资料：
PPT第@PPT当前页页码@页:【@ppt当前页内容@】
----------
其中，当前演讲稿的内容：
【衔接语（上一页）】
【当前演讲稿的内容】
【衔接语（下一页）】
【结束语/致谢语】
"""

default_ppt_start_prompt_gen_new_gpt35 = """请你根据我提供的ppt资料以及PPT首页的演讲稿,帮忙重新写一段ppt首页的演讲稿，作为完整演讲稿的一部分。
这里，一个当前页的演讲稿包括：1.开场 + 2.当前演讲稿的内容 + 3.衔接语（下一页）。其中衔接语是可选的，可以不生成，并不重要，第2部分的内容才是重点。
注意：你要说中文；
要求：仅有标题或字数少的ppt页要简练（如几句话）说明，请只围绕首页ppt的内容说；字数50字以内
@PPT生成要求@

当前页：PPT第1页
下一页：PPT第2页【@ppt下一页摘要@】

----------
首页ppt内容如下：
@ppt当前页内容@

----------
首页ppt演讲稿如下：
@ppt当前页演讲稿内容@

----------
请对首页ppt演讲稿进行@TASK_TYPE@,@TASK_TYPE@要求:@TASK_TYPE_OPTION@

输出的内容与结构部分如下，每个部分的开头也要有下面的前缀说明：
-【开场+首页演讲稿的内容】：
-【衔接语（下一页）】：
-【结束语/致谢语】：
这个也可以
"""

default_ppt_prompt_gen_new_gpt35 = """请你根据我提供的ppt资料以及PPT当前页的演讲稿,帮忙重新写一段当前ppt页面的演讲稿，作为完整演讲稿的一部分。
当前演讲稿主要是对当前ppt的介绍，介绍完即可结束，停止输出。
这里，一个当前页的演讲稿包括：1.衔接语（上一页）+ 2.当前演讲稿的内容 + 3.衔接语（下一页）。其中衔接语是可选的，可以不生成，并不重要，第2部分的内容才是重点。

上一页：PPT第@PPT上一页页码@页【@ppt上一页摘要@】
当前页：PPT第@PPT当前页页码@页
下一页：PPT第@PPT下一页页码@页【@ppt下一页摘要@】
说中文；当前页说完后，你马上你就要讲下一页，你会一次演讲说完所有几十页的ppt
@PPT生成要求@

----------
资料：
PPT第@PPT当前页页码@页:【@ppt当前页内容@】
----------
PPT当前页演讲稿如下：
@ppt当前页演讲稿内容@
----------
请对首页ppt演讲稿进行@TASK_TYPE@,@TASK_TYPE@要求:@TASK_TYPE_OPTION@

其中，当前演讲稿的内容：
【衔接语（上一页）】
【当前演讲稿的内容】
【衔接语（下一页）】
【结束语/致谢语】
"""

default_ppt_option_prompt_gpt35 = """要求:@PPT生成要求@"""

default_compress_text_prompt_gpt35 = f"""ppt解析内容： 
@ppt当前页内容@

以上是ppt某一页解析出来的全部内容，请你用一句中文总结这张ppt主要说的具体内容是什么，需要体现该ppt的特色内容，但不要超出ppt的范围，20字以内
提示：ppt的开头可能含有标题信息
这张ppt主要说的具体内容是：
"""

default_ppt_task_to_option = {
    "改写": "改变文本的表述方式，优化语法或表达，提升语言质量。",
    "缩写": "提炼长文本信息，简明扼要地呈现核心内容，方便快速阅读。",
    "扩写": "补充细节，使短文本内容更完整、具体和丰富，满足深入阅读需求。"
}
# default_ppt_task_prompt = {
#     "改写": "帮我把下面这段文字进行改写，修改一些内容风格：\n@query@",
#     "缩写": "帮我把下面这段文字进行缩写，使内容更精简：\n@query@",
#     "扩写": "帮我把下面这段文字进行扩写，使内容更丰富：\n@query@"
# }
default_ppt_task_prompt = {
    "改写": "你是一名演讲辅助专家，请使用专业的演讲写作技巧，帮我把下面这段文字进行改写，改变文本的表述方式，优化语法或表达，提升语言质量：\n@query@",
    "缩写": "你是一名演讲辅助专家，请使用专业的演讲写作技巧，帮我把下面这段文字进行缩写，使内容更精简，提炼长文本信息，简明扼要地呈现核心内容：\n@query@",
    "扩写": "你是一名演讲辅助专家，请使用专业的演讲写作技巧，帮我把下面这段文字进行扩写，补充细节，使短文本内容更完整、具体和丰富：\n@query@"
}

if setting.EXAM_MODEL == "our":
    default_compress_text_prompt = default_compress_text_prompt_gpt35
    default_ppt_start_prompt = default_ppt_start_prompt_gpt35
    default_ppt_prompt = default_ppt_prompt_gpt35
    default_ppt_option_prompt = default_ppt_option_prompt_gpt35
    default_ppt_start_gen_new_prompt = default_ppt_start_prompt_gen_new_gpt35
    default_ppt_gen_new_prompt = default_ppt_prompt_gen_new_gpt35
else:
    default_compress_text_prompt = default_compress_text_prompt_gpt35
    default_ppt_start_prompt = default_ppt_start_prompt_gpt35
    default_ppt_prompt = default_ppt_prompt_gpt35
    default_ppt_option_prompt = default_ppt_option_prompt_gpt35
    default_ppt_start_gen_new_prompt = default_ppt_start_prompt_gen_new_gpt35
    default_ppt_gen_new_prompt = default_ppt_prompt_gen_new_gpt35

# default_image_to_text_prompt = """请你提取图片中的文字，并按照正确的位置或顺序关系输出，你要尽可能保留原始图片中文字之间的重要的顺序和位置关系。
# 原样复制/不要修改这些文字
# 层级的所属关系可以考虑用两个空格缩进表示"""

default_image_to_text_prompt = """@ppt当前页内容@
    
以上是我代码解析出来的图片中的文字，所以可能某些图片形式的文字没有解析出来，或解析出来的文字可能存在顺序、位置和所属关系的错误，但解析出来的文字本身肯定是对的，解析出来的文字图片中都有。
现在，请你参考图片内容和这个解析结果，按照以下要求输出：
请你提取图片中的所有文字，并按照正确的位置或顺序关系输出，你要尽可能保留原始图片中文字之间的重要的顺序和位置关系。
"""

use_image_text_only = True
