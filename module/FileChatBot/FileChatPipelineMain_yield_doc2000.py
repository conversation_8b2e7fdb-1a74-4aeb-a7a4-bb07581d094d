import datetime
import sys
# sys.path.insert(0,'/root/autodl-tmp/ultra_sammy/aicc-llm-agent')
from collections import defaultdict

import pandas as pd
import requests,json
from setting import logger,FILECHAT_MODEL
import setting
import os
import re,time
import math
from hashlib import md5
# from main.RetrievalMain import Retrieval<PERSON><PERSON>
from model.FileChatAction.Doc2000Bot import Doc2000Bot
from model.CallLLM.qwen.qwen_token_lener import qwen_len_cuter, qwen_lener
from module.FileChatBot.FilechatPrompt import FINAL_ANSWER_PROMPT


from model.CallLLM.CallOurModel import CallOurModel
from main.RetrievalMain_bge import RetrievalMain_bge

class FileChatPipelineMain:
    def __init__(self,filechat_llm):
        self.answers = defaultdict(dict)
        self.history = defaultdict(list)
        self.call_llm_model = filechat_llm
        self.filechatagent = Doc2000Bot(filechat_llm)

    def search_and_chat(self, model_id ,query,retrieval:RetrievalMain_bge=None, update_info = {},task_list=[], chat_model_config={},llm_parameter={}):

        session_time = datetime.datetime.now().strftime("%Y-%m-%d_%H_%M_%S")
        # 预测进来的时候就要写self.answers,因为FileChatServer需要根据self.answers判断有没有训练过。

        status = []
        pipeline_info = {'model_id':model_id,'hash_id': md5(query.encode()).hexdigest(),
                  'starttime': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                  'query': query}

        pipeline_info.update(update_info)

        # self.answers[session_id][query_id] = answer
        # key = f'chatGptFileQuery_{session_id}_{query_id}'
        start = time.time()
        consume = []
        save_file_name = re.sub('，|。|？|！|：|；|,|\.|\?|!|:|;|、|\.|\"|\,|\?|\'|\n', '',query)[0:20]
        # if save_file_name in str(os.listdir(os.path.join(setting.MAIN_DIR, 'pretrain_model'))):
        #     return
        save_file_name = save_file_name  + f'_{session_time}_' + ".json".replace('\n', '').replace('\t','').replace(' ', '')
        save_file_path = os.path.join(setting.MAIN_DIR, 'data', 'filechat_result',save_file_name)
        use_rewrite = chat_model_config.get('use_rewrite',False)
        use_prerank = chat_model_config.get('use_prerank',False)


        try:
            # 1.重写
            if use_rewrite == True:
                status += ['正在重写问题...']
                # yield None,'\n'.join(status) , []
                update_info, rewrite_result = self.filechatagent.rewrite_query(query=query)
                pipeline_info.update(update_info)
                logger.debug(f'1重写完成,重写结果:{rewrite_result}')
            else:
                rewrite_result = []
                logger.debug('1无需重写')

            # 条件生成
            # yield None, '正在生成查询条件...', []
            status += ['正在搜索资料...']
            # yield None,'\n'.join(status) , []
            logger.debug('2开始粗排')

            # 2开始粗排
            topk = 10
            rewrite_result = [query] + rewrite_result
            update_info_rewrite = defaultdict(list)
            retrieval_result_rewrite = []
            for q in rewrite_result:
                # update_info, retrieval_result = self.filechatagent.retrieval(query=q, topk=topk)
                retrieval_result = retrieval.predict(model_id,query=q, topk=topk)
                retrieval_result = retrieval_result['retrival_content']
                # for k,v in update_info.items():
                #     update_info_rewrite[k].append(v)
                retrieval_result_rewrite.append(retrieval_result)
            # answer.update(update_info_rewrite)

            logger.debug('2粗排完成')
            consume.append(['粗排完毕', time.time() - start])

            df = {"names": [], "text": [], "scores": [], "url": []}
            for retrieval_result in retrieval_result_rewrite:
                # 如果rewrite了，tok取少一些
                return_top_k = math.ceil(topk / max(1, len(retrieval_result_rewrite)-1))
                df["names"].extend(retrieval_result['filename'][:return_top_k])
                df["text"].extend(retrieval_result['match_text'][:return_top_k])
                df["scores"].extend(retrieval_result['score'][:return_top_k])
                df["url"].extend(retrieval_result['url'][:return_top_k])


            df = pd.DataFrame(df)
            df.sort_values('scores', ascending=False, inplace=True)
            df = df[:topk]
            # df = df[df['scores']>0.35]
            pipeline_info["2retrieval_result_final"] = df.to_dict(orient='records')

            logger.debug('3开始精排')
            # 窗口筛选候选人
            llm_rank_json = {'不符合的文章': []}

            pipeline_info['3llm_rank_output'] = ''
            pipeline_info['3llm_rank_prompt'] = ''

            status += ['正在锁定文章...']
            # yield None, '\n'.join(status), []

            if use_prerank:
                #######################################精排
                for window in range(0, len(df), 1):
                    sec = df.iloc[window:window + 1]
                    sec['index'] = range(1, len(sec) + 1)
                    sec = sec.iloc[0]
                    sec_for_llm_rank = '文章标题:' + sec['names']  + '\n' + sec['text']

                    # status += [f"判断文章是否合适:{sec['names']}..."]/
                    # yield None, '\n'.join(status), []


                    # update_info, prompt, llm_result = filechatagent.llm_rank(query=query, content=sec_for_llm_rank)
                    # 需要限制5000token
                    content = qwen_len_cuter(sec_for_llm_rank,query)

                    prompt = """@doc@\n\n\n问题：@query@\n前面的文章是否适合用来分析当前问题，先给出理由，然后输出：相关/不相关"""
                    prompt = prompt.replace('@doc@', content)
                    prompt = prompt.replace('@query@', query)
                    llm_result = self.call_llm_model.run(prompt)


                    pipeline_info['3llm_rank_output'] += llm_result + '\n=====================================================================\n'
                    pipeline_info['3llm_rank_prompt'] += prompt + '\n=====================================================================\n'
                    consume.append(['精排', time.time() - start])

                    if re.findall('不相关|相关性较低|不.{0,2}适合|不.{0,2}适用|不能.{0,2}解决|不能完全解决|文章.{0,2}不够全面|文章只能提供部分',llm_result):
                        llm_rank_json['不符合候的文章'] += [sec['names']]
                        logger.debug(f'{sec["names"]},不符合')
                    else:
                        df = df[:window + 1]
                        logger.debug(f'{sec["names"]},符合')
                        break

            no_candidate = len(llm_rank_json['不符合的文章'])==df.shape[0]

            pipeline_info.update(llm_rank_json)

            no_candidate = df.shape[0]==0
            fileid2url = {}
            fileid2filename = {}
            fileid2showindex = {}
            if not no_candidate:

                # answer['4llm_rank_json'] = llm_rank_json
                print(llm_rank_json,'llm_rank_jsonllm_rank_jsonllm_rank_jsonllm_rank_json')
                no_good_man = llm_rank_json['不符合的文章']
                pipeline_info['3no_good_man'] = no_good_man
                df = df[~df['names'].isin(no_good_man)]
                logger.debug('3精排完成')

                logger.debug('4最后回答开始')
                # fit_for_llm = '文档名称:' + df['names'] + '\n' + df['text'] + '\n'
                # fit_for_llm = '\n'.join(fit_for_llm)

                fit_for_llm = [f"\n{i + 1},{n}:\n{t}\n-------------------------------" for i, (n, t) in enumerate(zip(df['names'], df['text']))]
                fit_for_llm_final = fit_for_llm[0]

                for i in range(2, len(fit_for_llm) + 1):
                    temp = "\n".join(fit_for_llm[:i])
                    if qwen_lener(temp) >= 10000:
                        break
                    else:
                        fit_for_llm_final = temp


                df = df.rename(columns = {'names':'filename','text':'match_text','scores':'score'})
                retrieval_result_convert_to_llm = retrieval.retrieval_result_convert_to_llm(query,df.to_dict(orient='list'))
                fit_for_llm = retrieval_result_convert_to_llm['fit_for_llm']
                fileid2showindex = retrieval_result_convert_to_llm['fileid2showindex']
                pipeline_info['fileid2showindex'] = fileid2showindex

                final_answer_prompt = qwen_len_cuter(fit_for_llm,query=query, max_len=4000)
            else:

                final_answer_prompt = """@query@"""
                final_answer_prompt = final_answer_prompt.replace('@query@', query)


            # # # 回答
            # responce = call_with_messages(fit_for_llm)
            # answer_output = ''

            with open(save_file_path, 'w',
                      encoding='utf-8-sig') as f:
                f.write(json.dumps(pipeline_info, ensure_ascii=False, indent=2))

            status += [f"正在整理资料，准备回答问题"]
            # yield None, '\n'.join(status), []
            pipeline_info['4final_answer_prompt'] = final_answer_prompt

            # 非流
            # responce = summary_llm.run(final_answer_prompt)
            # i = responce
            # answer['4answer'] = i
            # # 重写
            # final_answer, rewrite_prompt = self.rewrite(query, i)
            # answer['5rewrite_prompt'] = rewrite_prompt

            # yield None, '\n'.join(status), display_resume
            logger.info(f'query:{query},input_token_len:{qwen_lener(final_answer_prompt)}')



            with open(save_file_path, 'w',
                      encoding='utf-8-sig') as f:
                f.write(json.dumps(pipeline_info, ensure_ascii=False, indent=2))

            # final_answer_prompt = '请说你好'
            responce = self.call_llm_model.stream_run(final_answer_prompt,return_raw=True,llm_parameter=llm_parameter)
            pipeline_info['chunk'] = []
            pipeline_info['fileid2url'] = fileid2url
            pipeline_info['fileid2filename'] = fileid2filename
            for i in responce:
                answer = i['answer']
                pipeline_info['raw_answer'] = answer
                if '正在回答问题' not in status:
                    status += [f"正在回答问题"]
                if no_candidate:
                    answer = '没有相关可以参考的文档,以下是大模型引用自己知识的回答：' + '\n\n' + answer
                if '回答encode' not in str(consume):
                    consume.append(['回答encode', time.time() - start])
                # yield clean_cite_text(i), '\n'.join(status), display_resume
                # yield clean_cite_text(i),pipeline_info
                pipeline_info['replace_answer'] = self.replace_fileid_to_jump(answer, fileid2url, fileid2filename)
                pipeline_info['chunk'].append(i['choices'][0]['delta']['content'])
                i['choices'][0]['delta']['content'] = self.replace_fileid_to_jump(i['choices'][0]['delta']['content'], fileid2url, fileid2filename)
                i['fileid2showindex'] = fileid2showindex
                yield i

            pipeline_info['llm_parameter'] = llm_parameter
            logger.info(f'query:{query},output_token_len:{qwen_lener(answer)}')
            #耗时计算
            consume = pd.DataFrame(consume, columns=['步骤', '耗时'])
            consume_time = consume['耗时']
            consume_time = consume_time - consume_time.shift(1)
            consume['耗时'] = consume_time
            consume = consume.dropna()
            consume = consume.set_index('步骤')
            consume = consume.to_dict(orient='records')
            pipeline_info['consume'] = consume


            # print(display_resume,'display_resumedisplay_resumedisplay_resume')
            #     print(answer_output, end="\r")
            # responce = i

            # logger.debug(f'query:{query},answer:{answer}')
            logger.debug(f'流程完毕')
            status += [f"回答完成"]
            # yield clean_cite_text(i), '\n'.join(status), display_resume

            with open(save_file_path, 'w',
                      encoding='utf-8-sig') as f:
                f.write(json.dumps(pipeline_info, ensure_ascii=False, indent=2))
        except Exception as e:
            logger.error('openai解码报错', exc_info=True)
            pipeline_info['error'] = str(e)
            if '模型没上线' not in str(e):
                with open(save_file_path, 'w',
                          encoding='utf-8-sig') as f:
                    f.write(json.dumps(pipeline_info, ensure_ascii=False, indent=2))
            status += [f"大模型报错"]
            # yield '大模型报错', '\n'.join(status), []
            # yield '大模型报错',pipeline_info
            yield '大模型报错'
            # logger.debug(f'query:{query},answer:{answer}')

    def replace_fileid_to_jump(self,answer,fileid2filename,fileid2url):
        for fileid in fileid2filename:
            filename = fileid2filename[fileid]
            url = fileid2url[fileid]
            """<a href="https://example.com" target="_blank">显示文字</a>"""
            answer = answer.replace(fileid,f"""<a href="{url}" target="_blank">{filename}</a>""")
        return answer

if __name__=='__main__':
    import os
    import requests, json, datetime
    import setting


    # filechat_llm = CallOurModel(url='http://region-3.seetacloud.com:19965')
    filechat_llm = CallOurModel(url='http://region-3.seetacloud.com:46181')
    filechat = FileChatPipelineMain(filechat_llm)

    # retrieval = RetrievalMain_bge()
    # queries = ['曾处理过特殊资产和破产重组业务的律师有哪些？处理过哪些项目？']
    from tqdm import tqdm

    # print('开始提交任务')
    # tasks = []
    # session_time = datetime.datetime.now().strftime("%Y-%m-%d_%H_%M_%S")
    #
    # import re
    #
    # model_id = '体检报告'
    # query = '买了航空旅客意外保险，到达目的后，不小心在出机场的发生了意外，可以获得理赔吗？'
    # g = filechat.search_and_chat(model_id,query,retrieval)
    # for i,g in g:
    #     print(i)
    #     pass


    # df = df.sort_values('hash_id')
    # df.to_excel(os.path.join(setting.MAIN_DIR, 'eval', '格式测试_丰姐2.xlsx'))
    # df.to_csv(os.path.join(setting.MAIN_DIR, 'eval', 'predict_result.csv'), encoding='utf-8-sig')

























