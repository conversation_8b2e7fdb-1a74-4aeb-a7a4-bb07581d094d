import datetime
import sys
# sys.path.insert(0,'/root/autodl-tmp/ultra_sammy/aicc-llm-agent')
from collections import defaultdict

import pandas as pd
import requests,json
from setting import logger,FILECHAT_MODEL
import setting
import os
import re,time

# from main.RetrievalMain import Retrieval<PERSON>ain
from model.FileChatAction.LawyerBot import LawyerBot
from model.CallLLM.CallOurModel import CallOurModel

class FileChatPipelineMain:
    def __init__(self,filechat_llm):
        self.answers = defaultdict(dict)
        self.history = defaultdict(list)
        self.call_llm_model = filechat_llm
        self.filechatagent = LawyerBot(filechat_llm)


    def search_and_chat(self,model_id ,query,retrieval=None,update_info={},task_list=[], chat_model_config={},llm_parameter={}):

        session_time = datetime.datetime.now().strftime("%Y-%m-%d_%H_%M_%S")
        # 预测进来的时候就要写self.answers,因为FileChatServer需要根据self.answers判断有没有训练过。
        from hashlib import md5

        answer = {'hash_id': md5(query.encode()).hexdigest(),
                  'starttime': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                  'session_id': query}

        answer.update(update_info)

        # self.answers[session_id][query_id] = answer
        # key = f'chatGptFileQuery_{session_id}_{query_id}'
        start = time.time()
        consume = []
        try:
            # 条件生成
            logger.debug(f'query:{query}')
            df = pd.DataFrame()
            if not task_list:
                yield None, 'agent 选择中...', []
                update_info,task_list = self.filechatagent.get_task(query)
                logger.debug(f"task解析:{update_info['task_llm_output']}")
                answer.update(update_info)

            if 'task2' in task_list and len(task_list)==1:
                from model.FileChatAction.prompt import haiwai_prompt
                final_answer_prompt = haiwai_prompt.replace('@query@',query)

            elif 'task5' in task_list:
                final_answer_prompt = query

            else:
                struct_key = ['姓名', '职务', '执业领域', '邮箱', '电话', '工作经历', '荣誉奖项', '社会职务', '教育背景', '职业资格', '工作语言', '代表业绩']
                logger.debug('1开始生成condition')
                yield None, '正在生成查询条件...', []

                llm_query_condition_output, query_condition_prompt = self.filechatagent.query_condition_generate(query,
                                                                                                            struct_key=struct_key)
                answer['1llm_query_condition_prompt'] = query_condition_prompt
                # 手动condition

                # label_df = pd.read_csv(r"D:\develop\aicc-llm-agent\eval\lawyer_answer_check_label.csv")
                # mapper = dict(zip(label_df['session_id'],label_df['condition_label']))
                # llm_query_condition_output = eval(str(mapper[query]) if '[' in mapper[query] else str(mapper[query].split(',')))
                # query_condition_output_json = {'条件':llm_query_condition_output,'输出':llm_query_condition_output}

                answer['1llm_query_condition_output'] = llm_query_condition_output
                query_condition_output_json = self.filechatagent.get_condition_from_llm(llm_query_condition_output,
                                                                                   struct_key)
                consume.append(time.time() - start)
                logger.debug('1condition完成')

                # 获取条件
                conditions = query_condition_output_json['条件']
                conditions = list(set(conditions + ['工作经历', '代表业绩']))  # 必须检
                conditions = [i for i in conditions if i in struct_key]

                output_key = query_condition_output_json['输出']
                output_key = list(set(output_key) - set(conditions))
                output_key = [i for i in output_key if i in struct_key]

                yield None, '正在搜索资料...', []
                logger.debug('2开始粗排')
                # 2开始粗排
                import requests

                # url = 'http://127.0.0.1:6012/search_lawyer'
                url = 'http://192.168.1.239:6011/search_lawyer'
                answer['2conditions'] = conditions
                topk = 5
                answer['2topk'] = topk
                # model_id =  '240320_v2_col_0320_m3_people_keyword_gpt4qpl_pos1_neg1_epoch1_d0320_pretrain_v2'
                # model_id  =  "240322_col_seg100_0322_m3_people_gpt4log_pos1_neg1_epoch1_d0322_pretrain"
                model_id = "240328_col_seg100_dictseg_p2_0328_people_gpt4log_keywordFB_gpt4loggenN_gpt4labelseg100_manualFAQSEGTWI_pos1_neg1_epoch1_d0328_85344_pretrain"
                # or_and = query_condition_output_json['logit']
                # weight_method = 'max' if or_and=='or' else 'mean'
                # model_id = '240321_v2_col_combine_0320_m3_people_keyword_gpt4qpl_pos1_neg1_epoch1_d0320_pretrain_v2'
                post_data = {'model_id': model_id, "query": query, "field_list": conditions, "is_whole": False,
                             "top_k": topk, "weight_method": "sort_twice1", "vec_type": "dense"}  # best
                # post_data = {"query": query,"field_list": conditions,"model_id": "old","is_whole": False,"top_k": topk,"weight_method": "variance","vec_type": "colbert"}
                answer['2post_data'] = post_data
                retrieval_result = requests.post(url, data=json.dumps(post_data)).json()
                logger.debug('2粗排完成')
                answer['2retrieval_result'] = retrieval_result
                consume.append(time.time() - start)

                names = [i[0].replace(' ', '') for i in retrieval_result['people']]
                text = retrieval_result['text']

                text = [['\n'.join(value) for value in json.loads(i).values()] for i in text]  # 精简text
                text = ['\n'.join(i) for i in text]  # 精简text

                df = pd.DataFrame({'names': names, 'text': text})
                df['index'] = range(1, len(df) + 1)

                #
                # logger.debug('3开始精排')
                # # 窗口筛选候选人
                # llm_rank_json = {'不符合候选人': []}
                #
                # answer['3llm_rank_output'] = ''
                # answer['3llm_rank_prompt'] = ''
                #
                # for window in range(0, len(df), 5):
                #     sec = df.iloc[window:window+5]
                #     sec['index'] = range(1, len(sec) + 1)
                #     sec_candidateid_to_name = dict(zip('候选人' + sec['index'].map(str), sec['names']))
                #
                #     sec_for_llm_rank = '候选人' + sec['index'].map(str) + '\n' + sec['text']
                #     sec_for_llm_rank = '\n'.join(sec_for_llm_rank)
                #
                #     llm_rank_output, llm_rank_prompt = filechatagent.llm_rank_prompt(query=query, contents=sec_for_llm_rank)
                #     answer['3llm_rank_output'] += llm_rank_output + '\n'
                #     answer['3llm_rank_prompt'] += llm_rank_prompt + '\n'
                #
                #     sec_llm_rank_json = filechatagent.get_LLM_json(llm_rank_output)
                #     llm_rank_json['不符合候选人'] += [sec_candidateid_to_name[i.replace('id','')] for i in sec_llm_rank_json['不符合候选人']]
                #
                # # answer['4llm_rank_json'] = llm_rank_json
                # consume.append(time.time() - start)
                # no_good_man = llm_rank_json['不符合候选人']
                # answer['3no_good_man'] = no_good_man
                # df = df[~df['names'].isin(no_good_man)]
                # logger.debug('3精排完成')

                logger.debug('4最后回答开始')
                answer['3good_man'] = df['names'].unique().tolist()
                df['index'] = range(1, len(df) + 1)
                output_key = [i for i in output_key if '姓名' != i]
                if output_key:
                    # output_info_supply = mysql[mysql['姓名'].isin(df['names'].tolist())]
                    # output_info_supply = output_info_supply[list(set(['姓名'] + output_key))]
                    # df = pd.merge(df, output_info_supply, left_on='names', right_on='姓名')
                    for col in output_key:
                        df['text'] = df['text'] + '\n' + col + '\n' + df[col]
                answer['3candidates'] = df['names'].tolist()

                fit_for_llm = '姓名:' + df['names'] + '\n' + df['text'] + '\n'
                fit_for_llm = '\n'.join(fit_for_llm)

                # final_answer_prompt = """@doc@\n\n问题：@query@,列举出所有满足条件的候选人,回答的时候先判断每个人符合与不符合的理由,不要提到不相关的候选人,只说正确的候选人"""
                # final_answer_prompt = """@doc@\n\n问题：@query@,列举出所有满足条件的候选人,不要提到不相关的候选人,只说正确的候选人"""
                # final_answer_prompt = """@doc@\n\n问题：@query@,列举出所有满足条件的候选人,不要提到不相关的候选人,只说正确的候选人"""
#                 final_answer_prompt = """@doc@
#
# 问题：@query@
#
# 请先详细分析问题以及问题中的关键词汇、法律术语以及简历中对应的关键信息，再列举出满足条件的所有候选人,不要提到不相关的候选人,请给出简历原文作为依据。  在输出候选人的理由后，请判断这个人是否符合条件。
# 对于你不确定或者不知道的信息，必须拒识，不要推理、编造或者按照问题修改。 如果你没办法确定候选人符合条件，请不要输出信息。
#
#
# 问题中会涉及到法律术语，对于你不懂的，请告知用户你不懂词的含义。
# 如果有多位律师符合条件，不要遗漏。
#
#
# 先推理再根据简历原文以及推理结果给出结论。
#
# 请使用格式：
# 根据简历知识库中的简历原文，.....。
# 所以.....
#
#
# 可以使用list帮助查看更轻松。"""
                final_answer_prompt = """@doc@
问题：@query@
请根据用户query详细回答问题，列举出所有满足条件的候选人,不要提到不相关的候选人,只说正确的候选人
"""
                final_answer_prompt = final_answer_prompt.replace('@doc@', fit_for_llm)
                final_answer_prompt = final_answer_prompt.replace('@query@', query)
                #
                # # # 回答
                # responce = call_with_messages(fit_for_llm)
                # answer_output = ''
                save_file_name = re.sub('，|。|？|！|：|；|,|\.|\?|!|:|;|、', '',
                                        query) + f'_{session_time}_' + ".json".replace('\n', '').replace('\t',
                                                                                                         '').replace(
                    ' ', '')[0:20]
                with open(os.path.join(setting.MAIN_DIR, 'pretrain_model', save_file_name), 'w',
                          encoding='utf-8-sig') as f:
                    f.write(json.dumps(answer, ensure_ascii=False, indent=2))

                doc_list = final_answer_prompt.split("\n\n", 5)
                temp = ""
                for d in doc_list:
                    yeji = 0
                    jingli = 0
                    for line in d.split("\n"):
                        if (line[:5] == "工作经历:"):
                            if (jingli == 0):
                                jingli += 1
                                line = line.replace("工作经历:", "工作经历:\n")
                            else:
                                line = line[5:]
                        if (line[:5] == "代表业绩:"):
                            if (yeji == 0):
                                yeji += 1
                                line = line.replace("代表业绩:", "代表业绩:\n")
                            else:
                                line = line[5:]
                        temp += line + '\n'
                    temp += "\n"
                final_answer_prompt = temp


            yield None, '正在整理资料，准备回答问题...', []
            answer['4final_answer_prompt'] = final_answer_prompt

            # 非流
            # responce = self.call_llm_model.run(final_answer_prompt)
            # i = responce
            # answer['4answer'] = i
            # # 重写
            # final_answer, rewrite_prompt = self.rewrite(query, i)
            # answer['5rewrite_prompt'] = rewrite_prompt
            # answer['5final_answer'] = final_answer

            # 流式
            responce = self.call_llm_model.stream_run(final_answer_prompt,return_raw=True,llm_parameter=llm_parameter)
            for i in responce:
                answer = i['answer']
                answer['4answer'] = answer
                yield i

            # logger.debug(f'query:{query},answer:{answer}')
            logger.debug(f'流程完毕')
            save_file_name = re.sub('，|。|？|！|：|；|,|\.|\?|!|:|;|、', '',
                                    query) + f'_{session_time}_' + ".json".replace('\n', '').replace('\t','').replace(' ', '')
            with open(os.path.join(setting.MAIN_DIR, 'pretrain_model', save_file_name), 'w',
                      encoding='utf-8-sig') as f:
                f.write(json.dumps(answer, ensure_ascii=False, indent=2))
        except Exception as e:
            logger.error('openai解码报错', exc_info=True)
            answer['error'] = str(e)
            save_file_name = re.sub('，|。|？|！|：|；|,|\.|\?|!|:|;|、', '',
                                    query) + f'_{session_time}_' + ".json"
            with open(os.path.join(setting.MAIN_DIR, 'pretrain_model', save_file_name), 'w',
                      encoding='utf-8-sig') as f:
                f.write(json.dumps(answer, ensure_ascii=False, indent=2))
            yield '大模型报错'
            # logger.debug(f'query:{query},answer:{answer}')

    def rewrite(self, query, content):
        prompt = "@doc@\n\n问题:@query@\n\n上面的简历原文是正确，但是推理过程可能是错误的。\n请只根据上文中的简历原文回答问题并抄写简历原文作为理由，不要参考推理信息。 不要输出不符合的候选人。\n回答格式：\n根据简历库知识....所以..."
        prompt = prompt.replace('@doc@', content)
        prompt = prompt.replace('@query@', query)
        responce = self.call_llm_model.run(prompt)
        i = responce
        return i, prompt


if __name__=='__main__':
    import os
    import requests, json, datetime
    import setting


    queries = pd.read_csv(os.path.join(setting.MAIN_DIR, r'eval/doc2000_eval_data/radom_2000.csv')).sample(frac=1)
    filechat_llm = CallOurModel(url='http://127.0.0.1:6006', temperature=0.1)
    # filechat_llm = CallOurModel(url='http://127.0.0.1:19965', temperature=0.1)
    # filechat_llm = CallOurModel(url='http://region-3.seetacloud.com:19965', temperature=0.1)

    filechat = FileChatPipelineMain(filechat_llm)
    import pandas as pd
    from concurrent.futures import ThreadPoolExecutor
    queries = queries['question'].unique().tolist()

    queries = """想找中国政法大学毕业的硕士以上的律师
我们想寻找一位有多次服务国企解决投资金融法律问题的律师
想找一个有为借壳上市提供服务经验的律师
哪些合伙人有能力撰写招股书
寻找有专利代理师资格，并且从事过专利权纠纷案件的律师
能做集中申报的合伙人
在银行金融行业，处理过离职纠纷的律师
寻找一个有再融资及重大资产重组方面经验的律师
寻找有商标权纠纷处理经验的律师
服务过海外银行的合伙人
在专利侵权赔偿案件方面有相关经验的律师
在跨境交易领域有经验的律师，拥有美国纽约州律师执业资格
职务侵占类案件我们有没有律师在这方面有经验
我想找一个有为理财产品提供法律服务的律师
参与处理过金融集资诈骗类案件的律师有哪些
参与过境内发行上市项目的律师有哪些，他们做过哪些案例
比较擅长争议解决的律师，需要处理公司控制权争夺问题
担任仲裁委员会仲裁员且毕业于北大的人
在投融资并购和跨境交易领域方面有丰富经验的律师
有做过财富管理，并且为信托机构提供过法律服务的律师""".split('\n')


    # queries = ['曾处理过特殊资产和破产重组业务的律师有哪些？处理过哪些项目？']
    from tqdm import tqdm

    print('开始提交任务')
    tasks = []
    session_time = datetime.datetime.now().strftime("%Y-%m-%d_%H_%M_%S")
    # for query in queries:
    #     excutor.submit(filechat.search_and_chat,query,session_time)

    import re

    for query in tqdm(queries):
        # if re.sub('，|。|？|！|：|；|,|\.|\?|!|:|;|、', '',
        #                                 query) + f'_{session_time}_' + ".json" not in os.listdir(os.path.join(setting.MAIN_DIR,'pretrain_model')):
        g = filechat.search_and_chat(query, session_time)
        for _ in g:
            pass

    import os
    import pandas as pd


    df = []
    for file in os.listdir(os.path.join(setting.MAIN_DIR, 'pretrain_model')):
        if session_time in file:
            file = os.path.join(setting.MAIN_DIR, 'pretrain_model', file)
            output = json.loads(open(file, 'r', encoding='utf-8-sig').read())
            output = {i: str(j) for i, j in output.items()}
            df.append(output)
    df = pd.DataFrame(df)
    # df = df.sort_values('hash_id')
    df.to_excel(os.path.join(setting.MAIN_DIR, 'eval', 'predict_result.xlsx'))
    df.to_csv(os.path.join(setting.MAIN_DIR, 'eval', 'predict_result.csv'), encoding='utf-8-sig')

















