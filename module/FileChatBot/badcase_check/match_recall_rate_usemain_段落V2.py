import os
import re
import time

import pandas as pd
import json

rewrite = False


# model_id = '0715演示'
# model_id = '0715所有文档_500'
# model_id = '0715演示_1000_xiaobu'
# model_id = '0715演示_500_m3-law-filechat-23w-seg1000-batch2_ck793'
#
#
# model_id = '0715_model25kv'
# model_id = '0715演示_500_xiaobu-57w-ck976-mix-ori_8_2'
# model_id = '0715_kv_xiaobu-57w-ck976-mix-ori_8_2'
# # model_id = '0715所有文档_500'
#
#
# output_path = '0715演示_result'
# right_id_file = '0715_段落11477id.json'
# sheet_name = '0715演示'
#================================================================================================
model_id = '0731演示_xiaobu_GPTGen4w_ck61_mix_ori_8_2'
model_id = '0731演示_400_火山'
model_id = '0731演示_doubao-embedding-large-text-240915'
model_id = '0731演示_kv_火山'
model_id = '0731演示_1000_无窗口_xiaobu_GPTGen4w_ck61_mix_ori_8_2'
model_id = '0731演示_1000_xiaobu_GPTGen4w_ck61_mix_ori_8_2'
# model_id = '0731演示_xiaobu-57w-ck976-mix-ori_8_2'
# model_id = '0731演示'
# model_id = '0731演示_1000_ck1552'

output_path = '0731演示_result'
right_id_file = '0731_段落id.json'
sheet_name = '0731演示'
#================================================================================================

# model_id = '中国人寿nlp跨文档测试'
# model_id = '中国人寿nlp跨文档测试_kv'
# model_id = '中国人寿nlp跨文档测试_400'
# # model_id = '中国人寿nlp跨文档测试_kv_400'

# model_id = '中国人寿nlp跨文档测试_kv_xiaobu-100w-batch256-step2_ck793'
# model_id = '中国人寿nlp跨文档测试_400_xiaobu-100w-batch256-step2_ck793'
# model_id = '中国人寿nlp跨文档测试_kv_xiaobu-57w-ck366'
# model_id = '中国人寿nlp跨文档测试_kv_xiaobu_GPTGen8w_ck112_mix_ori_8_2'
#
# model_id = '中国人寿nlp跨文档测试_kv_火山'
#
# filename_base = False
# output_path = '中国人寿nlp跨文档_result' if not filename_base else '中国人寿nlp跨文档_result_filename_base'
# right_id_file = '中国人寿nlp跨文档测试_段落id.json'
# sheet_name = '中国人寿nlp跨文档测试'

#################################

is_text_len = True
word_level = True

rerank = None
filename_base = False
rewrite = False





if rewrite:
    output_path +=  '_rewirte'
output_filename = f'段落_{model_id[:100]}_rerank{str(rerank)}_wordlevel{word_level}'
if rewrite:
    output_filename += '_rewrite'
output_filename += '_result.xlsx'
#"C:\Users\<USER>\Desktop\中国人寿nlp跨文档测试_kv_ck2562_all_text.json"









os.makedirs(output_path,exist_ok=True)
from main.RetrievalMain_bge import RetrievalMain_bge

import numpy as np

main = RetrievalMain_bge()
main.load_model(model_id=model_id)



raw = open(right_id_file,encoding='utf-8').read()
raw = json.loads(raw)
#所有中文提取
if '跨文档' in model_id:
    raw = {re.findall(r'[\u4e00-\u9fa5]+',i)[0]:raw[i] for i in raw}

if '0715' in model_id:
    raw = {re.sub(r'\.txt$','',i):raw[i] for i in raw}

if '0731' in model_id:
    raw = {'.'.join(i.split('.')[:-1]):raw[i] for i in raw}

raw = {re.sub(r'\.txt$','',i):raw[i] for i in raw}

index_2_text = {}
for i in raw:
    index_2_text.update({int(n):m for n,m in raw[i].items()})

from collections import defaultdict
text_2_index = defaultdict(list)
for i,j in index_2_text.items():
    text_2_index[j].append(i)

id_2_filename = {}
for i in raw:
    for j in raw[i]:
        id_2_filename[int(j)] = i

t_f_mapper = {}
for i in raw:
    for j in raw[i]:
        t_f_mapper[raw[i][j]] = i

check = pd.DataFrame({"text":text_2_index.keys()})
check['text_len'] = check['text'].apply(lambda x:len(x))
#求check的分位点
check['text_len'].quantile([0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9])

# retrieval_result = pd.read_excel('2024-07-18_retrieval.xlsx')
# retrieval_result = retrieval_result[retrieval_result['model_id']==model_id]

df = pd.read_excel(r"filechat产品测试.xlsx",sheet_name=sheet_name)
df['输入'] = df['输入'].astype(str)
df['正确段落id'] = df['正确段落id'].apply(lambda x:str(x).replace('\n','').replace(' ',''))
# df['正确切片id'] = df['正确切片id'].apply(lambda x:str(x).replace('\n','').replace(' ',''))

from module.FileChatBot.badcase_check.mmmutils import parse_right_ider


excel = pd.ExcelWriter(rf"{output_path}/{output_filename}")


topk_result = []
# for topk in [3,5,10,15,20,30]:
for topk in [3,5,10]:
    output_data = []
    for question, raw_right_id in zip(df['输入'], df['正确段落id']):
        # if '2023年4月到2024年3月之间，上海争议解决部门的业务细分领域为常法的客户有几个？' not in question:
        #     continue
        w = ''
        try:
            if str(raw_right_id) == 'nan' or raw_right_id == '无':
                parse_right_id = [['无']]
            else:
                print('before:', raw_right_id)
                parse_right_id,w = parse_right_ider(raw_right_id)
                print('after:', parse_right_id)
        except:
            import logging
            logging.error(f'error in {raw_right_id}', exc_info=True)
            parse_right_id = [['无']]

        question = str(question)
        real_question = re.sub('\A\d\.','',question)
        real_question = real_question.strip().lower()


        sec = main.predict(model_id, real_question, topk=topk,rerank=rerank,is_text_len=is_text_len,filename_base=filename_base,rewrite=rewrite,debug=True)
        # import sys
        # sys.path.append(r'C:\Users\<USER>\Desktop\火山rag评测')
        # from train_0731_kv import vk_search
        # sec = vk_search(real_question,topk=topk)


        retrival_content = sec['retrival_content']
        sec.pop('retrival_content')
        if '跨文档' in model_id:
            retrival_content['filename'] = [re.findall(r'[\u4e00-\u9fa5]+', i)[0] for i in retrival_content['filename']]
        if '0715' in model_id or '0731' in model_id:
            retrival_content['filename'] = ['.'.join(i.split('.')[:-1]) for i in retrival_content['filename']]


        #搜索最佳答案
        # if type(parse_right_id[0][0]) == int:
        #     from module.FileChatBot.badcase_check.mmmutils import parse_right_ider,get_filename_text,get_best_match
        #     right_id_text, filename_2_match_text = get_filename_text(parse_right_id, id_2_filename, index_2_text, retrival_content)
        #     best_match = get_best_match(right_id_text, filename_2_match_text,max_len=1000*topk)
        #     retrival_content.update(best_match)


        sec.update(retrival_content)

        #再补充
        # supply_sec = main.predict('中国人寿nlp跨文档测试_200', real_question, topk=1, rerank=rerank, text_len=text_len,
        #                    filename_base=filename_base, rewrite=rewrite)
        # sec['match_text']+= supply_sec['retrival_content']['match_text']
        # sec['filename']+= supply_sec['retrival_content']['filename']

        # supply_sec = main.predict('中国人寿nlp跨文档测试_200', real_question, topk=1, rerank=rerank, text_len=text_len,
        #                    filename_base=filename_base, rewrite=rewrite)
        # sec['match_text']+=supply_sec['retrival_content']['match_text']
        # sec['filename']+=supply_sec['retrival_content']['filename']

        model_name = sec.get('model_name','')
        rerank_model_name = sec.get('rerank','')
        docs_for_rerank = retrival_content.get('docs_for_rerank',[])


        # if model_id in sec['model_id'].tolist():
        #     sec = sec[sec['model_id']==model_id]
        assert len(sec)>=1

        # match_text = eval(match_text)
        fit_for_llm = sec.get('fit_for_llm','')
        filename_range_keywords = retrival_content.get('filename_range_keywords',[])
        rewrite_query = retrival_content.get('rewrite_query',[])

        match_text = sec['match_text']
        filenames = sec['filename']
        filename_2_match_text = defaultdict(list)
        for filename, text in zip(filenames, match_text):
            filename_2_match_text[filename].append(text)


        #计算召回率
        from module.FileChatBot.badcase_check.mmmutils import recall_count,get_best_match,get_filename_text
        right_id,find,right_id_text,unhit_text,hit_text,right_gap,right_in_predict_position,recall = recall_count(match_text, parse_right_id, index_2_text, id_2_filename, filename_2_match_text, word_level)
        #xlsx问题不计算召回率
        # if 'xlsx' in str(right_id_text.keys()):
        #     recall = None


        #计算top20最佳
        top20_best_recall_rate = 0
        docs_for_rerank_best_match = {}
        # if type(parse_right_id[0][0]) == int:
        if False:
            docs_for_rerank = main.predict(model_id, real_question, topk=20,rerank=False,is_text_len=False,filename_base=False,rewrite=False,debug=True)
            docs_for_rerank = docs_for_rerank['retrival_content']

            if '跨文档' in model_id:
                docs_for_rerank['filename'] = [re.findall(r'[\u4e00-\u9fa5]+', i)[0] for i in
                                                docs_for_rerank['filename']]
            docs_for_rerank_right_id_text, filename_2_docs_for_rerank = get_filename_text(parse_right_id, id_2_filename, index_2_text, docs_for_rerank)

            docs_for_rerank_match_text = docs_for_rerank['match_text']
            docs_for_rerank_filenames = docs_for_rerank['filename']
            docs_for_rerank_filename_2_match_text = defaultdict(list)
            for filename, text in zip(docs_for_rerank_filenames, docs_for_rerank_match_text):
                docs_for_rerank_filename_2_match_text[filename].append(text)

            docs_for_rerank_best_match = get_best_match(docs_for_rerank_right_id_text, docs_for_rerank_filename_2_match_text,max_len=1000*topk)
            # docs_for_rerank_best_match_best = get_best_match(docs_for_rerank_right_id_text, docs_for_rerank_filename_2_match_text,max_len=100000000)


            docs_for_rerank_filename_2_match_text = defaultdict(list)
            for filename, text in zip(docs_for_rerank_best_match['filename'], docs_for_rerank_best_match['match_text']):
                docs_for_rerank_filename_2_match_text[filename].append(text)

            _,_,_,_,_,_,_,top20_best_recall_rate = recall_count(docs_for_rerank_best_match['match_text'], parse_right_id, index_2_text, id_2_filename, docs_for_rerank_filename_2_match_text, word_level)
            docs_for_rerank = docs_for_rerank['match_text']

        item = {}
        item['question'] = question
        item['filename_range_keywords'] = filename_range_keywords
        item['rewrite_query'] = rewrite_query
        item['raw_right_id'] = raw_right_id
        item['parse_right_id'] = parse_right_id
        item['right_id'] = right_id
        item['recall_id'] = find
        item['right_id_text'] = json.dumps(right_id_text, ensure_ascii=False,indent=2)
        item['filename_2_match_text'] = json.dumps(filename_2_match_text, ensure_ascii=False,indent=2)
        item['recall_id_text'] = json.dumps(match_text,ensure_ascii=False,indent=2)
        item['unhit_text'] = json.dumps(unhit_text, ensure_ascii=False,indent=2)
        item['hit_text'] = json.dumps(hit_text, ensure_ascii=False,indent=2)
        right_id_filename = [t_f_mapper.get(index_2_text.get(i,f'{i}无法找到对应切片文件'),f'{i}无法找到对应切片文件') for i in right_id]
        item['right_id_filename'] = '\n'.join(list(right_id_text.keys()))
        item['recall_id_filename'] = '\n'.join(pd.Series(sec['filename']).drop_duplicates().tolist())
        item['right_gap'] = json.dumps(right_gap, ensure_ascii=False,indent=2)
        item['right_in_predict_position'] = json.dumps(right_in_predict_position, ensure_ascii=False)

        item['docs_for_rerank'] = json.dumps(docs_for_rerank, ensure_ascii=False,indent=2)
        item['docs_for_rerank_best_match'] = json.dumps(docs_for_rerank_best_match.get('match_text',[]), ensure_ascii=False,indent=2)
        if recall!=None:
            item['recall - top20_best_recall_rate'] = recall - top20_best_recall_rate
        item['llm_choose_doc'] = json.dumps(retrival_content.get('llm_choose_doc',[]), ensure_ascii=False,indent=2)

        item['fit_for_llm'] = fit_for_llm
        # item['all_parse_right_id_text'] = ['\n'.join([index_2_text.get(i,f'{i}无法找到对应切片文件') for i in j]) for j in parse_right_id]
        if '无法找到对应切片文件' not in str(right_id_filename):
            item['recall_rate'] = recall
        item['model_name'] = model_name
        item['rerank_model_name'] = rerank_model_name
        output_data.append(item)
        # break
    output_df = pd.DataFrame(output_data)
    print(output_df['recall_rate'].mean(),'topk:',topk)
    output_df['old_recall_rate'] = df['recall_rate']
    output_df['gap'] = output_df['recall_rate'] - output_df['old_recall_rate']
    output_df['min_len_parse_right_id'] = output_df['parse_right_id'].apply(lambda x: x[x.index(min(x, key=lambda x: len(x)))])
    output_df['min_len_parse_right_id_len'] = output_df['min_len_parse_right_id'].apply(lambda x: len(x))
    output_df['min_len_parse_right_id_text'] = output_df['min_len_parse_right_id'].apply(lambda x: '\n'.join([index_2_text.get(i,f'{i}无法找到对应切片') for i in x]))
    output_df['min_len_parse_right_id_text_len'] = output_df['min_len_parse_right_id_text'].apply(lambda x: len(x))
    output_df['max_recall_rate'] = topk*1000/output_df['min_len_parse_right_id_text_len']
    output_df['max_recall_rate'] = output_df['max_recall_rate'].apply(lambda x: min(1,x))


    # output_df.to_excel(rf"段落切片/{model_id[:100]}_{topk}.xlsx")

    topk_result.append({'topk':topk,'recall_rate':output_df['recall_rate'].mean(),
                        'max_recall_rate':output_df[output_df['recall_rate'].notnull()]['max_recall_rate'].mean(),
                        '100%rate':output_df[output_df['recall_rate'].notnull()]['recall_rate'].apply(lambda x: 1 if x>0.95 else 0).mean()
                        })
    output_df.to_excel(excel , sheet_name=f'{topk}')

topk_result_df = pd.DataFrame(topk_result)
topk_result_df['有效样本'] = output_df['recall_rate'].count()
topk_result_df['rerank'] = str(rerank)
topk_result_df['model_id'] = model_id

topk_result_df.to_excel(excel, sheet_name='all')
# excel.save()
excel.close()
print('段落id')
print(f'model_id:{model_id} ,topk:{topk} ,rerank:{rerank}')
# print(topk_result_df[['有效样本','topk','recall_rate','rerank','max_recall_rate']].to_csv(index=False,sep='\t'))
# print(topk_result_df[['有效样本','topk','recall_rate','rerank']].to_csv(index=False,sep='\t'))
show_topk_result_df = topk_result_df.drop(['model_id','max_recall_rate'],axis=1).pivot(index=[ '有效样本','rerank'],columns=['topk'])
show_topk_result_df = show_topk_result_df.reset_index()
show_topk_result_df.columns = [i[0] if i[1]=='' else i[1] for i in show_topk_result_df.columns ]
show_topk_result_df = show_topk_result_df.round(3)
print('model_id',model_id)
print(show_topk_result_df.to_csv(index=False,sep='\t'))



check = output_df[output_df['raw_right_id']!='nan']
check['min_len_parse_right_id_text_len'].describe(percentiles=[0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9])


cc = df[df['question'].isin(check['question'])]

#输入right_id_text和filename_2_match_text，返回3000字内最佳的match_text
# def get_best_match_text(right_id_text,filename_2_match_text):



#重置old recall
# import pandas as pd
# import os
# baseline = pd.read_excel(r"C:\Users\<USER>\Desktop\rewrite_实验\baseline段落_中国人寿nlp跨文档测试_400_rerank20_wordlevelTrue_result.xlsx")
#
#
# root = r'C:\Users\<USER>\Desktop\rewrite_实验'
#
# for file in os.listdir(root):
#     df = pd.read_excel(os.path.join(root,file))
#     df['old_recall_rate'] = baseline['recall_rate']
#     df['gap'] = df['recall_rate'] - df['old_recall_rate']
#     df.to_excel(os.path.join(root,file),index=False)




