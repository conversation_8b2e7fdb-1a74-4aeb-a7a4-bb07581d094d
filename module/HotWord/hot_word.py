import time
import requests
import jieba
from flask import Flask, request
import json
import os
from setting import logger

app = Flask(__name__)


def get_stop_word(path):
    stop_word_list = []
    with open(path, "r", encoding="utf-8") as f:
        for i in f.readlines():
            stop_word_list.append(i.replace("\n", ""))
    return stop_word_list


def compute_word(hot_word, cut_all):
    word_hot_list = jieba.cut(hot_word, cut_all=cut_all)

    word_hot_dict = {}
    # 统计客服热词
    for i in word_hot_list:
        if i not in word_hot_dict:
            word_hot_dict[i] = 1
        else:
            word_hot_dict[i] += 1
    word_hot_sort = sorted(word_hot_dict.items(), key=lambda d: d[1], reverse=True)

    return word_hot_sort


def remove_symbol(word_list: list):
    for i in word_list:
        if i in get_stop_word("stop_words.txt"):
            word_list.remove(i)
    return word_list


def make_call_data(data):
    """
    {
    "id": "id",
    "content": [
        {
            "index": 0,
            "text": "文本",
            "type": "0"
        },
        {
            "index": 0,
            "text": "文本",
            "type": "0"
        }
    ],
    "callBackUrl":"回调地址"
}
    :return:
    回调数据：
    {
    "id":"id",
    "seatHotWord":["热词1"],
    "cusHotWord":["热词1"]
    }
    """
    start_time = time.time()
    info_in_data = {"service": "HotWord", "from": data}
    result = {"sn": "", "code": 0, "msg": "api succeed", "query": "", "data": {}}
    logger.info(json.dumps(info_in_data, ensure_ascii=False))

    sn = data.get("id", "")
    content = data.get("content", [])
    url = data.get("callBackUrl", "")
    model_set = data.get("model", False)

    cus_hot_word = ""
    seat_hot_word = ""
    logger.info(content)
    for i in content:
        if i["type"] == "0":  # 客服
            cus_hot_word += i.get("text", "") + "。"
        else:
            seat_hot_word += i.get("text", "") + "。"

    if model_set:
        cut_all = True
    else:
        cut_all = False

    cus_hot_sort = compute_word(cus_hot_word, cut_all)
    seat_hot_sort = compute_word(seat_hot_word, cut_all)

    count_hot_sort = compute_word(seat_hot_word + "。" + cus_hot_word, cut_all)
    txt_path = os.path.join(os.path.dirname(__file__), "stop_words.txt")
    stop_word = get_stop_word(txt_path)
    result_cus_hot = [i[0] for i in cus_hot_sort[:30] if i[0] not in stop_word and len(i[0]) > 1]
    result_seat_hot = [i[0] for i in seat_hot_sort[:30] if i[0] not in stop_word and len(i[0]) > 1]
    result_count_hot = [i[0] for i in count_hot_sort[:30] if i[0] not in stop_word and len(i[0]) > 1]

    logger.info(result_count_hot)
    logger.info(result_cus_hot)
    logger.info(result_seat_hot)

    # 回调
    if url:
        call_back_data = {
            "id": sn,
            "seatHotWord": result_seat_hot,
            "cusHotWord": result_cus_hot,
            "countHotWord": result_count_hot
        }
        headers = {
            'Content-Type': 'application/json'
        }
        logger.info(f"回调请求：{call_back_data}")
        response = requests.post(url, headers=headers, data=json.dumps(call_back_data), timeout=10)
        logger.info(f"回调响应：{response.text}")

    info_out_data = {"service": "HotWord", "out": result, "time": time.time() - start_time}
    logger.info(json.dumps(info_out_data, ensure_ascii=False))
    return json.dumps(result, ensure_ascii=False)