# -*- coding: utf-8 -*-
# 运行环境python3
# 如果使用出错请注意sse版本是否正确, pip install sseclient-py==1.7.2
import time
import urllib
import uuid
import json
import requests
import sseclient
import hmac
import hashlib
import base64

_SIGN_HOST = "hunyuan.cloud.tencent.com"
_SIGN_PATH = "hyllm/v1/chat/completions"
_URL = "https://hunyuan.cloud.tencent.com/hyllm/v1/chat/completions"


class TencentHyChat:
    def __init__(self, appid, secretid, secretkey, enable_stream):
        self.appid = appid
        self.secretid = secretid
        self.secretkey = secretkey
        self.enable_stream = enable_stream

    def run(self, content):
        request = self.__get_parm(content)
        signature = self.__gen_signature(self.__gen_sign_params(request))
        # print(signature)
        headers = {
            "Content-Type": "application/json",
            "Authorization": str(signature)
        }
        print('Input:\n{} | {} | {}'.format(_URL, headers, request))

        URL = _URL
        resp = requests.post(URL, headers=headers, json=request, stream=True)
        print('Output:')
        outputs = ''
        if self.enable_stream == 1:
            client = sseclient.SSEClient(resp)
            for event in client.events():
                if event.data != '':
                    data_js = json.loads(event.data)
                    try:
                        if data_js['choices'][0]['finish_reason'] == 'stop':
                            print("\n")
                            print(event.data,'output_data')
                            break
                        output = data_js['choices'][0]['delta']['content']
                        if output:
                            outputs+=output
                            yield outputs
                        # print(outputs,'outputs')
                    except Exception as e:
                        print(e)
                        print(data_js)
        else:
            print(resp.json())

    def __get_parm(self, content):
        timestamp = int(time.time()) + 10000
        json_data = {
            "app_id": self.appid,
            "secret_id": self.secretid,
            "query_id": "test_query_id_" + str(uuid.uuid4()),
            "messages": [
                # 第一条role应为user
                {"role": "user", "content": content}
            ],
            "temperature": 0.8,
            "top_p": 0.8,
            "stream": self.enable_stream,
            "timestamp": timestamp,
            "expired": timestamp + 24 * 60 * 60
        }
        return json_data

    def __gen_signature(self, param):
        sort_dict = sorted(param.keys())
        sign_str = _SIGN_HOST + "/" + _SIGN_PATH + "?"
        for key in sort_dict:
            sign_str = sign_str + key + "=" + str(param[key]) + '&'
        sign_str = sign_str[:-1]
        print(sign_str)
        hmacstr = hmac.new(self.secretkey.encode('utf-8'),
                           sign_str.encode('utf-8'), hashlib.sha1).digest()
        s = base64.b64encode(hmacstr)
        s = s.decode('utf-8')
        return s

    def __gen_sign_params(self, data):
        params = dict()
        params['app_id'] = data["app_id"]
        params['secret_id'] = data['secret_id']
        params['query_id'] = data['query_id']
        # float类型签名使用%g方式，浮点数字(根据值的大小采用%e或%f)
        params['temperature'] = '%g' % data['temperature']
        params['top_p'] = '%g' % data['top_p']
        params['stream'] = data["stream"]
        messagestr = ""
        # 数组按照json结构拼接字符串
        for message in data["messages"]:
            content = message["content"]
            messagestr += '{"role":"' + message["role"] + '","content":"' + content + '"},'
        messagestr = messagestr.strip(",")
        print(messagestr)
        params['messages'] = r"[{}]".format(messagestr)
        params['timestamp'] = str(data["timestamp"])
        params['expired'] = str(data["expired"])
        return params

AppId = 1309897870
SecretId = "AKIDYv3vWrxnNhlZ7PTHp8ByVmnY3IHTQ7Yj"
SecretKey = "yf6HDtJTM0NUMyLSIsCG4cuwhCTUFk00"
tx_LLM = TencentHyChat(AppId, SecretId, SecretKey, 1)

if __name__ == "__main__":
    # 将AppId、SecretId、SecretKey替换为自己的即可
    AppId = 1309897870
    SecretId = "AKIDYv3vWrxnNhlZ7PTHp8ByVmnY3IHTQ7Yj"
    SecretKey = "yf6HDtJTM0NUMyLSIsCG4cuwhCTUFk00"
    output = TencentHyChat(AppId, SecretId, SecretKey, 1).run("帮我写一首诗歌\n")
    answer = ''
    for i in output:
        answer+=i
    print('end!!!!!')