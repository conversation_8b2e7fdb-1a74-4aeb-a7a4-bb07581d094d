import requests
import json
import time


url = "http://region-3.seetacloud.com:46181/glm/stream_predict"

def our_LLM(query_id,query):
    payload = json.dumps({
        "query": query,
        "query_id": query_id,
        "history": [],
        "temperature": 0.8
    })
    headers = {
      'Content-Type': 'application/json'
    }
    response = requests.request("POST", url, headers=headers, data=payload)

    while True:
        payload = json.dumps({
          "query_id": query_id,
          "history": [],
          "temperature": 0.8
        })
        response = requests.request("POST", url, headers=headers, data=payload)
        response = json.loads(response.text)
        answer = response["answer"]
        if answer.endswith("@@@@@@END@@@@@@"):
            answer = answer[:-len("@@@@@@END@@@@@@")]
            if answer.endswith('</s>'):
                answer = answer[:-len("</s>")]
            yield answer
            break
        yield answer
        time.sleep(0.1)

    # print(f"END\n{answer}")

if __name__=='__main__':
    query_id = str(time.time())
    for i in our_LLM(query_id,'你是谁'):
        print(i)