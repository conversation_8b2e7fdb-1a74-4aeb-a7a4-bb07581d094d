#!/usr/bin/python
# -*- coding: UTF-8 -*-

class Module:
    def __init__(self):
        pass

    def build_model(self):
        """
        创建模型, 深度学习模型需使用
        """
        pass

    def train(self, model_id, data, save_dir):
        """
        训练模型，并保存模型
        model_id: 模型ID, 必传
        data: 训练数据, 必传
        save_dir: 保存模型的路径, 必传
        :return:
        """
        pass

    def load_model(self, model_id, save_dir):
        """
        加载本地模型，即上线模型
        model_id: 模型ID, 必传
        save_dir: 模型路径, 必传
        """
        pass

    def offline_model(self, model_id):
        """
        下线模型
        model_id: 模型ID, 必传
        """
        pass

    def predict(self, model_id, data):
        """
        用于正常在线预测请求, 支持预测多个数据
        model_id: 模型ID, 必传
        data: 待预测数据 (list), 必传
        :return:
        """
        pass

    def get_data(self, data):
        """
        整理模型需要的数据格式
        data: 待转换格式的数据, 必传
        :return:
        """
        pass

    def get_data_generator(self, data):
        """
        根据批量输入，整理模型需要的数据格式，简单的模型可以不需要
        data: 数据, 必传
        :return:generator
        """
        pass

    def test(self, model_id):
        """
        用于测试, 不一定需要
        model_id: 模型ID, 必传
        """
        pass