# -*- coding: UTF-8 -*-

import json
import os
import pickle
import time
import numpy as np
from tqdm import tqdm
import pandas as pd
import setting

# os.environ['CUDA_VISIBLE_DEVICES'] = "1"
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.metrics.pairwise import normalize
from sklearn.model_selection import train_test_split
from transformers import BertTokenizer, BertConfig

from database.REDIS import REDIS
from module.PreRank.ArcCSEPreRank.ArcCSE import ArcLoss, ArcCSE, ArcMetric
from module.PreRank.ArcCSEPreRank.DataGeneratorPretrain_anto_num import DataGenerator
from setting import logger
from utils.my_utils import MyEncoder, complete_match_text, my_cosine_similarity, RankReturnObject
from model.BM25.BM25 import BM25model, InvertedIndex, HNSWModel, FAISSModel
from model.TraAndSim.TraAndSim import TraAndSim


class ArcCSEPreRankPretrain:
    def __init__(self, need_trans_to_sim=True):
        self.models = dict()
        self.need_trans_to_sim = need_trans_to_sim
        self.tra_sim_model = TraAndSim()
        self.max_len = setting.ARCCSE_PRE_RANK_MAX_LEN
        self.batch_samples = setting.ARCCSE_PRE_RANK_BATCH_SAMPLES
        self.adjusted_ratio = 1
        self.use_arc_loss = setting.ARCCSE_PRE_RANK_USE_ARC_LOSS
        self.use_tri_loss = setting.ARCCSE_PRE_RANK_USE_TRI_LOSS
        self.redis = REDIS()
        self.ratio_name = "adjusted_ratio_" + str(self.__class__.__name__)

    def build_model(self):
        pass

    def train(self, model_id, data, data_test, save_dir, just_test=True, epoch=1):
        start_time = time.time()
        estimated_train_time = self.train_time(data, get_baseline=True)[0]
        logger.debug(f"预估训练耗时：{round(estimated_train_time * self.adjusted_ratio,2)}秒,即{round(estimated_train_time * self.adjusted_ratio / 60, 3)}分钟，使用上一次预估耗时的调整比例值为：{self.adjusted_ratio}")
        logger.debug(f"准备开始训练 - model_id: {model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载预训练模型
        config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
        config.emb_layer = setting.ARCCSE_PRE_RANK_EMB_LAYER
        config.max_len = self.max_len
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)

        if not just_test:
            # 加载数据
            data_train, len_train, data_valid, len_valid, text2norm_id, norm_id2norm_text, text2item_cm, all_text = self.get_data_generator(data=data, tokenizer=tokenizer, emb_dim=config.hidden_size)
            logger.debug(f"数据加载完成 - 训练集数据量: {len_train}, 验证集数据量: {len_valid}")

            # 训练模型
            opt = tf.keras.optimizers.Adam(learning_rate=setting.ARCCSE_PRE_RANK_LEARNING_RATE, epsilon=1e-08)
            loss = ArcLoss(name="sim_loss")
            metrics = [
                ArcMetric(metric_type="auc", name="sim_auc", threshold=setting.ARCCSE_PRE_RANK_THRESHOLD),
                ArcMetric(metric_type="acc", name="sim_acc", threshold=setting.ARCCSE_PRE_RANK_THRESHOLD),
                ArcMetric(metric_type="precision", name="sim_pre", threshold=setting.ARCCSE_PRE_RANK_THRESHOLD),
                ArcMetric(metric_type="recall", name="sim_rec", threshold=setting.ARCCSE_PRE_RANK_THRESHOLD),
                ArcMetric(metric_type="f1", name="sim_f1", threshold=setting.ARCCSE_PRE_RANK_THRESHOLD)
            ]

            model.compile(optimizer=opt, loss=loss, metrics=metrics)

            model_file = os.path.join(save_dir, 'best_model.h5')
            model.save_weights(model_file)
            callbacks = [
                tf.keras.callbacks.ModelCheckpoint(model_file, monitor='sim_f1', save_best_only=True, save_weights_only=True),
                tf.keras.callbacks.ReduceLROnPlateau(monitor='loss', factor=0.2, patience=3),
                tf.keras.callbacks.EarlyStopping(monitor='loss', patience=5, verbose=0), # 当 patience 次迭代损失未改善，Keras停止训练
            ]
            logger.debug(f"开始训练 - model_id: {model_id}")
            history = model.fit(
                data_train,
                epochs=epoch,  # setting.ARCCSE_PRE_RANK_EPOCHS,
                callbacks=callbacks
            )
            os.makedirs(os.path.join(save_dir, "save_pretrained"), exist_ok=True)
            model.bert.save_pretrained(os.path.join(save_dir, "save_pretrained"))
            config.save_pretrained(os.path.join(save_dir, "save_pretrained"))
            tokenizer.save_pretrained(os.path.join(save_dir, "save_pretrained"))
            model.load_weights(os.path.join(save_dir, 'best_model.h5'))
        else:
            config = BertConfig.from_pretrained(os.path.join(save_dir, "save_pretrained"))
            tokenizer = BertTokenizer.from_pretrained(os.path.join(save_dir, "save_pretrained"))
            model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
            inputs = model.get_data(["测试一下"], max_len=128)
            model(inputs)
            model.load_weights(os.path.join(save_dir, 'save_pretrained/tf_model.h5'))
            pass

        # 训练结束，保存测试数据的 embedding
        data_train, len_train, data_valid, len_valid, text2norm_id, norm_id2norm_text, text2item_cm, all_text = self.get_data_generator(data=data_test, tokenizer=tokenizer, emb_dim=config.hidden_size)

        # 获取 all_text 的 embedding
        print(f"ARCCSE的emb layer:{model.emb_layer}")
        embeddings = self.encode_sentences(texts=all_text, model=model)
        embeddings_norm = normalize(embeddings, copy=True)

        # 保存模型
        config.save_pretrained(save_dir)
        tokenizer.save_pretrained(save_dir)
        # with open(os.path.join(save_dir, 'train_history.json'), 'w', encoding='utf-8') as f:
        #     json.dump(history.history, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, "text2norm_id.pkl"), 'wb') as f:
            pickle.dump(text2norm_id, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)
        np.save(os.path.join(save_dir, 'embeddings.npy'), embeddings)
        np.save(os.path.join(save_dir, 'embeddings_norm.npy'), embeddings_norm)

        # 检索模型训练
        if setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "bm25":
            search = BM25model(all_text)
        elif setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "hnsw":
            search = HNSWModel(all_text, embeddings_norm)
        elif setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "faiss":
            search = FAISSModel(all_text, embeddings_norm)
        else:
            search = InvertedIndex(all_text)

        #联想输入模型训练
        context_input_process_search = BM25model(list(norm_id2norm_text.values()))
        with open(os.path.join(save_dir, "search_rank.pkl"), 'wb') as f:
            pickle.dump(search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_context_input.pkl"), 'wb') as f:
            pickle.dump(context_input_process_search, f, pickle.HIGHEST_PROTOCOL)

        # 比例值写入redis
        real_train_time = time.time() - start_time
        self.adjusted_ratio = round(real_train_time / estimated_train_time, 4)
        self.redis.set_data(self.ratio_name, str(self.adjusted_ratio))
        logger.debug(f"修正后预估耗时：{round(estimated_train_time * self.adjusted_ratio,2)}秒,即{round(estimated_train_time * self.adjusted_ratio / 60, 3)}分钟，更新预估耗时的调整比例值为：{self.adjusted_ratio}")

        # 训练成功
        logger.debug(f"训练成功 - model_id:{model_id}, 耗时:{time.time() - start_time}s,数据量:{len_train + len_valid}, batch:{setting.BERT_CLASSIFICATION_BATCH_SIZE}, epoch:{setting.BERT_CLASSIFICATION_EPOCHS}")
        try:
            return 0
            # best_idx = np.argmax(history.history["val_sim_auc"])
            # return history.history["val_sim_acc"][best_idx]
        except:
            return 0

    def load_model(self, model_id, save_dir):
        try:
            tokenizer = BertTokenizer.from_pretrained(save_dir)
            config = BertConfig.from_pretrained(save_dir)
            model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
            inputs = model.get_data(["测试一下"], max_len=128)
            model(inputs)
            try:
                model.load_weights(os.path.join(save_dir, 'best_model.h5'))
            except:
                model.load_weights(os.path.join(save_dir, 'best_model.ckpt')).expect_partial()
                logger.debug(f"old model, use ckpt")

            with open(os.path.join(save_dir, "text2norm_id.pkl"), 'rb') as f:
                text2norm_id = pickle.load(f)
            with open(os.path.join(save_dir, "norm_id2norm_text.pkl"), 'rb') as f:
                norm_id2norm_text = pickle.load(f)
            with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
                all_text = pickle.load(f)
            with open(os.path.join(save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)
            embeddings = np.load(os.path.join(save_dir, 'embeddings.npy'), allow_pickle=True)
            embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm.npy'), allow_pickle=True)

            # END (兼容旧模型) 检查是否有 search 文件，如果不存在要重新生成
            with open(os.path.join(save_dir, "search_rank.pkl"), 'rb') as f:
                search = pickle.load(f)
            with open(os.path.join(save_dir, "search_context_input.pkl"), 'rb') as f:
                context_input_process_search = pickle.load(f)

            all_text_2_idx = dict()
            for i, text in enumerate(all_text):
                all_text_2_idx[text] = i

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['model'] = model
            self.models[model_id]['text2norm_id'] = text2norm_id
            self.models[model_id]['norm_id2norm_text'] = norm_id2norm_text
            self.models[model_id]['all_text'] = all_text
            self.models[model_id]['all_text_2_idx'] = all_text_2_idx
            self.models[model_id]['text2item_cm'] = text2item_cm
            self.models[model_id]['embeddings'] = embeddings
            self.models[model_id]['embeddings_norm'] = embeddings_norm.astype(np.float16)
            self.models[model_id]['search'] = search
            self.models[model_id]['context_input_process_search'] = context_input_process_search

            self.predict(model_id=model_id, query="测试一下", topk=2)
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, query, query_list=None, topk=50, context_input_process='', test_flag=False):
        result = []
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": ""
        }

        if model_id not in self.models:
            try:
                is_exist = self.check_model_file_exist(model_id=model_id)
                if is_exist:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91024"
                    error_dict["error_msg"] = "预测错误: 模型未加载，请重新上线"
                else:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91025"
                    error_dict["error_msg"] = "预测错误: 模型未加载，模型文件缺失，请重新训练"
            except Exception as ee:
                error_dict["error_type"] = 0
                error_dict["error_code"] = "NLU91017"
                error_dict["error_msg"] = f"预测错误: 模型未加载, {ee}"
            logger.error(f"预测失败 [{model_id}], 错误信息: {error_dict['error_msg']}")
            return result, error_dict

        norm_id2norm_text = self.models[model_id]['norm_id2norm_text']
        text2norm_id = self.models[model_id]['text2norm_id']
        all_text = self.models[model_id]['all_text']
        all_text_2_idx = self.models[model_id]['all_text_2_idx']
        text2item_cm = self.models[model_id]['text2item_cm']
        embeddings_norm = self.models[model_id]['embeddings_norm']
        model = self.models[model_id]['model']
        search = self.models[model_id]['search']
        context_input_process_search = self.models[model_id]['context_input_process_search']

        #联想输入
        if context_input_process:
            context_input_process = int(context_input_process) if int(context_input_process) != 1 else 10
            recall_text = context_input_process_search.search(query, 200, True)[:context_input_process]
            recall_id = [text2norm_id.get(i, '') for i in recall_text]
            return (recall_id, recall_text), error_dict

        # 精确匹配
        if not test_flag:
            if query_list is None or len(query_list) == 0:
                query_list = [query]
            for temp_query in query_list:
                temp_query = complete_match_text(temp_query)
                if temp_query in text2item_cm:
                    norm_query, norm_query_id = text2item_cm[temp_query]
                    item = RankReturnObject(query=query, sim_query=temp_query, norm_query=norm_query,
                                            norm_query_id=norm_query_id, score=1, save_bigger=True)
                    result.append(item)
                    return result, error_dict

        # 粗排
        emb = self.encode_sentences(texts=[query], model=model)

        if setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "hnsw":
            recall_text, _ = search.search(emb, min(len(all_text), setting.ARCCSE_PRE_RANK_SEARCH_RETURN))
        elif setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "faiss":
            recall_text = search.search(emb, setting.ARCCSE_PRE_RANK_SEARCH_RETURN)
        else:
            recall_text = search.search(query, setting.ARCCSE_PRE_RANK_SEARCH_RETURN)

        if test_flag:
            recall_text = all_text

        if not test_flag:
            logger.debug(f"粗排结束: 检索数据量 {len(recall_text)}")
        indexer = [all_text_2_idx.get(i, 0) for i in recall_text]
        select_emb = embeddings_norm[indexer]

        scores = my_cosine_similarity(select_emb, emb.astype(np.float16))[:, 0]
        scores = scores.clip(min=0, max=1)
        index = np.argsort(scores)
        index = index[::-1]
        norm_query_id_set = set()
        if not test_flag:
            logger.debug(f"排序结束")

        for idx in index:
            sim_query = recall_text[idx]
            if test_flag and sim_query == query:
                continue
            norm_query_id = text2norm_id[sim_query]
            if norm_query_id not in norm_query_id_set:
                norm_query_id_set.add(norm_query_id)
                norm_query = norm_id2norm_text[norm_query_id]
                item = RankReturnObject(query=query, sim_query=sim_query, norm_query=norm_query,
                                        norm_query_id=norm_query_id, score=scores[idx], save_bigger=True)
                result.append(item)
            if len(result) >= topk:
                break
        if not test_flag:
            logger.debug(f"返回结果: {result}")
        return result, error_dict

    def predict_emb(self, model_id, query_list=None):
        norm_id2norm_text = self.models[model_id]['norm_id2norm_text']
        text2norm_id = self.models[model_id]['text2norm_id']
        all_text = self.models[model_id]['all_text']
        all_text_2_idx = self.models[model_id]['all_text_2_idx']
        text2item_cm = self.models[model_id]['text2item_cm']
        embeddings_norm = self.models[model_id]['embeddings_norm']
        model = self.models[model_id]['model']
        search = self.models[model_id]['search']
        context_input_process_search = self.models[model_id]['context_input_process_search']

        # 粗排
        emb = self.encode_sentences(texts=query_list, model=model)
        return emb

    def labeling(self, model_id, texts, text_ids=None):
        labeling_result = []

        text2norm_id = self.models[model_id]['text2norm_id']
        all_text = self.models[model_id]['all_text']
        embeddings_norm = self.models[model_id]['embeddings_norm'] # [m, dim]
        model = self.models[model_id]['model'] # [m, dim]

        embeddings = self.encode_sentences(texts=texts, model=model) # [n, dim]

        scores = my_cosine_similarity(embeddings_norm, embeddings) # [m, n]
        max_idx = np.argmax(scores, axis=0)

        for i in range(max_idx.shape[0]):
            result = {
                "text": texts[i],
                "id": text_ids[i] if text_ids is not None else None,
                "label": text2norm_id[all_text[max_idx[i]]],
                "score": scores[max_idx[i], i],
                "margin_score": scores[max_idx[i], i]
            }
            labeling_result.append(result)
        return labeling_result

    def get_data(self, data):
        pass

    def get_data_generator(self, data, tokenizer=None, emb_dim=312):
        # 整理数据
        text2norm_id = dict()
        norm_id2norm_text = dict()
        text2item_cm = dict() # 精确匹配字典
        text_list_train = []
        text_list_valid = []

        for data_dict in tqdm(data):
            norm_query = data_dict['title'].strip().lower()
            if len(norm_query) == 0:
                continue
            norm_query_id = data_dict['labelId']
            texts = set()
            texts.add(norm_query)

            norm_id2norm_text[norm_query_id] = norm_query
            text2norm_id[norm_query] = norm_query_id
            norm_query_ = complete_match_text(norm_query)
            if len(norm_query_):
                text2item_cm[norm_query_] = [norm_query, norm_query_id]

            for text in data_dict["labelData"].split("||"):
                text = text.strip().lower()
                if len(text):
                    texts.add(text)
                    text2norm_id[text] = norm_query_id
                text = complete_match_text(text)
                if len(text):
                    text2item_cm[text] = [norm_query, norm_query_id]

            texts = list(texts)
            if self.need_trans_to_sim:
                texts = [self.tra_sim_model.tra_or_sim(t, target="sim") for t in texts]
            if len(texts) <= 0:
                continue
            else:
                if "anto" in data_dict and len(texts) < 2:
                    texts.append(texts[0])
                text_list_train.append(texts)
            # elif len(texts) == 1:
            #     text_list_train.append(texts)
            # else:
            #     texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
            #     text_list_train.append(texts_train)
            #     text_list_valid.append(texts_valid)

        all_text = list(text2norm_id.keys())

        num_train_text = np.sum([len(l) for l in text_list_train])
        num_valid_text = np.sum([len(l) for l in text_list_valid])
        data_gen_train = DataGenerator(text_list=text_list_train, tokenizer=tokenizer, dimension=emb_dim)
        data_gen_valid = DataGenerator(text_list=text_list_valid, tokenizer=tokenizer, dimension=emb_dim) if num_valid_text else None
        return data_gen_train, num_train_text, data_gen_valid, num_valid_text, text2norm_id, norm_id2norm_text, text2item_cm, all_text

    def encode_sentences(self, texts, model):
        embeddings = list()

        for start_idx in range(0, len(texts), setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE):
            end_idx = min(start_idx+setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE, len(texts))
            inputs = model.get_data(texts=texts[start_idx:end_idx], max_len=self.max_len, return_tensor=True)
            emb = model.get_encoder_layer(**inputs)
            embeddings.append(emb.numpy())

        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings

    def check_model_file_exist(self, model_id):
        model_file_exist = True
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")

        try:
            tokenizer = BertTokenizer.from_pretrained(save_dir)
            config = BertConfig.from_pretrained(save_dir)
            _ = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
            with open(os.path.join(save_dir, "text2norm_id.pkl"), 'rb') as f:
                _ = pickle.load(f)
            with open(os.path.join(save_dir, "norm_id2norm_text.pkl"), 'rb') as f:
                _ = pickle.load(f)
            with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
                _ = pickle.load(f)
            with open(os.path.join(save_dir, "text2item_cm.pkl"), 'rb') as f:
                _ = pickle.load(f)
            with open(os.path.join(save_dir, "search_rank.pkl"), 'rb') as f:
                _ = pickle.load(f)
            _ = np.load(os.path.join(save_dir, 'embeddings.npy'), allow_pickle=True)
            _ = np.load(os.path.join(save_dir, 'embeddings_norm.npy'), allow_pickle=True)
        except:
            model_file_exist = False
        return model_file_exist

    def test_acc(self, model_id, data, topk=5, model_save_dir="", result_name=""):
        """
        用 data 中的文本测试 topk 准确率, 需要返回 topk+1 个结果，因为包含自身。
        耗时: 30 分钟
        """
        total = 0
        acc = [0] * topk
        error_df = {"query": [], "true": [], "pred": [], "scores": [], "sim_query": []}
        norm_id2norm_text = self.models[model_id]['norm_id2norm_text']
        text2norm_id = self.models[model_id]['text2norm_id']

        # 整理数据
        text_list_valid = []
        text2norm_id = {}
        for data_dict in data:
            norm_query = data_dict['title'].strip().lower()
            norm_query_id = data_dict['labelId']
            texts = set()
            texts.add(norm_query)
            text2norm_id[norm_query] = norm_query_id
            for text in data_dict["labelData"].split("||"):
                text = text.strip().lower()
                if len(text):
                    texts.add(text)
                    text2norm_id[text] = norm_query_id
            texts = list(texts)
            if len(texts) > 1:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                text_list_valid.append(texts_valid)

        pbar = tqdm(text_list_valid)
        for text_list in pbar:
            pbar.set_description('测试进度')
            for text in text_list:
                norm_query_id = text2norm_id[text.lower()]
                norm_intent = norm_id2norm_text[norm_query_id]
                heap, _ = self.predict(model_id, text, topk=topk, test_flag=True)
                pred_ids = np.array([item.norm_query_id for item in heap])
                pred_intent = [norm_id2norm_text[item.norm_query_id] for item in heap]
                pred_scores = [item.score for item in heap]
                sim_query = [item.sim_query for item in heap]
                is_error = True
                for j in range(min(len(heap), topk)):
                    if norm_query_id in pred_ids[:j + 1]:
                        for k in range(j, topk):
                            acc[k] += 1
                            is_error = False
                        break
                if is_error:
                    error_df["query"].append(text)
                    error_df["true"].append(norm_intent)
                    error_df["pred"].append(pred_intent)
                    error_df["scores"].append(pred_scores)
                    error_df["sim_query"].append(sim_query)

                total += 1
            acc_dict = {f'acc{1}': acc[0] / total}
            pbar.set_postfix(acc_dict)

        with open(os.path.join(model_save_dir, f'test_result_{result_name}.txt'), 'w', encoding='utf-8') as f:
            for i in range(topk):
                line = f'top {i + 1} 准确率: {acc[i] / total}'
                print(line)
                f.writelines(line + '\n')
        error_df = pd.DataFrame(error_df)
        error_df.to_csv(os.path.join(model_save_dir, f"error_df_{result_name}.csv"), encoding="utf-8-sig", index=False)

    def test_threshold(self, model_id, data_pos, data_neg, model_save_dir):
        # 正样本
        all_texts = set()
        for data_dict in data_pos:
            norm_query = data_dict['title'].strip()
            texts = set()
            texts.add(norm_query)

            for text in data_dict["labelData"].split("||"):
                text = text.strip()
                if len(text):
                    texts.add(text)

            texts = list(texts)
            if len(texts) <= 1:
                continue
            else:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                for text in texts_valid:
                    all_texts.add(text)

        scores = []
        for text in tqdm(all_texts):
            heap = self.predict(model_id, text, topk=2)
            if heap[0].sim_query == text:
                scores.append(heap[1].score)
            else:
                scores.append(heap[0].score)

        with open(os.path.join(model_save_dir, 'test_threshold_pos.json'), 'w', encoding='utf-8') as f:
            json.dump(scores, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

        # 负样本
        all_texts = set()
        for data_dict in data_neg:
            norm_query = data_dict['title'].strip()
            all_texts.add(norm_query)
            for text in data_dict["labelData"].split("||"):
                text = text.strip()
                if len(text):
                    all_texts.add(text)

        scores = []
        for text in tqdm(all_texts):
            heap = self.predict(model_id, text, topk=1)
            scores.append(heap[0].score)

        with open(os.path.join(model_save_dir, 'test_threshold_neg.json'), 'w', encoding='utf-8') as f:
            json.dump(scores, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

    def train_time(self, data, get_baseline=False):
        # ratio = 2.9
        num_text = 0
        for data_dict in data:
            num_text += 1
            texts = data_dict["labelData"].strip()
            num_text += len(texts.split("||"))
        num_batches = max(num_text//setting.ARCCSE_PRE_RANK_BATCH_SAMPLES, 1)
        epoch = setting.ARCCSE_PRE_RANK_EPOCHS

        if not get_baseline:
            try:
                self.adjusted_ratio = float(self.redis.get_data(self.ratio_name))
                logger.debug(f"读取redis的{self.ratio_name}值为：{self.adjusted_ratio}")
            except:
                logger.debug(f"读取redis的{self.ratio_name}值失败")
        estimated_time = num_batches * epoch # 基准值
        adjusted_estimated_time = num_batches * epoch * self.adjusted_ratio

        return estimated_time, adjusted_estimated_time


if __name__ == "__main__":
    import pandas as pd
    import random

    # 初始化
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    model = ArcCSEPreRankPretrain()
    R = REDIS()
    # data_test = R.get_data(f'webank_train_data_1393_0902')[:]
    data_test = R.get_data(f'faq_model1_all')[:]

    # 准备数据
    # data_list = []
    # for file in ["../../../data/百度知道问答/output.csv", "../../../data/百度知道问答/output1.csv"]:
    #     df = pd.read_csv(file)
    #     for i in range(df.shape[0]):
    #         answer = df["answer"][i]
    #         title = df["title"][i]
    #         title = title.strip().replace("\n", "").replace(" ", "").replace("\t", "")
    #         answer = answer.strip().replace("\n", "").replace(" ", "").replace("\t", "")
    #         answer_list = []
    #         for _ in range(10):
    #             random_length = int(len(answer) * (random.random() * 0.7 + 0.3))
    #             start_idx = random.randint(0, len(answer)-random_length)
    #             answer_cut = answer[start_idx:start_idx+random_length]
    #             answer_list.append(answer_cut)
    #         data_list.append({"labelId": f"{len(data_list)}", "title": title, "keywords": "", "labelData": "||".join(answer_list)})
    with open("/data/cbk/NLP_Platform/data/语义匹配/all_sts_pretrain_data_with_our_anto.json", "r", encoding="utf-8") as f:
        data_list = json.load(f)

    # 预训练模型
    start = time.time()
    model_id = f"pretrain_tiny_arccse{setting.ARCCSE_PRE_RANK_LOSS}_arc{setting.ARCCSE_PRE_RANK_USE_ARC_LOSS}_tri(replace){setting.ARCCSE_PRE_RANK_USE_TRI_LOSS}_esimcse_shuffle(b_t)_batch{setting.ARCCSE_PRE_RANK_BATCH_SAMPLES}_epoch1_out_anto"
    # model_id = f"pretrain_tiny"
    # model_id = "pretrain_tiny_arccse2n_arcTrue_tri2_1_9(not_replace02)True_batch64_epoch1_t0.7_allsts"
    model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{model.__class__.__name__}/")
    score = model.train(model_id=model_id, data=data_list, data_test=data_test, save_dir=model_save_dir, just_test=False)
    train_time = time.time()-start
    print(f"训练得分: {score}, 耗时: {train_time}")
    print("wait")

    # 加载模型
    model.load_model(model_id=model_id, save_dir=model_save_dir)

    # 测试准确率
    start = time.time()
    # model.test_acc(model_id=model_id, data=data_test, topk=5, model_save_dir=model_save_dir, result_name="webank_1393")
    model.test_acc(model_id=model_id, data=data_test, topk=5, model_save_dir=model_save_dir, result_name="tencent_boc")
    print(f"测试准确率耗时: {time.time()-start}, 训练耗时:{train_time}")
    # print("wait")
    #
    # from sklearn.metrics.pairwise import cosine_similarity
    # emb = model.predict_emb(model_id=model_id, query_list=[
    #     "快速赎回的余额理财我什么时候能收到钱",
    #     "我快速赎回余额理财具体什么时候收到钱",
    #     "快速赎回的余额理财哪个时间点能收到钱",
    #     "余额理财快速赎回到账时间",
    #     "余额理财普通赎回到账时间",
    #     "快速赎回的余额理财我哪个时候能收到钱",
    #     "普通赎回的余额理财我什么时候能收到钱",
    #     "快速赎回的余额理财我什么时间能收钱",
    #     "普通赎回的余额理财我什么时间能收钱",
    #     "快速赎回余额理财我什么时间能收钱",
    #     "普通赎回余额理财我什么时间能收钱",
    #     "我能用手机银行把我的中银理财给赎回吗"])
    # print(cosine_similarity(emb[0:1, :], emb[1:, :]))
    #
    # emb = model.predict_emb(model_id=model_id, query_list=[
    #     "通常的稳利季季增利k赎回到账时效是多久",
    #     "稳利季季增利k赎回到通常要多长时间到账",
    #     "一般情况下稳利季季增利k赎回的到账时间",
    #     "稳利季季增利k赎回到账时间",
    #     "稳利季季增利f赎回到账时间",
    #     "通常的稳利季季增利k赎回到账时间是多久",
    #     "通常的稳利季季增利f赎回到账时效是多久",
    #     "通常的稳利季季增利k赎回到账时效多久",
    #     "通常的稳利季季增利f赎回到账时效多久",
    #     "通常稳利季季增利k赎回到账时效多久",
    #     "通常稳利季季增利f赎回到账时效多久"])
    # print(cosine_similarity(emb[0:1, :], emb[1:, :]))
    #
    # emb = model.predict_emb(model_id=model_id, query_list=[
    #     "为什么花呗账单显示还要还款",
    #     "花呗全额还清怎么显示没有还款"])
    # print(cosine_similarity(emb[0:1, :], emb[1:, :]))
    # emb = model.predict_emb(model_id=model_id, query_list=[
    #     "借呗一般是多久评估",
    #     "大概多久评估一次借呗条件"])
    # print(cosine_similarity(emb[0:1, :], emb[1:, :]))
    # emb = model.predict_emb(model_id=model_id, query_list=[
    #     "么，这个是不是我现在不能不能用花呗",
    #     "我的花呗不能用了是吧"])
    # print(cosine_similarity(emb[0:1, :], emb[1:, :]))
