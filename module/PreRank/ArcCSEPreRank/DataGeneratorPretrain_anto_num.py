# -*- coding: UTF-8 -*-

import random

import numpy as np
from tensorflow import keras
import cn2an
import setting
from database.REDIS import REDIS
# import synonyms
import jieba
import string
import re


class DataGenerator(keras.utils.Sequence):
    def __init__(self, text_list, tokenizer, dimension=312):
        self.text_list = text_list
        self.idx_list = [i for i in range(len(text_list))]
        self.tokenizer = tokenizer
        self.batch_num_samples = min(len(text_list), setting.ARCCSE_PRE_RANK_BATCH_SAMPLES)
        self.num_text = np.sum([len(t) for t in self.text_list])
        self.dimension = dimension
        self.num_batches = max(len(self.idx_list)//self.batch_num_samples, 1)
        self.item_idx_list = [i for i in range(self.num_batches)]
        random.shuffle(self.item_idx_list)
        self.en_digits = string.digits+string.ascii_lowercase+string.ascii_uppercase
        self.zh_words = set()
        self.use_cls = False
        with open("/data/cbk/NLP_Platform/data/中文词库/webdict_with_freq.txt", "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip().split(" ")[0]
                if len(line) <= 5:
                    self.zh_words.add(line)
        self.zh_words = list(self.zh_words)

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return self.num_batches

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        true_index = self.item_idx_list[index]
        X, Y = self.__data_generation(self.idx_list[true_index*self.batch_num_samples:(true_index+1)*self.batch_num_samples])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        pass

    def __data_generation(self, idx_list):
        """
        生成 cur_id 的数据。
        """
        input_ids = np.zeros(shape=(self.batch_num_samples*2, setting.ARCCSE_PRE_RANK_MAX_LEN), dtype=np.int32)
        token_type_ids = np.zeros(shape=(self.batch_num_samples*2, setting.ARCCSE_PRE_RANK_MAX_LEN), dtype=np.int32)
        attention_mask = np.zeros(shape=(self.batch_num_samples*2, setting.ARCCSE_PRE_RANK_MAX_LEN), dtype=np.int32)
        labels = np.zeros(shape=(self.batch_num_samples*2, 1))
        input_ids_tri = np.zeros(shape=(self.batch_num_samples*3, setting.ARCCSE_PRE_RANK_MAX_LEN), dtype=np.int32)
        token_type_ids_tri = np.zeros(shape=(self.batch_num_samples*3, setting.ARCCSE_PRE_RANK_MAX_LEN), dtype=np.int32)
        attention_mask_tri = np.zeros(shape=(self.batch_num_samples*3, setting.ARCCSE_PRE_RANK_MAX_LEN), dtype=np.int32)
        labels_tri = np.zeros(shape=(self.batch_num_samples*3, 1))

        for i, idx in enumerate(idx_list):
            for j in range(2):
                # 一个 idx 采样两个样本，作为正样本
                if j < len(self.text_list[idx]):
                    text = self.text_list[idx][j]
                else:
                    text = self.text_list[idx][0]
                    if j != 0 and "吗" in text[-2:] and random.random() > 0.8:
                        text = text[:-2] + text[-2:].replace("吗", "不")
                    number_str = re.findall("\d+[\.]?\d*", text)
                    if len(number_str) and random.random() > 0.5:
                        try:
                            # 随机将数字替换成中文数字
                            number_str.sort(key=lambda x: -len(x))
                            number_str_zh = cn2an.an2cn(number_str[0])
                            text = text.replace(number_str[0], number_str_zh)
                        except:
                            pass
                    if j != 0 and len(text) > 20 and random.random() > 0.5:
                        temp_idx = random.randint(int(len(text)*0.4), int(len(text)*0.6))
                        if text[temp_idx] not in "01234567890一二三四五六七八九十百千万亿点.":
                            # 有数字的地方不能替换
                            text = text[temp_idx:] + text[:temp_idx]

                text = self.esimcse_repeat_word(text)

                inputs = self.tokenizer.encode_plus(text, add_special_tokens=True, max_length=setting.ARCCSE_PRE_RANK_MAX_LEN, truncation=True, padding="max_length")
                input_ids[i*2+j, :] = inputs["input_ids"]
                attention_mask[i*2+j, :] = inputs["attention_mask"]
                token_type_ids[i*2+j, :] = inputs["token_type_ids"]
                if j == 0 and setting.ARCCSE_PRE_RANK_USE_TRI_LOSS:
                    rand_num = random.random()
                    if rand_num < 0.1:
                        text_2_remove_length = int(len(text)*0.4)
                        start_idx = random.randint(0, len(text)-text_2_remove_length)
                        text2 = text[:start_idx] + text[start_idx+text_2_remove_length:]
                        text1 = text[:start_idx] + text[start_idx+text_2_remove_length//2:]
                    else:
                        text_word_list = jieba.lcut(text)
                        if len(text_word_list) < 2:
                            text_word_list = text_word_list + [""] * (2-len(text_word_list))
                        text_word_list_replace = self.random_replace_word(text_word_list)
                        text1 = "".join(text_word_list_replace[0])
                        text2 = "".join(text_word_list_replace[1])

                    inputs = self.tokenizer.encode_plus(text, add_special_tokens=True, max_length=setting.ARCCSE_PRE_RANK_MAX_LEN, truncation=True, padding="max_length")
                    input_ids_tri[i * 3, :] = inputs["input_ids"]
                    token_type_ids_tri[i * 3, :] = inputs["attention_mask"]
                    attention_mask_tri[i * 3, :] = inputs["token_type_ids"]
                    inputs = self.tokenizer.encode_plus(text1, add_special_tokens=True, max_length=setting.ARCCSE_PRE_RANK_MAX_LEN, truncation=True, padding="max_length")
                    input_ids_tri[i * 3 + 1, :] = inputs["input_ids"]
                    token_type_ids_tri[i * 3 + 1, :] = inputs["attention_mask"]
                    attention_mask_tri[i * 3 + 1, :] = inputs["token_type_ids"]
                    inputs = self.tokenizer.encode_plus(text2, add_special_tokens=True, max_length=setting.ARCCSE_PRE_RANK_MAX_LEN, truncation=True, padding="max_length")
                    input_ids_tri[i * 3 + 2, :] = inputs["input_ids"]
                    token_type_ids_tri[i * 3 + 2, :] = inputs["attention_mask"]
                    attention_mask_tri[i * 3 + 2, :] = inputs["token_type_ids"]


        if setting.ARCCSE_PRE_RANK_USE_TRI_LOSS:
            input_ids = np.vstack([input_ids, input_ids_tri])
            token_type_ids = np.vstack([token_type_ids, token_type_ids_tri])
            attention_mask = np.vstack([attention_mask, attention_mask_tri])
            labels = np.vstack([labels, labels_tri])

        length_mask_shape = [s for s in input_ids.shape]
        length_mask_shape.append(self.dimension)
        length_mask = np.zeros(shape=length_mask_shape)
        if not self.use_cls:
            lengths = np.sum(attention_mask, -1) - 2
            for i, l in enumerate(lengths):
                length_mask[i, 1:l + 1, :] = 1
                if l < 1:
                    raise Exception("句子长度有问题")
        else:
            lengths = np.sum(attention_mask, -1)
            for i, l in enumerate(lengths):
                length_mask[i, 0:l, :] = 1
                if l < 3:
                    raise Exception("句子长度有问题")

        length_mask_shape.pop(-2)
        length_div = np.ones(shape=length_mask_shape)
        for i, l in enumerate(lengths):
            length_div[i, :] = l

        return (
            {
                'input_ids': input_ids,
                'token_type_ids': token_type_ids,
                'attention_mask': attention_mask,
                'length_mask': length_mask,
                'length_div': length_div
            },
            labels
        )

    def random_replace_word(self, text_word_list):
        text_word_idx = [idx for idx in range(len(text_word_list))]
        random.shuffle(text_word_idx)
        text_word_list_replace = [text_word_list.copy(), text_word_list.copy()]
        for i in range(2):
            word = text_word_list[text_word_idx[i]]
            new_word = ""
            if i == 0 and random.random() <= 0.2:
                new_word = word

            # # 先找相似词
            # if not new_word and len(word):
            #     sim_words = synonyms.nearby(word, 10)
            #     sim_words = sim_words[0]
            #     if len(sim_words):
            #         new_word = random.choice(sim_words[1:])
            # # 相似字
            # if not new_word and len(word):
            #     char_idx = random.randint(0, len(word)-1)
            #     sim_chars = synonyms.nearby(word[char_idx], 10)
            #     sim_chars = sim_chars[0]
            #     if len(sim_chars):
            #         sim_char = random.choice(sim_chars[1:])
            #         new_word = word[:char_idx] + sim_char + word[char_idx+1:]

            # 判断是否为数字和英文
            if not new_word and len(word):
                all_en_digits = True
                for c in word:
                    if c not in self.en_digits:
                        all_en_digits = False
                        break
                if all_en_digits:
                    char_idx = random.randint(0, len(word)-1)
                    while True:
                        random_char = random.choice(self.en_digits)
                        if word[char_idx].lower() != random_char.lower():
                            new_word = word[:char_idx] + random_char + word[char_idx+1:]
                            break

            # 随机词
            if not new_word:
                new_word = random.choice(self.zh_words)

            for j in range(i, 2):
                text_word_list_replace[j][text_word_idx[i]] = new_word

        return text_word_list_replace

    @staticmethod
    def esimcse_repeat_word(text):
        if len(text) < 2:
            return text
        if random.random() > 0.5:
            return text
        actual_len = len(text)
        dup_len = random.randint(a=0, b=max(1, int(0.15 * actual_len)))
        dup_word_index = random.sample(list(range(1, actual_len)), k=dup_len)
        dup_text = ''
        for index, word in enumerate(text):
            dup_text += word
            if index in dup_word_index and word not in "01234567890一二三四五六七八九十百千万亿点.":
                dup_text += word
        return dup_text


if __name__ == '__main__':
    from sklearn.model_selection import train_test_split
    from transformers import BertTokenizer

    data = [
        {
            "labelId": "0",
            "title": "我要去办理业务，你们几点上班啊",
            "keywords": "",
            "labelData": "周一到周五你们几点开门"
        },
        {
            "labelId": "1",
            "title": "我的消费积分超过有效期怎么兑换",
            "keywords": "",
            "labelData": "信用卡消费积分到期以后怎么恢复"
        },
        {
            "labelId": "2",
            "title": "莫雷尔指指肩膀，向士兵们暗示那是一个军官，应当给他找个地方暖和暖和。",
            "keywords": "",
            "labelData": "莫雷尔指着他的肩，向士兵们示意，这是一个军官，应当让他暖和一下。"
        },
        {
            "labelId": "7123052",
            "title": "SSA was established in 2009.",
            "keywords": "",
            "labelData": ""
        },
        {
            "labelId": "7123053",
            "title": "Unfortunately for the Spanish, the British captured Jamaica in 1655 and henceforth gave the settlement the rather unimaginative name  Spanish Town.",
            "keywords": "",
            "labelData": "In 1655 the British captured Jamaica upsetting Spain."
        },
        {
            "labelId": "7123054",
            "title": "Cavendish, as requested.",
            "keywords": "",
            "labelData": ""
        }
    ]

    # 划分数据集
    text_list_train = []
    text_list_valid = []
    for data_dict in data:
        norm_query = data_dict['title']
        texts = set()
        texts.add(norm_query)

        for text in data_dict['labelData'].split("||"):
            text_strip = text.strip()
            if len(text_strip):
                texts.add(text_strip)
        texts = list(texts)

        if len(texts) <= 0:
            continue
        else:
            text_list_train.append(texts)

    tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_VOCAB)
    train_gen = DataGenerator(text_list=text_list_train, tokenizer=tokenizer)

    print("wait")
    for text_list in text_list_train:
        for text in text_list:
            text_vocab_list = jieba.lcut(text)
            text_word_list_replace = train_gen.random_replace_word(text_vocab_list)
            print(f"{text}\n{text_vocab_list}\n{text_word_list_replace}\n\n")

    text1 = "".join(text_word_list_replace[0])
    text2 = "".join(text_word_list_replace[1])

    for d in train_gen:
        print(d)
        break

    text = random.choice(random.choice(text_list_train))
    text_vocab_list = jieba.lcut(text)
    text_word_list_replace = train_gen.random_replace_word(text_vocab_list)
    text1 = "".join(text_word_list_replace[0])
    text2 = "".join(text_word_list_replace[1])
    print(text)
    print(text1)
    print(text2)
    print("wait")
