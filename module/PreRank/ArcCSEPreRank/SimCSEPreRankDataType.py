# -*- coding: UTF-8 -*-
import json
import os
import pickle
import random
import time,os

import jieba
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')
from matplotlib import pyplot as plt
from tqdm import tqdm

import setting
import torch

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.metrics.pairwise import normalize
from transformers import BertTokenizer, BertConfig, AutoTokenizer

from database.REDIS import REDIS
from module.PreRank.ArcCSEPreRank.ArcCSE import ArcCSE
from setting import logger
from utils.my_utils import MyEncoder, complete_match_text, my_cosine_similarity, RankReturnObject, remove_front_end_symbol, pre_suf_replace_function
from utils.onnx_utils import convert_tf2onnx,create_session,inference_with_onnx
from model.BM25.BM25 import BM25model, HNSWModel
from sklearn.feature_extraction.text import CountVectorizer, TfidfTransformer
from model.TraAndSim.TraAndSim import TraAndSim


class SimCSEPreRankDataType:
    def __init__(self, need_remove_front_end_symbol=True):
        self.tra_and_sim_model = TraAndSim()
        self.models = dict()
        self.max_len = setting.ARCCSE_PRE_RANK_MAX_LEN
        self.batch_samples = setting.ARCCSE_PRE_RANK_BATCH_SAMPLES
        self.use_arc_loss = setting.ARCCSE_PRE_RANK_USE_ARC_LOSS
        self.use_tri_loss = setting.ARCCSE_PRE_RANK_USE_TRI_LOSS
        self.redis = REDIS()
        self.adjusted_ratio = 2
        self.adjusted_ratio_inc = 1
        self.need_remove_front_end_symbol = need_remove_front_end_symbol
        self.ratio_name = "adjusted_ratio_" + str(self.__class__.__name__)
        self.ratio_name_inc = "adjusted_ratio_inc_" + str(self.__class__.__name__)
        #没有onnx就转换
        #加载onnx模型
        if setting.CLIENT=='predict':
            intra_op_num_threads = 2
        else:
            intra_op_num_threads = 5

        bert_model_path = f'{setting.MAIN_DIR}/pretrain_model/roberta_intent_anto_num_en_0712'
        self.config = BertConfig.from_pretrained(bert_model_path)
        self.config.emb_layer = setting.ARCCSE_PRE_RANK_EMB_LAYER
        self.config.max_len = self.max_len
        self.tokenizer_bert = AutoTokenizer.from_pretrained(bert_model_path)
        self.model_king_bert = create_session(onnx_model_path=os.path.join(bert_model_path,'tf_model.onnx'), intra_op_num_threads=intra_op_num_threads)

        jina_model_path = f'{setting.MAIN_DIR}/pretrain_model/ft-jina-1220-153w-bidi-cosent-batch256'
        self.tokenizer_jina = AutoTokenizer.from_pretrained(jina_model_path)
        onnx_model_path = os.path.join(jina_model_path, 'tf_model.onnx')
        print(onnx_model_path,'onnx_model_pathonnx_model_path')
        self.model_king_jina = create_session(onnx_model_path=onnx_model_path, intra_op_num_threads=intra_op_num_threads)

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        start_time = time.time()
        estimated_time, adjusted_estimated_time = self.train_time(data=data, is_inc=False)
        logger.debug(f"全量训练{model_id},预估训练耗时:{round(adjusted_estimated_time,2)}秒,即{round(adjusted_estimated_time/60,3)}分钟,使用上一次预估耗时的调整比例值为:{self.adjusted_ratio}")
        logger.debug(f"全量训练{model_id},准备开始训练")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载预训练模型
        tokenizer = AutoTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        logger.debug(f"全量训练{model_id},加载预训练模型成功")

        # 加载数据
        text2norm_id_dict, norm_id2norm_text_dict, all_text_dict, text2item_cm_dict, label_id_text_to_origin_text, origin_faq_title = self.get_data_generator(data=data)
        data_count_str = ""
        for data_type, temp in all_text_dict.items():
            data_count_str += f",{data_type}:{len(temp)}"
        logger.debug(f"全量训练{model_id},意图数据加载完成{data_count_str}")

        # 获取 all_text 的 embedding
        search_dict = {}
        embeddings_dict = {}
        embeddings_norm_dict = {}
        for data_type, all_text in all_text_dict.items():
            if len(all_text):
                embeddings = self.encode_sentences(texts=all_text, tokenizer=tokenizer)
                embeddings_norm = normalize(embeddings, copy=True)
                search = HNSWModel(all_text, embeddings_norm)
            else:
                embeddings = None
                embeddings_norm = None
                search = None
            logger.debug(f"全量训练{model_id},获取{data_type}的embedding成功")
            search_dict[data_type] = search
            embeddings_dict[data_type] = embeddings
            embeddings_norm_dict[data_type] = embeddings_norm

        # 联想输入模型训练
        if len(origin_faq_title):
            context_input_process_search = BM25model(origin_faq_title)
        else:
            context_input_process_search = None

        # 保存模型
        tokenizer.save_pretrained(save_dir)
        with open(os.path.join(save_dir, "text2norm_id_dict.pkl"), 'wb') as f:
            pickle.dump(text2norm_id_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text_dict.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text_dict.pkl"), 'wb') as f:
            pickle.dump(all_text_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm_dict.pkl"), 'wb') as f:
            pickle.dump(text2item_cm_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "embeddings_dict.pkl"), 'wb') as f:
            pickle.dump(embeddings_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "embeddings_norm_dict.pkl"), 'wb') as f:
            pickle.dump(embeddings_norm_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_dict.pkl"), 'wb') as f:
            pickle.dump(search_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_context_input.pkl"), 'wb') as f:
            pickle.dump(context_input_process_search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, 'label_id_text_to_origin_text.json'), 'w', encoding='utf-8') as f:
            json.dump(label_id_text_to_origin_text, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        logger.debug(f"全量训练{model_id},保存结束")

        # 估算threshold
        threshold_dict = {}
        with open(os.path.join(save_dir, 'threshold_dict.json'), 'w', encoding='utf-8') as f:
            json.dump(threshold_dict, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        threshold_dict = self.test_threshold(model_id, save_dir, need_run=True)
        with open(os.path.join(save_dir, 'threshold_dict.json'), 'w', encoding='utf-8') as f:
            json.dump(threshold_dict, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

        # 语料优化建议和准确率
        logger.debug(f"全量训练{model_id},开始测试准确率和语料优化建议")
        mixed = []
        score = self.acc(model_id, save_dir)
        with open(os.path.join(save_dir, 'acc_list.json'), 'w', encoding='utf-8') as f:
            json.dump([score], cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, "mixed.pkl"), 'wb') as f:
            pickle.dump(mixed, f, pickle.HIGHEST_PROTOCOL)
        mixed_df = pd.DataFrame(mixed)
        mixed_df.to_csv(os.path.join(save_dir, "suggest.csv"), index=False, encoding="utf-8-sig")
        logger.debug(f"全量训练{model_id},测试准确率和语料优化建议成功,acc:{score}")

        # 比例值写入redis
        real_train_time = time.time() - start_time
        self.adjusted_ratio = round(real_train_time/estimated_time, 4)
        self.redis.set_data(self.ratio_name, str(self.adjusted_ratio))
        logger.debug(f"全量训练{model_id},修正后预估耗时:{round(estimated_time*self.adjusted_ratio,2)}秒,即{round(estimated_time*self.adjusted_ratio/60,3)}分钟,更新预估耗时的调整比例值为:{self.adjusted_ratio}")

        # 训练成功
        logger.debug(f"全量训练{model_id},训练成功,耗时:{time.time()-start_time}s,batch:{setting.BERT_CLASSIFICATION_BATCH_SIZE}")
        return score, mixed

    def increment_train(self, model_id, data, save_dir):
        start_time = time.time()
        estimated_time, adjusted_estimated_time = self.train_time(data=data, is_inc=True)
        logger.debug(f"增量训练{model_id},预估训练耗时:{round(adjusted_estimated_time,2)}秒,即{round(adjusted_estimated_time/60,3)}分钟,使用上一次预估耗时的调整比例值为:{self.adjusted_ratio_inc}")
        logger.debug(f"增量训练{model_id},准备开始训练")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 判断历史使用的模型是什么
        if os.path.exists(os.path.join(save_dir,'vocab.txt')):
            tokenizer = AutoTokenizer.from_pretrained(save_dir)
        else:
            tokenizer = AutoTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)

        logger.debug(f"增量训练{model_id},加载预训练模型成功")

        # 加载数据
        text2norm_id_dict, norm_id2norm_text_dict, all_text_dict, text2item_cm_dict, label_id_text_to_origin_text, origin_faq_title = self.get_data_generator(data=data)
        data_count_str = ""
        for data_type, temp in all_text_dict.items():
            data_count_str += f",{data_type}:{len(temp)}"
        logger.debug(f"增量训练{model_id},意图数据加载完成{data_count_str}")

        # 训练模型
        logger.debug(f"增量训练{model_id},保存模型文件成功")

        # 加载旧模型数据
        old_all_text_dict = {}
        # 兼容旧版模型，data_type只有intent faq chat的
        if os.path.exists(os.path.join(save_dir, "all_text_intent.pkl")):
            with open(os.path.join(save_dir, "all_text_intent.pkl"), 'rb') as f:
                old_all_text_intent = pickle.load(f)
            old_all_text_dict["intent"] = old_all_text_intent
        if os.path.exists(os.path.join(save_dir, "all_text_faq.pkl")):
            with open(os.path.join(save_dir, "all_text_faq.pkl"), 'rb') as f:
                old_all_text_faq = pickle.load(f)
            old_all_text_dict["faq"] = old_all_text_faq
        if os.path.exists(os.path.join(save_dir, "all_text_chat.pkl")):
            with open(os.path.join(save_dir, "all_text_chat.pkl"), 'rb') as f:
                old_all_text_chat = pickle.load(f)
            old_all_text_dict["chat"] = old_all_text_chat
        # 新版模型，data_type动态
        if os.path.exists(os.path.join(save_dir, "all_text_dict.pkl")):
            with open(os.path.join(save_dir, "all_text_dict.pkl"), 'rb') as f:
                old_all_text_dict_temp = pickle.load(f)
            for data_type, old_all_text in old_all_text_dict_temp.items():
                old_all_text_dict[data_type] = old_all_text

        emb_dim = None
        old_embeddings_dict = {}
        # 兼容旧版模型，data_type只有intent faq chat的
        if os.path.exists(os.path.join(save_dir, "embeddings_intent.npy")):
            try:
                old_embeddings_intent = np.load(os.path.join(save_dir, 'embeddings_intent.npy'), allow_pickle=True)
                emb_dim = old_embeddings_intent.shape[1]
                old_embeddings_dict["intent"] = old_embeddings_intent
            except:
                old_embeddings_dict["intent"] = None
                old_all_text_dict["intent"] = []
        if os.path.exists(os.path.join(save_dir, "embeddings_faq.npy")):
            try:
                old_embeddings_faq = np.load(os.path.join(save_dir, 'embeddings_faq.npy'), allow_pickle=True)
                emb_dim = old_embeddings_faq.shape[1]
                old_embeddings_dict["faq"] = old_embeddings_faq
            except:
                old_embeddings_dict["faq"] = None
                old_all_text_dict["faq"] = []
        if os.path.exists(os.path.join(save_dir, "embeddings_chat.npy")):
            try:
                old_embeddings_chat = np.load(os.path.join(save_dir, 'embeddings_chat.npy'), allow_pickle=True)
                emb_dim = old_embeddings_chat.shape[1]
                old_embeddings_dict["chat"] = old_embeddings_chat
            except:
                old_embeddings_dict["chat"] = None
                old_all_text_dict["chat"] = []
        # 新版模型，data_type动态
        if os.path.exists(os.path.join(save_dir, "embeddings_dict.pkl")):
            try:
                with open(os.path.join(save_dir, "embeddings_dict.pkl"), 'rb') as f:
                    old_embeddings_dict_temp = pickle.load(f)
                for data_type, old_embeddings in old_embeddings_dict_temp.items():
                    emb_dim = old_embeddings.shape[1]
                    old_embeddings_dict[data_type] = old_embeddings
            except:
                pass

        old_all_text_to_idx_dict = {}
        for data_type, old_all_text in old_all_text_dict.items():
            old_all_text_to_idx_dict[data_type] = {}
            for i, t in enumerate(old_all_text):
                old_all_text_to_idx_dict[data_type][t] = i

        if emb_dim is None:
            temp_emb = self.encode_sentences(texts=["测试一下"], tokenizer=tokenizer)
            emb_dim = temp_emb.shape[1]
        logger.debug(f"增量训练{model_id},加载旧模型数据成功")

        # 获取 all_text 的 embedding
        search_dict = {}
        embeddings_dict = {}
        embeddings_norm_dict = {}
        for data_type, all_text in all_text_dict.items():
            if data_type not in old_all_text_to_idx_dict:
                old_all_text_to_idx_dict[data_type] = {}
            if data_type not in old_embeddings_dict:
                old_embeddings_dict[data_type] = None
            if len(all_text):
                embeddings = np.zeros(shape=(len(all_text), emb_dim))
                text_need_encode_list = []
                text_need_encode_idx_list = []
                for idx, t in enumerate(all_text):
                    old_idx = old_all_text_to_idx_dict[data_type].get(t, -1)
                    if old_idx >= 0:
                        embeddings[idx, :] = old_embeddings_dict[data_type][old_idx, :]
                    else:
                        text_need_encode_idx_list.append(idx)
                        text_need_encode_list.append(t)
                if len(text_need_encode_list):
                    embeddings_need_encode = self.encode_sentences(texts=text_need_encode_list, tokenizer=tokenizer)
                    embeddings[text_need_encode_idx_list, :] = embeddings_need_encode
                embeddings_norm = normalize(embeddings, copy=True)
                search = HNSWModel(all_text, embeddings_norm)
            else:
                embeddings = None
                embeddings_norm = None
                search = None
            search_dict[data_type] = search
            embeddings_dict[data_type] = embeddings
            embeddings_norm_dict[data_type] = embeddings_norm
            logger.debug(f"增量训练{model_id},获取{data_type}的embedding成功")

        # 联想输入模型训练
        if len(origin_faq_title):
            context_input_process_search = BM25model(origin_faq_title)
        else:
            context_input_process_search = None

        # 保存模型
        tokenizer.save_pretrained(save_dir)
        with open(os.path.join(save_dir, "text2norm_id_dict.pkl"), 'wb') as f:
            pickle.dump(text2norm_id_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text_dict.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text_dict.pkl"), 'wb') as f:
            pickle.dump(all_text_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm_dict.pkl"), 'wb') as f:
            pickle.dump(text2item_cm_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "embeddings_dict.pkl"), 'wb') as f:
            pickle.dump(embeddings_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "embeddings_norm_dict.pkl"), 'wb') as f:
            pickle.dump(embeddings_norm_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_dict.pkl"), 'wb') as f:
            pickle.dump(search_dict, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_context_input.pkl"), 'wb') as f:
            pickle.dump(context_input_process_search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, 'label_id_text_to_origin_text.json'), 'w', encoding='utf-8') as f:
            json.dump(label_id_text_to_origin_text, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        logger.debug(f"增量训练{model_id},保存结束")

        # 准确率
        if os.path.exists(os.path.join(save_dir, 'acc_list.json')):
            with open(os.path.join(save_dir, 'acc_list.json'), 'r', encoding='utf-8') as f:
                acc_list = json.load(f)
        else:
            acc_list = [0]
        score = acc_list[0]
        logger.debug(f"增量训练{model_id},使用全量的准确率,acc:{acc_list}")

        # 语料优化建
        if os.path.exists(os.path.join(save_dir, "mixed.pkl")):
            with open(os.path.join(save_dir, "mixed.pkl"), 'rb') as f:
                mixed = pickle.load(f)
        else:
            mixed = []
        logger.debug(f"增量训练{model_id},使用全量的优化建议")

        # 比例值写入redis
        real_train_time = time.time() - start_time
        self.adjusted_ratio_inc = round(real_train_time/estimated_time, 4)
        self.redis.set_data(self.ratio_name_inc, str(self.adjusted_ratio_inc))
        logger.debug(f"增量训练{model_id},修正后预估耗时:{round(estimated_time*self.adjusted_ratio_inc,2)}秒,即{round(estimated_time*self.adjusted_ratio_inc/60,3)}分钟,更新预估耗时的调整比例值为:{self.adjusted_ratio_inc}")

        # 训练成功
        logger.debug(f"增量训练{model_id},训练成功,耗时:{time.time()-start_time}s")
        return score, mixed

    def load_model(self, model_id, save_dir):
        try:
            tokenizer = AutoTokenizer.from_pretrained(save_dir)

            text2norm_id_dict = {}
            if os.path.exists(os.path.join(save_dir, "text2norm_id_dict.pkl")):
                with open(os.path.join(save_dir, "text2norm_id_dict.pkl"), 'rb') as f:
                    text2norm_id_dict = pickle.load(f)
            else:
                with open(os.path.join(save_dir, "text2norm_id_intent.pkl"), 'rb') as f:
                    text2norm_id_dict["intent"] = pickle.load(f)
                with open(os.path.join(save_dir, "text2norm_id_faq.pkl"), 'rb') as f:
                    text2norm_id_dict["faq"] = pickle.load(f)
                with open(os.path.join(save_dir, "text2norm_id_chat.pkl"), 'rb') as f:
                    text2norm_id_dict["chat"] = pickle.load(f)

            norm_id2norm_text_dict = {}
            if os.path.exists(os.path.join(save_dir, "norm_id2norm_text_dict.pkl")):
                with open(os.path.join(save_dir, "norm_id2norm_text_dict.pkl"), 'rb') as f:
                    norm_id2norm_text_dict = pickle.load(f)
            else:
                with open(os.path.join(save_dir, "norm_id2norm_text_intent.pkl"), 'rb') as f:
                    norm_id2norm_text_dict["intent"] = pickle.load(f)
                with open(os.path.join(save_dir, "norm_id2norm_text_faq.pkl"), 'rb') as f:
                    norm_id2norm_text_dict["faq"] = pickle.load(f)
                with open(os.path.join(save_dir, "norm_id2norm_text_chat.pkl"), 'rb') as f:
                    norm_id2norm_text_dict["chat"] = pickle.load(f)

            all_text_dict = {}
            if os.path.exists(os.path.join(save_dir, "all_text_dict.pkl")):
                with open(os.path.join(save_dir, "all_text_dict.pkl"), 'rb') as f:
                    all_text_dict = pickle.load(f)
            else:
                with open(os.path.join(save_dir, "all_text_intent.pkl"), 'rb') as f:
                    all_text_dict["intent"] = pickle.load(f)
                with open(os.path.join(save_dir, "all_text_faq.pkl"), 'rb') as f:
                    all_text_dict["faq"] = pickle.load(f)
                with open(os.path.join(save_dir, "all_text_chat.pkl"), 'rb') as f:
                    all_text_dict["chat"] = pickle.load(f)

            text2item_cm_dict = {}
            if os.path.exists(os.path.join(save_dir, "text2item_cm_dict.pkl")):
                with open(os.path.join(save_dir, "text2item_cm_dict.pkl"), 'rb') as f:
                    text2item_cm_dict = pickle.load(f)
            else:
                with open(os.path.join(save_dir, "text2item_cm_intent.pkl"), 'rb') as f:
                    text2item_cm_dict["intent"] = pickle.load(f)
                with open(os.path.join(save_dir, "text2item_cm_faq.pkl"), 'rb') as f:
                    text2item_cm_dict["faq"] = pickle.load(f)
                with open(os.path.join(save_dir, "text2item_cm_chat.pkl"), 'rb') as f:
                    text2item_cm_dict["chat"] = pickle.load(f)

            search_dict = {}
            if os.path.exists(os.path.join(save_dir, "search_dict.pkl")):
                with open(os.path.join(save_dir, "search_dict.pkl"), 'rb') as f:
                    search_dict = pickle.load(f)
            else:
                with open(os.path.join(save_dir, "search_intent.pkl"), 'rb') as f:
                    search_intent = pickle.load(f)
                with open(os.path.join(save_dir, "search_faq.pkl"), 'rb') as f:
                    search_faq = pickle.load(f)
                with open(os.path.join(save_dir, "search_chat.pkl"), 'rb') as f:
                    search_chat = pickle.load(f)
                search_dict["intent"] = search_intent
                search_dict["faq"] = search_faq
                search_dict["chat"] = search_chat
            for data_type, search in search_dict.items():
                try:
                    if search.p.ef != setting.ARCCSE_PRE_RANK_HNSW_EF:
                        search.p.set_ef(setting.ARCCSE_PRE_RANK_HNSW_EF)
                except:
                    pass

            with open(os.path.join(save_dir, "search_context_input.pkl"), 'rb') as f:
                context_input_process_search = pickle.load(f)

            if os.path.exists(os.path.join(save_dir, 'label_id_text_to_origin_text.json')):
                with open(os.path.join(save_dir, 'label_id_text_to_origin_text.json'), 'r', encoding='utf-8') as f:
                    label_id_text_to_origin_text = json.load(f)
            else:
                label_id_text_to_origin_text = {}

            all_text_2_idx_dict = {}
            threshold_dict = {}
            for data_type, all_text in all_text_dict.items():
                all_text_2_idx_dict[data_type] = dict()
                for i, text in enumerate(all_text):
                    all_text_2_idx_dict[data_type][text] = i
                threshold_dict[data_type] = [0.65, 0.75, 0.93]

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['text2norm_id_dict'] = text2norm_id_dict
            self.models[model_id]['norm_id2norm_text_dict'] = norm_id2norm_text_dict
            self.models[model_id]['all_text_dict'] = all_text_dict
            self.models[model_id]['all_text_2_idx_dict'] = all_text_2_idx_dict
            self.models[model_id]['text2item_cm_dict'] = text2item_cm_dict
            self.models[model_id]['search_dict'] = search_dict
            self.models[model_id]['context_input_process_search'] = context_input_process_search
            self.models[model_id]['label_id_text_to_origin_text'] = label_id_text_to_origin_text
            self.models[model_id]["threshold_dict"] = threshold_dict
            self.predict(model_id=model_id, query="测试一下", topk=2)
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, query, query_list=None, query_embedding=None, topk=50, use_data_type=None, context_input_process='', test_flag=False, remove_stop_words_flag=False, need_trans_to_simple=True):
        result = {"intent": [], "faq": [], "chat": []}
        threshold_dict = {}
        error_dict = {"error_code": 0, "error_type": 1, "error_msg": ""}
        origin_query = query  # 原始输入
        if query_list is None or len(query_list) == 0:
            query_list = [query]

        # 预测时需要把繁体转简体
        if need_trans_to_simple:
            if query and isinstance(query, str):
                if self.need_remove_front_end_symbol:
                    query = remove_front_end_symbol(query)
                query = self.tra_and_sim_model.tra_or_sim(query, target="sim")
                if not test_flag:
                    logger.debug(f"成功繁体转简体,query:{query}")
            if query_list and isinstance(query_list, list):
                if self.need_remove_front_end_symbol:
                    query_list = [remove_front_end_symbol(q) for q in query_list]
                query_list = [self.tra_and_sim_model.tra_or_sim(q, target="sim") for q in query_list]
                if not test_flag:
                    logger.debug(f"成功繁体转简体,query_list:{query_list}")

        if model_id not in self.models:
            try:
                is_exist = self.check_model_file_exist(model_id=model_id)
                if is_exist:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91024"
                    error_dict["error_msg"] = "预测错误: 模型未加载，请重新上线"
                else:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91025"
                    error_dict["error_msg"] = "预测错误: 模型未加载，模型文件缺失，请重新训练"
            except Exception as ee:
                error_dict["error_type"] = 0
                error_dict["error_code"] = "NLU91017"
                error_dict["error_msg"] = f"预测错误: 模型未加载, {ee}"
            logger.error(f"预测失败 [{model_id}], 错误信息: {error_dict['error_msg']}")
            return result, error_dict, threshold_dict

        tokenizer = self.models[model_id]['tokenizer']
        norm_id2norm_text_dict = self.models[model_id]['norm_id2norm_text_dict']
        text2norm_id_dict = self.models[model_id]['text2norm_id_dict']
        all_text_dict = self.models[model_id]['all_text_dict']
        text2item_cm_dict = self.models[model_id]['text2item_cm_dict']
        search_dict = self.models[model_id]['search_dict']
        context_input_process_search = self.models[model_id]['context_input_process_search']
        threshold_dict = self.models[model_id]['threshold_dict']
        label_id_text_to_origin_text = self.models[model_id]['label_id_text_to_origin_text']
        if use_data_type is None:
            # 使用所有data_type
            use_data_type = list(all_text_dict.keys())

        # 粗排
        is_hnsw_l2 = False
        for k in search_dict.keys():
            if search_dict[k]!=None:
                if search_dict[k].p.space != "cosine":
                    is_hnsw_l2 = True
                    break
        logger.debug(f"意图识别predict,HNSW是不是使用l2:{is_hnsw_l2},model_id:{model_id}")

        #联想输入
        if context_input_process:
            context_input_process = int(context_input_process) if int(context_input_process) != 1 else 10
            if context_input_process_search is None:
                recall_text = []
            else:
                recall_text = context_input_process_search.search(query, 200, True)[:context_input_process]
            recall_id = []
            for i, r_text in enumerate(recall_text):
                if r_text not in text2norm_id_dict["faq"]:
                    if self.need_remove_front_end_symbol:
                        r_text = remove_front_end_symbol(r_text)
                    # 繁体的先转回简体
                    idd = text2norm_id_dict["faq"][self.tra_and_sim_model.tra_or_sim(r_text, target="sim")]
                    recall_id.append(idd[0] if type(idd)==list else idd)
                else:
                    idd = text2norm_id_dict["faq"][r_text]
                    recall_id.append(idd[0] if type(idd)==list else idd)
                # label_id = recall_id[-1]
                # if label_id in label_id_text_to_origin_text and r_text in label_id_text_to_origin_text[label_id]:
                #     recall_text[i] = label_id_text_to_origin_text[label_id][r_text]
            return (recall_id, recall_text), error_dict

        # 去除停用词，暂定先去除语气词
        def remove_stop_words(ori_text):
            all_stop_words = "么阿啊啦唉呢吧哇呀吗哦噢喔呵嘿吁吖呗咩哎"
            new_text = ori_text
            # for w in all_stop_words:
            #     new_text = new_text.replace(w, "")
            if len(ori_text)<5:
                return ori_text

            #前后缀替换
            new_text = pre_suf_replace_function(new_text)
            if len(new_text):
                return new_text
            else:
                return ori_text

        if remove_stop_words_flag:
            logger.debug(f"需要移除停用词,移除前:{query}, {query_list}")
            query = remove_stop_words(query)
            if query_list and len(query_list):
                query_list = [remove_front_end_symbol(remove_stop_words(q)).strip() for q in query_list]
                query_list = [q for q in query_list if len(q)]
            logger.debug(f"需要移除停用词,移除后:{query}, {query_list}")

        # 精确匹配
        norm_query_id_set_complete = {"intent": set(), "faq": set(), "chat": set()}
        if not test_flag:
            if query_list is None or len(query_list) == 0:
                query_list = [query]
            for temp_query in query_list:
                temp_query = complete_match_text(temp_query)
                for result_key, text2item_cm in text2item_cm_dict.items():
                    if result_key not in norm_query_id_set_complete:
                        norm_query_id_set_complete[result_key] = set()
                    if result_key not in result:
                        result[result_key] = []
                    if temp_query in text2item_cm:
                        temp_item = text2item_cm[temp_query]
                        if type(temp_item[0]) == list:  # 修正中通的一个语料保存了多个的标准问的版本
                            temp_item = temp_item[0]
                        if len(temp_item) == 3:
                            norm_query, norm_query_id, sim_query = temp_item
                        else:
                            norm_query, norm_query_id = temp_item
                            sim_query = temp_query
                        if norm_query_id not in norm_query_id_set_complete[result_key]:
                            norm_query_id_set_complete[result_key].add(norm_query_id)
                            origin_norm_query = norm_query
                            origin_sim_query = sim_query
                            if norm_query_id in label_id_text_to_origin_text:
                                if norm_query in label_id_text_to_origin_text[norm_query_id]:
                                    origin_norm_query = label_id_text_to_origin_text[norm_query_id][norm_query]
                                if sim_query in label_id_text_to_origin_text[norm_query_id]:
                                    origin_sim_query = label_id_text_to_origin_text[norm_query_id][sim_query]
                            item = RankReturnObject(query=origin_query, sim_query=origin_sim_query, norm_query=origin_norm_query,
                                                    norm_query_id=norm_query_id, score=1, save_bigger=True)
                            result[result_key].append(item)
            all_data_type_cm = True
            for result_key, text2item_cm in text2item_cm_dict.items():
                if result_key not in result:
                    result[result_key] = []
                if len(result[result_key]) == 0:
                    all_data_type_cm = False
            # if all_data_type_cm:
            #     # 所有类型的数据都匹配到了，直接返回
            #     return result, error_dict, threshold_dict

        # 句子embedding
        embedding_start = time.time()
        if query_embedding is not None:
            emb = query_embedding
        else:
            emb = self.encode_sentences(texts=query_list, tokenizer=tokenizer)
            emb = normalize(emb)
        logger.debug(f"句子embedding耗时:{time.time()-embedding_start}")

        hnsw_start = time.time()
        for i, query in enumerate(query_list):
            # 粗排
            recall_text_dict = {}
            select_emb_dict = {}
            scores_dict = {}
            for data_type, all_text in all_text_dict.items():
                recall_text_dict[data_type] = []
                select_emb_dict[data_type] = None
                if is_hnsw_l2:
                    # 旧版HNSW，需要用embedding计算余弦相似度
                    if data_type in use_data_type and len(all_text):
                        recall_text, select_emb = search_dict[data_type].search(emb[i:i+1, :], min(setting.ARCCSE_PRE_RANK_SEARCH_RETURN, len(all_text)), return_emb=True)
                        recall_text_dict[data_type] = recall_text
                        select_emb_dict[data_type] = select_emb
                    if not test_flag:
                        logger.debug(f"粗排结束,检索数量,query:{query},{data_type}:{len(recall_text_dict[data_type])}")
                    # 计算分数
                    if len(recall_text_dict[data_type]) and select_emb_dict[data_type] is not None:
                        scores = my_cosine_similarity(select_emb_dict[data_type], emb[i:i+1, :].astype(np.float16))[:, 0]
                        scores = scores.clip(min=0, max=1).tolist()
                    else:
                        scores = None
                    scores_dict[data_type] = scores
                else:
                    # 新版HNSW，直接返回余弦相似度
                    if data_type in use_data_type and len(all_text):
                        recall_text, distance = search_dict[data_type].search(emb[i:i+1, :], min(setting.ARCCSE_PRE_RANK_SEARCH_RETURN, len(all_text)), return_emb=False)
                        recall_text_dict[data_type] = recall_text
                    if not test_flag:
                        logger.debug(f"粗排结束,检索数量,query:{query},{data_type}:{len(recall_text_dict[data_type])}")
                    # 计算分数
                    if len(recall_text_dict[data_type]):
                        scores = (1-distance)[0]
                        scores = scores.clip(min=0, max=1).tolist()
                    else:
                        scores = None
                    scores_dict[data_type] = scores

            # 生成结果
            for data_type in all_text_dict.keys():
                result_key = data_type
                scores = scores_dict[data_type]
                recall_text = recall_text_dict[data_type]
                text2norm_id = text2norm_id_dict[data_type]
                norm_id2norm_text = norm_id2norm_text_dict[data_type]

                if scores is None:
                    continue
                index = np.argsort(scores)
                index = index[::-1]
                norm_query_id_set = set()
                if not test_flag:
                    logger.debug(f"排序结束")
                for idx in index:
                    sim_query = recall_text[idx]
                    if test_flag and sim_query == query:
                        continue
                    norm_query_id = text2norm_id[sim_query]
                    norm_query_ids = norm_query_id if type(norm_query_id)==list else [norm_query_id]
                    for norm_query_id in norm_query_ids:
                        if result_key not in norm_query_id_set_complete:
                            norm_query_id_set_complete[result_key] = set()
                        if result_key not in result:
                            result[result_key] = []
                        if norm_query_id not in norm_query_id_set_complete[result_key] and norm_query_id not in norm_query_id_set:
                            norm_query_id_set.add(norm_query_id)
                            norm_query = norm_id2norm_text[norm_query_id]
                            origin_norm_query = norm_query
                            origin_sim_query = sim_query
                            if norm_query_id in label_id_text_to_origin_text:
                                if norm_query in label_id_text_to_origin_text[norm_query_id]:
                                    origin_norm_query = label_id_text_to_origin_text[norm_query_id][norm_query]
                                if sim_query in label_id_text_to_origin_text[norm_query_id]:
                                    origin_sim_query = label_id_text_to_origin_text[norm_query_id][sim_query]
                            item = RankReturnObject(query=origin_query, sim_query=origin_sim_query, norm_query=origin_norm_query,
                                                    norm_query_id=norm_query_id, score=scores[idx], save_bigger=True)
                            result[result_key].append(item)
                    if len(norm_query_id_set) >= topk:
                        break
            if not test_flag:
                logger.debug(f"意图识别完成,query:{query}")

        logger.debug(f"粗排耗时:{time.time()-hnsw_start}")

        if not test_flag:
            for data_type in result.keys():
                result_temp = [item.__dict__ for item in result[data_type]]
                logger.debug(f"多个query_list检索出的结果,{data_type}:{result_temp}")
        for k in result.keys():
            result[k].sort(key=lambda xx: -xx.score)
            temp_result_list = []
            temp_norm_query_id_set = set()
            for x in result[k]:
                if x.norm_query_id not in temp_norm_query_id_set:
                    temp_norm_query_id_set.add(x.norm_query_id)
                    temp_result_list.append(x)
                    if len(temp_norm_query_id_set) >= topk:
                        break
            result[k] = temp_result_list
        if not test_flag:
            for data_type in result.keys():
                result_temp = [item.__dict__ for item in result[data_type]]
                logger.debug(f"返回结果,{data_type}:{result_temp}")
        return result, error_dict, threshold_dict

    def labeling(self, model_id, texts, text_ids=None):
        labeling_result = []

        text2norm_id_dict = self.models[model_id]['text2norm_id_dict']
        all_text_dict = self.models[model_id]['all_text_dict']
        tokenizer = self.models[model_id]['tokenizer']

        embeddings_norm_dict = {}
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")
        if os.path.exists(os.path.join(save_dir, "embeddings_norm_dict.pkl")):
            with open(os.path.join(save_dir, "embeddings_norm_dict.pkl"), 'rb') as f:
                embeddings_norm_dict = pickle.load(f)
        else:
            embeddings_norm_intent = np.load(os.path.join(save_dir, 'embeddings_norm_intent.npy'), allow_pickle=True)
            embeddings_norm_faq = np.load(os.path.join(save_dir, 'embeddings_norm_faq.npy'), allow_pickle=True)
            embeddings_norm_chat = np.load(os.path.join(save_dir, 'embeddings_norm_chat.npy'), allow_pickle=True)
            embeddings_norm_dict["intent"] = embeddings_norm_intent
            embeddings_norm_dict["faq"] = embeddings_norm_faq
            embeddings_norm_dict["chat"] = embeddings_norm_chat
        for data_type, embeddings_norm in embeddings_norm_dict.items():
            embeddings_norm_dict[data_type] = embeddings_norm.astype(np.float16)

        embeddings_norm_list_temp = []
        all_text = []
        text2norm_id = {}
        for data_type in all_text_dict.keys():
            if len(all_text_dict[data_type]) and embeddings_norm_dict[data_type]:
                all_text += all_text_dict[data_type]
                embeddings_norm_list_temp.append(embeddings_norm_dict[data_type])
            for k, v in text2norm_id_dict[data_type].items():
                text2norm_id[k] = v[0]
        embeddings_norm = np.vstack(embeddings_norm_list_temp)

        embeddings = self.encode_sentences(texts=texts, tokenizer=tokenizer)  # [n, dim]
        scores = my_cosine_similarity(embeddings_norm, embeddings)  # [m, n]
        max_idx = np.argmax(scores, axis=0)
        for i in range(max_idx.shape[0]):
            result = {
                "text": texts[i],
                "id": text_ids[i] if text_ids is not None else None,
                "label": text2norm_id[all_text[max_idx[i]]],
                "score": scores[max_idx[i], i],
                "margin_score": scores[max_idx[i], i]
            }
            labeling_result.append(result)
        return labeling_result


    def get_data_generator(self, data):
        # 整理数据
        from collections import defaultdict
        text2norm_id_dict = {}
        norm_id2norm_text_dict = {}
        text2item_cm_dict = {}
        label_id_text_to_origin_text = dict()
        origin_faq_title = []

        for data_dict in data:
            data_type = data_dict.get("type", "faq")
            norm_query = data_dict['title'].strip()
            if len(norm_query) == 0:
                continue
            norm_query_id = data_dict['labelId']
            origin_norm_query = norm_query  # 原始文本，用于保留繁体的
            if self.need_remove_front_end_symbol:
                norm_query = remove_front_end_symbol(norm_query)
            norm_query = self.tra_and_sim_model.tra_or_sim(norm_query, target="sim")  # 转为简体embedding和精确匹配
            norm_query_cm = complete_match_text(norm_query)
            if norm_query_id not in label_id_text_to_origin_text:
                label_id_text_to_origin_text[norm_query_id] = {}
            label_id_text_to_origin_text[norm_query_id][norm_query] = origin_norm_query
            if data_type == "faq":
                origin_faq_title.append(origin_norm_query)

            if data_type not in norm_id2norm_text_dict:
                norm_id2norm_text_dict[data_type] = dict()
            if data_type not in text2item_cm_dict:
                text2item_cm_dict[data_type] = dict()
            if data_type not in text2norm_id_dict:
                text2norm_id_dict[data_type] = defaultdict(list)

            norm_id2norm_text_dict[data_type][norm_query_id] = norm_query
            text2norm_id_dict[data_type][norm_query].append(norm_query_id)
            text2item_cm_dict[data_type][norm_query_cm] = [norm_query, norm_query_id, norm_query]  # 保存匹配的sim_query

            for text in data_dict.get("labelData", "").split("||"):
                text = text.strip()
                origin_text = text  # 原始文本，用于保留繁体的
                if self.need_remove_front_end_symbol:
                    text = remove_front_end_symbol(text)
                text = self.tra_and_sim_model.tra_or_sim(text, target="sim")  # 转为简体embedding和精确匹配
                text_cm = complete_match_text(text)
                if norm_query_id not in label_id_text_to_origin_text:
                    label_id_text_to_origin_text[norm_query_id] = {}
                label_id_text_to_origin_text[norm_query_id][text] = origin_text

                if len(text):
                    text2norm_id_dict[data_type][text].append(norm_query_id)
                    text2item_cm_dict[data_type][text_cm] = [norm_query, norm_query_id, text]  # 保存匹配的sim_query
        all_text_dict = {}
        for data_type in text2norm_id_dict.keys():
            all_text_dict[data_type] = list(text2norm_id_dict[data_type].keys())
        return text2norm_id_dict, norm_id2norm_text_dict, all_text_dict, text2item_cm_dict, label_id_text_to_origin_text, origin_faq_title

    def encode_sentences_old(self, texts, model):#非onnx版本
        embeddings = list()

        for start_idx in tqdm(range(0, len(texts), setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE)):
            end_idx = min(start_idx+setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE, len(texts))
            inputs = model.get_data(texts=texts[start_idx:end_idx], max_len=self.max_len, return_tensor=True)
            emb = model.get_encoder_layer(**inputs)
            embeddings.append(emb.numpy())

        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings

    def encode_sentences(self, texts, tokenizer=None):
        if tokenizer==None:
            tokenizer = self.tokenizer_jina
        embeddings = list()
        for start_idx in tqdm(range(0, len(texts), setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE)):
            end_idx = min(start_idx+setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE, len(texts))
            if str(tokenizer.vocab_size) == '8021':
                inputs = self.get_data_bert(texts=texts[start_idx:end_idx], max_len=self.max_len, tokenizer=tokenizer,return_tensor=True)
                emb = inference_with_onnx(self.model_king_bert, inputs)[0]
            else:
                inputs = self.get_data_jina(texts=texts[start_idx:end_idx], max_len=self.max_len,tokenizer=tokenizer, return_tensor=True)
                def mean_pooling(model_output, attention_mask):
                    token_embeddings = model_output
                    input_mask_expanded = (
                        attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
                    )
                    return torch.sum(token_embeddings * input_mask_expanded, 1) / torch.clamp(
                        input_mask_expanded.sum(1), min=1e-9
                    )
                outputs = inference_with_onnx(self.model_king_jina, inputs)
                # emb = mean_pooling(outputs[0], inputs['attention_mask'])
                emb = mean_pooling(torch.from_numpy(outputs[0]), torch.from_numpy(inputs['attention_mask']))

            embeddings.append(emb)

        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings


    def check_model_file_exist(self, model_id):
        model_file_exist = True
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")

        try:
            if not os.path.exists(os.path.join(save_dir, "vocab.json")):
                model_file_exist = False
        except:
            model_file_exist = False
        return model_file_exist

    def acc(self, model_id, save_dir):
        logger.debug(f"开始计算准确率")
        puzzl_label_start_time = time.time()

        self.load_model(model_id=model_id, save_dir=save_dir)

        correct_count = 0
        all_count = 0

        all_text_dict = self.models[model_id]['all_text_dict']
        text2norm_id_dict = self.models[model_id]['text2norm_id_dict']
        embeddings_norm_dict = {}
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")
        if os.path.exists(os.path.join(save_dir, "embeddings_norm_dict.pkl")):
            with open(os.path.join(save_dir, "embeddings_norm_dict.pkl"), 'rb') as f:
                embeddings_norm_dict = pickle.load(f)
        else:
            embeddings_norm_intent = np.load(os.path.join(save_dir, 'embeddings_norm_intent.npy'), allow_pickle=True)
            embeddings_norm_faq = np.load(os.path.join(save_dir, 'embeddings_norm_faq.npy'), allow_pickle=True)
            embeddings_norm_chat = np.load(os.path.join(save_dir, 'embeddings_norm_chat.npy'), allow_pickle=True)
            embeddings_norm_dict["intent"] = embeddings_norm_intent
            embeddings_norm_dict["faq"] = embeddings_norm_faq
            embeddings_norm_dict["chat"] = embeddings_norm_chat
        for data_type, embeddings_norm in embeddings_norm_dict.items():
            embeddings_norm_dict[data_type] = embeddings_norm.astype(np.float16)

        for data_type in all_text_dict.keys():
            # 数据
            all_text = all_text_dict[data_type]
            if len(all_text):
                embeddings_norm = embeddings_norm_dict[data_type]
            else:
                embeddings_norm = None
            text2norm_id = text2norm_id_dict[data_type]

            idx_list = np.arange(len(all_text))
            random.shuffle(idx_list)
            for i in tqdm(idx_list[:10000]):
                t = all_text[i]
                predict_result, _, _ = self.predict(model_id=model_id, query=t, query_list=[t], topk=3, query_embedding=embeddings_norm[i:i+1],
                                                    use_data_type=[data_type], test_flag=True)
                true_label_id = text2norm_id[t]
                if len(predict_result[data_type]):
                    all_count += 1
                    pred_label_id = predict_result[data_type][0].norm_query_id
                    if pred_label_id in true_label_id:
                        correct_count += 1
            print(f"测试准确率{data_type}预测完成,耗时:{time.time()-puzzl_label_start_time}")

        # 准确率
        acc = correct_count/max(all_count, 1)
        logger.debug(f"语料优化建议整理结束,准确率:{acc},耗时:{time.time()-puzzl_label_start_time}")
        self.offline_model(model_id=model_id)
        return acc

    def puzzl_label(self, model_id, save_dir):
        logger.debug(f"开始生成语料优化建议")
        puzzl_label_start_time = time.time()

        self.load_model(model_id=model_id, save_dir=save_dir)

        error_dict = {}
        labelids = []
        norm_id2norm_text_dict = self.models[model_id]['norm_id2norm_text_dict']
        all_text_dict = self.models[model_id]['all_text_dict']
        text2norm_id_dict = self.models[model_id]['text2norm_id_dict']
        embeddings_norm_dict = {}
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")
        if os.path.exists(os.path.join(save_dir, "embeddings_norm_dict.pkl")):
            with open(os.path.join(save_dir, "embeddings_norm_dict.pkl"), 'rb') as f:
                embeddings_norm_dict = pickle.load(f)
        else:
            embeddings_norm_intent = np.load(os.path.join(save_dir, 'embeddings_norm_intent.npy'), allow_pickle=True)
            embeddings_norm_faq = np.load(os.path.join(save_dir, 'embeddings_norm_faq.npy'), allow_pickle=True)
            embeddings_norm_chat = np.load(os.path.join(save_dir, 'embeddings_norm_chat.npy'), allow_pickle=True)
            embeddings_norm_dict["intent"] = embeddings_norm_intent
            embeddings_norm_dict["faq"] = embeddings_norm_faq
            embeddings_norm_dict["chat"] = embeddings_norm_chat
        for data_type, embeddings_norm in embeddings_norm_dict.items():
            embeddings_norm_dict[data_type] = embeddings_norm.astype(np.float16)

        correct_count = 0
        all_count = 0
        for data_type in norm_id2norm_text_dict.keys():
            # 数据
            all_text = all_text_dict[data_type]
            if len(all_text):
                embeddings_norm = embeddings_norm_dict[data_type]
            else:
                embeddings_norm = None
            text2norm_id = text2norm_id_dict[data_type]

            for i, t in tqdm(enumerate(all_text)):
                predict_result, _, _ = self.predict(model_id=model_id, query=t, topk=3, query_embedding=embeddings_norm[i:i+1],
                                                    use_data_type=[data_type], test_flag=True)
                all_count += 1
                true_label_id = text2norm_id[t]
                pred_label_id = predict_result[data_type][0].norm_query_id
                if true_label_id != pred_label_id:
                    key = (true_label_id, pred_label_id, data_type)
                    if key not in error_dict:
                        error_dict[key] = []
                    error_dict[key].append(t)
                else:
                    correct_count += 1
            print(f"语料优化建议{data_type}预测完成,耗时:{time.time()-puzzl_label_start_time}")

        suggest = []
        error_text_num = 0
        for key, value in error_dict.items():
            if len(value) >= 1:
                error_text_num += len(value)
                norm_id2norm_text = norm_id2norm_text_dict[key[2]]
                suggest.append({"lable": key[0], "pred_label": key[1], "期望意图": norm_id2norm_text[key[0]], "预测意图": norm_id2norm_text[key[1]], "texts": value, "num_text": len(value)})
        suggest.sort(key=lambda x: -len(x["texts"]))
        print(f"语料优化建议排序完成,耗时:{time.time()-puzzl_label_start_time}")

        # TFIDF 抽关键词
        all_text_list = []
        for all_text in all_text_dict.values():
            all_text_list += all_text
        corpos = [' '.join(jieba.lcut(text)) for text in all_text_list]
        vectorizer = CountVectorizer(token_pattern=r"(?u)\b\w+\b")
        transformer = TfidfTransformer()
        tfidf = transformer.fit_transform(vectorizer.fit_transform(corpos))
        labelids_set = []
        for data_type in norm_id2norm_text_dict.keys():
            labelids_set += list(norm_id2norm_text_dict[data_type].keys())
        labelids_set = list(set(labelids_set))
        labelid2tfidfvec = {}
        for i in labelids_set:
            vec = np.mean(tfidf[labelids == i], axis=0)
            labelid2tfidfvec[i] = vec.__array__()[0, :]
        for mixed_dict in suggest:
            pred_tfidf = labelid2tfidfvec[mixed_dict["pred_label"]]
            text_tfidf = transformer.transform(vectorizer.transform([' '.join(jieba.lcut(t)) for t in mixed_dict["texts"]]))
            text_tfidf = np.mean(text_tfidf, axis=0).__array__()[0, :]
            pred_tfidf = text_tfidf * pred_tfidf
            index = pred_tfidf.argsort()[-5:][::-1]
            key_words = [vectorizer.get_feature_names()[i] for i in index]
            mixed_dict["关键词"] = key_words
        print(f"语料优化建议抽取关键词完成,耗时:{time.time()-puzzl_label_start_time}")

        # 准确率
        acc = correct_count/max(all_count, 1)
        logger.debug(f"语料优化建议整理结束,准确率:{acc},suggest_length:{len(suggest)},error_text_num:{error_text_num},耗时:{time.time()-puzzl_label_start_time}")
        self.offline_model(model_id=model_id)
        return acc, suggest

    def test_threshold(self, model_id, save_dir, need_run=False):
        threshold_dict = {}
        if not need_run:
            logger.debug(f"直接返回默认的threshold")
            return threshold_dict

        logger.debug(f"开始估计threshold")
        threshold_start_time = time.time()
        self.load_model(model_id=model_id, save_dir=save_dir)

        text2norm_id_dict = self.models[model_id]['text2norm_id_dict']
        norm_id2norm_text_dict = self.models[model_id]['norm_id2norm_text_dict']
        all_text_2_idx_dict = self.models[model_id]['all_text_2_idx_dict']
        embeddings_norm_dict = {}
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")
        if os.path.exists(os.path.join(save_dir, "embeddings_norm_dict.pkl")):
            with open(os.path.join(save_dir, "embeddings_norm_dict.pkl"), 'rb') as f:
                embeddings_norm_dict = pickle.load(f)
        else:
            embeddings_norm_intent = np.load(os.path.join(save_dir, 'embeddings_norm_intent.npy'), allow_pickle=True)
            embeddings_norm_faq = np.load(os.path.join(save_dir, 'embeddings_norm_faq.npy'), allow_pickle=True)
            embeddings_norm_chat = np.load(os.path.join(save_dir, 'embeddings_norm_chat.npy'), allow_pickle=True)
            embeddings_norm_dict["intent"] = embeddings_norm_intent
            embeddings_norm_dict["faq"] = embeddings_norm_faq
            embeddings_norm_dict["chat"] = embeddings_norm_chat
        for data_type, embeddings_norm in embeddings_norm_dict.items():
            embeddings_norm_dict[data_type] = embeddings_norm.astype(np.float16)

        for data_type in text2norm_id_dict.keys():
            # 数据
            pos_scores = []
            neg_scores = []

            text2norm_id = text2norm_id_dict[data_type]
            norm_id2norm_text = norm_id2norm_text_dict[data_type]
            embeddings_norm = embeddings_norm_dict[data_type]
            all_text_2_idx = all_text_2_idx_dict[data_type]

            if len(norm_id2norm_text) <= 1:
                continue
            norm_id2texts = dict()
            for t, norm_id in text2norm_id.items():
                norm_id = norm_id[0]
                if norm_id not in norm_id2texts:
                    norm_id2texts[norm_id] = []
                norm_id2texts[norm_id].append(t)

            # 预测得分
            info_data = []
            sample_times = 2
            for norm_id, texts in tqdm(norm_id2texts.items()):
                for _ in range(sample_times):
                    a_pos_text = random.choice(texts)
                    emb_idx = all_text_2_idx[a_pos_text]
                    predict_result, _, _ = self.predict(model_id=model_id, query=a_pos_text, query_list=[a_pos_text], query_embedding=embeddings_norm[emb_idx: emb_idx+1, :],
                                                        topk=5, use_data_type=[data_type], test_flag=True)

                    min_neg_score = 100
                    for rank_return_object in predict_result[data_type]:
                        if rank_return_object.norm_query_id == norm_id:
                            pos_scores.append(rank_return_object.score)
                            #RankReturnObject(query=origin_query, sim_query=origin_sim_query, norm_query=origin_norm_query,norm_query_id=norm_query_id, score=scores[idx], save_bigger=True)
                            info_data.append({'data_type':data_type,'正确意图': norm_id2norm_text[norm_id],"命中意图":rank_return_object.norm_query,
                                              '相似句': a_pos_text,"命中相似句":rank_return_object.sim_query,
                                              'score': rank_return_object.score,"p/n":'pos'})
                        else:
                            if rank_return_object.score < min_neg_score:
                                min_neg_score = rank_return_object.score
                                info_data.append({'data_type': data_type, '正确意图': norm_id2norm_text[norm_id],
                                                  "命中意图": rank_return_object.norm_query,
                                                  '相似句': a_pos_text, "命中相似句": rank_return_object.sim_query,
                                                  'score': rank_return_object.score, "p/n": 'neg'})

                    if min_neg_score <= 1:
                        neg_scores.append(min_neg_score)

            info_data_df = pd.DataFrame(info_data)
            # info_data_df.to_csv(os.path.join(save_dir, f"info_data_{data_type}.csv"), index=False, encoding='utf-8-sig')

            plt.figure()
            plt.title(f"pos scores {data_type}")
            try:
                plt.hist(pos_scores, bins=30)
            except:
                print('plt.hist error')
            plt.xlim(0.0, 1)
            plt.savefig(os.path.join(save_dir, f"pos_scores_{data_type}.jpg"))
            # plt.show()

            plt.figure()
            plt.title(f"neg scores {data_type}")
            try:
                plt.hist(neg_scores, bins=30)
            except:
                print('plt.hist error')
            plt.xlim(0.0, 1)
            plt.savefig(os.path.join(save_dir, f"neg_scores_{data_type}.jpg"))
            # plt.show()

            pos_scores.sort()
            neg_scores.sort()
            if len(pos_scores) == 0 or len(neg_scores) == 0:
                continue
            complete_threshold = pos_scores[-max(len(pos_scores)//2, 1)]
            match_threshold = pos_scores[len(pos_scores)//20]
            neg_start = 1
            neg_end = len(neg_scores)//5+1
            neg_threshold = neg_scores[-neg_start]+0.01
            for i in range(neg_start, neg_end+1):
                neg_threshold = neg_scores[-i]+0.01
                if neg_threshold < match_threshold - 0.05:
                    break
            logger.debug(f"{data_type}的threshold计算完成:{neg_threshold},{match_threshold},{complete_threshold}")
            if neg_threshold >= match_threshold:
                continue
            if match_threshold >= complete_threshold:
                continue
            if data_type not in threshold_dict:
                threshold_dict[data_type] = [0.65, 0.75, 0.93]
            threshold_dict[data_type][0] = neg_threshold
            threshold_dict[data_type][1] = match_threshold
            threshold_dict[data_type][2] = complete_threshold
        self.offline_model(model_id=model_id)
        logger.debug(f"完成估计threshold,{threshold_dict},耗时:{time.time()-threshold_start_time}")
        return threshold_dict

    def test_threshold_aomen(self, model_id, save_dir, need_run=False):
        threshold_dict = {}
        if not need_run:
            logger.debug(f"直接返回默认的threshold")
            return threshold_dict

        logger.debug(f"开始估计threshold")
        threshold_start_time = time.time()
        self.load_model(model_id=model_id, save_dir=save_dir)

        text2norm_id_dict = self.models[model_id]['text2norm_id_dict']
        norm_id2norm_text_dict = self.models[model_id]['norm_id2norm_text_dict']
        all_text_2_idx_dict = self.models[model_id]['all_text_2_idx_dict']
        embeddings_norm_dict = {}
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")
        if os.path.exists(os.path.join(save_dir, "embeddings_norm_dict.pkl")):
            with open(os.path.join(save_dir, "embeddings_norm_dict.pkl"), 'rb') as f:
                embeddings_norm_dict = pickle.load(f)
        else:
            embeddings_norm_intent = np.load(os.path.join(save_dir, 'embeddings_norm_intent.npy'), allow_pickle=True)
            embeddings_norm_faq = np.load(os.path.join(save_dir, 'embeddings_norm_faq.npy'), allow_pickle=True)
            embeddings_norm_chat = np.load(os.path.join(save_dir, 'embeddings_norm_chat.npy'), allow_pickle=True)
            embeddings_norm_dict["intent"] = embeddings_norm_intent
            embeddings_norm_dict["faq"] = embeddings_norm_faq
            embeddings_norm_dict["chat"] = embeddings_norm_chat
        for data_type, embeddings_norm in embeddings_norm_dict.items():
            embeddings_norm_dict[data_type] = embeddings_norm.astype(np.float16)

        for data_type in text2norm_id_dict.keys():
            # 数据
            pos_scores = []
            small_neg_scores = []
            big_neg_scores = []

            for use_data_type in text2norm_id_dict.keys():

                text2norm_id = text2norm_id_dict[use_data_type]
                norm_id2norm_text = norm_id2norm_text_dict[use_data_type]
                embeddings_norm = embeddings_norm_dict[use_data_type]
                all_text_2_idx = all_text_2_idx_dict[use_data_type]
                if len(norm_id2norm_text) <= 1:
                    continue
                norm_id2texts = dict()
                for t, norm_id in text2norm_id.items():
                    if norm_id not in norm_id2texts:
                        norm_id2texts[norm_id] = []
                    norm_id2texts[norm_id].append(t)

                # 预测得分
                sample_times = 2
                for norm_id, texts in tqdm(norm_id2texts.items()):
                    for a_pos_text in texts:
                        emb_idx = all_text_2_idx[a_pos_text]
                        predict_result, _, _ = self.predict(model_id=model_id, query=a_pos_text, query_list=[a_pos_text], query_embedding=embeddings_norm[emb_idx: emb_idx+1, :],
                                                            topk=5, use_data_type=[data_type], test_flag=True)

                        min_neg_score = 100
                        big_neg_score = -100
                        for rank_return_object in predict_result[data_type]:
                            if rank_return_object.norm_query_id == norm_id:
                                pos_scores.append(rank_return_object.score)
                            else:
                                if rank_return_object.score < min_neg_score:
                                    min_neg_score = rank_return_object.score
                                if rank_return_object.score > big_neg_score:
                                    big_neg_score = rank_return_object.score
                                if rank_return_object.score > 0.9:
                                    print(f"{a_pos_text} ==> {rank_return_object.sim_query}  {rank_return_object.score}")

                        if min_neg_score <= 1:
                            small_neg_scores.append(min_neg_score)
                        if big_neg_score > 0:
                            big_neg_scores.append(big_neg_score)

            plt.figure()
            plt.title(f"pos scores {data_type}")
            try:
                plt.hist(pos_scores, bins=30)
            except:
                print('plt.hist error')
            plt.xlim(0.0, 1)
            plt.savefig(os.path.join(save_dir, f"pos_scores_{data_type}.jpg"))
            plt.show()

            plt.figure()
            plt.title(f"small neg scores {data_type}")
            try:
                plt.hist(small_neg_scores, bins=30)
            except:
                print('plt.hist error')
            plt.xlim(0.0, 1)
            plt.savefig(os.path.join(save_dir, f"small_neg_scores_{data_type}.jpg"))
            plt.show()

            plt.figure()
            plt.title(f"big neg scores {data_type}")
            try:
                plt.hist(big_neg_scores, bins=30)
            except:
                print('plt.hist error')
            plt.xlim(0.0, 1)
            plt.savefig(os.path.join(save_dir, f"big_neg_scores_{data_type}.jpg"))
            plt.show()

            pos_scores.sort()
            small_neg_scores.sort()
            big_neg_scores.sort()
            if len(pos_scores) == 0 or len(small_neg_scores) == 0:
                continue
            complete_threshold = pos_scores[-max(len(pos_scores)//2, 1)]
            match_threshold = pos_scores[len(pos_scores)//8]

            neg_threshold = small_neg_scores[int(len(small_neg_scores)*9/10)]
            if neg_threshold >= match_threshold - 0.1:
                neg_start = 1
                neg_end = len(small_neg_scores)//5+1
                neg_threshold = small_neg_scores[-neg_start]+0.01
                for i in range(neg_start, neg_end+1):
                    neg_threshold = small_neg_scores[-i]+0.01
                    if neg_threshold < match_threshold - 0.1:
                        break
            logger.debug(f"{data_type}的threshold计算完成:{neg_threshold},{match_threshold},{complete_threshold}")
            if neg_threshold >= match_threshold:
                continue
            if match_threshold >= complete_threshold:
                continue
            if data_type not in threshold_dict:
                threshold_dict[data_type] = [0.65, 0.75, 0.93]
            threshold_dict[data_type][0] = neg_threshold
            threshold_dict[data_type][1] = match_threshold
            threshold_dict[data_type][2] = complete_threshold
        self.offline_model(model_id=model_id)
        logger.debug(f"完成估计threshold,{threshold_dict},耗时:{time.time()-threshold_start_time}")
        return threshold_dict

    def train_time(self, data, is_inc=False):
        num_text = 0
        for data_dict in data:
            num_text += 1
            texts = data_dict.get("labelData", "").strip()
            num_text += len(texts.split("||"))
        num_batches = max(num_text//setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE, 1)

        try:
            if is_inc:
                self.adjusted_ratio_inc = float(self.redis.get_data(self.ratio_name_inc))
                logger.debug(f"读取redis的{self.ratio_name_inc}值为:{self.adjusted_ratio_inc}")
            else:
                self.adjusted_ratio = float(self.redis.get_data(self.ratio_name))
                logger.debug(f"读取redis的{self.ratio_name}值为:{self.adjusted_ratio}")
        except Exception as e:
            logger.warning(f"读取redis的{self.ratio_name_inc if is_inc else self.ratio_name}值失败,错误:{e}")

        estimated_time = num_batches  # 基准值
        if is_inc:
            adjusted_estimated_time = num_batches * self.adjusted_ratio_inc  # 预测值
        else:
            adjusted_estimated_time = num_batches * self.adjusted_ratio  # 预测值
        return estimated_time, adjusted_estimated_time

    def text_sim_scores(self, texts, model_path=None):
        from sklearn.metrics.pairwise import cosine_similarity
        if self.need_remove_front_end_symbol:
            texts = [remove_front_end_symbol(t) for t in texts]
        texts = [self.tra_and_sim_model.tra_or_sim(t, target="sim") for t in texts]

        # 加载预训练模型
        if model_path is None:
            model_path = setting.PRETRAIN_BERT_DIR
        config = BertConfig.from_pretrained(model_path)
        config.emb_layer = setting.SIMCSE_PRE_RANK_EMB_LAYER
        config.max_len = self.max_len
        tokenizer = AutoTokenizer.from_pretrained(model_path)

        # 计算相似度
        emb = self.encode_sentences([texts[0]], tokenizer)
        emb2 = self.encode_sentences(texts[1:], tokenizer)
        emb = np.vstack((emb, emb2))
        scores = cosine_similarity(emb, emb)
        result = []
        for i, t in enumerate(texts[1:]):
            result.append([t, float(scores[0, i+1])])
        result.sort(key=lambda x: -x[1])
        return result

    def get_data_bert(self, texts, max_len, tokenizer, return_tensor=False):
        if isinstance(texts, str):
            texts = [texts]
        data = {
            "input_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "token_type_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "attention_mask": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "length_mask": np.zeros(shape=(len(texts), max_len, self.config.hidden_size), dtype=np.float32),
            "length_div": np.ones(shape=(len(texts), self.config.hidden_size), dtype=np.float32)
        }
        for i, text in enumerate(texts):
            inputs = tokenizer.encode_plus(text, add_special_tokens=True, max_length=max_len, truncation=True, padding="max_length")
            data["input_ids"][i, :] = inputs["input_ids"]
            data["token_type_ids"][i, :] = inputs["token_type_ids"]
            data["attention_mask"][i, :] = inputs["attention_mask"]

        lengths = np.sum(data["attention_mask"], -1) - 2
        for i, l in enumerate(lengths):
            data["length_mask"][i, 1:l + 1, :] = 1
            if l < 1:
                raise Exception("句子长度有问题")
        for i, l in enumerate(lengths):
            data["length_div"][i, :] = l

        if return_tensor:
            data["input_ids"] = tf.convert_to_tensor(data["input_ids"])
            data["token_type_ids"] = tf.convert_to_tensor(data["token_type_ids"])
            data["attention_mask"] = tf.convert_to_tensor(data["attention_mask"])
            data["length_mask"] = tf.convert_to_tensor(data["length_mask"])
            data["length_div"] = tf.convert_to_tensor(data["length_div"])
        # data["length_div"] = None
        # data["length_mask"] = None
        return data

    def get_data_jina(self, texts, max_len, tokenizer, return_tensor=False):
        if isinstance(texts, str):
            texts = [texts]
        data = {
            "input_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int64),
            "attention_mask": np.zeros(shape=(len(texts), max_len), dtype=np.int64),
        }
        for i, text in enumerate(texts):
            inputs = tokenizer.encode_plus(text, add_special_tokens=True, max_length=max_len, truncation=True,padding='max_length')
            data["input_ids"][i, :] = inputs["input_ids"]
            data["attention_mask"][i, :] = inputs["attention_mask"]
        return data

if __name__ == "__main__":
    from sklearn.metrics.pairwise import cosine_similarity

    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    ranker = SimCSEPreRankDataType()

    emb_bert = ranker.encode_sentences(['专项附加扣除','专项附加扣除包括什么'], tokenizer=ranker.tokenizer_bert)
    emb_jina = ranker.encode_sentences(['专项附加扣除','专项附加扣除包括什么'], tokenizer=ranker.tokenizer_jina)
    print(cosine_similarity(emb_bert, emb_bert))
    print(cosine_similarity(emb_jina, emb_jina))

    emb_bert = ranker.encode_sentences(['皮肤科','皮肤科有咩新研究'], tokenizer=ranker.tokenizer_bert)
    emb_jina = ranker.encode_sentences(['皮肤科','皮肤科有咩新研究'], tokenizer=ranker.tokenizer_jina)
    print(cosine_similarity(emb_bert, emb_bert))
    print(cosine_similarity(emb_jina, emb_jina))

    emb_bert = ranker.encode_sentences(['帮我查一下今天的天气','今天气温如何'], tokenizer=ranker.tokenizer_bert)
    emb_jina = ranker.encode_sentences(['帮我查一下今天的天气','今天气温如何'], tokenizer=ranker.tokenizer_jina)
    print(cosine_similarity(emb_bert, emb_bert))
    print(cosine_similarity(emb_jina, emb_jina))


    # 训练
    with open("/data/cbk/skm_merge/aicc-nlp-nlu/demo/NLU测试模型和数据/old_model/train_data/1868908558578380802.json", "r", encoding="utf-8") as f:
        train_data = json.load(f)

    start = time.time()
    train_model_id = "test"
    train_model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{train_model_id}/{ranker.__class__.__name__}/")
    print(f"训练预估时间:{ranker.train_time(data=train_data, is_inc=False)}")
    train_result = ranker.train(model_id=train_model_id, data=train_data, save_dir=train_model_save_dir)
    print(f"训练得分: {train_result[0]}, 耗时: {time.time()-start}")
    ranker.load_model(model_id=train_model_id, save_dir=train_model_save_dir)
    pred_result = ranker.predict(model_id=train_model_id, query="续保500元优惠券怎么使用")
    context_result = ranker.predict(model_id=train_model_id, query="阿克苏振宇汽车销售有限责任公司展厅", context_input_process="1")

    
    with open("/data/cbk/skm_merge/aicc-nlp-nlu/demo/NLU测试模型和数据/old_model/train_data/1868908558578380802_inc.json", "r", encoding="utf-8") as f:
        train_data = json.load(f)
    start = time.time()
    train_model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{train_model_id}/{ranker.__class__.__name__}/")
    print(f"训练预估时间:{ranker.train_time(data=train_data, is_inc=False)}")
    train_result = ranker.increment_train(model_id=train_model_id, data=train_data, save_dir=train_model_save_dir)
    print(f"训练得分: {train_result[0]}, 耗时: {time.time()-start}")
    ranker.load_model(model_id=train_model_id, save_dir=train_model_save_dir)
    pred_result = ranker.predict(model_id=train_model_id, query="这是个测试样例啊啊")
    context_result = ranker.predict(model_id=train_model_id, query="阿克苏振宇汽车销售有限责任公司展厅", context_input_process="1")
    print("wait")


    # 兼容旧模型
    model_id = "old_model"
    save_dir = "/data/cbk/skm_merge/aicc-nlp-nlu/demo/NLU测试模型和数据/old_model/SimCSEPreRankDataType"
    ranker.load_model(model_id=model_id, save_dir=save_dir)
    pred_result = ranker.predict(model_id=model_id, query="这是个测试样例啊啊")
    

    # 兼容旧模型增量
    with open("/data/cbk/skm_merge/aicc-nlp-nlu/demo/NLU测试模型和数据/old_model/train_data/1868908558578380802_inc.json", "r", encoding="utf-8") as f:
        train_data = json.load(f)
    model_id = "old_model_inc"
    save_dir = "/data/cbk/skm_merge/aicc-nlp-nlu/demo/NLU测试模型和数据/old_model_inc/SimCSEPreRankDataType"
    train_result = ranker.increment_train(model_id=model_id, data=train_data, save_dir=save_dir)
    ranker.load_model(model_id=model_id, save_dir=save_dir)
    pred_result = ranker.predict(model_id=model_id, query="这是个测试样例啊啊")