#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:ss.py
@time:2022/10/08
"""
import synonyms
import jieba
import random


# 调换顺序
text = "稳利恒盈L12个月产品介绍"
temp_idx = random.randint(int(len(text) * 0.4), int(len(text) * 0.6))
text = text[temp_idx:] + text[:temp_idx]
print("wait")

# E-SimCSE
def esimcse_repeat_word(text):
    actual_len = len(text)
    dup_len = random.randint(a=0, b=max(2, int(0.15 * actual_len)))
    dup_word_index = random.sample(list(range(1, actual_len)), k=dup_len)
    dup_text = ''
    for index, word in enumerate(text):
        dup_text += word
        if index in dup_word_index:
            dup_text += word
    return dup_text
for _ in range(100):
    text = "稳利恒盈L12个月产品介绍"
    text2 = esimcse_repeat_word(text)
    print(text, " ", text2)
    if text == text2:
        print("wait")
print("wait")

# 分词替换
zh_vocabs = set()
with open("../../../data/中文词库/webdict_with_freq.txt", "r", encoding="utf-8") as f:
    for line in f:
        line = line.strip().split(" ")[0]
        if len(line) <= 5:
            zh_vocabs.add(line)

s = "稳利恒盈L12个月产品介绍"
a = jieba.lcut(s)
for i in a:
    b = synonyms.nearby(i[0], 10)
    print(i, b)

text = "这是1900年的东西"
text_vocab_list = jieba.lcut(text)
text_vocab_idx = [idx for idx in range(len(text_vocab_list))]
random.shuffle(text_vocab_idx)

print("wait")

# 判断短句长度
import pandas as pd
import json
import numpy as np
df_pretrain_webank_1393 = pd.read_csv("./tiny_error_df_webank_1393.csv", encoding="utf-8")
df_pretrain_tencent_boc = pd.read_csv("./tiny_error_df_tencent_boc.csv", encoding="utf-8")
df_webank_1393 = pd.read_csv("./error_df_webank_1393_(arc_tri_1_9_no_replace).csv", encoding="utf-8")
df_tencent_boc = pd.read_csv("./error_df_tencent_boc_(arc_tri_1_9_no_replace).csv", encoding="utf-8")

diff_list = []
for i in range(df_pretrain_tencent_boc.shape[0]):
    query = df_pretrain_tencent_boc["query"][i]
    sim_query = df_pretrain_tencent_boc["sim_query"][i]
    sim_query = sim_query.replace("'", '"')
    sim_query = json.loads(sim_query)
    diff_list.append(abs(len(query)-len(sim_query[0])))
print(f"df_pretrain_tencent_boc 长度差异值:{np.mean(diff_list)}")

diff_list = []
for i in range(df_pretrain_webank_1393.shape[0]):
    query = df_pretrain_webank_1393["query"][i]
    sim_query = df_pretrain_webank_1393["sim_query"][i]
    sim_query = sim_query.replace("'", '"')
    sim_query = json.loads(sim_query)
    diff_list.append(abs(len(query)-len(sim_query[0])))
print(f"df_pretrain_webank_1393 长度差异值:{np.mean(diff_list)}")

diff_list = []
for i in range(df_tencent_boc.shape[0]):
    query = df_tencent_boc["query"][i]
    sim_query = df_tencent_boc["sim_query"][i]
    sim_query = sim_query.replace("'", '"')
    sim_query = json.loads(sim_query)
    diff_list.append(abs(len(query)-len(sim_query[0])))
print(f"df_tencent_boc 长度差异值:{np.mean(diff_list)}")

diff_list = []
for i in range(df_webank_1393.shape[0]):
    query = df_webank_1393["query"][i]
    sim_query = df_webank_1393["sim_query"][i]
    sim_query = sim_query.replace("'", '"')
    sim_query = json.loads(sim_query)
    diff_list.append(abs(len(query)-len(sim_query[0])))
print(f"df_webank_1393 长度差异值:{np.mean(diff_list)}")

# 0.9
diff_list = []
for i in range(df_pretrain_tencent_boc.shape[0]):
    scores = df_pretrain_tencent_boc["scores"][i]
    scores = json.loads(scores)
    if scores[0] < 0.9:
        continue
    query = df_pretrain_tencent_boc["query"][i]
    sim_query = df_pretrain_tencent_boc["sim_query"][i]
    sim_query = sim_query.replace("'", '"')
    sim_query = json.loads(sim_query)
    diff_list.append(abs(len(query)-len(sim_query[0])))
print(f"df_pretrain_tencent_boc >0.9 长度差异值:{np.mean(diff_list)}")

diff_list = []
for i in range(df_pretrain_webank_1393.shape[0]):
    scores = df_pretrain_webank_1393["scores"][i]
    scores = json.loads(scores)
    if scores[0] < 0.9:
        continue
    query = df_pretrain_webank_1393["query"][i]
    sim_query = df_pretrain_webank_1393["sim_query"][i]
    sim_query = sim_query.replace("'", '"')
    sim_query = json.loads(sim_query)
    diff_list.append(abs(len(query)-len(sim_query[0])))
print(f"df_pretrain_webank_1393 >0.9 长度差异值:{np.mean(diff_list)}")

diff_list = []
for i in range(df_tencent_boc.shape[0]):
    scores = df_tencent_boc["scores"][i]
    scores = json.loads(scores)
    if scores[0] < 0.9:
        continue
    query = df_tencent_boc["query"][i]
    sim_query = df_tencent_boc["sim_query"][i]
    sim_query = sim_query.replace("'", '"')
    sim_query = json.loads(sim_query)
    diff_list.append(abs(len(query)-len(sim_query[0])))
print(f"df_tencent_boc >0.9 长度差异值:{np.mean(diff_list)}")

diff_list = []
for i in range(df_webank_1393.shape[0]):
    scores = df_webank_1393["scores"][i]
    scores = json.loads(scores)
    if scores[0] < 0.9:
        continue
    query = df_webank_1393["query"][i]
    sim_query = df_webank_1393["sim_query"][i]
    sim_query = sim_query.replace("'", '"')
    sim_query = json.loads(sim_query)
    diff_list.append(abs(len(query)-len(sim_query[0])))
print(f"df_webank_1393 >0.9 长度差异值:{np.mean(diff_list)}")
print("wait")
