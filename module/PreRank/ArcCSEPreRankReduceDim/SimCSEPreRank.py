# -*- coding: UTF-8 -*-

import json
import os
import pickle
import time

import jieba
import math
import numpy as np
import pandas as pd
from tqdm import tqdm

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.metrics.pairwise import normalize
from sklearn.model_selection import train_test_split
from transformers import BertTokenizer, BertConfig

from database.REDIS import REDIS
from module.PreRank.ArcCSEPreRankReduceDim.ArcCSE import ArcCSE
from module.PreRank.ArcCSEPreRankReduceDim.DataGenerator import DataGenerator
from setting import logger
from utils.my_utils import MyEncoder, complete_match_text, my_cosine_similarity, RankReturnObject
from model.BM25.BM25 import BM25model, InvertedIndex, HNSWModel, FAISSModel
from sklearn.feature_extraction.text import CountVectorizer, TfidfTransformer

class SimCSEPreRank:
    def __init__(self):
        self.models = dict()
        self.max_len = setting.ARCCSE_PRE_RANK_MAX_LEN
        self.batch_samples = setting.ARCCSE_PRE_RANK_BATCH_SAMPLES
        self.use_arc_loss = setting.ARCCSE_PRE_RANK_USE_ARC_LOSS
        self.use_tri_loss = setting.ARCCSE_PRE_RANK_USE_TRI_LOSS
        self.redis = REDIS()
        self.adjusted_ratio = 2
        self.adjusted_ratio_inc = 1
        self.ratio_name = "adjusted_ratio_" + str(self.__class__.__name__)
        self.ratio_name_inc = "adjusted_ratio_inc_" + str(self.__class__.__name__)

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        model_save_dir = save_dir
        start_time = time.time()
        estimated_time, adjusted_estimated_time = self.train_time(data=data, is_inc=False)
        logger.debug(f"全量训练{model_id},预估训练耗时:{round(adjusted_estimated_time,2)}秒,即{round(adjusted_estimated_time/60,3)}分钟,使用上一次预估耗时的调整比例值为:{self.adjusted_ratio}")
        logger.debug(f"全量训练{model_id},准备开始训练")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载预训练模型
        config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
        config.emb_layer = setting.ARCCSE_PRE_RANK_EMB_LAYER
        config.max_len = self.max_len
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
        logger.debug(f"全量训练{model_id},加载预训练模型成功")

        # 加载数据
        data_train, len_train, data_valid, len_valid, text2norm_id, norm_id2norm_text, text2item_cm, all_text = self.get_data_generator(data=data, tokenizer=tokenizer)
        logger.debug(f"全量训练{model_id},数据加载完成,训练集数据量:{len_train},验证集数据量:{len_valid}")

        # 训练模型
        inputs = model.get_data(["测试一下"], max_len=128)
        model(inputs)
        model.load_weights(os.path.join(setting.PRETRAIN_BERT_DIR, 'tf_model.h5'))
        model_file = os.path.join(save_dir, 'best_model.h5')
        model.save_weights(model_file)
        logger.debug(f"全量训练{model_id},保存模型文件成功")

        # 获取 all_text 的 embedding
        embeddings = self.encode_sentences(texts=all_text, model=model)
        embeddings_norm = normalize(embeddings, copy=True)
        logger.debug(f"全量训练{model_id},embedding结束")

        # 保存模型
        config.save_pretrained(save_dir)
        tokenizer.save_pretrained(save_dir)

        with open(os.path.join(save_dir, "text2norm_id.pkl"), 'wb') as f:
            pickle.dump(text2norm_id, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)
        np.save(os.path.join(save_dir, 'embeddings.npy'), embeddings)
        np.save(os.path.join(save_dir, 'embeddings_norm.npy'), embeddings_norm)

        # 检索模型训练
        if setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "bm25":
            search = BM25model(all_text)
        elif setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "hnsw":
            search = HNSWModel(all_text, embeddings_norm)
        elif setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "faiss":
            search = FAISSModel(all_text, embeddings_norm)
        else:
            search = InvertedIndex(all_text)

        #联想输入模型训练
        context_input_process_search = BM25model(list(norm_id2norm_text.values()))
        with open(os.path.join(save_dir, "search_rank.pkl"), 'wb') as f:
            pickle.dump(search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_context_input.pkl"), 'wb') as f:
            pickle.dump(context_input_process_search, f, pickle.HIGHEST_PROTOCOL)
        logger.debug(f"全量训练{model_id},保存结束")

        # 准确率
        logger.debug(f"全量训练{model_id},开始测试准确率")
        acc_list = self.test_acc(model_id=model_id, data=data, topk=5, model_save_dir=model_save_dir)
        score = acc_list[0]
        with open(os.path.join(save_dir, 'acc_list.json'), 'w', encoding='utf-8') as f:
            json.dump(acc_list, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        logger.debug(f"全量训练{model_id},测试准确率成功,acc:{acc_list}")

        # 语料优化建
        logger.debug(f"全量训练{model_id},开始测试语料优化建议")
        mixed = self.puzzl_label(model_id, save_dir, data)
        with open(os.path.join(save_dir, "mixed.pkl"), 'wb') as f:
            pickle.dump(mixed, f, pickle.HIGHEST_PROTOCOL)
        mixed_df = pd.DataFrame(mixed)
        mixed_df.to_csv(os.path.join(model_save_dir, "suggest.csv"), index=False, encoding="utf-8-sig")
        logger.debug(f"全量训练{model_id},语料优化建议结束")

        # 计算阈值
        self.test_threshold(model_id=model_id, data_pos=data, data_neg=None, model_save_dir=model_save_dir)

        # 比例值写入redis
        real_train_time = time.time() - start_time
        self.adjusted_ratio = round(real_train_time/estimated_time, 4)
        self.redis.set_data(self.ratio_name, str(self.adjusted_ratio))
        logger.debug(f"全量训练{model_id},修正后预估耗时:{round(estimated_time*self.adjusted_ratio,2)}秒,即{round(estimated_time*self.adjusted_ratio/60,3)}分钟,更新预估耗时的调整比例值为:{self.adjusted_ratio}")

        # 训练成功
        logger.debug(f"全量训练{model_id},训练成功,耗时:{time.time()-start_time}s,数据量:{len_train+len_valid},batch:{setting.BERT_CLASSIFICATION_BATCH_SIZE}")
        return score, mixed

    def increment_train(self, model_id, data, save_dir):
        model_save_dir = save_dir
        start_time = time.time()
        estimated_time, adjusted_estimated_time = self.train_time(data=data, is_inc=True)
        logger.debug(f"增量训练{model_id},预估训练耗时:{round(adjusted_estimated_time,2)}秒,即{round(adjusted_estimated_time/60,3)}分钟,使用上一次预估耗时的调整比例值为:{self.adjusted_ratio_inc}")
        logger.debug(f"增量训练{model_id},准备开始训练")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载预训练模型
        config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
        config.emb_layer = setting.ARCCSE_PRE_RANK_EMB_LAYER
        config.max_len = self.max_len
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
        logger.debug(f"增量训练{model_id},加载预训练模型成功")

        # 加载旧模型数据
        with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
            old_all_text = pickle.load(f)
        old_all_text_to_idx = {}
        for i, t in enumerate(old_all_text):
            old_all_text_to_idx[t] = i
        emb_dim = None
        if len(old_all_text):
            old_embeddings = np.load(os.path.join(save_dir, 'embeddings.npy'), allow_pickle=True)
            emb_dim = old_embeddings.shape[1]
        else:
            old_embeddings = None
        logger.debug(f"增量训练{model_id},加载旧模型数据成功")

        # 加载数据
        data_train, len_train, data_valid, len_valid, text2norm_id, norm_id2norm_text, text2item_cm, all_text = self.get_data_generator(data=data, tokenizer=tokenizer)
        logger.debug(f"增量训练{model_id},数据加载完成,训练集数据量:{len_train},验证集数据量:{len_valid}")

        # 训练模型
        inputs = model.get_data(["测试一下"], max_len=128)
        model(inputs)
        model.load_weights(os.path.join(setting.PRETRAIN_BERT_DIR, 'tf_model.h5'))
        model_file = os.path.join(save_dir, 'best_model.h5')
        model.save_weights(model_file)
        logger.debug(f"增量训练{model_id},保存模型文件成功")

        # 获取 all_text 的 embedding
        if len(all_text):
            embeddings = np.zeros(shape=(len(all_text), emb_dim))
            text_need_encode_list = []
            text_need_encode_idx_list = []
            for idx, t in enumerate(all_text):
                old_idx = old_all_text_to_idx.get(t, -1)
                if old_idx >= 0:
                    embeddings[idx, :] = old_embeddings[old_idx, :]
                else:
                    text_need_encode_idx_list.append(idx)
                    text_need_encode_list.append(t)
            if len(text_need_encode_list):
                embeddings_need_encode = self.encode_sentences(texts=text_need_encode_list, model=model)
                embeddings[text_need_encode_idx_list, :] = embeddings_need_encode
            embeddings_norm = normalize(embeddings, copy=True)
        else:
            embeddings = None
            embeddings_norm = None
        logger.debug(f"增量训练{model_id},embedding结束")

        # 保存模型
        config.save_pretrained(save_dir)
        tokenizer.save_pretrained(save_dir)

        with open(os.path.join(save_dir, "text2norm_id.pkl"), 'wb') as f:
            pickle.dump(text2norm_id, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)
        np.save(os.path.join(save_dir, 'embeddings.npy'), embeddings)
        np.save(os.path.join(save_dir, 'embeddings_norm.npy'), embeddings_norm)

        # 检索模型训练
        if setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "bm25":
            search = BM25model(all_text)
        elif setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "hnsw":
            search = HNSWModel(all_text, embeddings_norm)
        elif setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "faiss":
            search = FAISSModel(all_text, embeddings_norm)
        else:
            search = InvertedIndex(all_text)

        #联想输入模型训练
        context_input_process_search = BM25model(list(norm_id2norm_text.values()))
        with open(os.path.join(save_dir, "search_rank.pkl"), 'wb') as f:
            pickle.dump(search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_context_input.pkl"), 'wb') as f:
            pickle.dump(context_input_process_search, f, pickle.HIGHEST_PROTOCOL)
        logger.debug(f"增量训练{model_id},保存结束")

        # 准确率
        with open(os.path.join(save_dir, 'acc_list.json'), 'r', encoding='utf-8') as f:
            acc_list = json.load(f)
        score = acc_list[0]
        logger.debug(f"增量训练{model_id},使用全量的准确率,acc:{acc_list}")

        # 语料优化建
        with open(os.path.join(save_dir, "mixed.pkl"), 'rb') as f:
            mixed = pickle.load(f)
        logger.debug(f"增量训练{model_id},使用全量的优化建议")

        # 比例值写入redis
        real_train_time = time.time() - start_time
        self.adjusted_ratio_inc = round(real_train_time/estimated_time, 4)
        self.redis.set_data(self.ratio_name_inc, str(self.adjusted_ratio_inc))
        logger.debug(f"增量训练{model_id},修正后预估耗时:{round(estimated_time*self.adjusted_ratio_inc,2)}秒,即{round(estimated_time*self.adjusted_ratio_inc/60,3)}分钟,更新预估耗时的调整比例值为:{self.adjusted_ratio_inc}")

        # 训练成功
        logger.debug(f"增量训练{model_id},训练成功,耗时:{time.time()-start_time}s,数据量:{len_train+len_valid},batch:{setting.BERT_CLASSIFICATION_BATCH_SIZE}")
        return score, mixed

    def load_model(self, model_id, save_dir):
        try:
            tokenizer = BertTokenizer.from_pretrained(os.path.join(save_dir, "vocab.txt"))
            config = BertConfig.from_pretrained(save_dir)
            model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
            inputs = model.get_data(["测试一下"], max_len=128)
            model(inputs)
            model.load_weights(os.path.join(setting.PRETRAIN_BERT_DIR, 'tf_model.h5'))

            with open(os.path.join(save_dir, "text2norm_id.pkl"), 'rb') as f:
                text2norm_id = pickle.load(f)
            with open(os.path.join(save_dir, "norm_id2norm_text.pkl"), 'rb') as f:
                norm_id2norm_text = pickle.load(f)
            with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
                all_text = pickle.load(f)
            with open(os.path.join(save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)
            embeddings = np.load(os.path.join(save_dir, 'embeddings.npy'), allow_pickle=True)
            embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm.npy'), allow_pickle=True)

            # END (兼容旧模型) 检查是否有 search 文件，如果不存在要重新生成
            with open(os.path.join(save_dir, "search_rank.pkl"), 'rb') as f:
                search = pickle.load(f)
            with open(os.path.join(save_dir, "search_context_input.pkl"), 'rb') as f:
                context_input_process_search = pickle.load(f)

            all_text_2_idx = dict()
            for i, text in enumerate(all_text):
                all_text_2_idx[text] = i

            try:
                with open(os.path.join(save_dir, 'threshold_dict.json'), 'r', encoding='utf-8') as f:
                    threshold_dict = json.load(f)
            except:
                threshold_dict = {"complete_score": 0.98, "threshold": 0.9, "low_threshold": 0.75}

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['model'] = model
            self.models[model_id]['text2norm_id'] = text2norm_id
            self.models[model_id]['norm_id2norm_text'] = norm_id2norm_text
            self.models[model_id]['all_text'] = all_text
            self.models[model_id]['all_text_2_idx'] = all_text_2_idx
            self.models[model_id]['text2item_cm'] = text2item_cm
            self.models[model_id]['embeddings'] = embeddings
            self.models[model_id]['embeddings_norm'] = embeddings_norm.astype(np.float16)
            self.models[model_id]['search'] = search
            self.models[model_id]['context_input_process_search'] = context_input_process_search
            self.models[model_id]["threshold_dict"] = threshold_dict

            self.predict(model_id=model_id, query="测试一下", topk=2)
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, query, query_list=None, topk=50, context_input_process='', test_flag=False, remove_stop_words_flag=False):
        result = []
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": ""
        }

        if model_id not in self.models:
            try:
                is_exist = self.check_model_file_exist(model_id=model_id)
                if is_exist:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91024"
                    error_dict["error_msg"] = "预测错误: 模型未加载，请重新上线"
                else:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91025"
                    error_dict["error_msg"] = "预测错误: 模型未加载，模型文件缺失，请重新训练"
            except Exception as ee:
                error_dict["error_type"] = 0
                error_dict["error_code"] = "NLU91017"
                error_dict["error_msg"] = f"预测错误: 模型未加载, {ee}"
            logger.error(f"预测失败 [{model_id}], 错误信息: {error_dict['error_msg']}")
            return result, error_dict, {}

        norm_id2norm_text = self.models[model_id]['norm_id2norm_text']
        text2norm_id = self.models[model_id]['text2norm_id']
        all_text = self.models[model_id]['all_text']
        all_text_2_idx = self.models[model_id]['all_text_2_idx']
        text2item_cm = self.models[model_id]['text2item_cm']
        embeddings_norm = self.models[model_id]['embeddings_norm']
        model = self.models[model_id]['model']
        search = self.models[model_id]['search']
        context_input_process_search = self.models[model_id]['context_input_process_search']
        threshold_dict = self.models[model_id]['threshold_dict']

        #联想输入
        if context_input_process:
            context_input_process = int(context_input_process) if int(context_input_process) != 1 else 10
            recall_text = context_input_process_search.search(query, 200, True)[:context_input_process]
            recall_id = [text2norm_id.get(i, '') for i in recall_text]
            return (recall_id, recall_text), error_dict

        # 去除停用词，暂定先去除语气词
        def remove_stop_words(ori_text):
            all_stop_words = "么阿啊啦唉呢吧哇呀吗哦噢喔呵嘿吁吖呗咩哎"
            new_text = ori_text
            for w in all_stop_words:
                new_text = new_text.replace(w, "")
            if len(new_text):
                return new_text
            else:
                return ori_text

        if remove_stop_words_flag:
            logger.debug(f"需要移除停用词,移除前:{query}, {query_list}")
            query = remove_stop_words(query)
            if query_list and len(query_list):
                query_list = [remove_stop_words(q) for q in query_list]
            logger.debug(f"需要移除停用词,移除后:{query}, {query_list}")

        # 精确匹配
        if not test_flag:
            if query_list is None or len(query_list) == 0:
                query_list = [query]
            for temp_query in query_list:
                temp_query = complete_match_text(temp_query)
                if temp_query in text2item_cm:
                    norm_query, norm_query_id = text2item_cm[temp_query]
                    item = RankReturnObject(query=query, sim_query=temp_query, norm_query=norm_query,
                                            norm_query_id=norm_query_id, score=1, save_bigger=True)
                    result.append(item)
                    return result, error_dict, threshold_dict

        # 粗排
        emb = self.encode_sentences(texts=[query], model=model)

        if setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "hnsw":
            recall_text, _ = search.search(emb, min(len(all_text), setting.ARCCSE_PRE_RANK_SEARCH_RETURN))
        elif setting.ARCCSE_PRE_RANK_SEARCH_METHOD == "faiss":
            recall_text = search.search(emb, setting.ARCCSE_PRE_RANK_SEARCH_RETURN)
        else:
            recall_text = search.search(query, setting.ARCCSE_PRE_RANK_SEARCH_RETURN)

        # if test_flag:
        #     recall_text = all_text

        if not test_flag:
            logger.debug(f"粗排结束: 检索数据量 {len(recall_text)}")
        indexer = [all_text_2_idx.get(i, 0) for i in recall_text]
        select_emb = embeddings_norm[indexer]

        scores = my_cosine_similarity(select_emb, emb.astype(np.float16))[:, 0]
        scores = scores.clip(min=0, max=1)
        index = np.argsort(scores)
        index = index[::-1]
        norm_query_id_set = set()
        if not test_flag:
            logger.debug(f"排序结束")

        for idx in index:
            sim_query = recall_text[idx]
            if test_flag and sim_query == query:
                continue
            norm_query_id = text2norm_id[sim_query]
            if norm_query_id not in norm_query_id_set:
                norm_query_id_set.add(norm_query_id)
                norm_query = norm_id2norm_text[norm_query_id]
                item = RankReturnObject(query=query, sim_query=sim_query, norm_query=norm_query,
                                        norm_query_id=norm_query_id, score=scores[idx], save_bigger=True)
                result.append(item)
            if len(result) >= topk:
                break
        if not test_flag:
            result_ = [item.__dict__ for item in result]
            logger.debug(f"返回结果: {result_}")
        return result, error_dict, threshold_dict

    def labeling(self, model_id, texts, text_ids=None):
        labeling_result = []

        text2norm_id = self.models[model_id]['text2norm_id']
        all_text = self.models[model_id]['all_text']
        embeddings_norm = self.models[model_id]['embeddings_norm']  # [m, dim]
        model = self.models[model_id]['model']  # [m, dim]

        embeddings = self.encode_sentences(texts=texts, model=model)  # [n, dim]

        scores = my_cosine_similarity(embeddings_norm, embeddings)  # [m, n]
        max_idx = np.argmax(scores, axis=0)

        for i in range(max_idx.shape[0]):
            result = {
                "text": texts[i],
                "id": text_ids[i] if text_ids is not None else None,
                "label": text2norm_id[all_text[max_idx[i]]],
                "score": scores[max_idx[i], i],
                "margin_score": scores[max_idx[i], i]
            }
            labeling_result.append(result)
        return labeling_result

    def get_data(self, data):
        pass

    @staticmethod
    def get_data_generator(data, tokenizer=None):
        # 整理数据
        text2norm_id = dict()
        norm_id2norm_text = dict()
        text2item_cm = dict()  # 精确匹配字典
        text_list_train = []
        text_list_valid = []

        for data_dict in data:
            norm_query = data_dict['title'].strip()
            norm_query_id = data_dict['labelId']
            texts = set()
            texts.add(norm_query)

            norm_id2norm_text[norm_query_id] = norm_query
            text2norm_id[norm_query] = norm_query_id
            norm_query_ = complete_match_text(norm_query)
            if len(norm_query_):
                text2item_cm[norm_query_] = [norm_query, norm_query_id]

            for text in data_dict["labelData"].split("||"):
                text = text.strip()
                if len(text):
                    texts.add(text)
                    text2norm_id[text] = norm_query_id
                text = complete_match_text(text)
                if len(text):
                    text2item_cm[text] = [norm_query, norm_query_id]

            texts = list(texts)
            if len(texts) <= 0:
                continue
            elif len(texts) == 1:
                text_list_train.append(texts)
            else:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                text_list_train.append(texts_train)
                text_list_valid.append(texts_valid)

        all_text = list(text2norm_id.keys())

        num_train_text = np.sum([len(text_list) for text_list in text_list_train])
        num_valid_text = np.sum([len(text_list) for text_list in text_list_valid])
        data_gen_train = DataGenerator(text_list=text_list_train, tokenizer=tokenizer)
        data_gen_valid = DataGenerator(text_list=text_list_valid, tokenizer=tokenizer) if num_valid_text else None
        return data_gen_train, num_train_text, data_gen_valid, num_valid_text, text2norm_id, norm_id2norm_text, text2item_cm, all_text

    def encode_sentences(self, texts, model):
        embeddings = list()

        for start_idx in range(0, len(texts), setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE):
            end_idx = min(start_idx+setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE, len(texts))
            inputs = model.get_data(texts=texts[start_idx:end_idx], max_len=self.max_len, return_tensor=True)
            emb = model.get_encoder_layer(**inputs)
            embeddings.append(emb.numpy())

        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings

    def check_model_file_exist(self, model_id):
        model_file_exist = True
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")

        try:
            tokenizer = BertTokenizer.from_pretrained(save_dir)
            config = BertConfig.from_pretrained(save_dir)
            _ = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
            with open(os.path.join(save_dir, "text2norm_id.pkl"), 'rb') as f:
                _ = pickle.load(f)
            with open(os.path.join(save_dir, "norm_id2norm_text.pkl"), 'rb') as f:
                _ = pickle.load(f)
            with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
                _ = pickle.load(f)
            with open(os.path.join(save_dir, "text2item_cm.pkl"), 'rb') as f:
                _ = pickle.load(f)
            with open(os.path.join(save_dir, "search_rank.pkl"), 'rb') as f:
                _ = pickle.load(f)
            _ = np.load(os.path.join(save_dir, 'embeddings.npy'), allow_pickle=True)
            _ = np.load(os.path.join(save_dir, 'embeddings_norm.npy'), allow_pickle=True)
        except:
            model_file_exist = False
        return model_file_exist

    def test_acc(self, model_id, data, topk=5, model_save_dir=""):
        """
        用 data 中的文本测试 topk 准确率, 需要返回 topk+1 个结果，因为包含自身。
        耗时: 30 分钟
        """
        total = 0
        acc = [0] * topk

        # 加载模型
        self.load_model(model_id=model_id, save_dir=model_save_dir)

        # 整理数据
        text_list_valid = []
        text2norm_id = {}
        for data_dict in data:
            norm_query = data_dict['title'].strip()
            norm_query_id = data_dict['labelId']
            texts = set()
            texts.add(norm_query)
            text2norm_id[norm_query] = norm_query_id
            for text in data_dict["labelData"].split("||"):
                text = text.strip()
                if len(text):
                    texts.add(text)
                    text2norm_id[text] = norm_query_id
            texts = list(texts)
            if len(texts) > 1:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                text_list_valid.append(texts_valid)

        pbar = tqdm(text_list_valid)
        for text_list in pbar:
            pbar.set_description('测试进度')
            for text in text_list:
                norm_query_id = text2norm_id[text]
                heap, _, _ = self.predict(model_id, text, topk=topk, test_flag=True)
                pred_ids = np.array([item.norm_query_id for item in heap])
                for j in range(min(len(heap), topk)):
                    if norm_query_id in pred_ids[:j + 1]:
                        for k in range(j, topk):
                            acc[k] += 1
                        break
                total += 1
            acc_dict = {f'acc{topk}': acc[topk - 1] / total}
            pbar.set_postfix(acc_dict)

        self.offline_model(model_id=model_id)
        with open(os.path.join(model_save_dir, 'test_result.txt'), 'w', encoding='utf-8') as f:
            for i in range(topk):
                line = f'top {i + 1} 准确率: {acc[i] / total}'
                print(line)
                f.writelines(line + '\n')
        return [a / total for a in acc]

    def puzzl_label(self, model_id, save_dir, data):
        puzzl_label_start_time = time.time()
        texts = []
        labelids = []
        for data_dict in data:
            norm_query = data_dict['title'].strip()
            labelid = data_dict["labelId"]
            texts.append(norm_query)
            labelids.append(labelid)
            for t in data_dict["labelData"].strip().split("||"):
                if len(t.strip()):
                    texts.append(t.strip())
                    labelids.append(labelid)
        print(f"puzzl 数据量:{len(texts)}, time:{time.time()-puzzl_label_start_time}")

        self.load_model(model_id=model_id, save_dir=save_dir)
        norm_id2norm_text = self.models[model_id]['norm_id2norm_text']

        error_dict = {}
        acc = 0
        for i, text in tqdm(enumerate(texts)):
            heap, _, _ = self.predict(model_id, text, topk=1, test_flag=True)
            true_label_id = labelids[i]
            pred_label_id = heap[0].norm_query_id
            if true_label_id == pred_label_id:
                acc += 1
                continue
            key = (true_label_id, pred_label_id)
            if key not in error_dict:
                error_dict[key] = []
            error_dict[key].append(texts[i])
        logger.debug(f"语料优化建议, acc:{acc/len(texts)}")
        self.offline_model(model_id=model_id)

        mixed = []
        error_text_num = 0
        for key, value in error_dict.items():
            if len(value) >= 1:
                error_text_num += len(value)
                mixed.append({"lable": key[0], "pred_label": key[1], "期望意图": norm_id2norm_text[key[0]], "预测意图": norm_id2norm_text[key[1]], "texts": value, "num_text": len(value)})
        mixed.sort(key=lambda x: -len(x["texts"]))
        print(f"puzzl 整理结束, mixed len:{len(mixed)}, error_text_num:{error_text_num}, time:{time.time()-puzzl_label_start_time}")

        # TFIDF 抽关键词
        corpos = [' '.join(jieba.lcut(text)) for text in texts]
        vectorizer = CountVectorizer(token_pattern=r"(?u)\b\w+\b")
        transformer = TfidfTransformer()
        tfidf = transformer.fit_transform(vectorizer.fit_transform(corpos))
        labelids_set = list(set(labelids))  # np.array(labelids)
        labelid2tfidfvec = {}
        for i in labelids_set:
            if i in labelids:
                vec = np.mean(tfidf[labelids == i], axis=0)
                labelid2tfidfvec[i] = vec.__array__()[0, :]
            else:
                labelid2tfidfvec[i] = np.zeros(shape=(len(vectorizer.vocabulary_)))
        for mixed_dict in mixed:
            pred_tfidf = labelid2tfidfvec[mixed_dict["pred_label"]]
            text_tfidf = transformer.transform(
                vectorizer.transform([' '.join(jieba.lcut(t)) for t in mixed_dict["texts"]]))
            text_tfidf = np.mean(text_tfidf, axis=0).__array__()[0, :]
            pred_tfidf = text_tfidf * pred_tfidf
            index = pred_tfidf.argsort()[-5:][::-1]
            key_words = [vectorizer.get_feature_names()[i] for i in index]
            mixed_dict["关键词"] = key_words
        print(f"puzzl 抽取关键词结束, 单词数:{len(vectorizer.get_feature_names())}, time:{time.time()-puzzl_label_start_time}")
        return mixed

    def test_threshold(self, model_id, data_pos, data_neg=None, model_save_dir=""):
        self.load_model(model_id=model_id, save_dir=model_save_dir)

        # 正样本
        all_texts = set()
        for data_dict in data_pos:
            norm_query = data_dict['title'].strip()
            texts = set()
            texts.add(norm_query)

            for text in data_dict["labelData"].split("||"):
                text = text.strip()
                if len(text):
                    texts.add(text)

            texts = list(texts)
            if len(texts) <= 1:
                continue
            else:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                for text in texts_valid:
                    all_texts.add(text)

        scores_pos = []
        for text in tqdm(all_texts):
            heap, _, _ = self.predict(model_id, text, topk=2, test_flag=True)
            if heap[0].sim_query == text:
                scores_pos.append([text, heap[1].sim_query, heap[1].score])
            else:
                scores_pos.append([text, heap[0].sim_query, heap[0].score])

        with open(os.path.join(model_save_dir, 'test_threshold_pos.json'), 'w', encoding='utf-8') as f:
            json.dump(scores_pos, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

        pos = [i[2] for i in scores_pos]
        pos.sort()

        # complete_match_scores = pos[int(len(pos)*0.9)]
        complete_match_scores = np.mean(pos)
        # match_scores = math.cos(math.acos(pos[int(len(pos)*0.05)]) - math.pi / 18)
        # match_scores = pos[int(len(pos)*0.05)]
        match_scores = math.cos(math.acos(np.mean(pos)) + math.pi / 18)
        no_match_scores = math.cos(math.acos(match_scores) + math.pi / 18)
        threshold_dict = {
            "complete_score": complete_match_scores,
            "threshold": match_scores,
            "low_threshold": no_match_scores
        }
        with open(os.path.join(model_save_dir, 'threshold_dict.json'), 'w', encoding='utf-8') as f:
            json.dump(threshold_dict, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

        if data_neg is not None:
            # 负样本
            all_texts = set()
            for data_dict in data_neg:
                norm_query = data_dict['title'].strip()
                all_texts.add(norm_query)
                for text in data_dict["labelData"].split("||"):
                    text = text.strip()
                    if len(text):
                        all_texts.add(text)
                        break

            scores_neg = []
            for text in tqdm(all_texts):
                heap, _, _ = self.predict(model_id, text, topk=1, test_flag=True)
                scores_neg.append([text, heap[0].sim_query, heap[0].score])

            with open(os.path.join(model_save_dir, 'test_threshold_neg.json'), 'w', encoding='utf-8') as f:
                json.dump(scores_neg, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

            neg = [i[2] for i in scores_neg]
            neg.sort()

            # 画图
            from matplotlib import pyplot as plt
            plt.figure()
            plt.title(f"pos scores {model_id}")
            plt.hist(pos, bins=30)
            plt.xlim(0.0, 1)
            plt.savefig(os.path.join(model_save_dir, f"pos_scores.jpg"))
            plt.show()
            plt.figure()
            plt.title(f"neg scores {model_id}")
            plt.hist(neg, bins=30)
            plt.xlim(0.0, 1)
            plt.savefig(os.path.join(model_save_dir, f"neg_scores.jpg"))
            plt.show()

        self.offline_model(model_id=model_id)

    def train_time(self, data, is_inc=False):
        num_text = 0
        for data_dict in data:
            num_text += 1
            texts = data_dict["labelData"].strip()
            num_text += len(texts.split("||"))
        num_batches = max(num_text//setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE, 1)

        try:
            if is_inc:
                self.adjusted_ratio_inc = float(self.redis.get_data(self.ratio_name_inc))
                logger.debug(f"读取redis的{self.ratio_name_inc}值为:{self.adjusted_ratio_inc}")
            else:
                self.adjusted_ratio = float(self.redis.get_data(self.ratio_name))
                logger.debug(f"读取redis的{self.ratio_name}值为:{self.adjusted_ratio}")
        except Exception as e:
            logger.warning(f"读取redis的{self.ratio_name_inc if is_inc else self.ratio_name}值失败,错误:{e}")

        estimated_time = num_batches  # 基准值
        if is_inc:
            adjusted_estimated_time = num_batches * self.adjusted_ratio_inc  # 预测值
        else:
            adjusted_estimated_time = num_batches * self.adjusted_ratio  # 预测值
        return estimated_time, adjusted_estimated_time


if __name__ == "__main__":
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    ranker = SimCSEPreRank()
    R = REDIS()
    # data_faq = R.get_data("faq_813_1123_data")
    # data_faq = R.get_data("faq_webank_hard_data")
    # data_faq = R.get_data(f'webank_train_data_1393_0902')[:]
    data_faq = R.get_data(f'faq_model1_all')[:]

    # 训练 faq 模型
    start = time.time()
    train_model_id = f"train_model_id"
    train_model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{train_model_id}/{ranker.__class__.__name__}/")
    print(f"训练预估时间:{ranker.train_time(data=data_faq, is_inc=False)}")
    train_score, _ = ranker.train(model_id=train_model_id, data=data_faq, save_dir=train_model_save_dir)
    train_time = time.time()-start
    print(f"训练得分: {train_score}, 耗时: {time.time()-start}")
    print("wait")

    ranker.load_model(model_id=train_model_id, save_dir=train_model_save_dir)

    # 测试阈值
    data_neg_test = R.get_data(f'faq_813_1123_data')
    ranker.test_threshold(model_id=train_model_id, data_pos=data_faq, data_neg=data_neg_test, model_save_dir=train_model_save_dir)

    # search faq
    test_query_list = ['请问余额理财赎回到账时间？', '用IE登录个人网银出现空白页，怎么办??']
    for test_query in test_query_list:
        pred_texts, _ = ranker.predict(model_id=train_model_id, query=test_query, topk=5)
        print(f'\n查询句子: {test_query}')
        for text_item in pred_texts:
            print(f'返回结果: {text_item.sim_query}, 置信度: {text_item.score}')

    # # search labeling
    # labeling_data = R.get_data(f'faq_labeling_test_data')
    # texts = []
    # text_ids = []
    # for data_dict in labeling_data:
    #     texts.append(data_dict["text"])
    #     text_ids.append(data_dict["id"])
    # label_result = ranker.labeling(model_id=model_id, texts=texts, text_ids=text_ids)
    # print(label_result)

    # # 测试1 粗排 top_k 准确率
    # ranker.test_acc(model_id=model_id, data=data_faq, topk=5, model_save_dir=model_save_dir)
    # while True:
    #     query = input("输入:")
    #     texts, _ = ranker.predict(model_id=model_id, query=query, topk=5)
    #     print(f'\n查询句子: {query}')
    #     for text_item in texts:
    #         print(f'返回结果: {text_item.sim_query}, 置信度: {text_item.score}')

    # # 测试阈值
    # data_chat = R.get_data(f'faq_chat_model1_all')
    # ranker.test_threshold(model_id=model_id, data_pos=data_faq, data_neg=data_chat, model_save_dir=model_save_dir)

"""
tiny bert tencent data
top 1 准确率: 0.9537103746397695
top 2 准确率: 0.989193083573487
top 3 准确率: 0.99585734870317
top 4 准确率: 0.9969380403458213
top 5 准确率: 0.9980187319884726

tiny ernie tencent data
top 1 准确率: 0.894992795389049
top 2 准确率: 0.9571325648414986
top 3 准确率: 0.9740634005763689
top 4 准确率: 0.9794668587896254
top 5 准确率: 0.984149855907781
"""