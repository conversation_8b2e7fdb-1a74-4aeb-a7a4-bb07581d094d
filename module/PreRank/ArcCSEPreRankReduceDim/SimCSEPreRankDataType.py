# -*- coding: UTF-8 -*-

import json
import os
import pickle
import random
import time

import jieba
import numpy as np
import pandas as pd
from matplotlib import pyplot as plt
from tqdm import tqdm

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.metrics.pairwise import normalize
from transformers import BertTokenizer, BertConfig

from database.REDIS import REDIS
from module.PreRank.ArcCSEPreRankReduceDim.ArcCSE import ArcCSE
from setting import logger
from utils.my_utils import MyEncoder, complete_match_text, my_cosine_similarity, RankReturnObject
from model.BM25.BM25 import BM25model, HNSWModel
from sklearn.feature_extraction.text import CountVectorizer, TfidfTransformer
from model.TraAndSim.TraAndSim import TraAndSim


class SimCSEPreRankDataType:
    def __init__(self):
        self.tra_and_sim_model = TraAndSim()
        self.models = dict()
        self.max_len = setting.ARCCSE_PRE_RANK_MAX_LEN
        self.batch_samples = setting.ARCCSE_PRE_RANK_BATCH_SAMPLES
        self.use_arc_loss = setting.ARCCSE_PRE_RANK_USE_ARC_LOSS
        self.use_tri_loss = setting.ARCCSE_PRE_RANK_USE_TRI_LOSS
        self.redis = REDIS()
        self.adjusted_ratio = 2
        self.adjusted_ratio_inc = 1
        self.ratio_name = "adjusted_ratio_" + str(self.__class__.__name__)
        self.ratio_name_inc = "adjusted_ratio_inc_" + str(self.__class__.__name__)

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        start_time = time.time()
        estimated_time, adjusted_estimated_time = self.train_time(data=data, is_inc=False)
        logger.debug(f"全量训练{model_id},预估训练耗时:{round(adjusted_estimated_time,2)}秒,即{round(adjusted_estimated_time/60,3)}分钟,使用上一次预估耗时的调整比例值为:{self.adjusted_ratio}")
        logger.debug(f"全量训练{model_id},准备开始训练")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载预训练模型
        config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
        config.emb_layer = setting.ARCCSE_PRE_RANK_EMB_LAYER
        config.max_len = self.max_len
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
        logger.debug(f"全量训练{model_id},加载预训练模型成功")

        # 加载数据
        text2norm_id_intent, text2norm_id_faq, text2norm_id_chat, norm_id2norm_text_intent, norm_id2norm_text_faq, norm_id2norm_text_chat, \
            all_text_intent, all_text_faq, all_text_chat, text2item_cm_intent, text2item_cm_faq, text2item_cm_chat, label_id_text_to_origin_text = self.get_data_generator(data=data)
        logger.debug(f"全量训练{model_id},意图数据加载完成,intent:{len(all_text_intent)},faq:{len(all_text_faq)},chat:{len(all_text_chat)}")

        # 训练模型
        inputs = model.get_data(["测试一下"], max_len=128)
        model(inputs)
        model.load_weights(os.path.join(setting.PRETRAIN_BERT_DIR, 'tf_model.h5'))
        model_file = os.path.join(save_dir, 'best_model.h5')
        model.save_weights(model_file)
        logger.debug(f"全量训练{model_id},保存模型文件成功")

        # 获取 all_text 的 embedding
        if len(all_text_intent):
            embeddings_intent = self.encode_sentences(texts=all_text_intent, model=model)
            embeddings_norm_intent = normalize(embeddings_intent, copy=True)
            search_intent = HNSWModel(all_text_intent, embeddings_norm_intent)
        else:
            embeddings_intent = None
            embeddings_norm_intent = None
            search_intent = None
        logger.debug(f"全量训练{model_id},获取intent的embedding成功")
        if len(all_text_faq):
            embeddings_faq = self.encode_sentences(texts=all_text_faq, model=model)
            embeddings_norm_faq = normalize(embeddings_faq, copy=True)
            search_faq = HNSWModel(all_text_faq, embeddings_norm_faq)
        else:
            embeddings_faq = None
            embeddings_norm_faq = None
            search_faq = None
        logger.debug(f"全量训练{model_id},获取faq的embedding成功")
        if len(all_text_chat):
            embeddings_chat = self.encode_sentences(texts=all_text_chat, model=model)
            embeddings_norm_chat = normalize(embeddings_chat, copy=True)
            search_chat = HNSWModel(all_text_chat, embeddings_norm_chat)
        else:
            embeddings_chat = None
            embeddings_norm_chat = None
            search_chat = None
        logger.debug(f"全量训练{model_id},获取chat的embedding成功")

        # 联想输入模型训练
        all_text = all_text_intent + all_text_faq + all_text_chat
        context_input_process_search = BM25model(list(norm_id2norm_text_faq.values()))

        # 保存模型
        config.save_pretrained(save_dir)
        tokenizer.save_pretrained(save_dir)
        with open(os.path.join(save_dir, "text2norm_id_intent.pkl"), 'wb') as f:
            pickle.dump(text2norm_id_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2norm_id_faq.pkl"), 'wb') as f:
            pickle.dump(text2norm_id_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2norm_id_chat.pkl"), 'wb') as f:
            pickle.dump(text2norm_id_chat, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text_intent.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text_faq.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text_chat.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text_chat, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text_intent.pkl"), 'wb') as f:
            pickle.dump(all_text_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text_faq.pkl"), 'wb') as f:
            pickle.dump(all_text_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text_chat.pkl"), 'wb') as f:
            pickle.dump(all_text_chat, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm_intent.pkl"), 'wb') as f:
            pickle.dump(text2item_cm_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm_faq.pkl"), 'wb') as f:
            pickle.dump(text2item_cm_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm_chat.pkl"), 'wb') as f:
            pickle.dump(text2item_cm_chat, f, pickle.HIGHEST_PROTOCOL)
        np.save(os.path.join(save_dir, 'embeddings_intent.npy'), embeddings_intent)
        np.save(os.path.join(save_dir, 'embeddings_norm_intent.npy'), embeddings_norm_intent)
        np.save(os.path.join(save_dir, 'embeddings_faq.npy'), embeddings_faq)
        np.save(os.path.join(save_dir, 'embeddings_norm_faq.npy'), embeddings_norm_faq)
        np.save(os.path.join(save_dir, 'embeddings_chat.npy'), embeddings_chat)
        np.save(os.path.join(save_dir, 'embeddings_norm_chat.npy'), embeddings_norm_chat)
        with open(os.path.join(save_dir, "search_intent.pkl"), 'wb') as f:
            pickle.dump(search_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_faq.pkl"), 'wb') as f:
            pickle.dump(search_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_chat.pkl"), 'wb') as f:
            pickle.dump(search_chat, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_context_input.pkl"), 'wb') as f:
            pickle.dump(context_input_process_search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, 'label_id_text_to_origin_text.json'), 'w', encoding='utf-8') as f:
            json.dump(label_id_text_to_origin_text, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        logger.debug(f"全量训练{model_id},保存结束")

        # 估算threshold
        threshold_dict = {}
        with open(os.path.join(save_dir, 'threshold_dict.json'), 'w', encoding='utf-8') as f:
            json.dump(threshold_dict, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        threshold_dict = self.test_threshold(model_id, save_dir, need_run=True)
        with open(os.path.join(save_dir, 'threshold_dict.json'), 'w', encoding='utf-8') as f:
            json.dump(threshold_dict, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

        # 语料优化建议和准确率
        logger.debug(f"全量训练{model_id},开始测试准确率和语料优化建议")
        mixed = []
        score = self.acc(model_id, save_dir)
        with open(os.path.join(save_dir, 'acc_list.json'), 'w', encoding='utf-8') as f:
            json.dump([score], cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, "mixed.pkl"), 'wb') as f:
            pickle.dump(mixed, f, pickle.HIGHEST_PROTOCOL)
        mixed_df = pd.DataFrame(mixed)
        mixed_df.to_csv(os.path.join(save_dir, "suggest.csv"), index=False, encoding="utf-8-sig")
        logger.debug(f"全量训练{model_id},测试准确率和语料优化建议成功,acc:{score}")

        # 比例值写入redis
        real_train_time = time.time() - start_time
        self.adjusted_ratio = round(real_train_time/estimated_time, 4)
        self.redis.set_data(self.ratio_name, str(self.adjusted_ratio))
        logger.debug(f"全量训练{model_id},修正后预估耗时:{round(estimated_time*self.adjusted_ratio,2)}秒,即{round(estimated_time*self.adjusted_ratio/60,3)}分钟,更新预估耗时的调整比例值为:{self.adjusted_ratio}")

        # 训练成功
        logger.debug(f"全量训练{model_id},训练成功,耗时:{time.time()-start_time}s,batch:{setting.BERT_CLASSIFICATION_BATCH_SIZE}")
        return score, mixed

    def increment_train(self, model_id, data, save_dir):
        start_time = time.time()
        estimated_time, adjusted_estimated_time = self.train_time(data=data, is_inc=True)
        logger.debug(f"增量训练{model_id},预估训练耗时:{round(adjusted_estimated_time,2)}秒,即{round(adjusted_estimated_time/60,3)}分钟,使用上一次预估耗时的调整比例值为:{self.adjusted_ratio_inc}")
        logger.debug(f"增量训练{model_id},准备开始训练")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载预训练模型
        config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
        config.emb_layer = setting.ARCCSE_PRE_RANK_EMB_LAYER
        config.max_len = self.max_len
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
        logger.debug(f"增量训练{model_id},加载预训练模型成功")

        # 加载数据
        text2norm_id_intent, text2norm_id_faq, text2norm_id_chat, norm_id2norm_text_intent, norm_id2norm_text_faq, norm_id2norm_text_chat, \
            all_text_intent, all_text_faq, all_text_chat, text2item_cm_intent, text2item_cm_faq, text2item_cm_chat, label_id_text_to_origin_text = self.get_data_generator(data=data)
        logger.debug(f"增量训练{model_id},意图数据加载完成,intent:{len(all_text_intent)},faq:{len(all_text_faq)},chat:{len(all_text_chat)}")

        # 训练模型
        inputs = model.get_data(["测试一下"], max_len=128)
        model(inputs)
        model.load_weights(os.path.join(setting.PRETRAIN_BERT_DIR, 'tf_model.h5'))
        model_file = os.path.join(save_dir, 'best_model.h5')
        model.save_weights(model_file)
        logger.debug(f"增量训练{model_id},保存模型文件成功")

        # 加载旧模型数据
        if os.path.exists(os.path.join(save_dir, "all_text_intent.pkl")):
            with open(os.path.join(save_dir, "all_text_intent.pkl"), 'rb') as f:
                old_all_text_intent = pickle.load(f)
        else:
            old_all_text_intent = []
        if os.path.exists(os.path.join(save_dir, "all_text_faq.pkl")):
            with open(os.path.join(save_dir, "all_text_faq.pkl"), 'rb') as f:
                old_all_text_faq = pickle.load(f)
        else:
            old_all_text_faq = []
        if os.path.exists(os.path.join(save_dir, "all_text_chat.pkl")):
            with open(os.path.join(save_dir, "all_text_chat.pkl"), 'rb') as f:
                old_all_text_chat = pickle.load(f)
        else:
            old_all_text_chat = []
        emb_dim = None
        old_embeddings_intent = None
        old_embeddings_faq = None
        old_embeddings_chat = None
        try:
            if len(old_all_text_intent):
                old_embeddings_intent = np.load(os.path.join(save_dir, 'embeddings_intent.npy'), allow_pickle=True)
                emb_dim = old_embeddings_intent.shape[1]
        except:
            old_embeddings_intent = None
            old_all_text_intent = []
        try:
            if len(old_all_text_faq):
                old_embeddings_faq = np.load(os.path.join(save_dir, 'embeddings_faq.npy'), allow_pickle=True)
                emb_dim = old_embeddings_faq.shape[1]
        except:
            old_embeddings_faq = None
            old_all_text_faq = []
        try:
            if len(old_all_text_chat):
                old_embeddings_chat = np.load(os.path.join(save_dir, 'embeddings_chat.npy'), allow_pickle=True)
                emb_dim = old_embeddings_chat.shape[1]
        except:
            old_embeddings_chat = None
            old_all_text_chat = []

        old_all_text_to_idx_intent = {}
        old_all_text_to_idx_faq = {}
        old_all_text_to_idx_chat = {}
        for i, t in enumerate(old_all_text_intent):
            old_all_text_to_idx_intent[t] = i
        for i, t in enumerate(old_all_text_faq):
            old_all_text_to_idx_faq[t] = i
        for i, t in enumerate(old_all_text_chat):
            old_all_text_to_idx_chat[t] = i

        if emb_dim is None:
            temp_emb = self.encode_sentences(texts=["测试一下"], model=model)
            emb_dim = temp_emb.shape[1]
        logger.debug(f"增量训练{model_id},加载旧模型数据成功")

        # 获取 all_text 的 embedding
        if len(all_text_intent):
            embeddings_intent = np.zeros(shape=(len(all_text_intent), emb_dim))
            text_need_encode_list = []
            text_need_encode_idx_list = []
            for idx, t in enumerate(all_text_intent):
                old_idx = old_all_text_to_idx_intent.get(t, -1)
                if old_idx >= 0:
                    embeddings_intent[idx, :] = old_embeddings_intent[old_idx, :]
                else:
                    text_need_encode_idx_list.append(idx)
                    text_need_encode_list.append(t)
            if len(text_need_encode_list):
                embeddings_need_encode = self.encode_sentences(texts=text_need_encode_list, model=model)
                embeddings_intent[text_need_encode_idx_list, :] = embeddings_need_encode
            embeddings_norm_intent = normalize(embeddings_intent, copy=True)
            search_intent = HNSWModel(all_text_intent, embeddings_norm_intent)
        else:
            embeddings_intent = None
            embeddings_norm_intent = None
            search_intent = None
        logger.debug(f"增量训练{model_id},获取intent的embedding成功")
        if len(all_text_faq):
            embeddings_faq = np.zeros(shape=(len(all_text_faq), emb_dim))
            text_need_encode_list = []
            text_need_encode_idx_list = []
            for idx, t in enumerate(all_text_faq):
                old_idx = old_all_text_to_idx_faq.get(t, -1)
                if old_idx >= 0:
                    embeddings_faq[idx, :] = old_embeddings_faq[old_idx, :]
                else:
                    text_need_encode_idx_list.append(idx)
                    text_need_encode_list.append(t)
            if len(text_need_encode_list):
                embeddings_need_encode = self.encode_sentences(texts=text_need_encode_list, model=model)
                embeddings_faq[text_need_encode_idx_list, :] = embeddings_need_encode
            embeddings_norm_faq = normalize(embeddings_faq, copy=True)
            search_faq = HNSWModel(all_text_faq, embeddings_norm_faq)
        else:
            embeddings_faq = None
            embeddings_norm_faq = None
            search_faq = None
        logger.debug(f"增量训练{model_id},获取faq的embedding成功")
        if len(all_text_chat):
            embeddings_chat = np.zeros(shape=(len(all_text_chat), emb_dim))
            text_need_encode_list = []
            text_need_encode_idx_list = []
            for idx, t in enumerate(all_text_chat):
                old_idx = old_all_text_to_idx_chat.get(t, -1)
                if old_idx >= 0:
                    embeddings_chat[idx, :] = old_embeddings_chat[old_idx, :]
                else:
                    text_need_encode_idx_list.append(idx)
                    text_need_encode_list.append(t)
            if len(text_need_encode_list):
                embeddings_need_encode = self.encode_sentences(texts=text_need_encode_list, model=model)
                embeddings_chat[text_need_encode_idx_list, :] = embeddings_need_encode
            embeddings_norm_chat = normalize(embeddings_chat, copy=True)
            search_chat = HNSWModel(all_text_chat, embeddings_norm_chat)
        else:
            embeddings_chat = None
            embeddings_norm_chat = None
            search_chat = None
        logger.debug(f"增量训练{model_id},获取chat的embedding成功")

        # 联想输入模型训练
        all_text = all_text_intent + all_text_faq + all_text_chat
        context_input_process_search = BM25model(list(norm_id2norm_text_faq.values()))

        # 保存模型
        config.save_pretrained(save_dir)
        tokenizer.save_pretrained(save_dir)
        with open(os.path.join(save_dir, "text2norm_id_intent.pkl"), 'wb') as f:
            pickle.dump(text2norm_id_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2norm_id_faq.pkl"), 'wb') as f:
            pickle.dump(text2norm_id_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2norm_id_chat.pkl"), 'wb') as f:
            pickle.dump(text2norm_id_chat, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text_intent.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text_faq.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "norm_id2norm_text_chat.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text_chat, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text_intent.pkl"), 'wb') as f:
            pickle.dump(all_text_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text_faq.pkl"), 'wb') as f:
            pickle.dump(all_text_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text_chat.pkl"), 'wb') as f:
            pickle.dump(all_text_chat, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm_intent.pkl"), 'wb') as f:
            pickle.dump(text2item_cm_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm_faq.pkl"), 'wb') as f:
            pickle.dump(text2item_cm_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "text2item_cm_chat.pkl"), 'wb') as f:
            pickle.dump(text2item_cm_chat, f, pickle.HIGHEST_PROTOCOL)
        np.save(os.path.join(save_dir, 'embeddings_intent.npy'), embeddings_intent)
        np.save(os.path.join(save_dir, 'embeddings_norm_intent.npy'), embeddings_norm_intent)
        np.save(os.path.join(save_dir, 'embeddings_faq.npy'), embeddings_faq)
        np.save(os.path.join(save_dir, 'embeddings_norm_faq.npy'), embeddings_norm_faq)
        np.save(os.path.join(save_dir, 'embeddings_chat.npy'), embeddings_chat)
        np.save(os.path.join(save_dir, 'embeddings_norm_chat.npy'), embeddings_norm_chat)
        with open(os.path.join(save_dir, "search_intent.pkl"), 'wb') as f:
            pickle.dump(search_intent, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_faq.pkl"), 'wb') as f:
            pickle.dump(search_faq, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_chat.pkl"), 'wb') as f:
            pickle.dump(search_chat, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "search_context_input.pkl"), 'wb') as f:
            pickle.dump(context_input_process_search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, 'label_id_text_to_origin_text.json'), 'w', encoding='utf-8') as f:
            json.dump(label_id_text_to_origin_text, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        logger.debug(f"增量训练{model_id},保存结束")

        # 准确率
        if os.path.exists(os.path.join(save_dir, 'acc_list.json')):
            with open(os.path.join(save_dir, 'acc_list.json'), 'r', encoding='utf-8') as f:
                acc_list = json.load(f)
        else:
            acc_list = [0]
        score = acc_list[0]
        logger.debug(f"增量训练{model_id},使用全量的准确率,acc:{acc_list}")

        # 语料优化建
        if os.path.exists(os.path.join(save_dir, "mixed.pkl")):
            with open(os.path.join(save_dir, "mixed.pkl"), 'rb') as f:
                mixed = pickle.load(f)
        else:
            mixed = []
        logger.debug(f"增量训练{model_id},使用全量的优化建议")

        # 比例值写入redis
        real_train_time = time.time() - start_time
        self.adjusted_ratio_inc = round(real_train_time/estimated_time, 4)
        self.redis.set_data(self.ratio_name_inc, str(self.adjusted_ratio_inc))
        logger.debug(f"增量训练{model_id},修正后预估耗时:{round(estimated_time*self.adjusted_ratio_inc,2)}秒,即{round(estimated_time*self.adjusted_ratio_inc/60,3)}分钟,更新预估耗时的调整比例值为:{self.adjusted_ratio_inc}")

        # 训练成功
        logger.debug(f"增量训练{model_id},训练成功,耗时:{time.time()-start_time}s")
        return score, mixed

    def load_model(self, model_id, save_dir):
        try:
            tokenizer = BertTokenizer.from_pretrained(os.path.join(save_dir, "vocab.txt"))
            config = BertConfig.from_pretrained(save_dir)
            model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
            inputs = model.get_data(["测试一下"], max_len=128)
            model(inputs)
            model.load_weights(os.path.join(setting.PRETRAIN_BERT_DIR, 'tf_model.h5'))

            with open(os.path.join(save_dir, "text2norm_id_intent.pkl"), 'rb') as f:
                text2norm_id_intent = pickle.load(f)
            with open(os.path.join(save_dir, "text2norm_id_faq.pkl"), 'rb') as f:
                text2norm_id_faq = pickle.load(f)
            with open(os.path.join(save_dir, "text2norm_id_chat.pkl"), 'rb') as f:
                text2norm_id_chat = pickle.load(f)
            with open(os.path.join(save_dir, "norm_id2norm_text_intent.pkl"), 'rb') as f:
                norm_id2norm_text_intent = pickle.load(f)
            with open(os.path.join(save_dir, "norm_id2norm_text_faq.pkl"), 'rb') as f:
                norm_id2norm_text_faq = pickle.load(f)
            with open(os.path.join(save_dir, "norm_id2norm_text_chat.pkl"), 'rb') as f:
                norm_id2norm_text_chat = pickle.load(f)
            with open(os.path.join(save_dir, "all_text_intent.pkl"), 'rb') as f:
                all_text_intent = pickle.load(f)
            with open(os.path.join(save_dir, "all_text_faq.pkl"), 'rb') as f:
                all_text_faq = pickle.load(f)
            with open(os.path.join(save_dir, "all_text_chat.pkl"), 'rb') as f:
                all_text_chat = pickle.load(f)
            with open(os.path.join(save_dir, "text2item_cm_intent.pkl"), 'rb') as f:
                text2item_cm_intent = pickle.load(f)
            with open(os.path.join(save_dir, "text2item_cm_faq.pkl"), 'rb') as f:
                text2item_cm_faq = pickle.load(f)
            with open(os.path.join(save_dir, "text2item_cm_chat.pkl"), 'rb') as f:
                text2item_cm_chat = pickle.load(f)
            embeddings_intent = np.load(os.path.join(save_dir, 'embeddings_intent.npy'), allow_pickle=True)
            embeddings_norm_intent = np.load(os.path.join(save_dir, 'embeddings_norm_intent.npy'), allow_pickle=True)
            embeddings_faq = np.load(os.path.join(save_dir, 'embeddings_faq.npy'), allow_pickle=True)
            embeddings_norm_faq = np.load(os.path.join(save_dir, 'embeddings_norm_faq.npy'), allow_pickle=True)
            embeddings_chat = np.load(os.path.join(save_dir, 'embeddings_chat.npy'), allow_pickle=True)
            embeddings_norm_chat = np.load(os.path.join(save_dir, 'embeddings_norm_chat.npy'), allow_pickle=True)
            with open(os.path.join(save_dir, "search_intent.pkl"), 'rb') as f:
                search_intent = pickle.load(f)
            with open(os.path.join(save_dir, "search_faq.pkl"), 'rb') as f:
                search_faq = pickle.load(f)
            with open(os.path.join(save_dir, "search_chat.pkl"), 'rb') as f:
                search_chat = pickle.load(f)
            with open(os.path.join(save_dir, "search_context_input.pkl"), 'rb') as f:
                context_input_process_search = pickle.load(f)
            if os.path.exists(os.path.join(save_dir, 'label_id_text_to_origin_text.json')):
                with open(os.path.join(save_dir, 'label_id_text_to_origin_text.json'), 'r', encoding='utf-8') as f:
                    label_id_text_to_origin_text = json.load(f)
            else:
                label_id_text_to_origin_text = {}
            all_text_2_idx_intent = dict()
            for i, text in enumerate(all_text_intent):
                all_text_2_idx_intent[text] = i
            all_text_2_idx_faq = dict()
            for i, text in enumerate(all_text_faq):
                all_text_2_idx_faq[text] = i
            all_text_2_idx_chat = dict()
            for i, text in enumerate(all_text_chat):
                all_text_2_idx_chat[text] = i
            # threshold_dict = {"intent": [0.65, 0.83, 0.93], "faq": [0.65, 0.83, 0.93], "chat": [0.65, 0.83, 0.93]}
            threshold_dict = {"intent": [0.65, 0.75, 0.93], "faq": [0.65, 0.75, 0.93], "chat": [0.65, 0.75, 0.93]}
            # try:
            #     with open(os.path.join(save_dir, 'threshold_dict.json'), 'r', encoding='utf-8') as f:
            #         threshold_dict = json.load(f)
            # except:
            #     # threshold_dict = {"complete_score": 0.98, "threshold": 0.9, "low_threshold": 0.75}
            #     threshold_dict = {"intent": [0.65, 0.83, 0.93], "faq": [0.65, 0.83, 0.93], "chat": [0.65, 0.83, 0.93]}

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['model'] = model
            self.models[model_id]['text2norm_id_intent'] = text2norm_id_intent
            self.models[model_id]['text2norm_id_faq'] = text2norm_id_faq
            self.models[model_id]['text2norm_id_chat'] = text2norm_id_chat
            self.models[model_id]['norm_id2norm_text_intent'] = norm_id2norm_text_intent
            self.models[model_id]['norm_id2norm_text_faq'] = norm_id2norm_text_faq
            self.models[model_id]['norm_id2norm_text_chat'] = norm_id2norm_text_chat
            self.models[model_id]['all_text_intent'] = all_text_intent
            self.models[model_id]['all_text_faq'] = all_text_faq
            self.models[model_id]['all_text_chat'] = all_text_chat
            self.models[model_id]['all_text_2_idx_intent'] = all_text_2_idx_intent
            self.models[model_id]['all_text_2_idx_faq'] = all_text_2_idx_faq
            self.models[model_id]['all_text_2_idx_chat'] = all_text_2_idx_chat
            self.models[model_id]['text2item_cm_intent'] = text2item_cm_intent
            self.models[model_id]['text2item_cm_faq'] = text2item_cm_faq
            self.models[model_id]['text2item_cm_chat'] = text2item_cm_chat
            self.models[model_id]['embeddings_intent'] = embeddings_intent
            self.models[model_id]['embeddings_faq'] = embeddings_faq
            self.models[model_id]['embeddings_chat'] = embeddings_chat
            self.models[model_id]['embeddings_norm_intent'] = embeddings_norm_intent.astype(np.float16)
            self.models[model_id]['embeddings_norm_faq'] = embeddings_norm_faq.astype(np.float16)
            self.models[model_id]['embeddings_norm_chat'] = embeddings_norm_chat.astype(np.float16)
            self.models[model_id]['search_intent'] = search_intent
            self.models[model_id]['search_faq'] = search_faq
            self.models[model_id]['search_chat'] = search_chat
            self.models[model_id]['context_input_process_search'] = context_input_process_search
            self.models[model_id]['label_id_text_to_origin_text'] = label_id_text_to_origin_text
            self.models[model_id]["threshold_dict"] = threshold_dict
            self.predict(model_id=model_id, query="测试一下", topk=2)
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, query, query_list=None, query_embedding=None, topk=50, intent_flag=1, faq_flag=1, chat_flag=1, context_input_process='', test_flag=False, remove_stop_words_flag=False, need_trans_to_simple=True):
        result = {"intent": [], "faq": [], "chat": []}
        threshold_dict = {}
        error_dict = {"error_code": 0, "error_type": 1, "error_msg": ""}
        origin_query = query  # 原始输入
        if query_list is None or len(query_list) == 0:
            query_list = [query]

        # 预测时需要把繁体转简体
        if need_trans_to_simple:
            if query and isinstance(query, str):
                query = self.tra_and_sim_model.tra_or_sim(query, target="sim")
                if not test_flag:
                    logger.debug(f"成功繁体转简体,query:{query}")
            if query_list and isinstance(query_list, list):
                query_list = [self.tra_and_sim_model.tra_or_sim(q, target="sim") for q in query_list]
                if not test_flag:
                    logger.debug(f"成功繁体转简体,query_list:{query_list}")

        if model_id not in self.models:
            try:
                is_exist = self.check_model_file_exist(model_id=model_id)
                if is_exist:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91024"
                    error_dict["error_msg"] = "预测错误: 模型未加载，请重新上线"
                else:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91025"
                    error_dict["error_msg"] = "预测错误: 模型未加载，模型文件缺失，请重新训练"
            except Exception as ee:
                error_dict["error_type"] = 0
                error_dict["error_code"] = "NLU91017"
                error_dict["error_msg"] = f"预测错误: 模型未加载, {ee}"
            logger.error(f"预测失败 [{model_id}], 错误信息: {error_dict['error_msg']}")
            return result, error_dict, threshold_dict

        model = self.models[model_id]['model']
        norm_id2norm_text_intent = self.models[model_id]['norm_id2norm_text_intent']
        norm_id2norm_text_faq = self.models[model_id]['norm_id2norm_text_faq']
        norm_id2norm_text_chat = self.models[model_id]['norm_id2norm_text_chat']
        text2norm_id_intent = self.models[model_id]['text2norm_id_intent']
        text2norm_id_faq = self.models[model_id]['text2norm_id_faq']
        text2norm_id_chat = self.models[model_id]['text2norm_id_chat']
        all_text_intent = self.models[model_id]['all_text_intent']
        all_text_faq = self.models[model_id]['all_text_faq']
        all_text_chat = self.models[model_id]['all_text_chat']
        text2item_cm_intent = self.models[model_id]['text2item_cm_intent']
        text2item_cm_faq = self.models[model_id]['text2item_cm_faq']
        text2item_cm_chat = self.models[model_id]['text2item_cm_chat']
        search_intent = self.models[model_id]['search_intent']
        search_faq = self.models[model_id]['search_faq']
        search_chat = self.models[model_id]['search_chat']
        context_input_process_search = self.models[model_id]['context_input_process_search']
        threshold_dict = self.models[model_id]['threshold_dict']
        label_id_text_to_origin_text = self.models[model_id]['label_id_text_to_origin_text']

        #联想输入
        if context_input_process:
            context_input_process = int(context_input_process) if int(context_input_process) != 1 else 10
            recall_text = context_input_process_search.search(query, 200, True)[:context_input_process]
            recall_id = []
            for i, r_text in enumerate(recall_text):
                recall_id.append(text2norm_id_faq[r_text])
                # label_id = recall_id[-1]
                # if label_id in label_id_text_to_origin_text and r_text in label_id_text_to_origin_text[label_id]:
                #     recall_text[i] = label_id_text_to_origin_text[label_id][r_text]
            return (recall_id, recall_text), error_dict

        # 去除停用词，暂定先去除语气词
        def remove_stop_words(ori_text):
            all_stop_words = "么阿啊啦唉呢吧哇呀吗哦噢喔呵嘿吁吖呗咩哎"
            new_text = ori_text
            for w in all_stop_words:
                new_text = new_text.replace(w, "")
            if len(new_text):
                return new_text
            else:
                return ori_text

        if remove_stop_words_flag:
            logger.debug(f"需要移除停用词,移除前:{query}, {query_list}")
            query = remove_stop_words(query)
            if query_list and len(query_list):
                query_list = [remove_stop_words(q) for q in query_list]
            logger.debug(f"需要移除停用词,移除后:{query}, {query_list}")

        # 精确匹配
        norm_query_id_set_complete = {"intent": set(), "faq": set(), "chat": set()}
        if not test_flag:
            if query_list is None or len(query_list) == 0:
                query_list = [query]
            for temp_query in query_list:
                temp_query = complete_match_text(temp_query)
                for result_key, text2item_cm in [["intent", text2item_cm_intent], ["faq", text2item_cm_faq], ["chat", text2item_cm_chat]]:
                    if temp_query in text2item_cm:
                        temp_item = text2item_cm[temp_query]
                        if len(temp_item) == 3:
                            norm_query, norm_query_id, sim_query = temp_item
                        else:
                            norm_query, norm_query_id = temp_item
                            sim_query = temp_query
                        if norm_query_id not in norm_query_id_set_complete[result_key]:
                            norm_query_id_set_complete[result_key].add(norm_query_id)
                            origin_norm_query = norm_query
                            origin_sim_query = sim_query
                            if norm_query_id in label_id_text_to_origin_text:
                                if norm_query in label_id_text_to_origin_text[norm_query_id]:
                                    origin_norm_query = label_id_text_to_origin_text[norm_query_id][norm_query]
                                if sim_query in label_id_text_to_origin_text[norm_query_id]:
                                    origin_sim_query = label_id_text_to_origin_text[norm_query_id][sim_query]
                            item = RankReturnObject(query=origin_query, sim_query=origin_sim_query, norm_query=origin_norm_query,
                                                    norm_query_id=norm_query_id, score=1, save_bigger=True)
                            result[result_key].append(item)
            if len(result["intent"]) and len(result["faq"]) and len(result["chat"]):
                return result, error_dict, threshold_dict

        # 句子embedding
        if query_embedding is not None:
            emb = query_embedding
        else:
            emb = self.encode_sentences(texts=query_list, model=model)
            emb = normalize(emb)

        for i, query in enumerate(query_list):
            # 粗排
            recall_text_intent = []
            select_emb_intent = None
            if intent_flag and len(all_text_intent):
                recall_text_intent, select_emb_intent = search_intent.search(emb[i:i+1, :], min(setting.ARCCSE_PRE_RANK_SEARCH_RETURN, len(all_text_intent)))
            recall_text_faq = []
            select_emb_faq = None
            if faq_flag and len(all_text_faq):
                recall_text_faq, select_emb_faq = search_faq.search(emb[i:i+1, :], min(setting.ARCCSE_PRE_RANK_SEARCH_RETURN, len(all_text_faq)))
            recall_text_chat = []
            select_emb_chat = None
            if chat_flag and len(all_text_chat):
                recall_text_chat, select_emb_chat = search_chat.search(emb[i:i+1, :], min(setting.ARCCSE_PRE_RANK_SEARCH_RETURN, len(all_text_chat)))
            if not test_flag:
                logger.debug(f"粗排结束,检索数量,query:{query},intent:{len(recall_text_intent)},faq:{len(recall_text_faq)},chat:{len(recall_text_chat)}")

            # 计算分数
            if len(recall_text_intent) and select_emb_intent is not None:
                scores_intent = my_cosine_similarity(select_emb_intent, emb[i:i+1, :].astype(np.float16))[:, 0]
                scores_intent = scores_intent.clip(min=0, max=1)
            else:
                scores_intent = None
            if len(recall_text_faq) and select_emb_faq is not None:
                scores_faq = my_cosine_similarity(select_emb_faq, emb[i:i+1, :].astype(np.float16))[:, 0]
                scores_faq = scores_faq.clip(min=0, max=1)
            else:
                scores_faq = None
            if len(recall_text_chat) and select_emb_chat is not None:
                scores_chat = my_cosine_similarity(select_emb_chat, emb[i:i+1, :].astype(np.float16))[:, 0]
                scores_chat = scores_chat.clip(min=0, max=1)
            else:
                scores_chat = None

            # 生成结果
            result_element = [
                ["intent", scores_intent, recall_text_intent, text2norm_id_intent, norm_id2norm_text_intent],
                ["faq", scores_faq, recall_text_faq, text2norm_id_faq, norm_id2norm_text_faq],
                ["chat", scores_chat, recall_text_chat, text2norm_id_chat, norm_id2norm_text_chat]
            ]
            for result_key, scores, recall_text, text2norm_id, norm_id2norm_text in result_element:
                if scores is None:
                    continue
                index = np.argsort(scores)
                index = index[::-1]
                norm_query_id_set = set()
                if not test_flag:
                    logger.debug(f"排序结束")
                for idx in index:
                    sim_query = recall_text[idx]
                    if test_flag and sim_query == query:
                        continue
                    norm_query_id = text2norm_id[sim_query]
                    if norm_query_id not in norm_query_id_set_complete[result_key] and norm_query_id not in norm_query_id_set:
                        norm_query_id_set.add(norm_query_id)
                        norm_query = norm_id2norm_text[norm_query_id]
                        origin_norm_query = norm_query
                        origin_sim_query = sim_query
                        if norm_query_id in label_id_text_to_origin_text:
                            if norm_query in label_id_text_to_origin_text[norm_query_id]:
                                origin_norm_query = label_id_text_to_origin_text[norm_query_id][norm_query]
                            if sim_query in label_id_text_to_origin_text[norm_query_id]:
                                origin_sim_query = label_id_text_to_origin_text[norm_query_id][sim_query]
                        item = RankReturnObject(query=origin_query, sim_query=origin_sim_query, norm_query=origin_norm_query,
                                                norm_query_id=norm_query_id, score=scores[idx], save_bigger=True)
                        result[result_key].append(item)
                    if len(norm_query_id_set) >= topk:
                        break
            if not test_flag:
                logger.debug(f"意图识别完成,query:{query}")

        if not test_flag:
            result_intent_ = [item.__dict__ for item in result["intent"]]
            result_faq_ = [item.__dict__ for item in result["faq"]]
            result_chat_ = [item.__dict__ for item in result["chat"]]
            logger.debug(f"多个query_list检索出的结果,intent:{result_intent_},faq:{result_faq_},chat:{result_chat_}")
        for k in result.keys():
            result[k].sort(key=lambda xx: -xx.score)
            temp_result_list = []
            temp_norm_query_id_set = set()
            for x in result[k]:
                if x.norm_query_id not in temp_norm_query_id_set:
                    temp_norm_query_id_set.add(x.norm_query_id)
                    temp_result_list.append(x)
                    if len(temp_norm_query_id_set) >= topk:
                        break
            result[k] = temp_result_list
        if not test_flag:
            result_intent_ = [item.__dict__ for item in result["intent"]]
            result_faq_ = [item.__dict__ for item in result["faq"]]
            result_chat_ = [item.__dict__ for item in result["chat"]]
            logger.debug(f"返回结果,intent:{result_intent_},faq:{result_faq_},chat:{result_chat_}")
        return result, error_dict, threshold_dict

    def labeling(self, model_id, texts, text_ids=None):
        labeling_result = []

        text2norm_id_intent = self.models[model_id]['text2norm_id_intent']
        text2norm_id_faq = self.models[model_id]['text2norm_id_faq']
        text2norm_id_chat = self.models[model_id]['text2norm_id_chat']
        all_text_intent = self.models[model_id]['all_text_intent']
        all_text_faq = self.models[model_id]['all_text_faq']
        all_text_chat = self.models[model_id]['all_text_chat']
        embeddings_norm_intent = self.models[model_id]['embeddings_norm_intent']
        embeddings_norm_faq = self.models[model_id]['embeddings_norm_faq']  # [m, dim]
        embeddings_norm_chat = self.models[model_id]['embeddings_norm_chat']  # [m, dim]
        model = self.models[model_id]['model']  # [m, dim]

        embeddings_norm = np.vstack((embeddings_norm_intent, embeddings_norm_faq, embeddings_norm_chat))
        all_text = all_text_intent + all_text_faq + all_text_chat
        text2norm_id = text2norm_id_intent
        for k, v in text2norm_id_faq.items():
            text2norm_id[k] = v
        for k, v in text2norm_id_chat.items():
            text2norm_id[k] = v

        embeddings = self.encode_sentences(texts=texts, model=model)  # [n, dim]
        scores = my_cosine_similarity(embeddings_norm, embeddings)  # [m, n]
        max_idx = np.argmax(scores, axis=0)
        for i in range(max_idx.shape[0]):
            result = {
                "text": texts[i],
                "id": text_ids[i] if text_ids is not None else None,
                "label": text2norm_id[all_text[max_idx[i]]],
                "score": scores[max_idx[i], i],
                "margin_score": scores[max_idx[i], i]
            }
            labeling_result.append(result)
        return labeling_result

    def get_data(self, data):
        pass

    def get_data_generator(self, data):
        # 整理数据
        text2norm_id_intent = dict()
        text2norm_id_faq = dict()
        text2norm_id_chat = dict()
        norm_id2norm_text_intent = dict()
        norm_id2norm_text_faq = dict()
        norm_id2norm_text_chat = dict()
        text2item_cm_intent = dict()
        text2item_cm_faq = dict()
        text2item_cm_chat = dict()
        label_id_text_to_origin_text = dict()

        for data_dict in data:
            if "type" not in data_dict:
                data_dict["type"] = "faq"
            norm_query = data_dict['title'].strip()
            if len(norm_query) == 0:
                continue
            norm_query_id = data_dict['labelId']
            origin_norm_query = norm_query  # 原始文本，用于保留繁体的
            norm_query = self.tra_and_sim_model.tra_or_sim(norm_query, target="sim")  # 转为简体embedding和精确匹配
            norm_query_cm = complete_match_text(norm_query)
            if norm_query_id not in label_id_text_to_origin_text:
                label_id_text_to_origin_text[norm_query_id] = {}
            label_id_text_to_origin_text[norm_query_id][norm_query] = origin_norm_query

            if data_dict["type"] == "intent":
                norm_id2norm_text_intent[norm_query_id] = norm_query
                text2norm_id_intent[norm_query] = norm_query_id
                text2item_cm_intent[norm_query_cm] = [norm_query, norm_query_id, norm_query]  # 保存匹配的sim_query
            elif data_dict["type"] == "faq":
                norm_id2norm_text_faq[norm_query_id] = norm_query
                text2norm_id_faq[norm_query] = norm_query_id
                text2item_cm_faq[norm_query_cm] = [norm_query, norm_query_id, norm_query]
            elif data_dict["type"] == "chat":
                norm_id2norm_text_chat[norm_query_id] = norm_query
                text2norm_id_chat[norm_query] = norm_query_id
                text2item_cm_chat[norm_query_cm] = [norm_query, norm_query_id, norm_query]
            else:
                logger.warning(f"有不识别的data_type: {data_dict['type']}")
                continue
            for text in data_dict.get("labelData", "").split("||"):
                text = text.strip()
                origin_text = text  # 原始文本，用于保留繁体的
                text = self.tra_and_sim_model.tra_or_sim(text, target="sim")  # 转为简体embedding和精确匹配
                text_cm = complete_match_text(text)
                if norm_query_id not in label_id_text_to_origin_text:
                    label_id_text_to_origin_text[norm_query_id] = {}
                label_id_text_to_origin_text[norm_query_id][text] = origin_text

                if len(text):
                    if data_dict["type"] == "intent":
                        text2norm_id_intent[text] = norm_query_id
                        text2item_cm_intent[text_cm] = [norm_query, norm_query_id, text]  # 保存匹配的sim_query
                    elif data_dict["type"] == "faq":
                        text2norm_id_faq[text] = norm_query_id
                        text2item_cm_faq[text_cm] = [norm_query, norm_query_id, text]
                    elif data_dict["type"] == "chat":
                        text2norm_id_chat[text] = norm_query_id
                        text2item_cm_chat[text_cm] = [norm_query, norm_query_id, text]
        all_text_intent = list(text2norm_id_intent.keys())
        all_text_faq = list(text2norm_id_faq.keys())
        all_text_chat = list(text2norm_id_chat.keys())
        return text2norm_id_intent, text2norm_id_faq, text2norm_id_chat, norm_id2norm_text_intent, norm_id2norm_text_faq, norm_id2norm_text_chat, \
            all_text_intent, all_text_faq, all_text_chat, text2item_cm_intent, text2item_cm_faq, text2item_cm_chat, label_id_text_to_origin_text

    def encode_sentences(self, texts, model):
        embeddings = list()

        for start_idx in range(0, len(texts), setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE):
            end_idx = min(start_idx+setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE, len(texts))
            inputs = model.get_data(texts=texts[start_idx:end_idx], max_len=self.max_len, return_tensor=True)
            emb = model.get_encoder_layer(**inputs)
            embeddings.append(emb.numpy())

        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings

    def check_model_file_exist(self, model_id):
        model_file_exist = True
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")

        try:
            if not os.path.exists(os.path.join(save_dir, "best_model.h5")):
                model_file_exist = False
            if not os.path.exists(os.path.join(save_dir, "config.json")):
                model_file_exist = False
            if not os.path.exists(os.path.join(save_dir, "vocab.txt")):
                model_file_exist = False
        except:
            model_file_exist = False
        return model_file_exist

    def acc(self, model_id, save_dir):
        logger.debug(f"开始计算准确率")
        puzzl_label_start_time = time.time()

        self.load_model(model_id=model_id, save_dir=save_dir)

        correct_count = 0
        all_count = 0
        for data_type in ["intent", "faq", "chat"]:
            # 数据
            if data_type == "intent":
                all_text = self.models[model_id]['all_text_intent']
                if len(all_text):
                    embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm_intent.npy'), allow_pickle=True)
                else:
                    embeddings_norm = None
                text2norm_id = self.models[model_id]['text2norm_id_intent']
            elif data_type == "faq":
                all_text = self.models[model_id]['all_text_faq']
                if len(all_text):
                    embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm_faq.npy'), allow_pickle=True)
                else:
                    embeddings_norm = None
                text2norm_id = self.models[model_id]['text2norm_id_faq']
            else:
                all_text = self.models[model_id]['all_text_chat']
                if len(all_text):
                    embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm_chat.npy'), allow_pickle=True)
                else:
                    embeddings_norm = None
                text2norm_id = self.models[model_id]['text2norm_id_chat']

            idx_list = np.arange(len(all_text))
            random.shuffle(idx_list)
            for i in tqdm(idx_list[:10000]):
                t = all_text[i]
                predict_result, _, _ = self.predict(model_id=model_id, query=t, query_list=[t], topk=3, query_embedding=embeddings_norm[i:i+1],
                                                    intent_flag=(data_type == "intent"), faq_flag=(data_type == "faq"), chat_flag=(data_type == "chat"), test_flag=True)
                true_label_id = text2norm_id[t]
                if len(predict_result[data_type]):
                    all_count += 1
                    pred_label_id = predict_result[data_type][0].norm_query_id
                    if true_label_id == pred_label_id:
                        correct_count += 1
            print(f"测试准确率{data_type}预测完成,耗时:{time.time()-puzzl_label_start_time}")

        # 准确率
        acc = correct_count/max(all_count, 1)
        logger.debug(f"语料优化建议整理结束,准确率:{acc},耗时:{time.time()-puzzl_label_start_time}")
        self.offline_model(model_id=model_id)
        return acc

    def puzzl_label(self, model_id, save_dir):
        logger.debug(f"开始生成语料优化建议")
        puzzl_label_start_time = time.time()

        self.load_model(model_id=model_id, save_dir=save_dir)

        error_dict = {}
        labelids = []
        norm_id2norm_text_intent = self.models[model_id]['norm_id2norm_text_intent']
        norm_id2norm_text_faq = self.models[model_id]['norm_id2norm_text_faq']
        norm_id2norm_text_chat = self.models[model_id]['norm_id2norm_text_chat']
        correct_count = 0
        all_count = 0
        for data_type in ["intent", "faq", "chat"]:
            # 数据
            if data_type == "intent":
                all_text = self.models[model_id]['all_text_intent']
                if len(all_text):
                    embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm_intent.npy'), allow_pickle=True)
                else:
                    embeddings_norm = None
                text2norm_id = self.models[model_id]['text2norm_id_intent']
            elif data_type == "faq":
                all_text = self.models[model_id]['all_text_faq']
                if len(all_text):
                    embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm_faq.npy'), allow_pickle=True)
                else:
                    embeddings_norm = None
                text2norm_id = self.models[model_id]['text2norm_id_faq']
            else:
                all_text = self.models[model_id]['all_text_chat']
                if len(all_text):
                    embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm_chat.npy'), allow_pickle=True)
                else:
                    embeddings_norm = None
                text2norm_id = self.models[model_id]['text2norm_id_chat']

            for i, t in tqdm(enumerate(all_text)):
                predict_result, _, _ = self.predict(model_id=model_id, query=t, topk=3, query_embedding=embeddings_norm[i:i+1],
                                                    intent_flag=(data_type == "intent"), faq_flag=(data_type == "faq"), chat_flag=(data_type == "chat"), test_flag=True)
                all_count += 1
                true_label_id = text2norm_id[t]
                pred_label_id = predict_result[data_type][0].norm_query_id
                if true_label_id != pred_label_id:
                    key = (true_label_id, pred_label_id, data_type)
                    if key not in error_dict:
                        error_dict[key] = []
                    error_dict[key].append(t)
                else:
                    correct_count += 1
            print(f"语料优化建议{data_type}预测完成,耗时:{time.time()-puzzl_label_start_time}")

        suggest = []
        error_text_num = 0
        for key, value in error_dict.items():
            if len(value) >= 1:
                error_text_num += len(value)
                if key[2] == "intent":
                    norm_id2norm_text = norm_id2norm_text_intent
                elif key[2] == "faq":
                    norm_id2norm_text = norm_id2norm_text_faq
                else:
                    norm_id2norm_text = norm_id2norm_text_chat
                suggest.append({"lable": key[0], "pred_label": key[1], "期望意图": norm_id2norm_text[key[0]], "预测意图": norm_id2norm_text[key[1]], "texts": value, "num_text": len(value)})
        suggest.sort(key=lambda x: -len(x["texts"]))
        print(f"语料优化建议排序完成,耗时:{time.time()-puzzl_label_start_time}")

        # TFIDF 抽关键词
        corpos = [' '.join(jieba.lcut(text)) for text in self.models[model_id]['all_text_intent']+self.models[model_id]['all_text_faq']+self.models[model_id]['all_text_chat']]
        vectorizer = CountVectorizer(token_pattern=r"(?u)\b\w+\b")
        transformer = TfidfTransformer()
        tfidf = transformer.fit_transform(vectorizer.fit_transform(corpos))
        labelids_set = list(set(list(norm_id2norm_text_intent.keys())+list(norm_id2norm_text_faq.keys())+list(norm_id2norm_text_chat.keys())))  # np.array(labelids)
        labelid2tfidfvec = {}
        for i in labelids_set:
            vec = np.mean(tfidf[labelids == i], axis=0)
            labelid2tfidfvec[i] = vec.__array__()[0, :]
        for mixed_dict in suggest:
            pred_tfidf = labelid2tfidfvec[mixed_dict["pred_label"]]
            text_tfidf = transformer.transform(vectorizer.transform([' '.join(jieba.lcut(t)) for t in mixed_dict["texts"]]))
            text_tfidf = np.mean(text_tfidf, axis=0).__array__()[0, :]
            pred_tfidf = text_tfidf * pred_tfidf
            index = pred_tfidf.argsort()[-5:][::-1]
            key_words = [vectorizer.get_feature_names()[i] for i in index]
            mixed_dict["关键词"] = key_words
        print(f"语料优化建议抽取关键词完成,耗时:{time.time()-puzzl_label_start_time}")

        # 准确率
        acc = correct_count/max(all_count, 1)
        logger.debug(f"语料优化建议整理结束,准确率:{acc},suggest_length:{len(suggest)},error_text_num:{error_text_num},耗时:{time.time()-puzzl_label_start_time}")
        self.offline_model(model_id=model_id)
        return acc, suggest

    def test_threshold(self, model_id, save_dir, need_run=False):
        threshold_dict = {
            "intent": [0.75, 0.9, 0.98],
            "faq": [0.75, 0.9, 0.98],
            "chat": [0.75, 0.9, 0.98]
        }
        if not need_run:
            logger.debug(f"直接返回默认的threshold")
            return threshold_dict

        logger.debug(f"开始估计threshold")
        threshold_start_time = time.time()
        self.load_model(model_id=model_id, save_dir=save_dir)

        for data_type in ["intent", "faq", "chat"]:
            # 数据
            pos_scores = []
            neg_scores = []
            if data_type == "intent":
                text2norm_id = self.models[model_id]['text2norm_id_intent']
                norm_id2norm_text = self.models[model_id]['norm_id2norm_text_intent']
                if len(text2norm_id):
                    embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm_intent.npy'), allow_pickle=True)
                else:
                    embeddings_norm = None
                all_text_2_idx = self.models[model_id]['all_text_2_idx_intent']
            elif data_type == "faq":
                text2norm_id = self.models[model_id]['text2norm_id_faq']
                norm_id2norm_text = self.models[model_id]['norm_id2norm_text_faq']
                if len(text2norm_id):
                    embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm_faq.npy'), allow_pickle=True)
                else:
                    embeddings_norm = None
                all_text_2_idx = self.models[model_id]['all_text_2_idx_faq']
            else:
                text2norm_id = self.models[model_id]['text2norm_id_chat']
                norm_id2norm_text = self.models[model_id]['norm_id2norm_text_chat']
                if len(text2norm_id):
                    embeddings_norm = np.load(os.path.join(save_dir, 'embeddings_norm_chat.npy'), allow_pickle=True)
                else:
                    embeddings_norm = None
                all_text_2_idx = self.models[model_id]['all_text_2_idx_chat']
            if len(norm_id2norm_text) <= 1:
                continue
            norm_id2texts = dict()
            for t, norm_id in text2norm_id.items():
                if norm_id not in norm_id2texts:
                    norm_id2texts[norm_id] = []
                norm_id2texts[norm_id].append(t)

            # 预测得分
            sample_times = 2
            for norm_id, texts in tqdm(norm_id2texts.items()):
                for _ in range(sample_times):
                    a_pos_text = random.choice(texts)
                    emb_idx = all_text_2_idx[a_pos_text]
                    predict_result, _, _ = self.predict(model_id=model_id, query=a_pos_text, query_list=[a_pos_text], query_embedding=embeddings_norm[emb_idx: emb_idx+1, :],
                                                        topk=5, intent_flag=(data_type == "intent"), faq_flag=(data_type == "faq"), chat_flag=(data_type == "chat"), test_flag=True)

                    min_neg_score = 100
                    for rank_return_object in predict_result[data_type]:
                        if rank_return_object.norm_query_id == norm_id:
                            pos_scores.append(rank_return_object.score)
                        else:
                            if rank_return_object.score < min_neg_score:
                                min_neg_score = rank_return_object.score
                    if min_neg_score <= 1:
                        neg_scores.append(min_neg_score)

            plt.figure()
            plt.title(f"pos scores {data_type}")
            plt.hist(pos_scores, bins=30)
            plt.xlim(0.0, 1)
            plt.savefig(os.path.join(save_dir, f"pos_scores_{data_type}.jpg"))
            # plt.show()

            plt.figure()
            plt.title(f"neg scores {data_type}")
            plt.hist(neg_scores, bins=30)
            plt.xlim(0.0, 1)
            plt.savefig(os.path.join(save_dir, f"neg_scores_{data_type}.jpg"))
            # plt.show()

            pos_scores.sort()
            neg_scores.sort()
            if len(pos_scores) == 0 or len(neg_scores) == 0:
                continue
            complete_threshold = pos_scores[-max(len(pos_scores)//2, 1)]
            match_threshold = pos_scores[len(pos_scores)//20]
            neg_start = 1
            neg_end = len(neg_scores)//5+1
            neg_threshold = neg_scores[-neg_start]+0.01
            for i in range(neg_start, neg_end+1):
                neg_threshold = neg_scores[-i]+0.01
                if neg_threshold < match_threshold - 0.05:
                    break
            logger.debug(f"{data_type}的threshold计算完成:{neg_threshold},{match_threshold},{complete_threshold}")
            if neg_threshold >= match_threshold:
                continue
            if match_threshold >= complete_threshold:
                continue
            threshold_dict[data_type][0] = neg_threshold
            threshold_dict[data_type][1] = match_threshold
            threshold_dict[data_type][2] = complete_threshold
        self.offline_model(model_id=model_id)
        logger.debug(f"完成估计threshold,{threshold_dict},耗时:{time.time()-threshold_start_time}")
        return threshold_dict

    def train_time(self, data, is_inc=False):
        num_text = 0
        for data_dict in data:
            num_text += 1
            texts = data_dict.get("labelData", "").strip()
            num_text += len(texts.split("||"))
        num_batches = max(num_text//setting.ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE, 1)

        try:
            if is_inc:
                self.adjusted_ratio_inc = float(self.redis.get_data(self.ratio_name_inc))
                logger.debug(f"读取redis的{self.ratio_name_inc}值为:{self.adjusted_ratio_inc}")
            else:
                self.adjusted_ratio = float(self.redis.get_data(self.ratio_name))
                logger.debug(f"读取redis的{self.ratio_name}值为:{self.adjusted_ratio}")
        except Exception as e:
            logger.warning(f"读取redis的{self.ratio_name_inc if is_inc else self.ratio_name}值失败,错误:{e}")

        estimated_time = num_batches  # 基准值
        if is_inc:
            adjusted_estimated_time = num_batches * self.adjusted_ratio_inc  # 预测值
        else:
            adjusted_estimated_time = num_batches * self.adjusted_ratio  # 预测值
        return estimated_time, adjusted_estimated_time


if __name__ == "__main__":
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    ranker = SimCSEPreRankDataType()
    R = REDIS()
    train_data = R.get_data(f'test_data_1_tra')[:10]
    inc_train_data = R.get_data(f'test_data_2')[:10]
    train_model_id = f"train_model_id"

    # 训练
    start = time.time()
    train_model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{train_model_id}/{ranker.__class__.__name__}/")
    print(f"训练预估时间:{ranker.train_time(data=train_data, is_inc=False)}")
    train_result = ranker.train(model_id=train_model_id, data=train_data, save_dir=train_model_save_dir)
    print(f"训练得分: {train_result[0]}, 耗时: {time.time()-start}")
    ranker.load_model(model_id=train_model_id, save_dir=train_model_save_dir)
    pred_result = ranker.predict(model_id=train_model_id, query="续保500元优惠券怎么使用")
    context_result = ranker.predict(model_id=train_model_id, query="阿克苏振宇汽车销售有限责任公司展厅", context_input_process="1")

    # 增量训练
    start = time.time()
    train_model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{train_model_id}/{ranker.__class__.__name__}/")
    print(f"训练预估时间:{ranker.train_time(data=inc_train_data, is_inc=True)}")
    inc_train_result = ranker.increment_train(model_id=train_model_id, data=inc_train_data, save_dir=train_model_save_dir)
    print(f"训练得分: {train_result[0]}, 耗时: {time.time()-start}")
    ranker.load_model(model_id=train_model_id, save_dir=train_model_save_dir)
    inc_pred_result = ranker.predict(model_id=train_model_id, query="续保500元优惠券怎么使用")
    inc_context_result = ranker.predict(model_id=train_model_id, query="阿克苏振宇汽车销售有限责任公司展厅", context_input_process="1")
    print("wait")
