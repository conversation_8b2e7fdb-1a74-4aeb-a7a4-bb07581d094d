import os
import time
import <PERSON>enshtein
import numpy as np
from tqdm import tqdm
import pickle

import setting
from database.REDIS import REDIS
from setting import logger
from utils.my_utils import RankReturnObject, remove_symbol


class EditDistancePreRank:
    def __init__(self, method='ratio'):
        self.method = method
        if method == 'distance':
            self.score_func = Levenshtein.distance
            self.save_bigger = False
        else:
            self.score_func = Levenshtein.ratio
            self.save_bigger = True
        self.models = dict()

    def load_model(self, model_id, model_save_dir):
        try:
            with open(os.path.join(model_save_dir, "text2norm_id.pkl"), 'rb') as f:
                text2norm_id = pickle.load(f)
            with open(os.path.join(model_save_dir, "norm_id2norm_text.pkl"), 'rb') as f:
                norm_id2norm_text = pickle.load(f)
            with open(os.path.join(model_save_dir, "all_text.pkl"), 'rb') as f:
                all_text = pickle.load(f)
            with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)

            self.models[model_id] = dict()
            self.models[model_id]['text2norm_id'] = text2norm_id
            self.models[model_id]['norm_id2norm_text'] = norm_id2norm_text
            self.models[model_id]['all_text'] = all_text
            self.models[model_id]['text2item_cm'] = text2item_cm
        except Exception as e:
            logger.error(f"模型加载失败 [{model_id}], 错误信息: {e}")
            raise Exception(f"模型加载失败 [{model_id}], 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"模型下线成功 [{model_id}]")

    def train(self, model_id, data, model_save_dir):
        start = time.time()

        # 模型保存地址
        os.makedirs(model_save_dir, exist_ok=True)

        # 整理数据
        text2norm_id = dict()
        norm_id2norm_text = dict()
        text2item_cm = dict() # 精确匹配字典

        for data_dict in data:
            norm_query = data_dict['title']
            norm_query_id = data_dict['labelId']
            norm_id2norm_text[norm_query_id] = norm_query
            text2norm_id[norm_query] = norm_query_id

            norm_query_ = remove_symbol(norm_query)
            text2item_cm[norm_query_] =[norm_query, norm_query_id]

            for text in data_dict["labelData"].split("||"):
                text = text.strip()
                if len(text):
                    text2norm_id[text] = norm_query_id
                text = remove_symbol(text)
                if len(text):
                    text2item_cm[text] =[norm_query, norm_query_id]

        all_text = list(text2norm_id.keys())

        # 保存模型
        with open(os.path.join(model_save_dir, "text2norm_id.pkl"), 'wb') as f:
            pickle.dump(text2norm_id, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "norm_id2norm_text.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)

        # self.models[model_id] = dict()
        # self.models[model_id]['text2norm_id'] = text2norm_id
        # self.models[model_id]['norm_id2norm_text'] = norm_id2norm_text
        # self.models[model_id]['all_text'] = all_text
        # self.models[model_id]['text2item_cm'] = text2item_cm

        logger.debug(f"训练完成 [{model_id}],数据量:{len(data)},文本量:{len(all_text)},耗时:{time.time()-start}")

    def incremental_train(self, model_id, data, model_save_dir):
        try:
            with open(os.path.join(model_save_dir, "text2norm_id.pkl"), 'rb') as f:
                text2norm_id = pickle.load(f)
            with open(os.path.join(model_save_dir, "norm_id2norm_text.pkl"), 'rb') as f:
                norm_id2norm_text = pickle.load(f)
            with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)
        except Exception as e:
            logger.error(f"增量训练失败 [{model_id}], 错误信息: {e}")
            raise Exception(f"增量训练失败 [{model_id}], 错误信息: {e}")

        # 加入新数据
        for data_dict in data:
            norm_query = data_dict['title']
            norm_query_id = data_dict['labelId']

            norm_query_ = remove_symbol(norm_query)
            text2item_cm[norm_query_] =[norm_query, norm_query_id]

            text2norm_id[norm_query] = norm_query_id
            norm_id2norm_text[norm_query_id] = norm_query
            for text in data_dict["labelData"].split("||"):
                text = text.strip()
                if len(text):
                    text2norm_id[text.strip()] = norm_query_id
                text = remove_symbol(text)
                if len(text):
                    text2item_cm[text] =[norm_query, norm_query_id]
        all_text = list(text2norm_id.keys())

        # 保存模型
        with open(os.path.join(model_save_dir, "text2norm_id.pkl"), 'wb') as f:
            pickle.dump(text2norm_id, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "norm_id2norm_text.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)

        self.models[model_id] = dict()
        self.models[model_id]['text2norm_id'] = text2norm_id
        self.models[model_id]['norm_id2norm_text'] = norm_id2norm_text
        self.models[model_id]['all_text'] = all_text
        self.models[model_id]['text2item_cm'] = text2item_cm

        logger.debug(f"增量训练完成 [{model_id}], 数据量: {len(data)}, 文本量: {len(all_text)}")

    def predict(self, model_id, query, topk=50):
        if model_id not in self.models:
            logger.error(f"预测失败 [{model_id}], 错误信息: 模型未加载")
            raise Exception(f"预测失败 [{model_id}], 错误信息: 模型未加载")

        text2norm_id = self.models[model_id]['text2norm_id']
        norm_id2norm_text = self.models[model_id]['norm_id2norm_text']
        all_text = self.models[model_id]['all_text']
        text2item_cm = self.models[model_id]['text2item_cm']

        query_ = remove_symbol(query)
        if query_ in text2item_cm:
            norm_query, norm_query_id = text2item_cm[query_]
            item = RankReturnObject(query=query, sim_query=query_, norm_query=norm_query,
                                    norm_query_id=norm_query_id, score=1, save_bigger=self.save_bigger)
            return [item]

        scores = np.zeros(shape=(len(all_text)))
        for i, text in enumerate(all_text):
            scores[i] = self.score_func(query, text)

        index = np.argsort(scores)

        if self.save_bigger:
            index = index[-topk:][::-1]
        else:
            index = index[:topk]

        heap = []
        for idx in index:
            sim_query = all_text[idx]
            norm_query_id = text2norm_id[sim_query]
            norm_query = norm_id2norm_text[norm_query_id]
            item = RankReturnObject(query=query, sim_query=sim_query, norm_query=norm_query,
                                    norm_query_id=norm_query_id, score=scores[idx], save_bigger=self.save_bigger)
            heap.append(item)
        return heap

    def test_acc(self, model_id, data, topk=5, model_save_dir=""):
        """
        用 data 中的文本测试 topk 准确率, 需要返回 topk+1 个结果，因为包含自身。
        """
        total = 0
        acc = [0] * topk

        pbar = tqdm(data)
        for data_dict in pbar:
            norm_query_id = data_dict['labelId']
            pbar.set_description('测试进度')
            for text in data_dict["labelData"].split("||"):
                sim_query = text.strip()
                heap = self.predict(model_id, sim_query, topk=topk+1)
                heap.sort(reverse=True)
                assert len(heap) == topk+1
                for idx in range(len(heap)):
                    if heap[idx].sim_query == sim_query:
                        heap.pop(idx)
                        break
                pred_ids = np.array([item.norm_query_id for item in heap])
                for j in range(topk):
                    if norm_query_id in pred_ids[:j + 1]:
                        acc[j] += 1
                total += 1
            acc_dict = {f'acc{topk}': acc[topk - 1] / total}
            pbar.set_postfix(acc_dict)

        with open(os.path.join(model_save_dir, 'test_result.txt'), 'w', encoding='utf-8') as f:
            for i in range(topk):
                line = f'top {i + 1} 准确率: {acc[i] / total}'
                print(line)
                f.writelines(line + '\n')

    def my_test_pos_neg_acc_threshold(self, id2text_valid, threshold, times=1):
        import random
        from tqdm import tqdm
        random.seed(setting.SEED)
        acc_list = []

        for _ in range(times):
            # 测试准确率，正负样本 1:1
            acc, total = 0, 0
            id_list = list(id2text_valid.keys())
            pbar = tqdm(id2text_valid.items())
            for valid_id, text_list in pbar:
                pbar.set_description('测试进度')
                for text1 in text_list:
                    # 正样本
                    total += 1
                    if self.score_func(text1, random.choice(text_list)) >= threshold:
                        acc += 1
                    # 负样本
                    neg_id = random.choice(id_list)
                    while neg_id == valid_id:
                        neg_id = random.choice(id_list)
                    total += 1
                    if self.score_func(text1, random.choice(id2text_valid[neg_id])) < threshold:
                        acc += 1
                acc_dict = {f'acc': acc / total}
                pbar.set_postfix(acc_dict)
            acc_list.append(acc/total)
        print(f"{times}次 threshold {threshold} 测试平均准确率: {np.mean(acc_list)}")

    def my_test_pos_neg_acc_sort(self, id2text_valid, neg_samples, times=1):
        import random
        from tqdm import tqdm
        random.seed(setting.SEED)
        acc_list = []

        for _ in range(times):
            acc, total = 0, 0
            id_list = list(id2text_valid.keys())
            pbar = tqdm(id2text_valid.items())
            for valid_id, text_list in pbar:
                pbar.set_description('测试进度')
                for text1 in text_list:
                    # 正样本
                    score_pos = self.score_func(text1, random.choice(text_list))

                    # 负样本
                    score_neg = []
                    for i in range(neg_samples):
                        neg_id = random.choice(id_list)
                        while neg_id == valid_id:
                            neg_id = random.choice(id_list)
                        score_neg.append(self.score_func(text1, random.choice(id2text_valid[neg_id])))

                    if score_pos >= np.max(score_neg):
                        acc += 1
                    total += 1
                    acc_dict = {f'acc': acc / total}
                    pbar.set_postfix(acc_dict)

            acc_list.append(acc/total)
        print(f"{times}次 sort {neg_samples} 测试平均准确率: {np.mean(acc_list)}")

if __name__ == '__main__':
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    ranker = EditDistancePreRank()
    R = REDIS()

    # 训练 faq 模型
    model_id = "my_test"
    model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{EditDistancePreRank.__name__}/")
    data_faq = R.get_data(f'faq_model1_all')
    ranker.train(model_id=model_id, data=data_faq, model_save_dir=model_save_dir)

    # 加载模型
    ranker.load_model(model_id=model_id, model_save_dir=model_save_dir)

    # search faq
    query_list = ['请问余额理财赎回到账时间？', '用IE登录个人网银出现空白页，怎么办??']
    for query in query_list:
        texts = ranker.predict(model_id=model_id, query=query, topk=5)
        print(f'\n查询句子: {query}')
        for text_item in texts:
            print(f'返回结果: {text_item.sim_query}, 置信度: {text_item.score}')

    # # 测试1 粗排 top_k 准确率
    # ranker.test_acc(model_name=model_name_faq, data=data_faq, topk=5, model_save_dir=model_save_dir_faq)
    # ranker.test_acc(model_name=model_name_chat, data=data_chat, topk=5, model_save_dir=model_save_dir_chat)