import logging

import pandas as pd
import copy

import setting
from setting import logger
from model.CallLLM.CallOpenAI_ultra import CallOpenAI as CallOpenAI_ultra
llm = CallOpenAI_ultra('qwen2.5-14b-instruct', base_url=setting.YUE_TRANSLATE_MODEL_URL, api_key="sk-12650c62ac694a959175757f9ec6b7c4")


import re
import json
def parse(result,norm):
    temp = re.findall("\{\n*.+\n*\}", result)
    temp[-1] = temp[-1]
    for _ in temp:
        ddd = json.loads(_)
        intent = ddd["意图名称"]
        idd = int(ddd["id"])
        if intent in norm:
            return intent
        elif idd!=-1:
            intent = norm[idd-1]
            return intent



example = """意图名称:“@intent@”
用户例句：@sim@
"""

prompt = """
### 参考的意图列表：

@example@

### 用户问题：
@query@

### 任务：
请你分析判断下用户问题以及潜在问题是什么，符合以上哪个意图。只输出意图名称，如果都不符合，请输出"未识别"。如果不确定是否符合，请输出模糊的意图，并输出不确定。 只输出json格式，不要输出其他内容。

json格式输出
{"意图名称": "意图名称", "id":"数字，不存在输出-1", "确定":"确定/不确定"}"""


sim = ['可以嘅，你想知啲咩？', '你哋嘅B超室喺边？', '点去你哋嘅医院呀？', 'CT喺边', '我应该点样保持好心情？', '你觉得交通安全要怎么做才好？', '失能同半失能嘅状况你明唔明呀？', '你认为结核病可厉啱嘛？', '你觉得健康教育科的情况如何？', '可以同你讲下洗牙嘅服务嗰？', '你觉得公共卫生部重要吗？', '你明边个系健康口腔嘅标准未呀？', '长者心理状况点样呢？', '你觉得结核病可以治好吗？', '可以同你分享一啲防癌治疗嘅好方法未呀？', '你觉得慢病政策为什么可以一直坚持呢？', '你会点嘘痰呀？', '你明唔明抑郁情绪同抑郁症嘅分别呀？', '特色诊疗嘅服务好唔好？', 'DR嘅用处系乜呢？', '', '好好好', '你真系好棒', '你是？', '你多大', '你哋呢个效果唔太好啊', '你的作用\n你嘅作用', '天气好唔好？', '你听唔到咩？', '你有咩光子嫩肤服务未呀？', '皮肤科新技术好唔好', '皮肤科设备好唔好', '有咩好避嘅？', '佢有咩症状你明唔明呀？', '佢系咩？', '点参加', '医保的待遇好不好', '讲讲你哋科室嘅亮点', '这病要咋治才好？', '准备咩呀？', '这药咋服用呀？', '去皮肤科要钱唔？', '点解预防呢啲病好D呀？', '嚟化验要注意咩事？', '高风险去边享受进一步嘅服务呢？', '你哋检验科嘅操作步骤系点样噶？', '符合条件', '备案得嗰阵先可以办好呀？', '生活习惯注意啥对身体好？']
norm = ['如何切换为粤语？', 'B超是什么', '南山慢病院的位置在哪', 'CT的作用是什么？', '黄褐斑有哪些注意事项', '怎样避免日常生活中的安全隐患', '什么是失能、半失能', '结核病可怕吗？', '菜单-健康教育科', '菜单-洗牙', '南山慢病院公共卫生部简介', '健康口腔\n健康牙口', '老年人心理关爱评估的内容是什么', '得了结核病会死吗？', '避免得癌症/肿瘤应在衣食住行注意哪些方面', '南山区如何确保慢性病防控政策的持续性和有效性', '如何留痰？', '抑郁情绪和抑郁症的区别是什么', '专家门诊/特色门诊', 'GE全数字化X线摄片系统是什么？', '打招呼', '我明白了', '你很聪明', '你是谁', '你多大', '你怎么什么都不懂', '你能干什么', '询问天气', '听得到吗', '皮肤科门诊特色诊疗范围', '皮肤科先进技术', '你们医院有哪些设备', '疾病的饮食要求', '它有什么表现', '它是什么', '怎么参加筛查', '医保的待遇有哪些', '精神卫生科亮点', '怎么治疗', '异地就医备案的办理渠道', '药物怎么使用', '有哪些免费检查', '怎么预防', '检验注意事项', '癌症高风险者要怎么做', '检验科工作流程', '哪些人能够参加筛查', '异地就医备案的办理时限需要多久', '日常生活要注意什么']
query = '你好'


def call_llm(query,norm:list,sim:list,history=[]):

    examples = '意图' + pd.Series(range(1,len(norm)+1)).map(str) +  ":" + pd.Series(norm)  + '\n例句：' + pd.Series(sim) + '\n'
    examples = examples.str.cat(sep='\n')
    inputs = prompt.replace('@example@', examples).replace('@query@', query)
    llm_parameter = {
      "model": "zs-gpt",
      "messages": [
            {
                "role": "system",
                "content": ""
            },
            {
                "role": "user",
                "content": inputs,
            }]  ,
      "temperature": 0.01,
            "stop":['<|im_end|>']}
    if history:
        llm_parameter['history'] = history + [{'role': 'user', 'content': inputs}]

    raw_result = llm.run(llm_parameter=llm_parameter)
    logger.info(f'LLMPreRank llm inputs:{inputs},output:{raw_result}')
    result = parse(raw_result,norm)
    logger.info(f'LLMPreRank llm output:{raw_result},parsed:{result}')
    return result



import pandas as pd
def organize_result(raw_pred_result_reg):
    pred_result_reg = copy.deepcopy(raw_pred_result_reg)
    use_key = [i for i in pred_result_reg if '_result' in i]
    use_col = ['sim_query','match_re','norm_query','norm_query_id','score']
    pred_result_reg = {i:j for i,j in pred_result_reg.items() if i in use_key}
    data = []
    for i in pred_result_reg:
        if 'sim_query' in pred_result_reg[i]:
            item = pred_result_reg[i]
            item = {i:item[i] for i in use_col}
            item['dtype'] = len(item['norm_query'])*[i]
            data.append(item)
    if data:
        data_df = pd.concat([pd.DataFrame(i) for i in data])
        data_df = data_df.sort_values('score',ascending=False)
    else:
        data_df = pd.DataFrame()
    return data_df



def select_norm_to_result(raw_pred_result_reg,select_dtype,select_data):
    use_key = [i for i in raw_pred_result_reg if '_result' in i] + ['data']
    for i in use_key:
        if i==select_dtype:
            raw_pred_result_reg[i].update(select_data)
    return raw_pred_result_reg


symbol_re = re.compile('，|，|。|？|！|：|；|,|\.|\?|!|:|;|、|（|）|\(|\)|」|「|/|"|“|”|\'|‘|’|【|】|《|》|<|>')
def remove_symbol(text,replace_to_what = ''):
    if replace_to_what:
        return symbol_re.sub(replace_to_what, text)
    else:
        return symbol_re.sub("", text)


def llmprerank(query,raw_pred_result_reg,update_threshold,history=[]):
    data_df = organize_result(raw_pred_result_reg)
    if data_df.shape[0]==0:
        return raw_pred_result_reg
    norm_query = data_df['norm_query'].tolist()
    sim_query = data_df['sim_query'].tolist()
    replace_norm = [remove_symbol(i) for i in norm_query]
    data_df['replace_norm'] = replace_norm
    llm_result = '大模型prerank失败'
    try:
        llm_result = call_llm(query,replace_norm,sim_query,history)
    except:
        logger.error(f'LLMPreRank error,query:{query},norm_query:{replace_norm},sim_query:{sim_query}',exc_info=True)

    #重置所有数据类型的返回为空
    use_key = [i for i in raw_pred_result_reg if '_result' in i] + ['data']
    for i in use_key:
        raw_pred_result_reg[i] = copy.deepcopy(update_threshold)

    raw_pred_result_reg['llm_result'] = llm_result
    if llm_result in replace_norm:
        sec = data_df[data_df['replace_norm']==llm_result].drop(columns='replace_norm')
        # print('before',raw_pred_result_reg)
        for index in range(len(sec)):
            select_data = sec.iloc[index:index+1]
            select_data['score'] = 1
            select_dtype = select_data['dtype'].tolist()[0]
            select_data = select_data.drop(columns = 'dtype').to_dict(orient='list')
            raw_pred_result_reg = select_norm_to_result(raw_pred_result_reg,select_dtype,select_data)
        return raw_pred_result_reg
    else:#未识别/大模型报错/大模型返回无法映射到norm
        return raw_pred_result_reg



if __name__=='__main__':
    query = "如何註冊 “fps"
    norm_query = ['默认节点', '如何註冊 “FPS”', '如何登記轉數快FPS', '使用轉數快FPS 服務有何好處', '我可以如何透過轉數快FPS 收款及付款', '登記轉數快FPS會否增加戶口風險', '如何將資金存入戶口', '如何開立戶口', '步骤二', '轉數快FPS 預設的付款上限和每日的付款上限是多少', '待啟動是什麼意思', '開戶需要甚麼申請資格', '有哪些轉賬方式', '如果我轉賬至錯誤的收款人該如何處理', '轉微信', '身份證拍攝不能通過怎麼辦', '美籍人士可以開立戶口嗎', '轉數快FPS 是否支援外幣轉賬', '我可以預設外幣買賣服務嗎', '匯往歐盟及阿聯酋地區應在匯款頁面何處輸入國際銀行帳戶號碼', '透過轉數快FPS 轉賬至其他銀行戶口會否收取手續費', '如何登入Fusion Bank app', '客戶服務熱線', '我為什麼開戶資料有誤要重新開戶', '外匯內地地址填寫', '查詢帳戶號碼', '是否一定要給家庭地址', 'How to scan QR code', '88活動', '詢問指引', '詢問等待時間', '詢問如何計算', '特選客戶優惠', '销户', '詢問確認審核時間', 'can’t find the button', '修改手機號', '幫朋友預約', '定期存款有多少利息', '帳號無金額未使用']
    sim_query = ['', '如何註冊 “FPS”？', '請問下如何去註冊‘轉數快’(FPS)呢？', 'How does registering an FPS ID improve my payment experience?', 'Could you guide me through configuring FPS for quick收款and付款?', 'Are there privacy concerns when registering personal details with FPS?', 'Could you list the steps to deposit funds through CHATS or FPS?', '想問下開戶口嘅步驟。', '第二步要怎麼認證呢', 'Is there a preset cap on FPS payments upon account opening, and how does it change later?', 'Prompt for activation', '開戶基本要求系點？', 'What options are available for transferring money using FPS?', "I sent money to the wrong FPS ID - what's the procedure to get it back?", '绑定wechat', 'How to position ID correctly within the scanning frame?', 'Is account registration available to people with U.S. citizenship?', 'Can I receive payments in foreign currencies through the FPS platform?', '我能否啟動外幣交易的預設服務？', '進行國際匯款到歐盟和阿聯酋時，帳號要在哪里填寫？', 'Will I incur costs when making interbank transfers through FPS?', '點樣登入Fusion Bankapp？', '我如何才能聯繫客戶服務？', '我開戶資料點解會錯，要再進行開戶？', 'How to fill in the remittance address?', '我忘记我的开户账号了', 'Can I fill in the address where I work?', 'QR码', '首次开户并且绑定微信就可以获得优惠券', '詢問指引', 'How long does it take to open an account?', 'How do you calculate it?', '請問我想開戶口享受您們嘅優惠，但是我怎樣知道自己是否特選客戶？', 'I want to freeze my account', 'Immediate review?', "I just need to scan it, but I can't find the scan button.", 'I have changed my phone', '幫朋友預約', 'How much money do I need to deposit to get interest?', '请问开户后，如果没有存放金钱，会不会有手续费']
    result = call_llm(query,norm_query,sim_query,history=[])


    # update_threshold = {'threshold':0.5}
    # query = '如何购票'
    # raw_pred_result_reg = {"code": 0, "model_id": "1871443221825982465", "text": "如何购票", "msg": "预测成功",
    #                        "intent_result": {"complete_score": 0.93, "threshold": 0.85, "low_threshold": 0.65},
    #                        "faq_result": {"match_type": "model", "sim_query": ["如何开发票", "如约巴士如何购票？"], "match_re": ["", ""], "norm_query": ["如何购票", "如约巴士如何购票？"], "norm_query_id": ["1871494402962472960", "1871494403331571712"], "score": [0.****************, 0.***************], "complete_score": 0.93, "threshold": 0.85, "low_threshold": 0.65}, "chat_result": {"complete_score": 0.93, "threshold": 0.85, "low_threshold": 0.65}, "data": {"match_type": "model", "sim_query": ["如何开发票", "如约巴士如何购票？"], "match_re": ["", ""], "norm_query": ["如何购票", "如约巴士如何购票？"], "norm_query_id": ["1871494402962472960", "1871494403331571712"], "score": [0.****************, 0.***************], "complete_score": 0.93, "threshold": 0.85, "low_threshold": 0.65}, "graph_result": {"match_type": "model", "sim_query": ["如何开具发票"], "match_re": [""], "norm_query": ["如何购票"], "norm_query_id": ["如何购票"], "score": [1], "complete_score": 0.93, "threshold": 0.85, "low_threshold": 0.65}, "sn": "gotest_1734920862_20267"}
    # result = llmprerank(query,raw_pred_result_reg,update_threshold)