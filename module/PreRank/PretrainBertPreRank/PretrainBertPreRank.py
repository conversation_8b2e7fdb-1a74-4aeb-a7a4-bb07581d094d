# -*- coding: utf-8 -*-

import os
import pickle
import time
import warnings

import numpy as np

import setting

warnings.filterwarnings('ignore')
os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.metrics.pairwise import normalize
from tqdm import tqdm

from database.REDIS import REDIS
from utils.my_utils import RankReturnObject, my_cosine_similarity, remove_symbol
from model.TextRepresentationPretrainBert.BertTextRepresentationLength import BertTextRepresentationLength
from setting import logger


class PretrainBertPreRank:
    def __init__(self):
        self.model = BertTextRepresentationLength(
            model_path=setting.PRETRAIN_BERT_DIR,
            emb_layer=setting.PRE_BERT_PRE_RANK_EMB_LAYER,
            max_len=setting.PRE_BERT_PRE_RANK_MAX_LEN
        )
        self.models = dict()

    def load_model(self, model_id, model_save_dir):
        try:
            with open(os.path.join(model_save_dir, "text2norm_id.pkl"), 'rb') as f:
                text2norm_id = pickle.load(f)
            with open(os.path.join(model_save_dir, "norm_id2norm_text.pkl"), 'rb') as f:
                norm_id2norm_text = pickle.load(f)
            with open(os.path.join(model_save_dir, "all_text.pkl"), 'rb') as f:
                all_text = pickle.load(f)
            with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)
            embeddings = np.load(os.path.join(model_save_dir, 'embeddings.npy'), allow_pickle=True)
            embeddings_norm = np.load(os.path.join(model_save_dir, 'embeddings_norm.npy'), allow_pickle=True)
            self.models[model_id] = dict()
            self.models[model_id]['text2norm_id'] = text2norm_id
            self.models[model_id]['norm_id2norm_text'] = norm_id2norm_text
            self.models[model_id]['all_text'] = all_text
            self.models[model_id]['text2item_cm'] = text2item_cm
            self.models[model_id]['embeddings'] = embeddings
            self.models[model_id]['embeddings_norm'] = embeddings_norm
            self.predict(model_id=model_id, query="测试一下", topk=1)
        except Exception as e:
            logger.error(f"模型加载失败 [{model_id}], 错误信息: {e}")
            raise Exception(f"模型加载失败 [{model_id}], 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"模型下线成功 [{model_id}]")

    def train(self, model_id, data, model_save_dir):
        """
        耗时: batch=64 num_batch=388 data=24819 7分钟
        """
        start = time.time()

        # 模型保存地址
        os.makedirs(model_save_dir, exist_ok=True)

        # 整理数据
        text2norm_id = dict()
        norm_id2norm_text = dict()
        text2item_cm = dict() # 精确匹配字典

        for data_dict in data:
            norm_query = data_dict['title']
            norm_query_id = data_dict['labelId']

            norm_id2norm_text[norm_query_id] = norm_query
            text2norm_id[norm_query] = norm_query_id
            norm_query_ = remove_symbol(norm_query)
            text2item_cm[norm_query_] =[norm_query, norm_query_id]

            for text in data_dict["labelData"].split("||"):
                text = text.strip()
                if len(text):
                    text2norm_id[text] = norm_query_id
                text = remove_symbol(text)
                if len(text):
                    text2item_cm[text] =[norm_query, norm_query_id]

        all_text = list(text2norm_id.keys())
        logger.debug(f"开始获取 embedding, model_id:{model_id},数据量:{len(all_text)}")

        # 获取 all_text 的 embedding
        embeddings = self.model.encode_sentences(text_list=all_text, batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE)
        embeddings_norm = normalize(embeddings, copy=True)

        # 保存模型
        with open(os.path.join(model_save_dir, "text2norm_id.pkl"), 'wb') as f:
            pickle.dump(text2norm_id, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "norm_id2norm_text.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)
        np.save(os.path.join(model_save_dir, 'embeddings.npy'), embeddings)
        np.save(os.path.join(model_save_dir, 'embeddings_norm.npy'), embeddings_norm)

        # self.models[model_id] = dict()
        # self.models[model_id]['text2norm_id'] = text2norm_id
        # self.models[model_id]['norm_id2norm_text'] = norm_id2norm_text
        # self.models[model_id]['all_text'] = all_text
        # self.models[model_id]['text2item_cm'] = text2item_cm
        # self.models[model_id]['embeddings'] = embeddings
        # self.models[model_id]['embeddings_norm'] = embeddings_norm

        logger.debug(f"训练完成 [{model_id}],数据量:{len(data)},文本量:{len(all_text)},Emb:{embeddings.shape},耗时:{time.time()-start}")

    def incremental_train(self, model_id, data, model_save_dir):
        # 加载模型
        try:
            with open(os.path.join(model_save_dir, "text2norm_id.pkl"), 'rb') as f:
                text2norm_id = pickle.load(f)
            with open(os.path.join(model_save_dir, "norm_id2norm_text.pkl"), 'rb') as f:
                norm_id2norm_text = pickle.load(f)
            with open(os.path.join(model_save_dir, "all_text.pkl"), 'rb') as f:
                all_text = pickle.load(f)
            with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)
            embeddings = np.load(os.path.join(model_save_dir, 'embeddings.npy'), allow_pickle=True)
            embeddings_norm = np.load(os.path.join(model_save_dir, 'embeddings_norm.npy'), allow_pickle=True)
        except Exception as e:
            logger.error(f"增量训练失败 [{model_id}], 错误信息: {e}")
            raise Exception(f"增量训练失败 [{model_id}], 错误信息: {e}")

        # 加入新数据
        new_text = []
        for data_dict in data:
            norm_query = data_dict['title']
            norm_query_id = data_dict['labelId']

            # 之前没有的 query 加到 new_text
            if norm_query not in text2norm_id:
                new_text.append(norm_query)
            # 不管之前有没有该 query 都直接更新数据，因为 id 可能会变
            text2norm_id[norm_query] = norm_query_id
            norm_id2norm_text[norm_query_id] = norm_query

            norm_query_ = remove_symbol(norm_query)
            text2item_cm[norm_query_] =[norm_query, norm_query_id]

            for text in data_dict["labelData"].split("||"):
                text = text.strip()
                if len(text):
                    # 之前没有的 query 加到 new_text
                    if text not in text2norm_id:
                        new_text.append(text)
                    # 不管之前有没有该 query 都直接更新数据，因为 id 可能会变
                    text2norm_id[text] = norm_query_id
                text = remove_symbol(text)
                if len(text):
                    text2item_cm[text] =[norm_query, norm_query_id]

        if len(new_text) != 0:
            all_text = all_text + new_text
            # 获取新数据的 embedding
            new_embeddings = self.model.encode_sentences(text_list=new_text, batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE)
            new_embeddings_norm = normalize(new_embeddings, copy=True)
            embeddings = np.vstack([embeddings, new_embeddings])
            embeddings_norm = np.vstack([embeddings_norm, new_embeddings_norm])

        # 保存模型
        with open(os.path.join(model_save_dir, "text2norm_id.pkl"), 'wb') as f:
            pickle.dump(text2norm_id, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "norm_id2norm_text.pkl"), 'wb') as f:
            pickle.dump(norm_id2norm_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)
        np.save(os.path.join(model_save_dir, 'embeddings.npy'), embeddings)
        np.save(os.path.join(model_save_dir, 'embeddings_norm.npy'), embeddings_norm)

        self.models[model_id] = dict()
        self.models[model_id]['text2norm_id'] = text2norm_id
        self.models[model_id]['norm_id2norm_text'] = norm_id2norm_text
        self.models[model_id]['all_text'] = all_text
        self.models[model_id]['text2item_cm'] = text2item_cm
        self.models[model_id]['embeddings'] = embeddings
        self.models[model_id]['embeddings_norm'] = embeddings_norm

        logger.debug(f"增量训练完成 [{model_id}], 数据量: {len(data)}, 文本量: {len(all_text)}, Emb: {embeddings.shape}")

    def predict(self, model_id, query, topk=50):
        if model_id not in self.models:
            logger.error(f"预测失败 [{model_id}], 错误信息: 模型未加载")
            raise Exception(f"预测失败 [{model_id}], 错误信息: 模型未加载")

        norm_id2norm_text = self.models[model_id]['norm_id2norm_text']
        text2norm_id = self.models[model_id]['text2norm_id']
        all_text = self.models[model_id]['all_text']
        text2item_cm = self.models[model_id]['text2item_cm']
        embeddings_norm = self.models[model_id]['embeddings_norm']

        # 精确匹配
        query_ = remove_symbol(query)
        if query_ in text2item_cm:
            norm_query, norm_query_id = text2item_cm[query_]
            item = RankReturnObject(query=query, sim_query=query_, norm_query=norm_query,
                                    norm_query_id=norm_query_id, score=1, save_bigger=True)
            return [item]

        # 粗排
        emb = self.model.encode_sentences(text_list=[query], batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE)
        scores = my_cosine_similarity(embeddings_norm, emb)[:, 0]
        index = np.argsort(scores)
        heap = []
        for idx in index[-topk:][::-1]:
            sim_query = all_text[idx]
            norm_query_id = text2norm_id[sim_query]
            norm_query = norm_id2norm_text[norm_query_id]
            item = RankReturnObject(query=query, sim_query=sim_query, norm_query=norm_query,
                                    norm_query_id=norm_query_id, score=scores[idx], save_bigger=True)
            heap.append(item)
        return heap

    def labeling(self, model_id, texts, text_ids=None):
        labeling_result = []

        text2norm_id = self.models[model_id]['text2norm_id']
        all_text = self.models[model_id]['all_text']
        embeddings_norm = self.models[model_id]['embeddings_norm'] # [m, dim]

        embeddings = self.model.encode_sentences(text_list=texts, batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE) # [n, dim]

        scores = my_cosine_similarity(embeddings_norm, embeddings) # [m, n]
        max_idx = np.argmax(scores, axis=0)

        for i in range(max_idx.shape[0]):
            result = {
                "text": texts[i],
                "id": text_ids[i] if text_ids is not None else None,
                "label": text2norm_id[all_text[max_idx[i]]],
                "score": scores[max_idx[i], i],
                "margin_score": scores[max_idx[i], i]
            }
            labeling_result.append(result)
        return labeling_result

    def test_acc(self, model_id, data, topk=5, model_save_dir=""):
        """
        用 data 中的文本测试 topk 准确率, 需要返回 topk+1 个结果，因为包含自身。
        耗时: 30 分钟
        """
        total = 0
        acc = [0] * topk

        pbar = tqdm(data)
        for data_dict in pbar:
            pbar.set_description('测试进度')
            norm_query_id = data_dict['labelId']
            for text in data_dict["labelData"].split("||"):
                sim_query = text.strip()
                heap = self.predict(model_id, sim_query, topk=topk+1)
                assert len(heap) == topk + 1
                for idx in range(len(heap)):
                    if heap[idx].sim_query == sim_query:
                        heap.pop(idx)
                        break
                assert len(heap) == topk
                pred_ids = np.array([item.norm_query_id for item in heap])
                for j in range(topk):
                    if norm_query_id in pred_ids[:j + 1]:
                        acc[j] += 1
                total += 1
            acc_dict = {f'acc{topk}': acc[topk - 1] / total}
            pbar.set_postfix(acc_dict)

        with open(os.path.join(model_save_dir, 'test_result.txt'), 'w', encoding='utf-8') as f:
            for i in range(topk):
                line = f'top {i + 1} 准确率: {acc[i] / total}'
                print(line)
                f.writelines(line + '\n')

    def my_test_pos_neg_acc_threshold(self, id2text_valid, threshold, times=1):
        import random
        from tqdm import tqdm
        from sklearn.metrics.pairwise import cosine_similarity
        random.seed(setting.SEED)
        acc_list = []

        for _ in range(times):
            # 测试准确率，正负样本 1:1
            acc, total = 0, 0
            id_list = list(id2text_valid.keys())
            pbar = tqdm(id2text_valid.items())
            for valid_id, text_list in pbar:
                pbar.set_description('测试进度')
                # 正样本
                text1_list = []
                text2_list = []
                for i, text1 in enumerate(text_list):
                    text1_list.append(text1)
                    text2_list.append(random.choice(text_list))
                emb_1 = self.model.encode_sentences(text_list=text1_list, batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE) # [n, dim]
                emb_2 = self.model.encode_sentences(text_list=text2_list, batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE) # [n, dim]
                score =  cosine_similarity(emb_1, emb_2) # [n, n]
                score = (score + 1) / 2
                score = np.diagonal(score)
                total += score.shape[0]
                acc += np.sum(score >= threshold)

                # 负样本
                text1_list = []
                text2_list = []
                for i, text1 in enumerate(text_list):
                    text1_list.append(text1)
                    neg_id = random.choice(id_list)
                    while neg_id == valid_id:
                        neg_id = random.choice(id_list)
                    text2_list.append(random.choice(id2text_valid[neg_id]))
                emb_1 = self.model.encode_sentences(text_list=text1_list, batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE) # [n, dim]
                emb_2 = self.model.encode_sentences(text_list=text2_list, batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE) # [n, dim]
                score =  cosine_similarity(emb_1, emb_2) # [n, n]
                score = (score + 1) / 2
                score = np.diagonal(score)
                total += score.shape[0]
                acc += np.sum(score < threshold)

                acc_dict = {f'acc': acc / total}
                pbar.set_postfix(acc_dict)

            acc_list.append(acc/total)
        print(f"{times}次 threshold {threshold} 测试平均准确率: {np.mean(acc_list)}")

    def my_test_pos_neg_acc_sort(self, id2text_valid, neg_samples, times=1):
        import random
        from tqdm import tqdm
        from sklearn.metrics.pairwise import cosine_similarity
        random.seed(setting.SEED)
        acc_list = []

        for _ in range(times):
            # 测试准确率，正负样本 1:1
            acc, total = 0, 0
            id_list = list(id2text_valid.keys())
            pbar = tqdm(id2text_valid.items())
            for valid_id, text_list in pbar:
                pbar.set_description('测试进度')
                for text1 in text_list:
                    text1_list = []
                    text2_list = []

                    # 正样本
                    text1_list.append(text1)
                    text2_list.append(random.choice(text_list))

                    # 负样本
                    for i in range(neg_samples):
                        neg_id = random.choice(id_list)
                        while neg_id == valid_id:
                            neg_id = random.choice(id_list)

                        text1_list.append(text1)
                        text2_list.append(random.choice(id2text_valid[neg_id]))

                    emb_1 = self.model.encode_sentences(text_list=text1_list, batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE) # [n, dim]
                    emb_2 = self.model.encode_sentences(text_list=text2_list, batch_size=setting.PRE_BERT_PRE_RANK_BATCH_SIZE) # [n, dim]
                    score =  cosine_similarity(emb_1, emb_2) # [n, n]
                    score = np.diagonal(score)
                    if score[0] == np.max(score):
                        acc += 1
                    total += 1
                    acc_dict = {f'acc': acc / total}
                    pbar.set_postfix(acc_dict)

            acc_list.append(acc/total)
        print(f"{times}次 sort {neg_samples} 测试平均准确率: {np.mean(acc_list)}")

if __name__ == '__main__':
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    ranker = PretrainBertPreRank()
    R = REDIS()

    # 训练 faq 模型
    model_id = "my_test"
    model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{ranker.__class__.__name__}/")
    data_faq = R.get_data(f'faq_model1_all')
    # ranker.train(model_id=model_id, data=data_faq, model_save_dir=model_save_dir)

    # 加载模型
    ranker.load_model(model_id=model_id, model_save_dir=model_save_dir)

    # search faq
    query_list = ['请问余额理财赎回到账时间？', '用IE登录个人网银出现空白页，怎么办??']
    for query in query_list:
        texts = ranker.predict(model_id=model_id, query=query, topk=5)
        print(f'\n查询句子: {query}')
        for text_item in texts:
            print(f'返回结果: {text_item.sim_query}, 置信度: {text_item.score}')

    # search labeling
    labeling_data = R.get_data(f'faq_labeling_test_data')
    texts = []
    text_ids = []
    for data_dict in labeling_data:
        texts.append(data_dict["text"])
        text_ids.append(data_dict["id"])
    label_result = ranker.labeling(model_id=model_id, texts=texts, text_ids=text_ids)
    print(label_result)

    # # 测试1 粗排 top_k 准确率
    # ranker.test_acc(model_id=model_id, data=data_faq, topk=5, model_save_dir=model_save_dir)