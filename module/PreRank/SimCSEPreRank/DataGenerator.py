# -*- coding: UTF-8 -*-

import random

import numpy as np
from tensorflow import keras

import setting
from database.REDIS import REDIS


class DataGenerator(keras.utils.Sequence):
    def __init__(self, text_list, tokenizer):
        self.text_list = text_list
        self.idx_list = [i for i in range(len(text_list))]
        self.tokenizer = tokenizer
        self.batch_size = min(len(text_list) * 2, setting.SIMCSE_PRE_RANK_BATCH_SIZE)
        self.num_text = np.sum([len(t) for t in self.text_list])
        # self.num_batches = max(min(setting.SIMCSE_PRE_RANK_NUM_BATCHS, self.num_text//self.batch_size*2), 1)
        self.num_batches = max(self.num_text//self.batch_size*2, 1)
        random.seed(setting.SEED)

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return self.num_batches

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        random.shuffle(self.idx_list)
        X, Y = self.__data_generation(self.idx_list[:(self.batch_size//2)])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        pass

    def __data_generation(self, idx_list):
        """
        生成 cur_id 的数据。
        """
        input_ids = np.zeros(shape=(self.batch_size, setting.SIMCSE_PRE_RANK_MAX_LEN), dtype=np.int32)
        token_type_ids = np.zeros(shape=(self.batch_size, setting.SIMCSE_PRE_RANK_MAX_LEN), dtype=np.int32)
        attention_mask = np.zeros(shape=(self.batch_size, setting.SIMCSE_PRE_RANK_MAX_LEN), dtype=np.int32)
        labels = np.zeros(shape=(self.batch_size, 1))
        index = 0

        for idx in idx_list:
            for _ in range(2):
                # 一个 idx 采样两个样本，作为正样本
                text = random.choice(self.text_list[idx])
                inputs = self.tokenizer.encode_plus(text, add_special_tokens=True,
                                                    max_length=setting.SIMCSE_PRE_RANK_MAX_LEN,
                                                    truncation=True,
                                                    padding="max_length")
                input_ids[index, :] = inputs["input_ids"]
                attention_mask[index, :] = inputs["attention_mask"]
                token_type_ids[index, :] = inputs["token_type_ids"]
                index += 1

        return (
            {
                'input_ids': input_ids,
                'token_type_ids': token_type_ids,
                'attention_mask': attention_mask
            },
            labels
        )


if __name__ == '__main__':
    from sklearn.model_selection import train_test_split
    from transformers import BertTokenizer

    data_key = "faq_model1_all"
    R = REDIS()
    data = R.get_data(data_key)

    # 划分数据集
    text_list_train = []
    text_list_valid = []
    for data_dict in data:
        norm_query = data_dict['title']
        texts = set()
        texts.add(norm_query)

        for text in data_dict['labelData'].split("||"):
            text_strip = text.strip()
            if len(text_strip):
                texts.add(text_strip)
        texts = list(texts)

        if len(texts) <= 0:
            continue
        elif len(texts) == 1:
            text_list_train.append(texts)
        else:
            texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
            text_list_train.append(texts_train)
            text_list_valid.append(texts_valid)

    tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_VOCAB)
    train_gen = DataGenerator(text_list=text_list_train, tokenizer=tokenizer)
    valid_gen = DataGenerator(text_list=text_list_valid, tokenizer=tokenizer)

    for d in train_gen:
        print(d)
        break

