import numpy as np
import pandas as pd
from scipy import stats, integrate
import seaborn as sns
import matplotlib.pyplot as plt
import json

with open("/data/cbk/NLP_Model/model1/SimCSEPreRank/test_threshold_neg.json", "r", encoding="utf-8") as f:
    scores_neg = json.load(f)

plt.figure(figsize=(10, 5))
plt.boxplot(scores_neg, vert=False,showmeans=True )
plt.title("neg bins")
plt.show()

plt.figure(figsize=(21, 12))
plt.hist(scores_neg, bins=50)
plt.title("neg distribute")
plt.grid()
plt.show()

with open("/data/cbk/NLP_Model/model1/SimCSEPreRank/test_threshold_pos.json", "r", encoding="utf-8") as f:
    scores_pos = json.load(f)

plt.figure(figsize=(10, 5))
plt.boxplot(scores_pos, vert=False,showmeans=True )
plt.title("pos bins")
plt.show()

plt.figure(figsize=(21, 12))
plt.hist(scores_pos, bins=50)
plt.title("pos distribute")
plt.grid()
plt.show()