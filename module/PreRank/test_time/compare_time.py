import os
import random
import time

import Levenshtein
import jieba
import numpy as np
import scipy
from faker import Faker
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.feature_extraction.text import TfidfTransformer
from sklearn.metrics.pairwise import normalize

import setting
from model.TextRepresentationPretrainBert.BertTextRepresentation import BertTextRepresentation
from utils.my_utils import my_cosine_similarity

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE

fake = Faker(locale='zh_CN')

times = 10000
texts = [fake.sentence(random.randint(10, 100)) for i in range(1000)]
query = "如果我从chrome浏览器访问个人网银就会失败"
nums = times*len(texts)
print(nums)

# 编辑距离
all_text = texts * times
start = time.time()
scores = np.zeros(shape=(len(all_text)))
for i, text in enumerate(all_text):
    scores[i] = Levenshtein.ratio(query, text)
index = np.argsort(scores)
print(f"编辑距离时间: {time.time() - start}")

# TFIDF
corpos = [' '.join(jieba.cut(text)) for text in texts]
vectorizer = CountVectorizer(token_pattern=r"(?u)\b\w+\b")
transformer = TfidfTransformer()
tfidf = transformer.fit_transform(vectorizer.fit_transform(corpos))
tfidf_norm = normalize(tfidf, copy=True)
tfidf_norms = [tfidf_norm] * times
tfidf_norm = scipy.sparse.vstack(tfidf_norms)

start = time.time()
query_tfidf = transformer.transform(vectorizer.transform([' '.join(jieba.cut(query))]))
scores = my_cosine_similarity(tfidf_norm, query_tfidf)[:, 0]
index = np.argsort(scores)
print(f"TFIDF时间: {time.time() - start}")

# Bert
model = BertTextRepresentation(model_path=setting.PRETRAIN_BERT_DIR, emb_layer=[1, -1], max_len=128)
emb = model.encode_sentences(text_list=texts)
emb_norm = normalize(emb, copy=True)
emb_norms = [emb_norm] * times
emb_norm = np.vstack(emb_norms)
start = time.time()
query_emb = model.encode_sentences(text_list=[query])
scores = my_cosine_similarity(emb_norm, query_emb)[:, 0]
index = np.argsort(scores)
print(f"Bert时间: {time.time() - start}")
