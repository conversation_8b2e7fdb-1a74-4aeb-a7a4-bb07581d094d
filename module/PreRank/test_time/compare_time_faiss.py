import os
import time

import faiss
import numpy as np

import setting
from utils.my_utils import my_cosine_similarity

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE

embeddings = np.load(os.path.join('/data/cbk/NLP_Model/model1/SimCSEPreRank/embeddings.npy'), allow_pickle=True)
embeddings_norm = np.load(os.path.join('/data/cbk/NLP_Model/model1/SimCSEPreRank/embeddings_norm.npy'), allow_pickle=True)
emb = np.random.random((1, embeddings_norm.shape[1])).astype('float32')

N = 10000
scores = my_cosine_similarity(embeddings_norm, emb)[:, 0]
true_index = np.argsort(scores)[-5:]
true_index = true_index[::-1]


# cos
"""
cpu 占用 35 个
cos_time: 0.05372471380233765
"""
start = time.time()
for _ in range(N):
    scores = my_cosine_similarity(embeddings_norm, emb)[:, 0]
    index = np.argsort(scores)
cos_time = time.time()-start
print(f"cos_time: {cos_time/N}")


# faiss
"""
cpu 占用 20
faiss_time: 0.0026465182304382323
"""
start = time.time()
index = faiss.IndexFlatIP(embeddings_norm.shape[1])
index.add(embeddings_norm)
for _ in range(N):
    D, I = index.search(emb, 5)
faiss_time = time.time()-start
print(f"faiss_time: {faiss_time/N}")


# fast faiss
"""
cpu 占用 20
fast faiss_time: 4.6912860870361326e-05
"""
start = time.time()
quantizer = faiss.IndexFlatIP(embeddings_norm.shape[1])
index = faiss.IndexIVFFlat(quantizer, embeddings_norm.shape[1], 100)
index.train(embeddings_norm)
index.add(embeddings_norm)
for _ in range(N):
    D, I = index.search(emb, 5)
faiss_time = time.time()-start
print(f"fast faiss_time: {faiss_time/N}")

input("WAIT")
