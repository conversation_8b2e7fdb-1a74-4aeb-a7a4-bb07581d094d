#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:threshold.py
@time:2022/11/24
"""
import json
import math
import numpy as np
from matplotlib import pyplot as plt

with open("/data/cbk/NLP_Model/tencent_pretrain_arccse_epoch10/ArcCSEPreRank/test_threshold_pos.json", "r", encoding="utf-8") as f:
    pos = json.load(f)
pos = [i[2] for i in pos]
pos.sort()
with open("/data/cbk/NLP_Model/tencent_pretrain_arccse_epoch10/ArcCSEPreRank/test_threshold_neg.json", "r", encoding="utf-8") as f:
    neg = json.load(f)
neg = [i[2] for i in neg]
neg.sort()

# 0.05分位减10度
complete_match_scores = pos[int(len(pos) * 0.9)]
match_scores = math.cos(math.acos(pos[int(len(pos)*0.05)]) - math.pi / 18)
no_match_scores = math.cos(math.acos(match_scores) + math.pi / 18)
print("0.05分位减10度", complete_match_scores, match_scores, no_match_scores)

plt.figure()
plt.title(f"pos scores")
plt.hist(pos, bins=30)
plt.plot([complete_match_scores, complete_match_scores], [0, 1400], c="red")
plt.plot([match_scores, match_scores], [0, 1400], c="red")
plt.plot([no_match_scores, no_match_scores], [0, 1400], c="red")
plt.xlim(0.0, 1)
plt.show()
plt.figure()
plt.title(f"neg scores")
plt.hist(neg, bins=30)
plt.plot([complete_match_scores, complete_match_scores], [0, 30], c="red")
plt.plot([match_scores, match_scores], [0, 30], c="red")
plt.plot([no_match_scores, no_match_scores], [0, 30], c="red")
plt.xlim(0.0, 1)
plt.show()


# 0.05分位
complete_match_scores = pos[int(len(pos) * 0.9)]
match_scores = pos[int(len(pos)*0.05)]
no_match_scores = math.cos(math.acos(match_scores) + math.pi / 18)
print("0.05分位", complete_match_scores, match_scores, no_match_scores)

plt.figure()
plt.title(f"pos scores")
plt.hist(pos, bins=30)
plt.plot([complete_match_scores, complete_match_scores], [0, 1400], c="red")
plt.plot([match_scores, match_scores], [0, 1400], c="red")
plt.plot([no_match_scores, no_match_scores], [0, 1400], c="red")
plt.xlim(0.0, 1)
plt.show()
plt.figure()
plt.title(f"neg scores")
plt.hist(neg, bins=30)
plt.plot([complete_match_scores, complete_match_scores], [0, 30], c="red")
plt.plot([match_scores, match_scores], [0, 30], c="red")
plt.plot([no_match_scores, no_match_scores], [0, 30], c="red")
plt.xlim(0.0, 1)
plt.show()

# mean+10度
complete_match_scores = pos[int(len(pos) * 0.9)]
match_scores = math.cos(math.acos(np.mean(pos)) + math.pi / 18)
no_match_scores = math.cos(math.acos(match_scores) + math.pi / 18)
print("mean+10度", complete_match_scores, match_scores, no_match_scores)

plt.figure()
plt.title(f"pos scores")
plt.hist(pos, bins=30)
plt.plot([complete_match_scores, complete_match_scores], [0, 1400], c="red")
plt.plot([match_scores, match_scores], [0, 1400], c="red")
plt.plot([no_match_scores, no_match_scores], [0, 1400], c="red")
plt.xlim(0.0, 1)
plt.show()
plt.figure()
plt.title(f"neg scores")
plt.hist(neg, bins=30)
plt.plot([complete_match_scores, complete_match_scores], [0, 30], c="red")
plt.plot([match_scores, match_scores], [0, 30], c="red")
plt.plot([no_match_scores, no_match_scores], [0, 30], c="red")
plt.xlim(0.0, 1)
plt.show()