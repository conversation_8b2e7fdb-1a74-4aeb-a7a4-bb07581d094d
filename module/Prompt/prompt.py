import setting
from setting import logger
import re
from utils.my_utils import remove_front_end_symbol_2
from model.TraAndSim.TraAndSim import TraAndSim

tra_sim_model = TraAndSim()

def get_prompt_openai(query: str, history: str, retrieval_result: str):
    if not retrieval_result: retrieval_result = '参考资料为空'
    if retrieval_result:
        input_ = "当前问题可以参考的资料有：\n###" + retrieval_result + '###\n\n' \
                                                           "请根据以上信息回答用户当前的问题，有些问题可能需要进行一定的计算才能回答，计算依据不能违背常识，忘掉你自己的知识，如果###中参考资料无关的问题坚决拒绝回答\n用户当前的问题：" \
                 + query.strip() + '\n' + """回答格式：\nanswer：*\nsource：文档number\n""" + \
                 """注意：搜到的参考文档内容之间并无直接联系，因为它们可能是从不同文档中搜到的互不相关的信息。所以内容文档之间无上下文联系关系。\n"""
    else:
        input_ = query
    if not retrieval_result and history:
        input_ = "用户当前的问题：" + query.strip() + '\n'
    if history:
        input_ = "用户与机器人的历史对话如下:【{}】\n".format(history) + input_
    return input_

def get_prompt_our(query: str, history: str, retrieval_result: str, lang=""):
    if not retrieval_result: retrieval_result = '参考资料为空'
    if retrieval_result:
        input_ = "当前问题可以参考的资料有：\n###" + retrieval_result + '###\n\n' \
                 + """以上是搜到的各个文档以及每个文档下的相关的各个具体段落内容（如各个具体段落内容的区分用了空行分开的“内容+整数id号”的格式,如内容2，内容4等。文档则是“文档+整数id号”的格式，如文档1，文档5等，而这里的举例仅供示例说明，你要按照实际搜到的参考资料来，比如内容并不一定是按照数字顺序来的）

                你是一个非常聪明且很听话的文档查找和回答助手,你知道的全部知识仅仅是上面的内容。
                现在请你根据以上你知道的内容回答用户当前的问题，当你从文档中找不到直接的答案信息回答用户时也要说明原因。但尽可能提供有用信息，帮助解答用户的疑问；可以做生活常识的推理
                
                要求：如果你从参考信息中确实找不到直接相关的答案信息，那你就不能回答问题；
                如果参考资料###中的内容没有直接信息可以回答用户当前的问题，则要给出不能回答的理由。
                不要输出参考资料中没有内容；
                
                您的输出格式应该是这样的：
                回答：你对问题的分析与回答
                答案来源：{"文档号":"","文档名称":"","答案所在处所属的内容号":""}.  示例:答案来源：{"文档号":"文档4","文档名称":"XXXX.pdf","答案所在处所属的内容号":"内容2"}
                注意：只有能找到答案时，答案来源中才给出具体结果，并且只给出包含了答案结果来源对应的文档号,文档名称,答案所在处所属的内容号
                
                用户当前的问题：""" + query.strip()
    else:
        input_ = query
    if not retrieval_result and history:
        input_ = "用户当前的问题：" + query.strip() + '\n'
    if history:
        input_ = "用户与机器人的历史对话如下:【{}】\n".format(history) + input_
    return input_

def gpt4_answer_replacer(answer):
    """gpt4的replacer"""
    # 各种替换
    answer = re.sub('文档(\d+)', '文档', answer)
    answer = re.sub('内容(\d+)', '内容', answer)
    if len(answer) < len('answer：'):
        answer = ''
    answer = answer.replace('answer：', '').replace('answer:', '')
    answer = answer.split('source')[0]
    return answer

def our_answer_replacer(answer):
    """our的replacer"""
    # 各种替换
    answer = re.sub('文档(\d+)', '文档', answer)
    answer = re.sub('内容(\d+)', '内容', answer)
    if len(answer) < len('{\"answer\": \"'):
        answer = ''
    answer = answer.replace('{\"answer\": \"', '').replace('{\"answer\"： \"', '')
    answer = answer.split('\", \"source\"')[0]
    return answer

# def get_prompt_en(query: str, history: str, retrieval_result: str, lang: str=""):
#     if not retrieval_result: retrieval_result = 'Reference is empty'
#     if retrieval_result:
#         lang_str_prompt = f"Your answer must be in {lang} language" if lang != "" else "*"
#         input_ = "Reference materials for current issues include: \n### " + retrieval_result + '###\n\n' + f"""
#         This is a documentation quiz task, a test of finding the right document content literally and reply back, to see if you can answer user question based on the given information, not what you know
#             i need you to forget everything you know and all you have learned,all your know is limited to the aboved content。
#             now,answer the current user question based on the aboved chatting history and reference only,because not relying on those would lead to giving answer with no ground
#             note:if you cannot found the exact answer there,then you need to clarify and tell user why,because you can only provide the answer when  you are sure you can found the answer from aboved info,otherwise ,use words like 'might' is much safer.
#             Note that the query finding result may be a directory.
#
#             your output format should be like this ：
#             -answer： """ + lang_str_prompt + """
#             -quoted from：ducoument & number; content & number  (however, ignore this if no answer can be found)
#             current user question：""" \
#                  + query.strip()
#     else:
#         input_ = query
#     if not retrieval_result and history:
#         input_ = "current user question：" + query.strip() + '\n'
#     if history:
#         input_ = "The historical conversation between the user and the robot is as follows:【{}】\n".format(history) + input_
#     return input_

def get_prompt_en(query: str, history: str, history_doc_content_list: list = None, retrieval_result: str = "", lang: str = "英语"):
    if lang == "简体中文" or lang == "繁体中文" or lang == "粤语":
        if not retrieval_result:
            retrieval_result = '参考资料为空'
        if retrieval_result:
            lang_str_prompt = f"你必须使用中文回答"
            input_ = f"""当前问题的参考资料包括: \n### {retrieval_result} ###\n----------\n
这是一项文档测试任务，测试从参考资料找到正确的文档内容并回复，看看你是否可以根据给定的资料而不是你所知道的来回答用户的问题。
我需要你忘记你所知道的一切，忘记你所学到的一切，你所了解的只是上面提到资料内容。
现在，你只能根据上述聊天历史和参考资料来回答当前用户的问题，因为不依赖这些会导致毫无根据地给出答案。 
注意：如果你在资料中找不到确切的答案，那么你需要澄清并告诉用户为什么，只有当你确信你能从上面的信息中找到答案时，你才能提供答案，否则，使用“可能”这样的词会更安全。 
注意：参考资料里的内容可能是一个目录。 

输出格式: {{"answer": "{lang_str_prompt}", "quote": "文档 id, 内容 id（如果没有就输出null）"}}

当前用户问题：{query.strip()}
"""
        else:
            input_ = query
        if not retrieval_result and history:
            input_ = f"当前用户问题：{query.strip()}\n"
        if history:
            if history_doc_content_list:
                history_doc_content_list_str = '\n'.join(history_doc_content_list)
                input_ = f"用户和机器人之间的历史对话如下:\n{history}\n上一轮对话参考资料:\n{history_doc_content_list_str}\n----------\n\n{input_}"
            else:
                input_ = f"用户和机器人之间的历史对话如下:\n{history}\n----------\n\n{input_}"
        return input_
    else:
        if not retrieval_result:
            retrieval_result = 'Reference is empty'
        if retrieval_result:
            lang_str_prompt = f"Your answer must be in English"
            input_ = f"""Reference materials for current issues include: \n### {retrieval_result} ###\n----------\n
This is a documentation quiz task, a test of finding the right document content literally and reply back, to see if you can answer user question based on the given information, not what you know
i need you to forget everything you know and all you have learned,all your know is limited to the aboved content。
now,answer the current user question based on the aboved chatting history and reference only,because not relying on those would lead to giving answer with no ground
note:if you cannot found the exact answer there,then you need to clarify and tell user why,because you can only provide the answer when  you are sure you can found the answer from aboved info,otherwise ,use words like 'might' is much safer.
Note that the query finding result may be a directory.

outputformat: {{"answer": "{lang_str_prompt}", "quote": "document id, content id (If not, output null)"}}

current user question：{query.strip()}
"""
        else:
            input_ = query
        if not retrieval_result and history:
            input_ = "current user question：" + query.strip() + '\n'
        if history:
            if history_doc_content_list:
                history_doc_content_list_str = '\n'.join(history_doc_content_list)
                input_ = f"The historical conversation between the user and the robot is as follows:\n{history}\n References for the previous round of conversation:\n{history_doc_content_list_str}\n----------\n\n{input_}"
            else:
                input_ = f"The historical conversation between the user and the robot is as follows:\n{history}\n----------\n\n{input_}"
        return input_

# def get_prompt_en_our(query: str, history: str, retrieval_result: str, lang: str = "英语"):
#     if lang == "简体中文" or lang == "繁体中文" or lang == "粤语":
#         if not retrieval_result:
#             retrieval_result = '参考资料为空'
#         if retrieval_result:
#             lang_str_prompt = f"你必须使用中文回答"
#             input_ = f"""### {retrieval_result} ###\n----------\n
# 你是一个文档问答机器人，努力从参考资料找到正确的文档内容并回复问题，如果用户只是打个招呼你也可以打个招呼。
# 我需要你忘记你所知道的一切，忘记你所学到的一切，你所了解的只是上面提到资料内容。
# 现在，你只能根据上述聊天历史和参考资料来回答当前用户的问题，因为不依赖这些会导致毫无根据地给出答案。 尽可能提供有用信息和做推理，帮助解答用户的疑问；
# 注意：如果你在资料中找不到确切的答案，那么你需要澄清并告诉用户为什么，只有当你确信你能从上面的信息中找到答案时，你才能提供答案，否则，使用“可能”这样的词会更安全。
# 注意：参考资料里的内容可能是一个目录。
#
# 输出格式: {{"answer": "{lang_str_prompt}，先推理后回答，不少于100字", "quote": "文档 id, 内容 id"}}
# 问题：{query.strip()}
# """
#         else:
#             input_ = query
#         if not retrieval_result and history:
#             input_ = f"当前用户问题：{query.strip()}\n"
#         if history:
#             input_ = f"用户和机器人之间的历史对话如下:\n{history}\n----------\n\n{input_}"
#         return input_
#     else:
#         if not retrieval_result:
#             retrieval_result = 'Reference is empty'
#         if retrieval_result:
#             lang_str_prompt = f"Your answer must be in English"
#             input_ = f"""### {retrieval_result} ###\n----------\n
# 你是一个文档问答机器人，努力从参考资料找到正确的文档内容并回复问题，如果用户只是打个招呼你也可以打个招呼。
# 我需要你忘记你所知道的一切，忘记你所学到的一切，你所了解的只是上面提到资料内容。
# 现在，你只能根据上述聊天历史和参考资料来回答当前用户的问题，因为不依赖这些会导致毫无根据地给出答案。 尽可能提供有用信息和做推理，帮助解答用户的疑问；
# 注意：如果你在资料中找不到确切的答案，那么你需要澄清并告诉用户为什么，只有当你确信你能从上面的信息中找到答案时，你才能提供答案，否则，使用“可能”这样的词会更安全。
# 注意：参考资料里的内容可能是一个目录。
#
#             输出格式: {{"answer": "{lang_str_prompt}，先推理后回答，不少于100字", "quote": "文档 id, 内容 id"}}
#             问题：{query.strip()}
#             """
#
#             input_ = f"""Reference materials for current issues include: \n### {retrieval_result} ###\n----------\n
# This is a documentation quiz task, a test of finding the right document content literally and reply back, to see if you can answer user question based on the given information, not what you know
# i need you to forget everything you know and all you have learned,all your know is limited to the aboved content。
# now,answer the current user question based on the aboved chatting history and reference only,because not relying on those would lead to giving answer with no ground
# note:if you cannot found the exact answer there,then you need to clarify and tell user why,because you can only provide the answer when  you are sure you can found the answer from aboved info,otherwise ,use words like 'might' is much safer.
# Note that the query finding result may be a directory.
#
# outputformat: {{"answer": "{lang_str_prompt}", "quote": "document id, content id"}}
#
# current user question：{query.strip()}
# """
#         else:
#             input_ = query
#         if not retrieval_result and history:
#             input_ = "current user question：" + query.strip() + '\n'
#         if history:
#             input_ = f"The historical conversation between the user and the robot is as follows:\n{history}\n----------\n\n{input_}"
#         return input_


def get_prompt_en_our(query: str, history: str, history_doc_content_list: list = None, retrieval_result: str = "", doc_name_str: str = "", lang: str = "英语"):
    if lang == "简体中文" or lang == "繁体中文" or lang == "粤语":
        if not retrieval_result:
            retrieval_result = '参考资料为空'
        if retrieval_result:
            lang_str_prompt = f"你必须使用中文回答"
            input_ = f"""当前问参考资料:\n### {retrieval_result} ###\n----------\n
这是一项文档测试任务，测试从参考资料找到正确的文档内容并回复，看看你是否可以根据给定的资料而不是你所知道的来回答用户的问题。
我需要你忘记你所知道的一切，忘记你所学到的一切，你所了解的只是上面提到资料内容。
现在，你只能根据上述聊天历史和参考资料来回答当前用户的问题，因为不依赖这些会导致毫无根据地给出答案。 尽可能提供有用信息和做推理，帮助解答用户的疑问；
注意：如果你在资料中找不到确切的答案，那么你需要澄清并告诉用户为什么，只有当你确信你能从上面的信息中找到答案时，你才能提供答案，否则，使用“可能”这样的词会更安全。 
注意：参考资料里的内容可能是一个目录。 
如果用户只是跟你闲聊，你可以根据人设直接回答，你的人设：【不需要吃饭，身高165cm，腰围不清楚，喜欢打篮球】，你能回答关于【{doc_name_str}】相关的问题

当前用户问题：{query.strip()}
注意：{lang_str_prompt}
输出格式:  {{"answer": "", "quote": "文档 id, 内容 id"}} 如果没有答案"quote"的值就是"null"
"""
        else:
            input_ = query
        if not retrieval_result and history:
            input_ = f"当前用户问题：{query.strip()}\n"
        if history:
            if history_doc_content_list:
                history_doc_content_list_str = '\n'.join(history_doc_content_list)
                input_ = f"用户和机器人之间的历史对话如下:\n{history}\n上一轮对话参考资料:\n{history_doc_content_list_str}\n----------\n\n{input_}"
            else:
                input_ = f"用户和机器人之间的历史对话如下:\n{history}\n----------\n\n{input_}"
        return input_
    else:
        if not retrieval_result:
            retrieval_result = 'Reference is empty'
        if retrieval_result:
            lang_str_prompt = f"Your answer must be in English"
            input_ = f"""Current reference materials:\n### {retrieval_result} ###\n----------\n
This is a document testing task that tests finding the correct document content from reference materials and replying to it, to see if you can answer user questions based on the given information rather than what you know.
I need you to forget everything you know, forget everything you have learned, all you know is the information mentioned above.
Now, you can only answer the current user's questions based on the chat history and reference materials mentioned above, as not relying on these will result in providing answers without any basis. Provide useful information and reasoning as much as possible to help answer users' questions;
Note: If you cannot find an exact answer in the information, you need to clarify and tell the user why. Only when you are confident that you can find the answer from the information above can you provide the answer. Otherwise, using the word 'may' would be safer.
Note: The content in the reference materials may be a directory.
If the user is just chatting with you, you can directly answer according to the profile. Your profile is: 【No need to eat, 165cm tall, unclear waist circumference, likes to play basketball】, and you can answer questions related to 【{doc_name_str}】

current user question：{query.strip()}
note: {lang_str_prompt}
outputformat: {{"answer": "", "quote": "document id, content id"}}  If there is no answer, the value of "quote" is "null"
"""
        else:
            input_ = query
        if not retrieval_result and history:
            input_ = "current user question：" + query.strip() + '\n'
        if history:
            if history_doc_content_list:
                history_doc_content_list_str = '\n'.join(history_doc_content_list)
                input_ = f"The historical conversation between the user and the robot is as follows:\n{history}\n References for the previous round of conversation:\n{history_doc_content_list_str}\n----------\n\n{input_}"
            else:
                input_ = f"The historical conversation between the user and the robot is as follows:\n{history}\n----------\n\n{input_}"
        return input_


def en_answer_replacer(answer, lang="英语"):
    """our的replacer"""
    logger.debug(f"answer_replacer_input,lang:{lang},answer:{answer}")
    # 各种替换
    answer = re.sub('document(\d+)', 'document', answer)
    answer = re.sub('content(\d+)', 'content', answer)

    if "answer" in answer:
        answer = answer[answer.find("answer")+len("answer"):]
    elif "Answer" in answer:
        answer = answer[answer.find("Answer")+len("Answer"):]

    answer = answer.split('quote')[0]
    answer = answer.split('Quote')[0]
    if lang == "繁体中文" or lang == "粤语":
        answer = tra_sim_model.tra_or_sim(answer, target="tra")
    elif lang == "简体中文":
        answer = tra_sim_model.tra_or_sim(answer, target="sim")

    # 有时候会生成\\n
    answer = answer.replace("\\n", "\n")

    # 有时候在answer里还有document1，文档1这类内容。只有出现在尾部的时候才需要清掉。
    # 1.(document 1, content 1)
    need_remove_pattern = re.findall(r"(\([,，documentcontent文档内容\d\s]*\))", answer)
    for need_remove in need_remove_pattern:
        answer = answer.replace(need_remove, "")
    answer =remove_front_end_symbol_2(answer)
    logger.debug(f"answer_replacer_output,lang:{lang},answer:{answer}")
    return answer

# def en_answer_replacer(answer):
#     """our的replacer"""
#     # 各种替换
#     answer = re.sub('ducoument(\d+)', 'ducoument', answer)
#     answer = re.sub('cotent(\d+)', 'cotent', answer)
#     if len(answer) < len('-answer: '):
#         answer = ''
#
#     answer = answer.replace("- Answer:", "").replace('-Answer:', '').replace('- answer:', '').replace('-answer:', '').replace("-答案：", "").replace("-答案:", "").replace("- 答案：", "").replace("- 答案:", "").replace("-回答：", "").replace("-回答:", "").replace("- 回答：", "").replace("- 回答:", "").strip()
#     answer = answer.split('-Quoted from: ')[0]
#     answer = answer.split('-quoted from: ')[0]
#     answer = answer.split('- 引用自：')[0]
#     return answer

def get_translate_prompt(text, source_lang, target_lang):
    # prompt = f"你是一个语言翻译专家，我有一段{source_lang}的文字，需要你把文字翻译成{target_lang}的。\n" \
    #          f"文字内容:【{text}】\n" \
    #          f"请帮我把上面的文字翻译成{target_lang}的，保证句子意思一致。\n" \
    #          f"不要输出任何和翻译结果无关的内容。\n" \
    #          f"返回json结构,字段为translate,保存翻译后的句子\n"
    prompt = f"你是一个语言翻译专家，我有一段文字内容，需要翻译成{target_lang}的。\n" \
             f"文字内容:{text}\n" \
             f"请帮我把上面的文字翻译成{target_lang}。\n" \
             f"不要输出任何和翻译结果无关的内容。\n" \
             f"返回json结构,字段为translate,保存翻译后的句子\n"
    return prompt

def get_language_judge_prompt(text, lang):
    # prompt = f"你是一个语言翻译专家，我有一段文字，请你判断这段文字是否属于{lang}的。\n" \
    #          f"文字内容:【{text}】\n" \
    #          f"请判断前面的文字内容是否为{lang}。\n" \
    #          f"返回Y或者N，不要出现其他任何字符。\n"

    prompt = f"文字内容:【{text}】\n" \
             f"请判断文字内容的语种，其对应的语种是否为{lang}。\n" \
             f"返回Y或者N，不要出现其他任何字符。\n"

    return prompt

# if setting.FILECHAT_MODEL in ['gpt3.5','gpt4']:
#     get_prompt = get_prompt_openai
#     answer_replacer = gpt4_answer_replacer
# elif setting.FILECHAT_MODEL in ['tx','our']:
#     get_prompt = get_prompt_our
#     answer_replacer = our_answer_replacer
# else:
#     get_prompt = get_prompt_our
#     answer_replacer = our_answer_replacer

if setting.FILECHAT_LANG=='en':
    if setting.FILECHAT_MODEL == "our":
        get_prompt = get_prompt_en_our
        answer_replacer = en_answer_replacer
    else:
        get_prompt = get_prompt_en
        answer_replacer = en_answer_replacer



if __name__ == '__main__':
    input_1 = get_prompt_en("你好", history="用户:术语定义\n机器人:性能测试（performance testing）：性能测试的目的是评估系统在特定负载条件下的表现，包括响应时间、吞吐量、资源利用率等。通过性能测试，可以发现系统在负载情况下的瓶颈和潜在问题，为性能调优提", retrieval_result="根据我所知的信息，早上9:15前打卡不算迟到。然而，如果您在早上8点打卡，那么可能会被视为迟到，因为这是在9:15之前。建议您按照公司的规定，在9:15之前完成打卡，以避免被视为迟到。", lang="简体中文")
    input_2 = get_prompt_en("你好", history="用户:术语定义\n机器人:性能测试（performance testing）：性能测试的目的是评估系统在特定负载条件下的表现，包括响应时间、吞吐量、资源利用率等。通过性能测试，可以发现系统在负载情况下的瓶颈和潜在问题，为性能调优提", retrieval_result="根据我所知的信息，早上9:15前打卡不算迟到。然而，如果您在早上8点打卡，那么可能会被视为迟到，因为这是在9:15之前。建议您按照公司的规定，在9:15之前完成打卡，以避免被视为迟到。", lang="英语")
    input_3 = get_prompt_en_our("我今年7月份来的，那我的年终双薪是我固定月薪的多少", history="", retrieval_result="文档1:青牛智胜-员工手册-第二版.pdf\n内容1:\n薪资结构：综合薪资 = 基本薪资（固定薪资） + 绩效薪资 + 浮动\n薪资 + 福利津贴 + 五险一金\n1. 基本薪资：员工任职岗位的基本薪资，基本薪资=基本薪资基数*\n（实际月度到岗天数/月度应到岗天数）；\n2. 绩效薪资：员工上个季度工作的考核结果及体现，绩效薪资=绩效\n薪资基数*绩效系数*（实际月度到岗天数/月度应到岗天数），绩效\n系数为员工的绩效考核成绩；\n3. 浮动薪资=其他贡献奖+年终双薪+年终奖金\n3.1 其他贡献奖=项目贡献奖+个人贡献奖\n3.2 年终双薪=固定月薪*（当年工作月份/12）\n3.3 年终奖金=固定月薪*年终绩效考核系数*(当年转正后工作月份\n/12)+其他\n注：\n1．公司会根据经营情况及员工年度工作表现，给部分同事发放浮动薪资；\n2．其他是指根据公司会根据经营情况拿出部分利润作为奖金包，由总裁办\n根据员工贡献进行分配；\n3．年终双薪及年���奖金不适用于未转正员工。\n内容2:\n個部門共同管理，其中單筆金額大於 1000 元，作為我司的固定\n内容3:\n青牛智胜（深圳）科技有限公司-ygsc002\n1.2 固定资产由行政组专人负责采购，财务管理部保管、登记，由两\n个部门共同管理，其中单笔金额大于 1000 元，作为我司的固定\n内容4:\n便捷、系統運營更加穩定，用 3~5 年的時間實現我們的願景：讓企業\n内容5:\n1. 应聘面试者通过我司的面试评估后，由人力行政部发起入职申请，\n文档2:test2.txt\n内容1:\n薪资结构：综合薪资 = 基本薪资（固定薪资）  +  岗位薪资 + 绩效薪资+浮动薪酬（年度） + 福利津贴（福利补贴+五险一金）等。\n基本薪资：员工任职岗位的基本薪资，基本薪资=基本薪资基数*（实际月度到岗天数/月度应到岗天数）；\n岗位薪资：员工任职岗位的补贴，岗位薪资=岗位薪资基数*（实际月度到岗天数/月度��到岗天数）；\n绩效薪资：员工上个季度工作的考核结果及体现，绩效薪资=绩效薪资基数*绩效系数*（实际月度到岗天数/月度应到岗天数），绩效系数为员工的考核考核成绩，具体参考《年度绩效考核及激励执行办法》；\n浮动薪酬=年终双薪+年终奖金\n年终双薪=固定月薪*（当年工作月份/12）\n年终奖金=固定月薪*年终绩效考核系数*(当年转正后工作月份/12)+其他\n注：\n1．公司会根据经营情况及员工年度工作表现，给部分同事发放浮动薪酬；\n2．其他是指根据公司会根据经营情况拿出部分利润作为奖金包，由总裁办根据员工贡献进行分配。\n内容2:\n1.2固定资产由行政组专人负责采购，财务管理部保管、登记，由两个部门共同管理，其中单笔金额大于1000元，作为我司的固定资产管理。\n内容3:\n1.2固定資產由行政組專人負責採購，財務管理部保管、登記，由兩個部門共��管理，其中單筆金額大於1000元，作為我司的固定資產管理。\n内容4:\n通過所有智勝人的努力，讓我們的產品研發更加高效、交付更加便捷、系統運營更加穩定，用3~5年的時間實現我們的願景：讓企業在選擇ai合作伙伴時首先想到青牛智勝，同時公司的發展更上一個臺階，我們絕大多數小伙伴也能通過自己多年的成長及對公司的貢獻獲得豐厚的回報。\n", lang="简体中文")
    answer_replace = en_answer_replacer("{\"answer\": \"You can make active repayments through various methods such as online banking transfer, mobile banking transfer, bank self-service terminal repayment, phone banking transfer, and counter repayment. (document 1, content 1)\", \"quote\": \"document1,内容1\"}")
    answer = {"answer": "根据我所知的信息，早上9:15前打卡不算迟到。然而，如果您在早上8点打卡，那么可能会被视为迟到，因为这是在9:15之前。建议您按照公司的规定，在9:15之前完成打卡，以避免被视为迟到。", "source": ["文档1", "内容2"]}
    import json
    answer = json.dumps(answer,ensure_ascii=False)
    our_answer_replacer(our_answer_replacer)
