# -*- coding: utf-8 -*-

import json
import os
import pickle
import time
import warnings

import numpy as np
import tensorflow as tf
from sklearn.model_selection import train_test_split
from transformers import BertTokenizer, RobertaConfig

import setting
from database.REDIS import REDIS
from module.Rank.BertPointwiseRank.BertPointwiseScore import BertPointwiseScore
from module.Rank.BertPointwiseRank.DataGenerator import DataGenerator
from setting import logger
from utils.my_utils import RankReturnObject, MyEncoder, remove_symbol

warnings.filterwarnings('ignore')
os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

class BertPointwiseRank:
    def __init__(self):
        self.models = dict()

    def load_model(self, model_id, model_save_dir):
        try:
            tokenizer = BertTokenizer.from_pretrained(model_save_dir)
            config = RobertaConfig.from_pretrained(model_save_dir)
            model = BertPointwiseScore.from_pretrained(
                os.path.join(model_save_dir, 'tf_model.h5'),
                config=config,
                from_pt=False
            )
            with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'rb') as f:
                text2item_cm = pickle.load(f)
            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['config'] = config
            self.models[model_id]['model'] = model
            self.models[model_id]['text2item_cm'] = text2item_cm
            item = RankReturnObject()
            item.sim_query = "测试一下"
            self.predict(model_id=model_id, query="测试一下", texts=[item, item], topk=1)
        except Exception as e:
            logger.error(f"模型加载失败 [{model_id}], 错误信息: {e}")
            raise Exception(f"模型加载失败 [{model_id}], 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"模型下线成功 [{model_id}]")

    def train(self, model_id, data, model_save_dir):
        """ 80 batch, 3 分钟一个 epoch """
        start = time.time()

        # 模型保存地址
        os.makedirs(model_save_dir, exist_ok=True)

        # 加载数据
        id2texts_train = dict()
        id2texts_valid = dict()
        text2item_cm = dict() # 精确匹配字典

        for data_dict in data:
            norm_query = data_dict['title']
            norm_query_id = data_dict['labelId']

            norm_query_ = remove_symbol(norm_query)
            text2item_cm[norm_query_] =[norm_query, norm_query_id]

            texts = set()
            texts.add(norm_query)
            for text in data_dict['labelData'].split("||"):
                text = text.strip()
                if len(text):
                    texts.add(text)
                text = remove_symbol(text)
                if len(text):
                    text2item_cm[text] =[norm_query, norm_query_id]
            texts = list(texts)

            if len(texts) <= 1:
                continue
            else:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                id2texts_train[norm_query_id] = texts_train
                id2texts_valid[norm_query_id] = texts_valid

        # 加载预训练模型
        config = RobertaConfig.from_pretrained(setting.PRETRAIN_BERT_CONFIG, num_labels=2)
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_VOCAB)
        model = BertPointwiseScore.from_pretrained(
            setting.PRETRAIN_BERT_MODEL_TF,
            config=config,
            from_pt=False
        )

        # 数据集划分
        logger.debug(f"[{model_id}], 训练集意图量: {len(id2texts_train)}, 验证集意图量: {len(id2texts_valid)}")
        train_gen = DataGenerator(id2texts=id2texts_train, tokenizer=tokenizer, shuffle=True)
        valid_gen = DataGenerator(id2texts=id2texts_valid, tokenizer=tokenizer, shuffle=False)

        # 训练模型
        opt = tf.keras.optimizers.Adam(learning_rate=setting.BERT_POINTEWISE_RANK_LEARNING_RATE, epsilon=1e-08)
        loss = tf.keras.losses.BinaryCrossentropy(from_logits=False)
        metrics = [tf.keras.metrics.BinaryAccuracy("accuracy")]
        model.compile(optimizer=opt, loss=loss, metrics=metrics)
        model.save_pretrained(model_save_dir)
        config.save_pretrained(model_save_dir)
        tokenizer.save_pretrained(model_save_dir)
        model_file = os.path.join(model_save_dir, 'tf_model.h5')
        callbacks = [
            tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_accuracy', save_best_only=True),
            # tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=3),
            # tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=3, verbose=0), # 当 patience 次迭代损失未改善，Keras停止训练
        ]
        history = model.fit(
            train_gen,
            epochs=setting.BERT_POINTEWISE_RANK_EPOCHS,
            steps_per_epoch=train_gen.__len__(),
            validation_data=valid_gen,
            validation_steps=valid_gen.__len__(),
            callbacks=callbacks
        )
        model = BertPointwiseScore.from_pretrained(
            model_file,
            config=config,
            from_pt=False
        )

        # 保存模型
        with open(os.path.join(model_save_dir, "text2item_cm.pkl"), 'wb') as f:
            pickle.dump(text2item_cm, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(model_save_dir, 'train_history.json'), 'w', encoding='utf-8') as f:
            json.dump(history.history, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

        # self.models[model_id] = dict()
        # self.models[model_id]['tokenizer'] = tokenizer
        # self.models[model_id]['config'] = config
        # self.models[model_id]['model'] = model
        # self.models[model_id]['text2item_cm'] = text2item_cm

        logger.debug(f"训练完成 [{model_id}],意图量:{len(id2texts_train)},耗时:{time.time()-start}")

        if "val_accuracy" in history.history:
            score = np.max(history.history["val_accuracy"])
        else:
            score = 0
        return score

    def predict(self, model_id, query, texts, topk):
        """
        在 texts 中搜索和 query 最相关的 text
        :param model_id: 模型id
        :param query: str, 待查询问句
        :param texts: list, list 里为 RankReturnObject
        :param topk: int, 返回结果个数
        :return: list, list 里为 RankReturnObject
        """
        if model_id not in self.models:
            logger.error(f"搜索失败 [{model_id}], 错误信息: 模型未加载")
            raise Exception(f"搜索失败 [{model_id}], 错误信息: 模型未加载")

        if texts is None or len(texts) == 0:
            return []

        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        text2item_cm = self.models[model_id]['text2item_cm']

        query_ = remove_symbol(query)
        if query_ in text2item_cm:
            norm_query, norm_query_id = text2item_cm[query_]
            item = RankReturnObject(query=query, sim_query=norm_query, norm_query=norm_query,
                                    norm_query_id=norm_query_id, score=1, save_bigger=True)
            return [item]

        data = self.get_bert_data(tokenizer, query, texts)
        result = model.predict(data)
        index = np.argsort(result[:, 0])
        index = index[-topk:][::-1]

        return_text_list = []
        for idx in index:
            texts[idx].score = float(result[idx])
            return_text_list.append(texts[idx])

        return return_text_list

    @staticmethod
    def get_bert_data(tokenizer, query, texts):
        input_ids = np.zeros(shape=(len(texts), setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
        token_type_ids = np.zeros(shape=(len(texts), setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
        attention_mask = np.zeros(shape=(len(texts), setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)

        for i, text_item in enumerate(texts):
            if isinstance(text_item, RankReturnObject):
                inputs = BertPointwiseScore.get_data(text1=query, text2=text_item.sim_query,
                                                     tokenizer=tokenizer, max_len=setting.BERT_POINTEWISE_RANK_MAX_LEN)
            else:
                inputs = BertPointwiseScore.get_data(text1=query, text2=text_item,
                                                     tokenizer=tokenizer, max_len=setting.BERT_POINTEWISE_RANK_MAX_LEN)

            input_ids[i, :] = inputs['input_ids']
            token_type_ids[i, :] = inputs['token_type_ids']
            attention_mask[i, :] = inputs['attention_mask']

        return {
            'input_ids': input_ids,
            'token_type_ids': token_type_ids,
            'attention_mask': attention_mask
        }

    def my_test_pos_neg_acc_threshold(self, model_id, id2text_valid, threshold=0.5, times=1):
        import random
        from tqdm import tqdm
        random.seed(setting.SEED)
        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        acc_list = []

        for _ in range(times):
            # 测试准确率，正负样本 1:1
            acc, total = 0, 0
            id_list = list(id2text_valid.keys())
            pbar = tqdm(id2text_valid.items())
            for valid_id, text_list in pbar:
                pbar.set_description('测试进度')
                # 正样本
                input_ids = np.zeros(shape=(len(text_list), setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
                token_type_ids = np.zeros(shape=(len(text_list), setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
                attention_mask = np.zeros(shape=(len(text_list), setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
                for i, text1 in enumerate(text_list):
                    inputs = BertPointwiseScore.get_data(text1=text1, text2=random.choice(text_list), tokenizer=tokenizer, max_len=setting.BERT_POINTEWISE_RANK_MAX_LEN)
                    input_ids[i, :] = inputs['input_ids']
                    token_type_ids[i, :] = inputs['token_type_ids']
                    attention_mask[i, :] = inputs['attention_mask']
                inputs = {
                    'input_ids': input_ids,
                    'token_type_ids': token_type_ids,
                    'attention_mask': attention_mask
                }
                score = model(inputs)
                score = score.numpy()
                total += score.shape[0]
                acc += np.sum(score >= threshold)

                # 负样本
                input_ids = np.zeros(shape=(len(text_list), setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
                token_type_ids = np.zeros(shape=(len(text_list), setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
                attention_mask = np.zeros(shape=(len(text_list), setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
                for i, text1 in enumerate(text_list):
                    neg_id = random.choice(id_list)
                    while neg_id == valid_id:
                        neg_id = random.choice(id_list)
                    inputs = BertPointwiseScore.get_data(text1=text1, text2=random.choice(id2text_valid[neg_id]), tokenizer=tokenizer, max_len=setting.BERT_POINTEWISE_RANK_MAX_LEN)
                    input_ids[i, :] = inputs['input_ids']
                    token_type_ids[i, :] = inputs['token_type_ids']
                    attention_mask[i, :] = inputs['attention_mask']
                inputs = {
                    'input_ids': input_ids,
                    'token_type_ids': token_type_ids,
                    'attention_mask': attention_mask
                }
                score = model(inputs)
                score = score.numpy()
                total += score.shape[0]
                acc += np.sum(score < threshold)

                acc_dict = {f'acc': acc / total}
                pbar.set_postfix(acc_dict)

            acc_list.append(acc/total)
        print(f"{times}次 threshold {threshold} 测试平均准确率: {np.mean(acc_list)} {model_save_dir}")

    def my_test_pos_neg_acc_sort(self, model_id, id2text_valid, neg_samples=20, times=1):
        import random
        from tqdm import tqdm
        random.seed(setting.SEED)
        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        acc_list = []

        for _ in range(times):
            # 测试准确率，正负样本 1:1
            acc, total = 0, 0
            id_list = list(id2text_valid.keys())
            pbar = tqdm(id2text_valid.items())
            for valid_id, text_list in pbar:
                pbar.set_description('测试进度')
                for text1 in text_list:
                    input_ids = np.zeros(shape=(neg_samples+1, setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
                    token_type_ids = np.zeros(shape=(neg_samples+1, setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)
                    attention_mask = np.zeros(shape=(neg_samples+1, setting.BERT_POINTEWISE_RANK_MAX_LEN), dtype=np.int32)

                    # 正样本
                    text2 = random.choice(text_list)
                    inputs = BertPointwiseScore.get_data(text1, text2, tokenizer, max_len=setting.BERT_POINTEWISE_RANK_MAX_LEN)
                    input_ids[0, :] = inputs['input_ids']
                    token_type_ids[0, :] = inputs['token_type_ids']
                    attention_mask[0, :] = inputs['attention_mask']

                    # 负样本
                    for i in range(neg_samples):
                        neg_id = random.choice(id_list)
                        while neg_id == valid_id:
                            neg_id = random.choice(id_list)
                        text2 = random.choice(id2text_valid[neg_id])
                        inputs = BertPointwiseScore.get_data(text1, text2, tokenizer, max_len=setting.BERT_POINTEWISE_RANK_MAX_LEN)
                        input_ids[i+1, :] = inputs['input_ids']
                        token_type_ids[i+1, :] = inputs['token_type_ids']
                        attention_mask[i+1, :] = inputs['attention_mask']

                    inputs = {
                        'input_ids': input_ids,
                        'token_type_ids': token_type_ids,
                        'attention_mask': attention_mask
                    }
                    score = model(inputs)
                    score = score.numpy()
                    if score[0, 0] == np.max(score):
                        acc += 1
                    total += 1
                    acc_dict = {f'acc': acc / total}
                    pbar.set_postfix(acc_dict)

            acc_list.append(acc/total)
        print(f"{times}次 sort {neg_samples} 测试平均准确率: {np.mean(acc_list)} {model_save_dir}")

if __name__ == '__main__':
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    ranker = BertPointwiseRank()
    R = REDIS()

    # 训练模型
    model_id = "pufa_2_新数据"
    model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{BertPointwiseRank.__name__}/")
    data = R.get_data(f'pufa_test_data_2')
    # score = ranker.train(model_id=model_id, data=data, model_save_dir=model_save_dir)
    # print(f"模型得分: {score}")

    # 加载模型
    ranker.load_model(model_id=model_id, model_save_dir=model_save_dir)

    # search faq
    query_list = ['我想更换号码，但是手机号变了', '请问您再说一遍，没听清楚', '我没听清，你说什么我没听清了啊。']
    texts = ["没听清", "说一遍", "听不清", "嗯，不好意思，刚才没听清。", "喂", "喂喂喂", "我没听清，你说的什么我没听清了啊。"]
    # query_list = ['我想更换号码，但是手机号变了', '注册手机号码更改吗']
    # texts = ["你们支持修改手机号码吗", "我要更换号码", "我自己能修改手机号吗", "注册手机号码能更改吗", "我去哪可以改我的手机号", "能帮我改我的手机号吗"]
    text_items = []
    for text in texts:
        item = RankReturnObject()
        item.sim_query = text
        text_items.append(item)
    for query in query_list:
        texts = ranker.predict(model_id=model_id, query=query, texts=text_items, topk=2)
        print(f'\n查询句子: {query}')
        for text_item in texts:
            print(f'返回结果: {text_item.sim_query}, 置信度: {text_item.score}')

    # 测试2
    # ranker.my_test_pos_neg_acc_threshold(model_id=model_id, id2text_valid=id2texts_valid, threshold=0.5)