import numpy as np
import tensorflow as tf
from transformers import Bert<PERSON>oken<PERSON>, RobertaConfig
from transformers.models.roberta import TFRobertaPreTrainedModel, TFRobertaMainLayer
from transformers.modeling_tf_utils import get_initializer


class BertPointwiseScore(TFRobertaPreTrainedModel):
    def __init__(self, config, *inputs, **kwargs):
        super().__init__(config, *inputs, **kwargs)
        self.bert = TFRobertaMainLayer(config, name="bert")
        self.dropout = tf.keras.layers.Dropout(config.hidden_dropout_prob)
        self.score = tf.keras.layers.Dense(
            1, kernel_initializer=get_initializer(config.initializer_range), name="score", activation="sigmoid"
        )

    def call(
            self,
            inputs=None,
            attention_mask=None,
            token_type_ids=None,
            position_ids=None,
            head_mask=None,
            inputs_embeds=None,
            output_attentions=None,
            output_hidden_states=None,
            return_dict=None,
            labels=None,
            training=False,
    ):
        return_dict = return_dict if return_dict is not None else self.bert.return_dict

        if isinstance(inputs, (tuple, list)):
            labels = inputs[9] if len(inputs) > 9 else labels
            if len(inputs) > 9:
                inputs = inputs[:9]

        outputs = self.bert(
            inputs,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
            training=training,
        )

        pooled_output = outputs[1]
        pooled_output = self.dropout(pooled_output, training=training)
        score = self.score(pooled_output)

        return score

    @staticmethod
    def get_data(text1, text2, tokenizer: BertTokenizer, max_len, return_tensor=False):
        inputs = tokenizer.encode_plus(text1, text2, padding='max_length', truncation=True, max_length=max_len)
        if return_tensor:
            inputs["input_ids"] = tf.reshape(tf.convert_to_tensor(inputs["input_ids"], dtype=tf.int32), shape=(1, -1))
            inputs["attention_mask"] = tf.reshape(tf.convert_to_tensor(inputs["attention_mask"], dtype=tf.int32), shape=(1, -1))
            inputs["token_type_ids"] = tf.reshape(tf.convert_to_tensor(inputs["token_type_ids"], dtype=tf.int32), shape=(1, -1))
        return inputs

    @property
    def dummy_inputs(self):
        inputs = {
            "input_ids": np.array([[1, 2, 3, 4, 5]])
        }
        return inputs


if __name__ == "__main__":
    import setting

    tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_VOCAB)
    config = RobertaConfig.from_pretrained(setting.PRETRAIN_BERT_CONFIG)
    model = BertPointwiseScore.from_pretrained(setting.PRETRAIN_BERT_MODEL_TF, config=config, from_pt=False)

    text_1 = "有什么办法可以测试文本相似度？"
    text_2 = "文本相似度的测量方法"
    text_3 = "这是一段测试代码"

    # 数据处理
    inputs_rel = BertPointwiseScore.get_data(text1=text_1, text2=text_2, tokenizer=tokenizer, max_len=setting.BERT_POINTEWISE_RANK_MAX_LEN, return_tensor=True)
    inputs_irr = BertPointwiseScore.get_data(text1=text_1, text2=text_3, tokenizer=tokenizer, max_len=setting.BERT_POINTEWISE_RANK_MAX_LEN, return_tensor=True)

    # 获取分数
    score1 = model(inputs_rel)
    score2 = model(inputs_irr)
    print(f"rel score: {score1}")
    print(f"irr score: {score2}")

    # 获取分数
    inputs = {
        "input_ids": tf.concat([inputs_rel["input_ids"], inputs_irr["input_ids"]], axis=0),
        "token_type_ids": tf.concat([inputs_rel["token_type_ids"], inputs_irr["token_type_ids"]], axis=0),
        "attention_mask": tf.concat([inputs_rel["attention_mask"], inputs_irr["attention_mask"]], axis=0)
    }
    logits = model(inputs)
    print(logits)
    logits = model(inputs)
    print(logits)