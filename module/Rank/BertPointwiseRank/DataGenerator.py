# -*- coding: UTF-8 -*-

import math
import os
import random

import <PERSON><PERSON>htein
import numpy as np
import tensorflow as tf
from tensorflow import keras

import setting

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
gpus = tf.config.experimental.list_physical_devices('GPU')
if len(gpus) > 0:
    tf.config.experimental.set_memory_growth(gpus[0], True)


class DataGenerator(keras.utils.Sequence):
    def __init__(self, id2texts, tokenizer, shuffle=False):
        self.id2texts = id2texts
        self.id_list = list(self.id2texts.keys())
        self.text_list = []
        for cur_id, texts in id2texts.items():
            for text in texts:
                self.text_list.append([text, cur_id])

        self.tokenizer = tokenizer
        self.num_sample = setting.BERT_POINTEWISE_RANK_NUM_SAMPLE
        self.neg_sample = setting.BERT_POINTEWISE_RANK_NEG_SAMPLE
        self.hard_neg_sample = setting.BERT_POINTEWISE_RANK_HARD_NEG_SAMPLE
        self.max_len = setting.BERT_POINTEWISE_RANK_MAX_LEN
        self.num_batches = math.ceil(len(self.text_list) / self.num_sample)
        self.shuffle = shuffle
        random.seed(setting.SEED)

        self.on_epoch_end()

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return self.num_batches

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        start = index * self.num_sample
        end = min((index+1)*self.num_sample, len(self.text_list))
        X, Y = self.__data_generation(self.text_list[start:end])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        if self.shuffle:
            random.shuffle(self.text_list)

    def __data_generation(self, text_list):
        batch_size = len(text_list) * (1+self.neg_sample+self.hard_neg_sample)
        input_ids = np.zeros(shape=(batch_size, self.max_len), dtype=np.int32)
        token_type_ids = np.zeros(shape=(batch_size, self.max_len), dtype=np.int32)
        attention_mask = np.zeros(shape=(batch_size, self.max_len), dtype=np.int32)
        labels = np.zeros(shape=batch_size)
        index = 0

        for text, cur_id in text_list:
            # 正样本
            pos_text = random.choice(self.id2texts[cur_id])
            label = 1
            inputs = self.tokenizer.encode_plus(text, pos_text, padding='max_length',
                                                truncation=True, max_length=self.max_len)
            input_ids[index, :] = inputs['input_ids']
            token_type_ids[index, :] = inputs['token_type_ids']
            attention_mask[index, :] = inputs['attention_mask']
            labels[index] = label
            index += 1

            # 随机采样得到负样本
            label = 0
            for _ in range(self.neg_sample):
                neg_label_id = random.choice(self.id_list)
                while neg_label_id == cur_id and len(self.id_list) > 1:
                    neg_label_id = random.choice(self.id_list)
                neg_text = random.choice(self.id2texts[neg_label_id])
                inputs = self.tokenizer.encode_plus(text, neg_text, padding='max_length',
                                                    truncation=True, max_length=self.max_len)
                input_ids[index, :] = inputs['input_ids']
                token_type_ids[index, :] = inputs['token_type_ids']
                attention_mask[index, :] = inputs['attention_mask']
                labels[index] = label
                index += 1

            # 获取 Hard 负样本，即难分类的
            for _ in range(self.hard_neg_sample):
                neg_text = self._get_hard_samples(cur_id=cur_id, text=text)
                inputs = self.tokenizer.encode_plus(text, neg_text, padding='max_length',
                                                    truncation=True, max_length=self.max_len)
                input_ids[index, :] = inputs['input_ids']
                token_type_ids[index, :] = inputs['token_type_ids']
                attention_mask[index, :] = inputs['attention_mask']
                labels[index] = label
                index += 1

        return (
            {
                'input_ids': input_ids,  # tf.convert_to_tensor(inputs[0], dtype=tf.int32),
                'token_type_ids': token_type_ids,  # tf.convert_to_tensor(inputs[2], dtype=tf.int32),
                'attention_mask': attention_mask,  # tf.convert_to_tensor(inputs[1], dtype=tf.int32)
            },
            labels
        )

    def _get_hard_samples(self, cur_id, text):
        # 防止只有一个意图时报错
        if len(self.id_list) - 1 <= 0:
            return random.choice(self.id2texts[cur_id])

        sample_texts = [''] * (len(self.id_list)-1)
        scores = np.zeros(shape=(len(sample_texts)))
        index = 0

        for neg_id in self.id_list:
            if neg_id != cur_id:
                neg_text = random.choice(self.id2texts[neg_id])
                sample_texts[index] = neg_text
                scores[index] = Levenshtein.ratio(text, neg_text)
                index += 1

        index = np.argsort(scores)
        return sample_texts[index[-1]]

if __name__ == '__main__':
    from database.REDIS import REDIS

    model_name = 'enterprise1_kb1'
    R = REDIS()
    data = R.get_data(f'faq_{model_name}_all')

    id2texts = dict()
    for data_dict in data:
        norm_query = data_dict['norm_query']
        norm_query_id = data_dict['norm_query_id']
        id2texts[norm_query_id] = data_dict['sim_query']
        if norm_query not in id2texts[norm_query_id]:
            id2texts[norm_query_id].append(norm_query)

    print(id2texts["0"])
    train_gen = DataGenerator(id2texts=id2texts, tokenizer=None, shuffle=True)