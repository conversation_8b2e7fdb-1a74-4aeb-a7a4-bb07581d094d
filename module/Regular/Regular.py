# -*- coding: utf-8 -*-
import json
import os
import re
import time
from setting import logger, CLIENT, SAVE_MODEL_DIR
from utils.my_utils import My<PERSON>nco<PERSON>, remove_symbol
import numpy as np


class Regular:
    def __init__(self):
        self.models = dict()
        self.search_switch = False

    def build_model(self, text):
        pass

    def train(self, model_id, data, save_dir):
        code = 0
        msg = '正则训练成功'
        os.makedirs(save_dir, exist_ok=True)

        try:
            key_regexs = {}
            for item in data:
                if "keywords" not in item:
                    continue
                regexs = item.get("keywords", [])
                if len(regexs) == 0:
                    continue
                if isinstance(regexs, str):
                    regexs = regexs.strip().replace(' ', '').replace('（', '(').replace('）', ')').split('||')
                else:
                    regexs = [r for r in regexs if r is not None]
                regexs = [r.replace("||", "|") for r in regexs]
                keywords = []
                for regex in regexs:
                    right, clean_regex = self.check_regular(regex)
                    if not right:
                        keywords.append("")
                    elif right and clean_regex and not clean_regex.startswith("|"):
                        keywords.append(clean_regex)
                    else:
                        keywords.append("")

                if len(keywords):
                    key_regexs[item["labelId"]] = {
                        'labelId': item['labelId'],
                        'title': item.get("title", ""),
                        'keywords': keywords
                    }
            with open(os.path.join(save_dir, 'key_regexs.json'), 'w', encoding='utf-8') as f:
                json.dump(key_regexs, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)
            logger.debug(f'正则训练成功, model_id: {model_id}')
            if model_id in self.models:
                self.load_model(model_id, save_dir)
            # self.models[model_id] = key_regexs
        except Exception as e:
            logger.error('[{}] 正则训练报错:{}, data:{}'.format(model_id, e, data))
            code = 1
            msg = f'训练错误:{e}'
        return code, msg

    def load_model(self, model_id, save_dir):
        msg = ''
        total_try_times = 2
        load_times = total_try_times - 1 if CLIENT == "train" else 0
        success = False
        try:
            while load_times < total_try_times and not success:
                try:
                    with open(os.path.join(save_dir, "key_regexs.json"), 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    logger.debug('[{}] 模型开始上线'.format(model_id))
                    self.models[model_id] = data
                    self.predict(model_id=model_id, replaced_query_list=['测试模型自请求'], labelIds=[], detail=1)
                    logger.debug(f"模型自测完成 - model_id: {model_id}")
                    logger.debug('[{}] 模型上线'.format(model_id))
                    success = True
                except Exception as ee:
                    if load_times >= total_try_times - 1:
                        logger.error('[{}] 模型上线报错,{}'.format(model_id, ee))
                        msg = ee
                        return msg
                    else:
                        logger.debug('[{}] 模型上线报错,等待重试, {}'.format(model_id, ee))
                    time.sleep(3)
                    success = False
                load_times += 1
        except Exception as e:
            logger.error('[{}] 模型上线报错,{}'.format(model_id, e))
            msg = e

        return msg

    def offline_model(self, model_id):
        msg = ''
        try:
            logger.debug('[{}] 模型开始下线'.format(model_id))
            del self.models[model_id]
            logger.debug('[{}] 模型下线成功'.format(model_id))
        except Exception as e:
            logger.error('[{}] 模型下线报错,{}'.format(model_id, e))
            msg = e
        return msg

    # 新规则~，^,-
    def new_rule(self, keywords, content):
        keywords = keywords[3:-3]  # 去除正则首尾的.*加括号 .*((你好^3)好).*
        resList = []
        resList_index = []
        fei_list = []  # 存储(^2xx)有^的规则

        # 去除末尾的#，并获取#限制的长度
        step_len = 6
        jin_number = re.search("#[\d]+", keywords)
        keyword = keywords
        if jin_number:
            step_len = int(jin_number.group().replace("#", ""))
            # 去掉#加数字
            keyword = re.sub("(#[\d]+)", "", keywords)

        # 规则符号替换
        if "#" in keywords:
            # keyword = keyword.replace("#", ".{0," + str(step_len) + "}")
            count_len = ""
            keyword = keyword.replace(".*", "(.*?)")
            res = re.search(keyword, content, re.I | re.S)
            if res:
                content_list = re.findall(keyword, content)[0]
                # 查询括号索引
                brackets_list = re.findall("([(].*?[)])", keyword)
                brackets_index = [i for i, x in enumerate(brackets_list) if x == "(.*?)"]
                for index in brackets_index:
                    # re.findall有个坑，返回值只有一个的时候是字符串，两个以上就是元组
                    if isinstance(content_list, tuple):
                        count_len += content_list[index]
                    else:
                        count_len += content_list

                if len("".join(count_len)) <= step_len:
                    for sentence in re.finditer(keyword, content, re.I | re.S):
                        resList.append(sentence.group())
                        resList_index.append(sentence.span())
                    return resList, resList_index
            return [], [[]]
        # 构造负向规则，第一次全量匹配
        if "^" in keywords:
            # 将括号里包含^的替换成.*
            # keyword = re.sub("(\^\d+)", r"\1}", keyword).replace("^", ".{0,")
            for i in re.findall(r'\([^()]+\)', keyword):
                if "^" in i:
                    fei_list.append(i)
                    keyword = keyword.replace(i, ".*")  # 非贪婪模式获取不到之后的数据 怎么好意思.*?
        if "-" in keyword:
            keyword = keyword.replace("-", "|")
        if "~" in keyword:
            keyword = re.sub("(~\d+)", r"\1}", keyword).replace("-", "|").replace("~", ".{0,")

        logger.debug("*" * 100)
        logger.debug(f"初始规则：{keywords}")
        logger.debug(f"变化后的规则：{keyword}")
        logger.debug("*" * 100)
        try:
            for sentence in re.finditer(keyword, content, re.I | re.S):
                resList.append(sentence.group())
                resList_index.append(sentence.span())
        except Exception as e:
            logger.error(f"正向规则匹配出错,规则：{keyword}")
            logger.error(f"正向规则匹配：{e}")

        # 负向需要做剔除匹配
        if "^" in keywords and resList:
            # 去除末尾设定的 # 的长度
            keywords = re.sub("(#\d+)", "", keywords)
            reject_rule_list = []
            for index, j in enumerate(fei_list):
                reject_tmp = keywords
                for k in fei_list:
                    if j == k:
                        continue
                    reject_tmp = reject_tmp.replace(k, "")
                reject_rule_list.append(reject_tmp)

            # 第二次匹配剔除
            for reject_rule in reject_rule_list:
                reject_rule = re.sub("(\^\d+)", r"\1}", reject_rule).replace("-", "|").replace("^", ".{0,").replace(
                    "#", ".*?")
                for res in resList:
                    if re.search(reject_rule, res, re.I | re.S):
                        # 非规则命中了，就要返回空
                        return [], [[]]
            return [content], [[0, len(content)]]
        return resList, resList_index

    # 正则
    def old_rule(self, res, regex, replaced_query, match_sentence_index_sum):
        replaced_query_symbol = remove_symbol(replaced_query)
        if re.match(regex, replaced_query_symbol, re.I | re.S):

            match_keyword, match_keyword_num = self.get_match_keywords(regex, replaced_query)
            # 匹配到的规则
            res['match_re'].append(regex)
            # 规则的索引
            res['match_keyword'].append("||".join(match_keyword))
            res['match_keyword_num'].append(match_keyword_num)

            # 新加返回匹配的关键字
            match_sentence = []
            for sentence in re.finditer(regex.strip(".*"), replaced_query_symbol, re.I | re.S):
                restore_index_list = self.restore_symbol(replaced_query, sentence.group())
                for restore_index in restore_index_list:
                    if restore_index not in res['match_sentence_index']:
                        res['match_sentence_index'].append(restore_index)

                        # 根据坐标取出内容
                        if len(sentence.group()) <= 1:
                            match_sentence.append(sentence.group())
                        else:
                            match_sentence.extend([replaced_query[restore_index[0]:restore_index[1] + 1]])

            res['match_sentence'] += match_sentence

            # 获取关键词在句子中的索引
            match_keyword_index = {}
            chinese_pattern = "[\u4e00-\u9fa5]+"
            chinese_chars = re.findall(chinese_pattern, regex)
            for i, query in enumerate(res['match_sentence']):
                start_index = res['match_sentence_index'][i][0]
                for keyword in chinese_chars:
                    keyword_len = len(keyword)-1
                    index = 0
                    while True:
                        index = query.find(keyword, index)
                        if index == -1:
                            break
                        if keyword in match_keyword_index:
                            match_keyword_index[keyword].append([index+start_index, index + keyword_len+start_index])
                        else:
                            match_keyword_index[keyword] = [[index+start_index, index + keyword_len+start_index]]
                        index += 1
            res['match_keyword_index'].update(match_keyword_index)

        return res

    def predict(self, model_id, replaced_query_list, labelIds, detail=0, full_match=False):
        result = []
        result_list = []
        replaced_query_two_dimension = []
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": ""
        }
        if len(np.array(replaced_query_list).shape) == 2:
            replaced_query_two_dimension = replaced_query_list
        if isinstance(model_id, str):
            model_id_list = [model_id]
        else:
            model_id_list = model_id
        for index, model_id in enumerate(model_id_list):
            if isinstance(replaced_query_list, str):
                replaced_query_list = [replaced_query_list]

            if isinstance(replaced_query_two_dimension[-1:], list) and len(model_id_list) == 3:  # 判断二维数组
                replaced_query_list = replaced_query_two_dimension[index]

            if model_id not in self.models.keys():
                try:
                    is_exist = self.check_model_file_exist(model_id=model_id)
                    if is_exist:
                        error_dict["error_type"] = 1
                        error_dict["error_code"] = "NLU91024"
                        error_dict["error_msg"] = "预测错误: 模型未加载，请重新上线"
                    else:
                        error_dict["error_type"] = 1
                        error_dict["error_code"] = "NLU91025"
                        error_dict["error_msg"] = "预测错误: 模型未加载，模型文件缺失，请重新训练"
                except Exception as ee:
                    error_dict["error_type"] = 0
                    error_dict["error_code"] = "NLU91017"
                    error_dict["error_msg"] = f"预测错误: 模型未加载, {ee}"
                logger.error(f"预测失败 [{model_id}], 错误信息: {error_dict['error_msg']}")
                return result, error_dict

            try:
                key_regexs = self.models[model_id]
                if not labelIds or len(labelIds) == 0:
                    labelIds = list(key_regexs.keys())

                for label in labelIds:
                    if label not in key_regexs.keys():
                        logger.warning('[{}] labelIds:{},里有id不存在,label:{}'.format(model_id, labelIds, label))
                        continue
                    key_regex = key_regexs[label]
                    regexs = key_regex.get('keywords', [])
                    res = {
                        "match_labelId": label,
                        "query_index": -1,
                        'match_title': key_regexs[label]['title'],
                        "match_re": [],
                        "match_index": [],
                        "match_keyword": [],
                        "match_keyword_num": [],
                        "match_sentence": [],
                        "match_sentence_index": [],
                        "match_num": 0,
                        "all_num": len(regexs)
                    }
                    if detail:
                        for query_index, replaced_query in enumerate(replaced_query_list):  # 兼容用户输入多个词
                            replaced_query_symbol = remove_symbol(replaced_query)
                            # 每次遍历新句子，清空之前的数据
                            res = {
                                "match_labelId": label,
                                "query_index": -1,
                                'match_title': key_regexs[label]['title'],
                                "match_re": [],
                                "match_index": [],
                                "regular_index": [],
                                "match_keyword": [],
                                "match_keyword_num": [],
                                "match_sentence": [],
                                "match_sentence_index": [],
                                "match_num": 0,
                                "all_num": len(regexs),
                                "match_keyword_index": {}
                            }
                            match_sentence_index_sum = []
                            for index, regex in enumerate(regexs):
                                if regex == "":
                                    continue
                                if "~" in regex or "^" in regex or "-" in regex or "#" in regex:
                                    sentence, sentence_index = self.new_rule(regex, replaced_query_symbol)
                                    if sentence:
                                        res["match_index"].append(query_index)
                                        res["regular_index"].append(index)
                                        res['match_re'].append(regex)
                                        content_index = self.restore_symbol(replaced_query, sentence)
                                        # 根据坐标取出内容
                                        # res['match_sentence'].extend([replaced_query[i[0]:i[1] + 1] for i in content_index])
                                        res['match_sentence'].extend(
                                            [replaced_query[i[0]:] if len(i) == 1 else replaced_query[i[0]:i[1] + 1] for i in
                                             content_index])
                                        res['match_sentence_index'].extend(content_index)
                                else:
                                    res = self.old_rule(res, regex, replaced_query, match_sentence_index_sum)
                                    if res["match_re"]:
                                        res["regular_index"].append(index)
                                        res["match_index"].append(query_index)

                            res['query_index'] = query_index
                            res["match_num"] = len(res['match_re'])
                            if res["match_num"]:
                                result.append(res)
                    else:
                        regexs_not_empty = [r for r in regexs if len(r)]
                        for replaced_query in replaced_query_list:
                            if full_match:
                                if re.fullmatch('|'.join(regexs_not_empty), replaced_query):
                                    result.append(res)
                            else:
                                if re.match('|'.join(regexs_not_empty), replaced_query):
                                    result.append(res)
            except Exception as e:
                logger.error('[{}] 正则匹配失败,错误:{}'.format(model_id, e), exc_info=True)
                raise e
        #     result_list.append(result)
        # logger.info(result_list)
        return result, error_dict

    def test(self, query, ori_regexs, ners):
        result = {}
        error_dict = {
            "error_code": 0,
            "error_type": 0,
            "error_msg": ""
        }
        logger.info(f"正则测试开始, query:{query}, ori_regexs:{ori_regexs}, ners:{ners}")
        regexs = []
        error_ners = []

        for regex in ori_regexs:
            if "~" in regex or "^" in regex or "-" in regex or "#" in regex:
                regexs.append(".*(" + regex + ").*")
                continue
            right, clean_regex = self.check_regular(regex.strip())
            if right and clean_regex:
                regexs.append(clean_regex)
            else:
                error_dict["error_code"] = "NLU91026"
                error_dict["error_msg"] = f"正则测试接口错误，正则有错：{regex}"
                logger.error(f"正则测试接口报错, 错误信息: {regex}")
                # 质检需要出错的规则不影响正常的规则
                if self.search_switch:
                    error_ners.append(regex)
                    continue
                return result, error_dict

        match_regexs = []
        match_ners = []
        match_query = []
        for i in error_ners:
            ners.remove(i)
        query_symbol = remove_symbol(query)  # 对话内容去符号

        for i, regex in enumerate(regexs):

            if "~" in regex or "^" in regex or "-" in regex or "#" in regex:
                sentence, sentence_index = self.new_rule(regex, query_symbol)
                if sentence:
                    content_index = self.restore_symbol(query, sentence)
                    # 根据坐标取出内容
                    match_query.extend([query[i[0]:] if len(i) == 1 else query[i[0]:i[1] + 1] for i in content_index])
                    match_regexs.append(regexs[i])
                    match_ners.append(ners[i])

            elif re.match(regex, query_symbol, re.I):
                keyword_result = re.finditer(regex.strip(".*"), query_symbol, re.I)  # 质检关键词
                keyword_list = []
                for keyword in keyword_result:
                    keyword_list.append(keyword.group())

                content_index = self.restore_symbol(query, keyword_list)
                # 根据坐标取出内容
                if len(keyword_list) <= 1:
                    match_query.extend(keyword_list)
                else:
                    match_query.extend([query[i[0]:i[1] + 1] for i in content_index])  # 为了使有符号的句子也能高亮
                match_regexs.append(regexs[i])
                match_ners.append(ners[i])

        result = {
            'msg': "正则测试结束",
            'query': query,
            'match_regexs': match_regexs,
            'match_query': match_query,
            'match_ners': match_ners
        }
        return result, error_dict

    def check_model_file_exist(self, model_id):
        model_file_exist = True
        save_dir = os.path.join(SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")

        if not os.path.exists(os.path.join(save_dir, "key_regexs.json")):
            model_file_exist = False
        return model_file_exist

    def batch_test(self):
        """
        用于批量测试请求，
        """
        pass

    def get_data(self):
        """
        根据输入，整理模型需要的数据格式，简单的模型可以不需要
        :return:
        """
        pass

    def get_batch_data(self):
        """
        根据批量输入，整理模型需要的数据格式，简单的模型可以不需要
        返回generator
        """
        pass

    # @staticmethod
    def check_regular(self, text):
        if text == "":
            return False, text

        # 正则中的||直接替换成|,可能预测时有问题,要在最外层加上 ()
        if not text.startswith("@") and not text.endswith("@"):
            text = f"({text})"

        if text.startswith("@"):
            text = text[1:]
        elif text.startswith("(?!"):
            pass
        elif text.startswith(".*"):
            pass

        elif self.search_switch:
            text = ".*" + text

        if text.endswith("@"):
            text = text[:-1] + "$"
        elif text.endswith(".*"):
            pass
        elif self.search_switch:
            text = text + ".*"

        try:
            re.compile(text)
        except Exception as e:
            if "?<!" in text:
                return True, text
            logger.error('正则书写可能有误:{},错误:{}'.format(text, e))
            return False, text
        return True, text

    @staticmethod
    def get_match_keywords(match_re, query):
        symbol = ".*()[]{}?+|"
        all_keywords = [match_re]
        for s in symbol:
            new_all_keywords = []
            for keyword in all_keywords:
                for k in keyword.split(s):
                    k = k.strip()
                    if len(k):
                        new_all_keywords.append(k)
            all_keywords = new_all_keywords
        match_keyword = [keyword for keyword in all_keywords if keyword in query]
        match_keyword = list(set(match_keyword))

        match_keyword_num = 0
        for keyword in match_keyword:
            match_keyword_num += query.count(keyword)
        return match_keyword, match_keyword_num

    # 还原去掉的标点符号位置
    @staticmethod
    def restore_symbol(content, hit_contents):
        result = []
        if isinstance(hit_contents, str):
            hit_contents = [hit_contents]

        for hit_content in hit_contents:
            hit_index = 0
            record_index = []

            for index, word in enumerate(content):

                # 符号不做计算
                if word in ["，", "。", "？", "！", "：", "；", ",", ".", "?", "!", ":", ";", "、"]:
                    continue
                if word == hit_content[hit_index]:
                    # 指添加句首跟句尾的下标
                    if hit_index == 0 or hit_index == len(hit_content) - 1:
                        record_index.append(index)
                    hit_index += 1
                else:
                    hit_index = 0  # 字符不匹配则指针清零
                    record_index = []

                # 命中的字符串指针指到末尾，清零并记录
                if hit_index >= len(hit_content):
                    hit_index = 0
                    result.append(record_index)
                    record_index = []
        return result


if __name__ == "__main__":
    model = Regular()
    # data = [
    #     {
    #         "labelId": "节点id",
    #         "scoreDataPoint": "嗯，请您稍等一下，女士，您好，非常抱歉给您添麻烦了，温馨提示您我们这边的消费贷业务是不能用于房地产类消费的，但是可以用于购买汽车家电等消费。||嗯，请您稍等一下，女士，您好，非常抱歉给您添麻烦了||温馨提示您我们这边的消费贷业务是不能用于房地产类消费的||但是可以用于购买汽车家电等消费。",
    #         "keywords": ["(非常抱歉).*(添麻烦)||(很抱歉).*(不好的体验)", "(温馨提示|特别提醍).*(消费贷).*(不能|不可以).*(房地产|买房)", "(买|购买)(汽车)(家电)", "", "(温馨提示)"]
    #     }
    # ]
    data = [{'labelData': '', 'keywords': '你好', 'title': '流程1', 'labelId': '970'},
            {'labelData': '', 'keywords': '.*测试.*', 'title': '流程1', 'labelId': '971'},
            {'labelData': '', 'keywords': '你好', 'title': '流程2', 'labelId': '965'},
            {'labelData': '', 'keywords': '你好', 'title': '流程3', 'labelId': '966'},
            {'labelData': '', 'keywords': '你好', 'title': '流程4', 'labelId': '967'},
            {'labelData': '', 'keywords': '你好', 'title': '流程5', 'labelId': '969'},
            {'labelData': '', 'keywords': '你好', 'title': '流程6', 'labelId': '968'}]
    model_id = "test_120101"
    save_dir = f"/data/cbk/NLP_Model/{model_id}/Regular"

    # model.train(model_id=model_id, data=data, save_dir=save_dir)
    model.load_model(model_id=model_id, save_dir=save_dir)
    result, _ = model.predict(model_id=model_id, replaced_query_list="是11你们22电话3", labelIds=[], detail=1)
    print("wait")

