#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file: BERT
@time: 2023/4/21f
"""
import copy
import re
import time

import numpy as np
import os
import jieba
import pandas as pd
import pickle
import shutil
import requests
from collections import defaultdict
from functools import lru_cache
from utils.filechat_utils import get_list_of_string,ultra_extract,doc_extract
from utils.pdf_parse_for_rag import my_pdf_extract
import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf

gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

import tensorflow as tf
from transformers import TFBertModel, BertTokenizer, BertConfig
import setting
from setting import logger
from setting import MAIN_DIR
from utils.my_utils import my_cosine_similarity
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.metrics.pairwise import normalize
from tqdm import tqdm
from model.BM25.BM25 import HNSWModel
from model.BM25.ES_search_for_rag import ES
from utils.my_utils import MyEncoder
from database.MINIO import MINIO
minio = MINIO()
# from model.FileChatAction.ChatWithDF.MyDFChat import llm

from model.CallLLM.CallOpenAI_ultra import CallOpenAI
# base_url = 'https://api3.apifans.com/v1/'
# api_key = "sk-9hvZQzOyFbl3GRdoB2AbE39e7dE34691978046B0C267259b"
# llm = CallOpenAI('gpt-4o', base_url=base_url, api_key=api_key)

from model.CallLLM.CallOpenAI_ultra import CallOpenAI
llm = CallOpenAI('qwen2.5-14b-instruct', base_url='https://dashscope.aliyuncs.com/compatible-mode/v1',
                 api_key="sk-12650c62ac694a959175757f9ec6b7c4")


# model_path = '/data/dsb/BERT/rbt4/'
# model_path = r'D:\develop\aicc-nlp-nlu\pretrain_model\roberta_chinese_clue_tiny'
# model_path = r'/data/sammy/project/aicc-nlu/pretrain_model/roberta_chinese_clue_tiny_anto'
import json

class BEGRetrieval:
    def __init__(self):
        self.seg_len = 100
        self.threshold = 0.55
        self.predict_models = {}
        self.es = ES()

    def train(self, model_id, doc_sim_answer, seg_lens, model_save_dir='',**kwargs):
        code = 0
        msg = '训练成功'
        try:
            os.makedirs(model_save_dir, exist_ok=True)
            t_q_dict = {}
            t_f_dict = {}
            t_a_dict = {}
            t_u_dict = {}
            f_a_dict = {}
            image_map_url = {}
            filename2seg_infos = {}
            es_index = kwargs.get('es_index', '')
            knowledge_detail_id = kwargs.get('knowledge_detail_id', '')
            use_embedding = kwargs.get('use_embedding', True)
            seg_lens = [seg_lens] if isinstance(seg_lens, int) else seg_lens
            for sim_answer_dict in tqdm(doc_sim_answer,desc='正在训练文档数'):
                file_name = sim_answer_dict["file_name"]
                answer = sim_answer_dict["answer"]
                question = sim_answer_dict["question"]
                url = sim_answer_dict["url"]
                suffix = file_name.split('.')[-1].lower()
                filename2seg_info = {}
                for seg_len in seg_lens:
                    raw_seg_len = int(sim_answer_dict.get('cut_len', seg_len))
                    window_size = int(sim_answer_dict.get('window_size', seg_len//2))
                    for sim in sim_answer_dict["sim"]:
                        #对filename去除掉java他们给的13位数字和文件后缀
                        fit_file_name = file_name
                        fit_file_name = re.sub(r'_\d{13}', '', fit_file_name)
                        fit_file_name = re.sub(r'\.\w+$', '', fit_file_name)
                        a_seg,seg_info = self.cut_text(fit_file_name, sim, suffix, raw_seg_len,window_size)
                        #上传到es
                        if es_index:
                            #知识库
                            if suffix not in ['png','jpg','jpeg','bmp']:
                                knowledge_es_index = es_index + '_knowledge'
                                a_seg_for_es,seg_info = self.cut_text(fit_file_name, sim, suffix='', raw_seg_len=100,window_size=100,columns='')
                                self.es.train(knowledge_es_index,knowledge_detail_id,file_name,a_seg_for_es)
                                logger.info('es库上传成功,es_index为：%s'%knowledge_es_index)
                                #考培库
                                coach_es_index = es_index + '_smartcoach'
                                a_seg_for_es,seg_info = self.cut_text(fit_file_name, sim, suffix='', raw_seg_len=4000,window_size=3500,columns='')
                                self.es.train(coach_es_index,knowledge_detail_id,file_name,a_seg_for_es)
                                logger.info('es库上传成功,es_index为：%s'%coach_es_index)
                            else:
                                logger.debug(f'{file_name}不上传到es')


                        for a_s in a_seg:
                            t_a_dict[a_s] = file_name
                            t_q_dict[a_s] = question
                            t_f_dict[a_s] = file_name
                            t_u_dict[a_s] = url
                            f_a_dict[file_name] = answer
                        if setting.FILECHAT_CUT_MERGE:
                            if not filename2seg_info:
                                filename2seg_info.update(seg_info)
                            else:
                                seg_info['text2seg_id'] = [[i[0]+len(filename2seg_info['seg']),i[1]+len(filename2seg_info['seg'])] for i in seg_info['text2seg_id']]
                                filename2seg_info['text2seg_id'].update(seg_info['text2seg_id'])
                                filename2seg_info['seg'].extend(seg_info['seg'])
                if setting.FILECHAT_CUT_MERGE:
                    filename2seg_infos[file_name] = filename2seg_info

                #补充知识如kv
                a_seg = sim_answer_dict.get('add',[])
                for a_s in a_seg:
                    t_a_dict[a_s] = file_name
                    t_q_dict[a_s] = question
                    t_f_dict[a_s] = file_name
                    t_u_dict[a_s] = url
                    f_a_dict[file_name] = answer

                image_map_url.update(sim_answer_dict.get('image_map_url', {}))

            #保存doc_sim_answer
            with open(f"{model_save_dir}/doc_sim_answer.json", "w", encoding="utf-8") as f:
                json.dump(doc_sim_answer, f, ensure_ascii=False, indent=2)

            if use_embedding:
                if setting.IMAGE_EMBEDDING_URL:
                    logger.info(f'{model_id}，use_embedding:{use_embedding}')
                    #图义搜索训练
                    image_key_text = list(image_map_url.keys())
                    if image_key_text:
                        logger.debug(f'开始训练图义模型:{model_id}')
                        image_url = list(image_map_url.values())
                        image_embedding = self.get_image_embedding(image_url)
                        logger.debug(f"image_embedding:{image_embedding.shape}")
                        image_hnsw_model = HNSWModel(documents=image_key_text, embeddings=image_embedding)
                        logger.debug(f"image_urls的hnsw结束:{len(image_key_text)}")
                        np.save(f"{model_save_dir}/image_embedding.npy", image_embedding)
                        with open(f"{model_save_dir}/image_hnsw_model.pkl", 'wb') as f:
                            pickle.dump(image_hnsw_model, f, pickle.HIGHEST_PROTOCOL)
                        with open(f"{model_save_dir}/image_key_text.json", "w", encoding="utf-8") as f:
                            json.dump(image_key_text, f, ensure_ascii=False, indent=2)


                #文本训练
                all_text = pd.Series(list(t_a_dict.keys())).drop_duplicates().tolist()
                all_text = [i for i in all_text if i.strip()]
                print(f'len:{[len(i) for i in all_text]}')
                print(f"all_text:{len(all_text)}")
                faq_embedding,final_text = self.get_embedding(model_name=setting.FILECHAT_EMBEDDING_MODEL_NAME, corpus=all_text)
                print(f"faq_embedding:{faq_embedding.shape}")
                hnsw_model = HNSWModel(documents=final_text, embeddings=faq_embedding)
                print(f"hnsw结束:{len(all_text)}")


                # from model.BM25.BM25 import BM25model
                # bm25_model = BM25model(all_text)
                # with open(f"{model_save_dir}/bm25_model.pkl", 'wb') as f:
                #     pickle.dump(bm25_model, f, pickle.HIGHEST_PROTOCOL)

                with open(f"{model_save_dir}/hnsw_model.pkl", 'wb') as f:
                    pickle.dump(hnsw_model, f, pickle.HIGHEST_PROTOCOL)
                with open(f"{model_save_dir}/model_name.json", "w", encoding="utf-8") as f:
                    json.dump([setting.FILECHAT_EMBEDDING_MODEL_NAME], f, ensure_ascii=False, indent=2)
                with open(f"{model_save_dir}/all_text.json", "w", encoding="utf-8") as f:
                    json.dump(final_text, f, ensure_ascii=False, indent=2)
                with open(f"{model_save_dir}/t_a_dict.json", "w", encoding="utf-8") as f:
                    json.dump(t_a_dict, f, ensure_ascii=False, indent=2)
                with open(f"{model_save_dir}/t_q_dict.json", "w", encoding="utf-8") as f:
                    json.dump(t_q_dict, f, ensure_ascii=False, indent=2)
                with open(f"{model_save_dir}/t_u_dict.json", "w", encoding="utf-8") as f:
                    json.dump(t_u_dict, f, ensure_ascii=False, indent=2)
                with open(f"{model_save_dir}/t_f_dict.json", "w", encoding="utf-8") as f:
                    json.dump(t_f_dict, f, ensure_ascii=False, indent=2)
                with open(f"{model_save_dir}/f_a_dict.json", "w", encoding="utf-8") as f:
                    json.dump(f_a_dict, f, ensure_ascii=False, indent=2)
                if setting.FILECHAT_CUT_MERGE:
                    with open(f"{model_save_dir}/filename2seg_infos.json", "w", encoding="utf-8") as f:
                        json.dump(filename2seg_infos, f, ensure_ascii=False, indent=2)
                np.save(f"{model_save_dir}/faq_embedding.npy", faq_embedding)

            return code,msg
        except Exception as e:
            code = 1
            msg = '训练失败 ：{}'.format(str(e))
            logger.error(f'filechat:{model_id}检索训练失败', exc_info=True)
            return code,msg

    def cut_text(self, file_name, sim:str, suffix, raw_seg_len,window_size,columns=None):
        """
        file_name:文件名称
        sim:文本
        suffix:文件后缀
        raw_seg_len:分段长度
        windows_size:步长
        """
        if columns is not None:
            pass
        else:
            if suffix in ['xlsx', 'csv']:  # 每个sheet追加表头
                columns = sim[:sim.index('---')][:-1] + '\n' + [i for i in sim.split('\n') if '---' in i][0]  # -1去掉\n
                columns = f'###文件元信息:\nfilename:{file_name}\n{columns}'
            else:
                columns = f'###文件元信息:\nfilename:{file_name}'

        #特殊处理逻辑
        if '\n$$$\n' in sim and sim.count('\n$$$\n')>1:
            columns = f'###文件元信息:\nfilename:{file_name}'
            full_doc_a_seg = sim.split('\n$$$\n')
            full_doc_a_seg = [f'{columns}\n{i}' for i in full_doc_a_seg]
            return full_doc_a_seg,{}

        use_seg_len = raw_seg_len - len(columns)

        a_seg = []
        start_id = 0
        while start_id < len(sim):
            is_find = False
            for end_id in range(start_id, len(sim)):
                if sim[end_id] in ["。", "\n"]:
                    a_seg.append(sim[start_id:end_id + 1])
                    start_id = end_id + 1
                    is_find = True
                    break
            if not is_find:
                a_seg.append(sim[start_id:])
                break
        a_seg = [a_s.strip() for a_s in a_seg if len(a_s.strip())]

        full_doc_a_seg = []
        seg_info = {'seg':a_seg,'columns':columns,'text2seg_id':{}}
        if len("\n".join(a_seg)) <= use_seg_len:
            append_text = "\n".join([columns] + a_seg)
            full_doc_a_seg.append(append_text)
            seg_info['text2seg_id'][append_text] = [0, len(a_seg)]
        else:
            start_idx = []
            for i, a_s in enumerate(a_seg):
                if len(start_idx) == 0:
                    start_idx.append(i)
                else:
                    if len("\n".join(a_seg[start_idx[-1]:i + 1])) >= window_size:
                        start_idx.append(i)
            # 主要逻辑
            for s_ii, s_i in enumerate(start_idx[:-1]):  # [:-1]不使用最后一个位置，否则会留没意义的尾巴
                if len("\n".join(a_seg[s_i:])) <= use_seg_len:
                    append_text = "\n".join([columns] + a_seg[s_i:])
                    full_doc_a_seg.append(append_text)
                    seg_info['text2seg_id'][append_text] = [s_i, len(a_seg)]
                else:
                    for e_i in range(start_idx[s_ii + 1], len(a_seg) + 1):
                        if len("\n".join(a_seg[s_i:e_i + 1])) >= use_seg_len:
                            append_text = "\n".join([columns] + a_seg[s_i:e_i])
                            full_doc_a_seg.append(append_text)
                            seg_info['text2seg_id'][append_text] = [s_i, e_i]
                            break
        full_doc_a_seg = [i.strip() for i in full_doc_a_seg]
        return full_doc_a_seg,seg_info


    def load_model(self, model_id, model_save_dir,need_info = False):
        self.predict_models[model_id] = {}

        with open(f"{model_save_dir}/model_name.json", "r", encoding="utf-8") as f:
            model_name = json.load(f)[0]
            self.predict_models[model_id]["model_name"] = model_name

        with open(f"{model_save_dir}/t_f_dict.json", "r", encoding="utf-8") as f:
            t_f_dict = json.load(f)
            self.predict_models[model_id]["t_f_dict"] = t_f_dict

        try:
            with open(f"{model_save_dir}/t_u_dict.json", "r", encoding="utf-8") as f:
                t_u_dict = json.load(f)
                self.predict_models[model_id]["t_u_dict"] = t_u_dict
        except:
            t_u_dict = {}
            self.predict_models[model_id]["t_u_dict"] = t_u_dict

        with open(f"{model_save_dir}/hnsw_model.pkl", 'rb') as f:
            hnsw_model = pickle.load(f)
            self.predict_models[model_id]["hnsw_model"] = hnsw_model

        if os.path.exists(f"{model_save_dir}/image_hnsw_model.pkl"):
            with open(f"{model_save_dir}/image_hnsw_model.pkl", 'rb') as f:
                image_hnsw_model = pickle.load(f)
                self.predict_models[model_id]["image_hnsw_model"] = image_hnsw_model

        # if os.path.exists(f"{model_save_dir}/bm25_model.pkl"):
        #     with open(f"{model_save_dir}/bm25_model.pkl", 'rb') as f:
        #         bm25_model = pickle.load(f)
        #         self.predict_models[model_id]["bm25_model"] = bm25_model

        if os.path.exists(f"{model_save_dir}/info.json"):
            info = json.load(open(f"{model_save_dir}/info.json", "r", encoding="utf-8"))
        else:
            info = {}
        self.predict_models[model_id]["info"] = info

        if os.path.exists(f"{model_save_dir}/filename2seg_infos.json"):
            filename2seg_infos = json.load(open(f"{model_save_dir}/filename2seg_infos.json", "r", encoding="utf-8"))
        else:
            filename2seg_infos = {}
        self.predict_models[model_id]["filename2seg_infos"] = filename2seg_infos

        if os.path.exists(f"{model_save_dir}/doc_sim_answer.json"):
            doc_sim_answer = json.load(open(f"{model_save_dir}/doc_sim_answer.json", "r", encoding="utf-8"))
            image_map_url = {}
            for i in doc_sim_answer:
                image_map_url.update(i.get('image_map_url',{}))
        else:
            image_map_url = {}
        self.predict_models[model_id]["image_map_url"] = image_map_url

        #预测不使用的信息
        if need_info:
            faq_embedding = np.load(f"{model_save_dir}/faq_embedding.npy")
            self.predict_models[model_id]["faq_embedding"] = faq_embedding

            if os.path.exists(f"{model_save_dir}/image_embedding.npy"):
                logger.debug(f'model_id：{model_id}，加载image_embedding')
                image_embedding = np.load(f"{model_save_dir}/image_embedding.npy")
                self.predict_models[model_id]["image_embedding"] = image_embedding

            with open(f"{model_save_dir}/f_a_dict.json", "r", encoding="utf-8") as f:
                f_a_dict = json.load(f)
                self.predict_models[model_id]["f_a_dict"] = f_a_dict
            with open(f"{model_save_dir}/all_text.json", "r", encoding="utf-8") as f:
                all_text = json.load(f)
                self.predict_models[model_id]["all_text"] = all_text

            if os.path.exists(f"{model_save_dir}/image_key_text.json"):
                with open(f"{model_save_dir}/image_key_text.json", "r", encoding="utf-8") as f:
                    image_key_text = json.load(f)
                    self.predict_models[model_id]["image_key_text"] = image_key_text

            with open(f"{model_save_dir}/t_a_dict.json", "r", encoding="utf-8") as f:
                t_a_dict = json.load(f)
                self.predict_models[model_id]["t_a_dict"] = t_a_dict
            with open(f"{model_save_dir}/t_q_dict.json", "r", encoding="utf-8") as f:
                t_q_dict = json.load(f)
                self.predict_models[model_id]["t_q_dict"] = t_q_dict
            with open(f"{model_save_dir}/doc_sim_answer.json", "r", encoding="utf-8") as f:
                doc_sim_answer = json.load(f)
                self.predict_models[model_id]["doc_sim_answer"] = doc_sim_answer

            output_dict = copy.deepcopy(self.predict_models[model_id])
            return output_dict


    # @lru_cache(maxsize=256)
    def predict(self, model_id, query, top_k=5, rerank = setting.FILECHAT_RERANK ,is_text_len=True,filename_range_keywords = '[]',debug = False,text_len=None):
        start = time.time()
        if is_text_len and text_len is None:
            text_len = 1000 * top_k
        else:
            pass


        filename_range_keywords = eval(filename_range_keywords)
        result = {"filename": [], "question": [], "match_text": [], "answer": [], "score": [], "url": [],'consume':{},
                  'filename_range_keywords':filename_range_keywords}
        assert model_id in self.predict_models, f"{model_id}模型没上线"
        model_name = self.predict_models[model_id]["model_name"]
        result['model_name'] = model_name

        embedding_start = time.time()
        image_search = query.startswith(('http','base64:')) and 'image_hnsw_model' in self.predict_models[model_id]
        threhold = 0
        if image_search:#图像检索
            logger.debug(f'启动图义搜索:{query[0:30]}')
            hnsw_model = self.predict_models[model_id]["image_hnsw_model"]
            embedding = self.get_image_embedding(query)
            hnsw_top_k = top_k
            # threhold = 0.75
        else:#文本检索
            hnsw_model = self.predict_models[model_id]["hnsw_model"]
            embedding, _ = self.get_embedding(model_name=model_name, corpus=[query])
            hnsw_top_k = min(int(10 * 20), len(hnsw_model.documents))  # 默认200条,不然怕200条字数凑不够
            if rerank:  # topk是0.5的情况
                hnsw_top_k = max(hnsw_top_k, rerank)


        result['consume']['embedding'] = time.time() - embedding_start



        hnsw_start = time.time()
        try:
            search_result = hnsw_model.search(embedding, top_k=hnsw_top_k)
        except:
            logger.error('hnsw search error',exc_info=True)
            range_k = [hnsw_top_k // 2**i for i in range(1, hnsw_top_k.bit_length())]
            for i in range_k:
                try:
                    search_result = hnsw_model.search(embedding, top_k=i)
                    break
                except:
                    pass

        if len(search_result) == 2:
            search_docs, search_embeddings = search_result

        else:#兼容法律检索hnsw,返回的emb是标准化了的
            search_docs, search_embeddings, distances = search_result
            embedding = normalize(embedding)
        result['consume']['hnsw'] = time.time() - hnsw_start

        #scores可有精排或者cosine
        cos_scores = my_cosine_similarity(search_embeddings, embedding.astype(np.float16))[:, 0]
        scores = cos_scores


        if image_search:
            new_search_docs = []
            new_cos_scores = []
            print(search_docs,'search_docssearch_docssearch_docs')
            for j in tqdm(self.predict_models[model_id]["hnsw_model"].documents):
                one_image_text = []
                one_image_score = []  # 图片后面的文本长度
                real_len = []
                for i,ss in zip(search_docs,cos_scores):
                    if i in j:
                        one_image_text.append(j)
                        one_image_score.append(ss)
                        real_len.append(len(j.split(i)[-1]))
                    if one_image_text:
                        one_image_text = [x for _, x in sorted(zip(real_len, one_image_text), reverse=True)]
                        one_image_score = [x for _, x in sorted(zip(real_len, one_image_score), reverse=True)]
                        #取图片后面文本最长的那个切片

                        new_search_docs.append(one_image_text[0])
                        new_cos_scores.append(one_image_score[0])

            search_docs = new_search_docs
            cos_scores = list(range(len(search_docs)))[::-1]
            scores = new_cos_scores
            rerank = False

        if rerank:
            max_text_len = 2000
            cos_scores = my_cosine_similarity(search_embeddings, embedding.astype(np.float16))[:, 0]
            pred_idx = np.argsort(cos_scores)[::-1]
            #search_docs按相关度重排序
            search_docs = [search_docs[i] for i in pred_idx]
            # # rerank追加bm25
            # search_docs[rerank - 5:rerank] = self.predict_models[model_id]['bm25_model'].search(query, 5)
            docs_for_rerank_raw = search_docs[:rerank]
            docs_for_rerank = [i[:max_text_len] for i in docs_for_rerank_raw]
            if debug:
                result['docs_for_rerank'] = docs_for_rerank
            succes = False
            for _ in range(3):
                try:
                    scores_data = self.rerank(query,docs_for_rerank)

                    if 'scores' in scores_data:
                        scores = scores_data["scores"]
                    else:
                        logger.error(f'精排错误返回:{scores_data}')

                    #llm精排
                    # top10_index_in_score = np.argsort(scores)[::-1].tolist()[5:15]
                    # search_doc_for_llm = [docs_for_rerank[i] for i in top10_index_in_score] #llm精排只取精排前10
                    # scores_llm = self.llm_rerank(query, str(search_doc_for_llm))
                    # choose_index = [index for index,i in enumerate(scores_llm) if i>0]
                    # result['llm_choose_doc'] = [search_doc_for_llm[i] for i in choose_index]
                    # for index,i in enumerate(choose_index):
                    #     scores[index+5] = scores[np.argsort(scores)[::-1].tolist()[5]]
                        # scores[top10_index_in_score[i]] = scores_llm[i]


                    scores = np.array(scores)
                    succes = True
                    result['rerank'] = scores_data['rerank_model_name']
                    logger.debug(f"精排:{scores_data['rerank_model_name']}完成")
                    break
                except:
                    time.sleep(1)
                    logger.error('rerank error', exc_info=True)
                    pass
            if not succes:
                result['rerank'] = 'rerank fail'

        pred_idx = np.argsort(scores)[::-1].tolist() + list(range(len(scores),len(cos_scores)))

        if rerank:
            scores = np.concatenate([scores, cos_scores[len(scores):]]) #这个得分是给结果返回的，不能用来argsort


        if not filename_range_keywords:
            filename_range_keywords = [0]
        else:
            filename_range_keywords = filename_range_keywords*1000
            # filename_range_keywords = pd.Series([self.predict_models[model_id]["t_f_dict"][search_docs[p_i]] for p_i in pred_idx]).drop_duplicates().tolist()
            # result['filename_range_keywords'] = filename_range_keywords


        for filename_range_keyword in filename_range_keywords:
            for p_i in pred_idx:
                each_item_max_len = 1000000
                # if self.predict_models[model_id]["t_a_dict"][search_docs[p_i]] in result["answer"]:
                #     continue
                if len(search_docs[p_i].strip()) < 1:
                    continue
                if search_docs[p_i][:each_item_max_len] in result["match_text"]:
                    continue
                if text_len!=None and len('\n'.join(result["match_text"] + [search_docs[p_i][:each_item_max_len]]))>text_len:
                    each_item_max_len = text_len - len('\n'.join(result["match_text"][:each_item_max_len]))
                    if each_item_max_len<100:#如果最后一个文本不满足字数,太小了,就不要了
                        break

                filename = self.predict_models[model_id]["t_f_dict"][search_docs[p_i]]
                if filename_range_keyword==0:
                    pass
                else:
                    if filename_range_keyword not in filename:
                        continue

                if float(scores[p_i])<threhold:
                    continue

                result["filename"].append(filename)
                result["match_text"].append(search_docs[p_i][:each_item_max_len])
                # result["question"].append(self.predict_models[model_id]["t_q_dict"][search_docs[p_i]])
                # filename = self.predict_models[model_id]["t_a_dict"][search_docs[p_i]]
                # result["answer"].append(self.predict_models[model_id]["f_a_dict"][filename]) #原文不返回了，不然太大了
                result["url"].append(self.predict_models[model_id]["t_u_dict"].get(search_docs[p_i], ""))
                result["score"].append(float(scores[p_i]))

                if text_len == None and len(result["filename"]) >= top_k:#topk返回
                    break
                if each_item_max_len!=1000000:#超字数返回
                    break

                if filename_range_keyword!=0:#每个文件只取一个
                    break

        result['consume']['total'] = time.time() - start
        return result

    def offline_model(self, model_id,info=False):
        msg = ''
        try:
            if info:
                logger.debug('[{}] 模型开始下线'.format(model_id))
            del self.predict_models[model_id]
            if info:
                logger.debug('[{}] 模型下线成功'.format(model_id))
        except Exception as e:
            logger.error('[{}] 模型下线报错,{}'.format(model_id,e))
            msg = e
        return msg

    def rerank(self,query,rerank_search_docs):
        # rerank_data = {"model": "reranker-law-filechat-74w_ck2135", "query": query, "corpus":rerank_search_docs}
        rerank_data = {"model": "reranker-law-filechat-116w_ck2318", "query": query, "corpus": rerank_search_docs}
        # rerank_data = {"model": "reranker-m3u-135w_ck2257", "query": query, "corpus": rerank_search_docs}
        rerank_data_json = json.dumps(rerank_data, ensure_ascii=False).encode('utf-8')
        scores_data = requests.post(setting.FILECHAT_RERANK_URL, data=rerank_data_json).json()
        scores_data['rerank_model_name'] = rerank_data['model']
        return scores_data


    @lru_cache(maxsize=128)
    def llm_rerank(self,query,search_docs,prompt=None):
        search_docs = eval(str(search_docs))
        king_prompt = """现有文段如下：
@search_docs@
用户问句：@query@

请根据用户问句输出最相关最相关最相关的文段号，请注意关注用户问句里面可能涉及的产品，输出list,接下来你只会输出list,不需要做任何解释和#注释
"""
        prompt = prompt if prompt != None else king_prompt
        # query = '寿福佑人生终身重大疾病保险（尊享版）和国寿个人税收优惠型健康保险（万能型）A款（2016版）这两个保险中，选择一个没有累计给付次数的限制的，我选哪个？'
        # # query = '如果被保险人有感染艾滋病的风险，哪些保险明确表明了不予保障？'
        # query = '六十天前公司给我买了国寿小额团体特定疾病保险，同时我额外又买了国寿附加严重恶性肿瘤疾病保险 。但今天我被首次确诊了两个保险里所指的严重恶性肿瘤，我现在应该找哪个保险申请合同保险金额？'
        # query = '国寿个人税收优惠型健康保险和国寿小额团体特定疾病保险都需要续保，如果我担心可能会因为健康问题而影响续保，那我最好选哪种保险呢？'
        # query = '申请人向保险公司请求给付保险金的诉讼时效最长的保险产品是？'
        # query = '除了国寿附加严重恶性肿瘤疾病保险，还有哪些保险对未成年人保险金有限制？'
        # query = '我作为被保险人，我希望受益人只是我自己。国寿绿洲团体定期寿险和国寿小额团体特定疾病保险这两个保险里，哪个保险可以实现我的愿望？'
        search_docs = [i[:500] for i in search_docs]
        search_docs = [f'------------文段号{index},------------\n' + i for index, i in enumerate(search_docs)]

        llm_params = {'temperature': 0.000000000001,'seed':2024}
        inputs = prompt.replace('@search_docs@', '\n\n'.join(search_docs)).replace('@query@', query)
        llm_params['messages'] = [{'role': 'user', 'content': inputs}]
        for i in llm.stream_run(llm_parameter=llm_params):
            # print(i)
            pass
        i = i.replace('\n', '')
        logger.info(f'query:{query},result:{i}')
        result = get_list_of_string(i)
        result = [int(i) for i in result]
        scores_data = [100 - index if index in result else 0 for index,i in enumerate(search_docs)]
        return scores_data


    def get_embedding(self,model_name, corpus):
        headers = {'Content-Type': 'application/json'}
        embeddings = list()
        start = time.time()
        final_text = []
        batch_size = 10
        max_text_len = 5000
        for start_idx in tqdm(range(0, len(corpus), batch_size),desc='获取embedding'):
            success = False
            for _ in range(3):
                try:
                    end_idx = min(start_idx + batch_size, len(corpus))
                    texts = corpus[start_idx:end_idx]
                    texts = [i for i in texts if i.strip()]
                    paylod = {"input": [i[:max_text_len] for i in texts],
                              "model":model_name}
                    data = requests.post(setting.FEILCHAT_EMBEDDING_URL, headers=headers, json=paylod,timeout=60)
                    data = data.json()
                    if 'data' not in data:
                        logger.error('获取embedding失败：{}'.format(data))
                        time.sleep(3)
                        continue
                    emb = [i['embedding'] for i in data['data']]
                    embeddings.append(emb)
                    final_text.extend(texts)
                    success = True
                    break
                except:
                    logger.error(f'获取embedding失败',exc_info=True)
                    time.sleep(1)
                    if data.text:
                        logger.error('获取embedding失败：{}'.format(data.text))
                    continue
            if not success:
                logger.error('embedding超时3次：{}'.format([len(i) for i in texts]))

        embeddings = np.concatenate(embeddings, axis=0)
        logger.info('获取embedding耗时{}'.format(time.time()-start))
        return embeddings,final_text

    def train_from_fileids(self,model_id,fileids:list):
        code = 0
        try:
            t_q_dict = {}
            t_f_dict = {}
            t_a_dict = {}
            t_u_dict = {}
            f_a_dict = {}
            filename2seg_infos ={}
            all_text = []
            faq_embedding = []
            doc_sim_answer = []
            image_embedding = []
            image_key_text = []
            info = {}
            is_list = ['all_text', 'faq_embedding','doc_sim_answer','image_embedding','image_key_text']
            is_dict = ['t_q_dict', 't_a_dict', 'f_a_dict', 't_f_dict', 't_u_dict']

            for fileid in fileids:
                save_dir = os.path.join(setting.FILECHAT_FILE_DIR, fileid, 'Retrieval')
                output_dict = self.load_model(fileid, save_dir, need_info=True)
                for key in is_dict:
                    cmd = f"""{key}.update(output_dict["{key}"])"""
                    exec(cmd)
                for key in is_list:
                    if key in output_dict:
                        cmd = f"""{key}.extend(output_dict["{key}"])"""
                        exec(cmd)
                info.update({list(output_dict["t_f_dict"].values())[0]:fileid})
                filename2seg_infos.update(output_dict['filename2seg_infos'])
                self.offline_model(fileid)


            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'Retrieval')
            os.makedirs(model_save_dir,exist_ok=True)


            faq_embedding = np.stack(faq_embedding)
            if image_embedding:
                image_embedding = np.stack(image_embedding)
                image_hnsw_model = HNSWModel(documents=image_key_text, embeddings=image_embedding)
                print(f"iamge_hnsw结束:{len(all_text)}")
                with open(f"{model_save_dir}/image_hnsw_model.pkl", 'wb') as f:
                    pickle.dump(image_hnsw_model, f, pickle.HIGHEST_PROTOCOL)

            hnsw_model = HNSWModel(documents=all_text, embeddings=faq_embedding)
            print(f"hnsw结束:{len(all_text)}")


            with open(f"{model_save_dir}/hnsw_model.pkl", 'wb') as f:
                pickle.dump(hnsw_model, f, pickle.HIGHEST_PROTOCOL)
            with open(f"{model_save_dir}/model_name.json", "w", encoding="utf-8") as f:
                json.dump([setting.FILECHAT_EMBEDDING_MODEL_NAME], f, ensure_ascii=False, indent=2)
            with open(f"{model_save_dir}/all_text.json", "w", encoding="utf-8") as f:
                json.dump(all_text, f, ensure_ascii=False, indent=2)
            with open(f"{model_save_dir}/t_a_dict.json", "w", encoding="utf-8") as f:
                json.dump(t_a_dict, f, ensure_ascii=False, indent=2)
            with open(f"{model_save_dir}/t_q_dict.json", "w", encoding="utf-8") as f:
                json.dump(t_q_dict, f, ensure_ascii=False, indent=2)
            with open(f"{model_save_dir}/t_u_dict.json", "w", encoding="utf-8") as f:
                json.dump(t_u_dict, f, ensure_ascii=False, indent=2)
            with open(f"{model_save_dir}/t_f_dict.json", "w", encoding="utf-8") as f:
                json.dump(t_f_dict, f, ensure_ascii=False, indent=2)
            with open(f"{model_save_dir}/f_a_dict.json", "w", encoding="utf-8") as f:
                json.dump(f_a_dict, f, ensure_ascii=False, indent=2)
            with open(f"{model_save_dir}/filename2seg_infos.json", "w", encoding="utf-8") as f:
                json.dump(filename2seg_infos, f, ensure_ascii=False, indent=2)
            with open(f"{model_save_dir}/doc_sim_answer.json", "w", encoding="utf-8") as f:
                json.dump(doc_sim_answer, f, ensure_ascii=False, indent=2)
            np.save(f"{model_save_dir}/faq_embedding.npy", faq_embedding)

            with open(f"{model_save_dir}/info.json", "w", encoding="utf-8") as f:
                json.dump(info, f, ensure_ascii=False, indent=2)
        except:
            code = 1
            logger.error(f'绑定失败:{model_id}', exc_info=True)
        train_record = []
        train_record.append({'model_id': model_id, 'model_class': 'RetrievalMain_bge', 'code': code})
        train_record_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'train_record')
        os.makedirs(train_record_dir, exist_ok=True)

        #追加train_log
        if os.path.exists(os.path.join(train_record_dir, "train_log.json")):
            with open(os.path.join(train_record_dir, "train_log.json"), "r", encoding='utf-8') as f:
                add_train_record = json.load(f)
            train_record.extend([i for i in add_train_record if i['model_class'] != 'RetrievalMain_bge'])

        with open(os.path.join(train_record_dir,"train_log.json"), "w", encoding='utf-8') as f:
            json.dump(train_record, fp=f, cls=MyEncoder, ensure_ascii=False, indent=2)
        return code

    def file_delete(self,fileids:list):
        for fileid in fileids:
            model_save_dir = os.path.join(setting.FILECHAT_FILE_DIR, fileid)
            if os.path.exists(f"{model_save_dir}/doc_sim_answer.json"):
                doc_sim_answer = json.load(open(f"{model_save_dir}/doc_sim_answer.json", "r", encoding="utf-8"))
                image_map_url = {}
                for i in doc_sim_answer:
                    image_map_url.update(i['image_map_url'])
                for url in image_map_url.values():
                    try:
                        minio.delete_file(url.split('/')[-1])
                    except:
                        logger.error(f"minio删除失败:{url}", exc_info=True)

            logger.info('开始删除文件{}'.format(model_save_dir))
            shutil.rmtree(model_save_dir)
            logger.info('删除完成{}'.format(model_save_dir))

    def train_from_t_f_dict(self,model_id,t_f_dict,model_name,model_save_dir=None):

        all_text = list(t_f_dict.keys())

        faq_embedding,all_text = self.get_embedding(model_name,all_text)

        hnsw_model = HNSWModel(documents=all_text, embeddings=faq_embedding)
        print(f"hnsw结束:{len(all_text)}")

        if model_save_dir==None:
            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'Retrieval')
        os.makedirs(model_save_dir,exist_ok=True)

        with open(f"{model_save_dir}/hnsw_model.pkl", 'wb') as f:
            pickle.dump(hnsw_model, f, pickle.HIGHEST_PROTOCOL)
        with open(f"{model_save_dir}/model_name.json", "w", encoding="utf-8") as f:
            json.dump([model_name], f, ensure_ascii=False, indent=2)
        with open(f"{model_save_dir}/all_text.json", "w", encoding="utf-8") as f:
            json.dump(all_text, f, ensure_ascii=False, indent=2)
        with open(f"{model_save_dir}/t_f_dict.json", "w", encoding="utf-8") as f:
            json.dump(t_f_dict, f, ensure_ascii=False, indent=2)
        # np.save(f"{model_save_dir}/faq_embedding.npy", faq_embedding)


    @lru_cache(maxsize=128)
    def query_to_filename_ranges(self,model_id,query,prompt=None):
        filenames_range_candidates = [i.split('.')[0].strip() for i in
                                      set(self.predict_models[model_id]['t_f_dict'].values())]
#         king_prompt = """现在有文件清单如下：
# @filenames_range_candidates@
# 用户问句：@query@
# 请根据用户问句输出需要查询的文件名称,仅当用户提到确定的文件名称才输出相关文件名称，文件名称从文件清单里选择，如果每个文件都要用到请返回[]，否则返回相应文件名称list，接下来你只会输出代码,不需要做任何解释和#注释
# """
        king_prompt = """现在有实体清单如下：
@filenames_range_candidates@
用户问句：@query@
请根据用户问句输出命中的实体,仅当用户提到确定的实体才输出相关实体，实体名称从实体清单里选择，没有命中任何实体请返回[]，否则返回相应实体名称list，接下来你只会输出代码,不需要做任何解释和#注释
"""
        prompt = prompt if prompt!=None else king_prompt
        # query = '寿福佑人生终身重大疾病保险（尊享版）和国寿个人税收优惠型健康保险（万能型）A款（2016版）这两个保险中，选择一个没有累计给付次数的限制的，我选哪个？'
        # # query = '如果被保险人有感染艾滋病的风险，哪些保险明确表明了不予保障？'
        # query = '六十天前公司给我买了国寿小额团体特定疾病保险，同时我额外又买了国寿附加严重恶性肿瘤疾病保险 。但今天我被首次确诊了两个保险里所指的严重恶性肿瘤，我现在应该找哪个保险申请合同保险金额？'
        # query = '国寿个人税收优惠型健康保险和国寿小额团体特定疾病保险都需要续保，如果我担心可能会因为健康问题而影响续保，那我最好选哪种保险呢？'
        # query = '申请人向保险公司请求给付保险金的诉讼时效最长的保险产品是？'
        # query = '除了国寿附加严重恶性肿瘤疾病保险，还有哪些保险对未成年人保险金有限制？'
        # query = '我作为被保险人，我希望受益人只是我自己。国寿绿洲团体定期寿险和国寿小额团体特定疾病保险这两个保险里，哪个保险可以实现我的愿望？'
        llm_params = {'temperature': 0.000000000001,'seed':2024}
        inputs = prompt.replace('@filenames_range_candidates@', str(filenames_range_candidates)).replace('@query@',
                                                                                                         query)
        llm_params['messages'] = [{'role': 'user', 'content': inputs}]
        # for i in llm.stream_run(llm_parameter=llm_params):
        #     # print(i)
        #     pass
        dd = {'这些保险产品分别属于哪些类型？': '[]',
 '我有国寿附加严重恶性肿瘤疾病保险和国寿福佑人生终身重大疾病保险，但我近期确诊脑垂体瘤，两个保险都能赔付合同所示的保险金吗？': "[ '国寿福佑人生终身重大疾病保险（尊享版）','国寿附加严重恶性肿瘤疾病保险']",
 '我做了根管治疗，可以去找我买的国寿附加扶贫保意外住院定额给付团体医疗保险和国寿小额团体特定疾病保险报销吗？': "['国寿附加扶贫保意外住院定额给付团体医疗保险', '国寿小额团体特定疾病保险']",
 '很多年前给我奶奶买了国寿福佑人生终身重大疾病保险和国寿盛世传家终身寿险，现在她得了老年痴呆症，我可不可以上这些保险申请报销？': "['国寿福佑人生终身重大疾病保险（尊享版）', '国寿盛世传家终身寿险']",
 '去年我买了国寿福佑人生终身重大疾病保险和国寿附加严重恶性肿瘤疾病保险 ，现在我得了轻分级的神经内分泌肿瘤，在这两个保险里我都可以申请到保险金吗？': "['国寿福佑人生终身重大疾病保险（尊享版）', '国寿附加严重恶性肿瘤疾病保险']",
 '如果买了国寿福佑人生终身重大疾病保险（尊享版），我不想覆盖的疾病种类存在一定的重叠，有必要再买国寿小额团体特定疾病保险吗？': "['国寿福佑人生终身重大疾病保险（尊享版）', '国寿小额团体特定疾病保险']",
 '我想在国寿福佑人生终身重大疾病保险（尊享版）和国寿个人税收优惠型健康保险（万能型）A款（2016版）这两个保险中，选择一个没有累计给付次数的限制的，我选哪个？': "['国寿福佑人生终身重大疾病保险（尊享版）', '国寿个人税收优惠型健康保险（万能型型）A款（2016版）']",
 '我已经确定要购买“国寿附加扶贫保意外住院定额给付团体医疗保险”作为附加合同，并且希望选择一个没有投保年龄限制的主合同，我该买哪种保险？': "['国寿附加扶贫保意外住院定额给付团体医疗保险']",
 '如果我想提前解除保险合同，各种保险产品会有哪些费用产生？': '[]',
 '我想知道万能型保险产品在投资回报率上有什么差异？': "['国寿个人税收优惠型健康保险','国寿销售精英团体养老年金保险']",
 '我不想要保险期最长为一年的保险，我要避开哪些保险？': '[]',
 '如果被保险人有感染艾滋病的风险，哪些保险明确表明了不予保障？': '[]',
 '如果某一个家庭里面年龄最小家庭成员的是2岁，年龄最大的家庭成员是68岁，他们想要买同一种终身保险产品，那国寿福佑人生终身重大疾病保险（尊享版）和国寿盛世传家终身寿险应该买哪一种？': "['国寿福佑人生终身重大疾病保险','国寿盛世传家终身寿险']",
 '国寿福佑人生终身重大疾病保险（尊享版） 和国寿小额团体特定疾病保险 ，有什么区别？': "['国寿福佑人生终身重大疾病保险（尊享版）', '国寿小额团体特定疾病保险']",
 '保险公司明确拒绝解除保险合同的情况是什么？': '[]',
 '有没有保险期为一年且不需要续保的保险产品': '[]',
 '国寿个人税收优惠型健康保险与国寿福佑人生终身重大疾病保险在心脏疾病上各有什么样的要求？': "['国寿个人税收优惠型健康保险（万能型）A款（2016版）', '国寿福佑人生终身重大疾病保险（尊享版）']",
 '申请人向保险公司请求给付保险金的诉讼时效最长的保险产品是？': '[]',
 '每种产品的保险费是多少？': '[]',
 '关于责任免除，有哪些保险明确标明了被保险人自杀而保险公司不承担给付保险金？': '[]',
 '在释义中，有哪些保险对遗传性疾病做出了解释？': '[]',
 '不同寿险保险产品的保险期限和保障范围是怎样的？': "['国寿盛世传家终身寿险', '国寿绿洲团体定期寿险']",
 '投保时，投保人年龄不小心申报错误，各保险产品的处理方式有什么不同？': '[]',
 '每种产品的保险费支付方式是怎么样的？': '[]',
 '哪些保险的保险金受益人只能是被保险人本人': '[]',
 '国寿个人税收优惠型健康保险（万能型）和国寿安翔飞行员失能收入损失保险，这两种险保险公司不予赔付的情况一致吗？': "['国寿安翔飞行员失能收入损失保险','国寿个人税收优惠型健康保险']",
 '我想购买能支持季交的保险，在国寿绿洲团体定期寿险和国寿盛世传家终身寿险里该如何选择': "['国寿绿洲团体定期寿险','国寿盛世传家终身寿险']",
 '六十天前公司给我买了国寿小额团体特定疾病保险，同时我额外又买了国寿附加严重恶性肿瘤疾病保险 。但今天我被首次确诊了两个保险里所指的严重恶性肿瘤，我现在应该找哪个保险申请合同保险金额？': "['国寿小额团体特定疾病保险', '国寿附加严重恶性肿瘤疾病保险']",
 '同样是团体保险，请总结一下国寿销售精英团体养老年金保险在缴纳保费的方式上与国寿小额团体特定疾病保险的不同点': "['国寿销售精英团体养老年金保险（万能型）', '国寿小额团体特定疾病保险']",
 '我想在中国人寿保险里找一个交费宽限期较长的保险，有什么推荐吗？': '[]',
 '我们有三个人打算购买团体险，在国寿绿洲团体定期寿险和国寿小额团体特定疾病保险里我们要选择哪一个？': "['国寿绿洲团体定期寿险', '国寿小额团体特定疾病保险']",
 '我作为被保险人，我希望受益人只是我自己。国寿绿洲团体定期寿险和国寿小额团体特定疾病保险这两个保险里，哪个保险可以实现我的愿望？': "['国寿绿洲团体定期寿险', '国寿小额团体特定疾病保险']",
 '国寿盛世传家终身寿险和国寿绿洲团体定期寿险的保险支付方式是什么？': "['国寿盛世传家终身寿险', '国寿绿洲团体定期寿险']",
 '我曾经在某家航空公司工作，我能同时投保国寿安翔飞行员失能收入损失保险和国寿个人税收优惠型健康保险（万能型）A款（2016版）吗？': "['国寿安翔飞行员失能收入损失保险', '国寿个人税收优惠型健康保险（万能型）A款（2016版）']",
 '所有保险里的争议处理方式都是一样的吗？': '[]',
 '如果得了重大疾病需要申请保险金，国寿福佑人生终身重大疾病保险、国寿附加严重恶性肿瘤疾病保险要求的材料有哪些？': "['国寿福佑人生终身重大疾病保险（尊享版）', '国寿附加严重恶性肿瘤疾病保险']",
 '有哪些保险明确允许65周岁老年人购买？': '[]',
 '保险里有什么关于既往症的要求？': '[]',
 '除了国寿附加严重恶性肿瘤疾病保险，还有哪些保险对未成年人保险金有限制？': '[]',
 '我是公司的hr，按要求给员工们购买了国寿销售精英团体养老年金保险和国寿附加扶贫保意外住院定额给付团体医疗保险，如果我有需要变更地址，应该如何操作？': "['国寿销售精英团体养老年金保险（万能型）', '国寿附加扶贫保意外住院定额给付团体医疗保险']",
 '请给我总结一下国寿附加扶贫保意外住院定额给付团体医疗保险和国寿小额团体特定疾病保险在责任免除这一块的注意事项': "['国寿附加扶贫保意外住院定额给付团体医疗保险', '国寿小额团体特定疾病保险']",
 '万能型的保险产品有哪些？他们的现金价值有什么区别？': "['国寿个人税收优惠型健康保险（万能型）A款（2016版）', '国寿销售精英团体养老年金保险（万能型）']",
 '国寿个人税收优惠型健康保险和国寿小额团体特定疾病保险都需要续保，如果我担心可能会因为健康问题而影响续保，那我最好选哪种保险呢？': "['国寿个人税收优惠型健康保险（万能型）A款（2016版）', '国寿小额团体特定疾病保险']",
 '某一位客户想为自己买一份终身保险，并且指定受益人为他的父母，我应该给他推荐哪一个保险？': "['国寿盛世传家终身寿险', '国寿福佑人生终身重大疾病保险（尊享版）']",
 '我的收入比较稳定，基本三个月会结算一次工资，我更适合买哪种？': '[]'}
        dd = {k.lower():v for k, v in dd.items()}
        i = dd[query]
        i = i.replace('\n','')
        logger.info(f'query:{query},result:{i}')
        filenames_ranges = re.findall(r'\[.*?\]', i)
        try:
            if filenames_ranges:
                filenames_ranges = eval(filenames_ranges[0])
                # if not filenames_ranges:
                #     filenames_ranges = filenames_range_candidates
        except:
            logger.error(f"eval error:{filenames_ranges}", exc_info=True)
            filenames_ranges = []
        filenames_ranges = str(filenames_ranges)
        return filenames_ranges

    # @lru_cache(maxsize=128)
    # def query_to_filename_ranges(self,model_id,query,prompt=None):
    #     filenames_range_candidates = [i.split('.')[0].strip() for i in
    #                                   set(self.predict_models[model_id]['t_f_dict'].values())]
    #     hit_range = []
    #     for i in filenames_range_candidates:
    #         #编辑距离
    #         score= len(set(i)&set(query))/len(set(i))
    #         if score>0.8:
    #             hit_range.append(i)
    #     return hit_range

    def filename_base_predict(self, model_id, query, top_k=5, rerank = setting.FILECHAT_RERANK ,is_text_len=True):
        filename_ranges = self.query_to_filename_ranges(model_id, query)
        result = self.predict(model_id, query, top_k=top_k, rerank = rerank ,filename_range_keywords = filename_ranges,is_text_len=is_text_len)
        return result

    @lru_cache(maxsize=128)
    def rewrite(self,query,content=''):
        prompt = """用户对保险产品提问，已知保险条款信息摘要
    {
"保险合同构成": "本合同由保险单、利益条款、基本条款、现金价值表、声明、批注、批单及相关书面协议构成。",
"投保范围": "凡出生28日以上、55周岁以下、身体健康者均可作为被保险人，由本人或对其具有保险利益的人作为投保人投保。",
"保险期间": "本合同的保险期间为自本合同生效之日起至本合同终止日止。",
"基本保险金额": "本合同的基本保险金额为本合同保险单上载明的金额。",
"重大疾病": "本合同涵盖100种重大疾病，包括恶性肿瘤、急性心肌梗死、严重脑中风后遗症等，详述了每种疾病的定义和不在保障范围内的情况。",
"第一类特定疾病": "第一类特定疾病包括30种，涵盖轻度恶性肿瘤、较轻急性心肌梗死、轻度脑中风后遗症等，详细列出每种疾病的定义和不在保障范围内的情况。",
"第二类特定疾病": "第二类特定疾病包括20种，涵盖心脏瓣膜介入手术、轻度脑损伤、肝叶切除等，详细列出每种疾病的定义和不在保障范围内的情况。",
"男性、女性、少儿特定重大疾病": "男性特定重大疾病包括原发于胃、气管、肝、前列腺、食道、胰腺的恶性肿瘤、冠状动脉搭桥术、慢性复发性胰腺炎；女性特定重大疾病包括原发于乳腺、子宫体、子宫颈、卵巢的恶性肿瘤、严重系统性红斑狼疮性肾病、严重类风湿性关节炎、侵蚀性葡萄胎；少儿特定重大疾病包括原发于脑、骨、造血系统的恶性肿瘤、严重非恶性颅内肿瘤、严重脑炎后遗症、重大器官移植术、严重脊髓灰质炎、严重心肌炎。",
"保险责任": "本公司承担重大疾病保险金、第一类特定疾病保险金、第二类特定疾病保险金、男性/女性/少儿特定重大疾病额外保险金、身故保险金、身体高度残疾保险金及第二类特定疾病豁免保险费责任。",
"责任免除": "因投保人故意杀害、被保险人故意犯罪、自伤、自杀、毒品、酒后驾驶、艾滋病、战争、核爆、遗传性疾病、先天性畸形等导致的重大疾病、特定疾病、身故或高度残疾不在保障范围内。",
"保险费": "保险费按年交付，交费期间分为五年、十年、十五年和二十年四种，由投保人在投保时选择。",
"身体高度残疾鉴定": "被保险人因意外或疾病造成身体高度残疾，应由二级以上医院或本公司认可的医疗机构或鉴定机构出具残疾证明，如有异议，以司法鉴定结果为准。",
"保险金申请所需证明和资料": "申请重大疾病保险金、特定疾病保险金、豁免保险费、身故保险金、身体高度残疾保险金时，需提供保险单、申请人身份证明、相关诊断证明、病历、住院及出院证明文件等。",
"投保人解除合同的处理": "投保人可在合同成立后解除合同，需填写解除合同申请书并提交保险合同和身份证明。签收保险单后十五日内解除合同，退还已收全部保险费；十五日后解除合同，退还现金价值。发生特定疾病后，投保人不得解除合同。",
"借款": "发生特定疾病后，投保人不得申请借款。",
"附则": "本合同基本条款“投保人解除合同的处理”事项不适用于本合同；基本条款与利益条款相抵触的，以利益条款为准。",
"释义": "定义了专科医生、组织病理学检查、icd-10与icd-o-3、tnm分期、甲状腺癌的tnm分期、肢体、肌力、语言能力完全丧失、严重咀嚼吞咽功能障碍、六项基本日常生活活动、永久不可逆、nyha心功能状态分级、意外伤害、身体高度残疾、毒品、酒后驾驶、无合法有效驾驶证驾驶、无合法有效行驶证、机动车、战争、军事冲突、暴乱、感染艾滋病病毒或患艾滋病、遗传性疾病、先天性畸形或变形等术语。"
}
保险条款信息摘要的key是条款，value是条款摘要
用户问题：@query@

请根据用户问题和保险条款信息摘要，对用户问题进行信息补充，补充后的结果我是用来搜索资料的，将可能需要的key和value关键词补充到问题里面，把补充后的问题以list返回，返回的内容我可以用python的eval函数解析：["问题"]
"""
#         prompt = """请直接回答用户问题，如果不知道相关信息，请进行编造"""
#
#         prompt = """
# 相关信息:
# @相关信息@
#
# 用户问题：@query@
# 请根据相关信息回答用户问题
#         """
#
#         prompt = """相关信息:
# @相关信息@
#
# 用户问题：@query@
# 请根据用户问题和相关信息，对用户问题进行信息补充，补充后的结果我是用来搜索资料的，将可能需要关键词补充到问题里面，把补充后的问题以list返回，返回的内容我可以用python的eval函数解析：["问题"]
# """
#请根据用户问题和相关信息，对用户问题进行信息补充，补充后的结果我是用来搜索资料的，把补充后的问题以list返回，返回的内容我可以用python的eval函数解析：["问题"]
        prompt = """Think step by step and rewrite better search queries for Wikipedia to answer the given question. Split the queries with ';' and end the query with '**'. \n\n Question: '''Rita Coolidge sang the title song for which Bond film?''' \nThought: I need to search which title song of a Bond film is sang by Rita Coolidge, then find the name of that Bond film. \nQuery: Bond song Rita Coolidge; Bond film** \n\n
Question: '''@query@''' \n
中文输出
Thought: """
        prompt = """Provide a better search query for web search engine to answer the given question, end the query with '**'. 
 Question: Ezzard Charles was a world champion in which sport? 
 Query: Ezzard Charles champion** 
 Question: What is the correct name of laughing gas? 
 Query: laughing gas name** 
 Question: @query@
中文输出
 Query: 
"""
        llm_params = {'temperature': 0}
        inputs = prompt.replace('@query@', query)
        inputs = inputs.replace('@相关信息@', content)
        llm_params['messages'] = [{'role': 'user', 'content': inputs}]
        for i in llm.stream_run(llm_parameter=llm_params):
            # print(i)
            pass
        i = i.replace('\n', '')
        # i = f'["{i}"]'
        # result = get_list_of_string(i)
        result = i.split('Query: ')[-1].replace('**','')
        result = re.split(';',result)
        logger.info(f"query:{query},llm_output:{i},result:{result}")
        return result,inputs


    def rewrite_predict(self,model_id, query,topk):
        rewrite_query,inputs =  self.rewrite(query)
        final_result = defaultdict(list)
        text_len = 1000*topk

        for i in (rewrite_query[:5])*100:
            print(i,'asdasdasdasdasd')
            result = self.predict(model_id,i,top_k=topk,rerank=20)
            gap_len = 1000000
            # if self.predict_models[model_id]["t_a_dict"][search_docs[p_i]] in result["answer"]:
            #     continue
            for index,search_doc in enumerate(result["match_text"]):
                if search_doc[:gap_len] in final_result["match_text"]:
                    continue
                if text_len != None and len('\n'.join(final_result["match_text"] + [search_doc])) > text_len:
                    gap_len = text_len - len('\n'.join(final_result["match_text"]))
                    if gap_len < 100:  # 如果最后一个文本不满足字数,太小了,就不要了
                        break

                final_result['match_text'].append(result['match_text'][index][:gap_len])
                append = True
                for j in ['filename','score','url']:
                    final_result[j].append(result[j][index])
                #每次只添加一个
                if append:
                    break
                if gap_len!=1000000:
                    break

        final_result['rewrite_query'] = rewrite_query
        final_result['rewrite_prompt_inputs'] = inputs
        return final_result

    def rewrite_depend_on_retrieval_result_predict(self,model_id, query, topk,content = ''):
        #根据检索结果对问题进行rewrite


        rewrite_query = self.rewrite(query,content)
        final_result = defaultdict(list)
        text_len = 1000*topk

        result = self.predict(model_id,rewrite_query[0],top_k=topk,rerank=None)
        gap_len = 1000000
        # if self.predict_models[model_id]["t_a_dict"][search_docs[p_i]] in result["answer"]:
        #     continue
        for index,search_doc in enumerate(result["match_text"]):
            if search_doc[:gap_len] in final_result["match_text"]:
                continue
            if text_len != None and len('\n'.join(final_result["match_text"] + [search_doc])) > text_len:
                gap_len = text_len - len('\n'.join(final_result["match_text"]))
                if gap_len < 100:  # 如果最后一个文本不满足字数,太小了,就不要了
                    break

            final_result['match_text'].append(result['match_text'][index][:gap_len])
            append = True
            for j in ['filename','score','url']:
                final_result[j].append(result[j][index])
            #每次只添加一个
            if append:
                break
            if gap_len!=1000000:
                break

        final_result['rewrite_query'] = rewrite_query
        return final_result


    def get_image_embedding(self, query):
        headers = {'Content-Type': 'application/json'}
        if type(query)!=list:
            query = [query]
        embeddings = list()
        start = time.time()
        batch_size = 1
        for start_idx in tqdm(range(0, len(query), batch_size),desc='获取图像embedding'):
            success = False
            for _ in range(3):
                try:
                    end_idx = min(start_idx + batch_size, len(query))
                    texts = query[start_idx:end_idx]
                    paylod = {"input": [i for i in texts]}
                    data = requests.post(setting.IMAGE_EMBEDDING_URL, headers=headers, json=paylod,timeout=60)
                    data = data.json()
                    if 'data' not in data:
                        logger.error('获取图像embedding失败：{}'.format(data))
                        time.sleep(3)
                        continue
                    emb = [i['embedding'] for i in data['data']]
                    embeddings.append(emb)
                    success = True
                    break
                except:
                    logger.error(f'获取图像embedding失败',exc_info=True)
                    time.sleep(1)
                    if data.text:
                        logger.error('获取图像embedding失败：{}'.format(data.text))
                    continue
            if not success:
                logger.error('获取图像embedding超时3次：{}'.format([len(i) for i in texts]))

        embeddings = np.concatenate(embeddings, axis=0)
        logger.info('获取图像embedding耗时{}'.format(time.time()-start))
        return embeddings

    def path_to_traindata(self,file_path,file_url,cut_len,upload_image=True):

        image_map_url = {}
        if file_path.endswith(('docx','pdf')):  # docx不启用ocr
            if file_path.endswith('docx'):
                texts, output_image_paths = doc_extract(file_path, ocr=False, need_image_paths=True)
            else:
                texts, output_image_paths = my_pdf_extract(file_path, ocr=True,need_image_paths=True)
            for image_path in output_image_paths:
                if upload_image:
                    minio.upload_file(os.path.split(image_path)[-1], image_path)
                    url = minio.get_download_url(os.path.split(image_path)[-1])
                    image_map_url[os.path.split(image_path)[-1].replace('.png', '')] = url
                else:
                    image_map_url[os.path.split(image_path)[-1].replace('.png', '')] = image_path
        else:
            texts = ultra_extract(file_path)
        assert len(''.join(texts).strip()) != 0, f'{file_url},解析为空，上传失败'

        # 翻译
        # text_num += len(texts)
        # char_num += sum([len(t_) for t_ in texts])
        # logger.debug(f"准备翻译,source_lang:{source_lang_list[index]},target_langs:{target_lang_list[index]}")
        # translate_texts = self.translate_model.translate_text(
        #     text_list=texts,
        #     source_lang=source_lang_list[index],
        #     target_langs=target_lang_list[index],
        #     parallel=True
        # )
        # texts = translate_texts
        # trans_text_num += len(texts)
        # trans_char_num += sum([len(t_) for t_ in texts])

        sim_answer_dict = {}
        sim_answer_dict["file_name"] = file_url.split('/')[-1]
        suffix = sim_answer_dict["file_name"].split('.')[-1].lower()

        sim_answer_dict["answer"] = texts
        sim_answer_dict["question"] = ''
        sim_answer_dict["url"] = file_url
        sim_answer_dict['cut_len'] = cut_len
        sim_answer_dict["sim"] = ['\n'.join(texts)] if suffix not in ['csv', 'xlsx'] else texts
        sim_answer_dict['image_map_url'] = image_map_url
        # sim_answer_dict['md5_code'] = md5_code
        # sim_answer_dict['md5_filename'] = f"{md5_code}_{filename.split('/')[-1]}"
        return sim_answer_dict

    def rewrite_history(self,history:list):
        history = history[-2:]
        prompt = """case1:['iphone的内存有多大','屏幕呢'],rewrite:iphone的屏幕有多大
        case2:['电脑的内存有多大','电脑的屏幕有多大'],rewrite:no
        case3:['飞机怎么起飞','汽车怎么开'],rewrite:no
        case4:@history,rewrite:
        请直接输出case4的rewrite结果,不需要解释:"""
        # prompt = f"@history上面是用户说的两个query,如果不拼接可能意图表达不明确,特别是第一个query是实体第二个query没有实体的情况基本都要拼接,是否要拼接请直接输出yes/no，不需要解释:"
        # prompt = f"@history\n请判断第二个query是否根据第一个query提出的，如果是则输出yes,否则输出no,直接输出yes/no，不需要解释:"
        # input_prompt = prompt.replace('@history','\n'.join(item['query']))
        input_prompt = prompt.replace('@history', str(history))
        llm_output = llm.run(input_prompt)

        if llm_output=='no':
            rewrite_query = history[-1]
        else:
            rewrite_query = llm_output
        info_dict = {'history': history, 'result': rewrite_query, 'llm_output': llm_output}
        return rewrite_query,info_dict

if '__main__' == __name__:
    # doc = open('../doc/新建文本文档.txt','r').readlines()
    model = BEGRetrieval()
    # model.train_from_fileids('haha',['1909951804732719104'])
    model_id = '1916763673439801345'
    # query = 'https://aicc-test.qnzsai.com/minio/aiccsrb/other/photo_1746860348427.jpg'
    # query = 'https://aicc-test.qnzsai.com/minio/aiccsrb/other/photo_1746860348427.jpg'
    model.load_model(model_id, model_save_dir=r'D:\develop\NLP_Model\1916763673439801345\Retrieval')

    import base64
    root_path = r'C:\Users\<USER>\Desktop\博物馆项目\图片命中\new'
    for file in os.listdir(root_path):
        if file.endswith('.png'):
            image_path = os.path.join(root_path, file)
            base64_data = base64.b64encode(open(image_path, 'rb').read()).decode()
            query = f'base64:{base64_data}'
            result = model.predict(model_id,query,top_k=1)
            print(result)





    # model.load_model(model_id='haha',model_save_dir=r'C:\Users\<USER>\Desktop\新建文件夹\Retrieval',need_info=True)
    # result = model.rewrite('去年我买了国寿福佑人生终身重大疾病保险和国寿附加严重恶性肿瘤疾病保险 ，现在我得了轻分级的神经内分泌肿瘤，在这两个保险里我都可以申请到保险金吗？')
    # print(result)
    # model.get_embedding(setting.FILECHAT_EMBEDDING_MODEL_NAME,['你好'])

    # model_id = 'test_image'
    #
    # # model_save_dir = rf'/data/servetest/NLP_Model/{model_id}/Retrieval'
    # model_save_dir = os.path.join(setting.SAVE_MODEL_DIR,model_id,'Retrieval')
    #
    # path = r"C:\Users\<USER>\Desktop\刘氏庄园——泥塑卷(文.图).docx"
    # # dd = model.path_to_traindata(path,path,cut_len=1000,upload_image=True)
    # # model.train(model_id,[dd],1000,model_save_dir)
    # model.load_model(model_id,model_save_dir)
    #
    # result = model.predict(model_id,'http://10.0.30.2:9000/nlpforjava/citation_image_cbb9f.png',1)
    #
    #
    # model.train_from_fileids('test_image_model_id',[model_id])
    # 1+'1'






