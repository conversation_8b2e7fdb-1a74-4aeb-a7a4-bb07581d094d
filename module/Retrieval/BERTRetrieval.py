#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file: BERT
@time: 2023/4/21f
"""
import numpy as np
import os
import jieba
import pandas as pd
import pickle

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf

gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

import tensorflow as tf
from transformers import TFBertModel, BertTokenizer, BertConfig

from setting import logger
from utils.my_utils import my_cosine_similarity
from sklearn.metrics.pairwise import cosine_similarity
from tqdm import tqdm
from model.BM25.BM25 import HNSWModel


# model_path = '/data/dsb/BERT/rbt4/'
# model_path = r'D:\develop\aicc-nlp-nlu\pretrain_model\roberta_chinese_clue_tiny'
# model_path = r'/data/sammy/project/aicc-nlu/pretrain_model/roberta_chinese_clue_tiny_anto'


class BERTRetrieval:
    def __init__(self):
        self.max_len = 512
        self.emb_layer = [1, -1]
        self.config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
        self.tokenizer = BertTokenizer.from_pretrained(os.path.join(setting.PRETRAIN_BERT_DIR, 'vocab.txt'))
        model = TFBertModel.from_pretrained(setting.PRETRAIN_BERT_DIR, from_pt=False,config=self.config)
        inputs = self.get_data(["测试一下"], max_len=128)
        input = {
            "input_ids": inputs['input_ids'],
            "token_type_ids": inputs['token_type_ids'],
            'attention_mask': inputs['attention_mask']
        }
        model(input)
        model.load_weights(os.path.join(setting.PRETRAIN_BERT_DIR, 'tf_model.h5'))
        self.bert = model
        self.models = {}

    def train(self, model_id, all_text, save_dir):
        os.makedirs(save_dir, exist_ok=True)
        # self.train_tfidf(texts)
        embeddings_norm = self.encode_sentences(all_text)
        search = HNSWModel(all_text, embeddings_norm)
        with open(os.path.join(save_dir, "bert.pkl"), 'wb') as f:
            pickle.dump(search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)

    def load_model(self, model_id, save_dir):
        self.models[model_id] = dict()
        with open(os.path.join(save_dir, "bert.pkl"), 'rb') as f:
            bert = pickle.load(f)
        with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
            all_text = pickle.load(f)

        self.models[model_id]['all_text'] = all_text
        self.models[model_id]['bert'] = bert

    def predict(self, model_id, query,topn=10):
        bert = self.models[model_id]['bert']
        all_text = self.models[model_id]['all_text']

        emb = self.encode_sentences(texts=[query])
        recall_text, _ = bert.search(emb, min(len(all_text), topn))
        #精排
        score = cosine_similarity(emb,_)
        result = [recall_text[i] for i in score[0].argsort()][::-1]
        logger.debug(f'query:{query},bert检索结果:{dict(zip(recall_text,score[0].tolist()))}')
        return result

    def offline_model(self, model_id):
        msg = ''
        try:
            logger.debug('[{}] 模型开始下线'.format(model_id))
            del self.models[model_id]
            logger.debug('[{}] 模型下线成功'.format(model_id))
        except Exception as e:
            logger.error('[{}] 模型下线报错,{}'.format(model_id,e))
            msg = e
        return msg

    def get_encoder_layer(self, input_ids, attention_mask, token_type_ids, length_mask=None, length_div=None,
                          texts=None, **kwargs):
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            output_hidden_states=True,
            **kwargs
        )
        hidden_states = outputs[2]

        if isinstance(self.emb_layer, str):
            emb = hidden_states[-1][:, 0, :]
        elif isinstance(self.emb_layer, list):
            if length_mask is not None and length_div is not None:
                emb = None
                for layer in self.emb_layer:
                    emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
                emb = emb / len(self.emb_layer)  # 不同层输出的均值
                emb = emb * length_mask
                # 手动添加权重
                # text = [self.tokenizer.convert_ids_to_tokens(i) for i in list(input_ids.numpy())]
                # weight = [[1 if j not in ['代','少','兵'] else 10 for j in i] for i in text]
                # weight = tf.convert_to_tensor(weight,dtype=tf.float32)
                # weight = tf.expand_dims(weight, -1)
                # emb = emb*weight

                emb = tf.reduce_sum(emb, axis=1)
                emb = emb / length_div
            else:
                emb = None
                for layer in self.emb_layer:
                    emb = hidden_states[layer] if emb is None else emb + hidden_states[layer]
                emb = emb / len(self.emb_layer)
                emb = tf.reduce_mean(emb, axis=1)
        else:
            raise Exception(f"{self.__class__.__name__} 参数出错: emb_layer {self.emb_layer}")
        return emb

    def get_data(self, texts, max_len, return_tensor=False):
        if isinstance(texts, str):
            texts = [texts]
        data = {
            "input_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "token_type_ids": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "attention_mask": np.zeros(shape=(len(texts), max_len), dtype=np.int32),
            "length_mask": np.zeros(shape=(len(texts), max_len, self.config.hidden_size), dtype=np.float32),
            "length_div": np.ones(shape=(len(texts), self.config.hidden_size), dtype=np.float32)
        }

        for i, text in enumerate(texts):
            inputs = self.tokenizer.encode_plus(text, add_special_tokens=True, max_length=max_len, truncation=True,
                                                padding="max_length")
            if sum(inputs['attention_mask'])==2:
                logger.debug(f'错误字符:{texts[i]}')
                inputs = self.tokenizer.encode_plus('__', add_special_tokens=True, max_length=max_len, truncation=True,
                                                    padding="max_length")
            data["input_ids"][i, :] = inputs["input_ids"]
            data["token_type_ids"][i, :] = inputs["token_type_ids"]
            data["attention_mask"][i, :] = inputs["attention_mask"]

        lengths = np.sum(data["attention_mask"], -1) - 2
        for i, l in enumerate(lengths):
            data["length_mask"][i, 1:l + 1, :] = 1
            if l < 1:
                raise Exception("句子长度有问题")
        for i, l in enumerate(lengths):
            data["length_div"][i, :] = l

        if return_tensor:
            data["input_ids"] = tf.convert_to_tensor(data["input_ids"])
            data["token_type_ids"] = tf.convert_to_tensor(data["token_type_ids"])
            data["attention_mask"] = tf.convert_to_tensor(data["attention_mask"])
            data["length_mask"] = tf.convert_to_tensor(data["length_mask"])
            data["length_div"] = tf.convert_to_tensor(data["length_div"])
        # data["length_div"] = None
        # data["length_mask"] = None
        return data

    def search(self, text, topn=10):

        emb = self.encode_sentences(texts=[text])

        scores = my_cosine_similarity(self.embeddings_norm, emb.astype(np.float16))[:, 0]
        # scores = scores.clip(min=0, max=1)
        index = np.argsort(scores)
        index = index[::-1][:topn]

        out = list(self.texts[index])
        # print('scores: ', scores[index])
        # print('out: ', out)
        return out

    def encode_sentences(self, texts):
        embeddings = list()

        for start_idx in tqdm(range(0, len(texts), 16)):
            end_idx = min(start_idx + 16, len(texts))
            inputs = self.get_data(texts=texts[start_idx:end_idx], max_len=self.max_len, return_tensor=True)
            inputs['texts'] = texts[start_idx:end_idx]
            emb = self.get_encoder_layer(**inputs)
            embeddings.append(emb.numpy())

        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings

    def train_tfidf(self, text):
        # text = [[i for i in j] for j in text]
        text = [jieba.lcut(i) for i in text]
        text = [' '.join(i) for i in text]
        from sklearn.feature_extraction.text import TfidfVectorizer
        tf_trainer = TfidfVectorizer(token_pattern=r"(?u)\b\w+\b")
        tf_trainer.fit_transform(text)
        self.tfidf = tf_trainer
        self.vocab = pd.DataFrame(self.tfidf.vocabulary_, index=['index']).T.sort_values('index')

    def get_tfidf_weight(self, text):
        token = jieba.lcut(text)
        weight = self.tfidf.transform([' '.join(token)]).toarray().tolist()[0]
        my_vocab = self.vocab.copy()
        my_vocab['weight'] = weight
        oov = [i for i in token if i not in my_vocab.index.tolist()]
        # my_vocab = pd.concat([my_vocab,pd.DataFrame({'weight':[my_vocab['weight'].mean()]*len(oov)},index=oov)])#my_vocab['weight'].mean()
        my_vocab = pd.concat(
            [my_vocab, pd.DataFrame({'weight': [0] * len(oov)}, index=oov)])  # my_vocab['weight'].mean()
        print(my_vocab, 'my_vocabmy_vocab')
        my_vocab = my_vocab.loc[token].to_dict()['weight']
        final_weight = []
        for i in my_vocab:
            for j in i:
                final_weight.append(my_vocab[i])
        return final_weight


if '__main__' == __name__:
    # doc = open('../doc/新建文本文档.txt','r').readlines()
    from utils.filechat_utils import ultra_extract
    doc = ultra_extract(r"/data/sammy/project/NLP_Model/检索测试/Retriveval/files/青牛智胜-员工手册-第二版.pdf")
    # model = ArcCSE(doc)
    # print(model.search('尤丽芳'))
    # print(model.search('方代康'))
    # print(model.search('吴家舒'))
    # print(model.search('申甲'))
    # print(model.search('易滨滨'))
    #
    # print(model.search('代少兵'))
    # print(model.search('黄超凡'))

    # doc = open('../doc/新建文本文档.txt','r').readlines()
    # from rank.BERT_with_weight import ArcCSE
    # from utils.filechat_utils import ultra_extract
    # doc = ultra_extract(r'D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2023-09\青牛智胜-员工手册-第二版.pdf')
    output_path = r'D:\develop\NLP_Model\test'
    model_id = 'test'
    model = BERTRetrieval()
    model.train(model_id,doc,output_path)
    model.load_model(model_id,output_path)
    model.predict(model_id,'薪资结构', 10)
