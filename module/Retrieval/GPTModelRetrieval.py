#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file: BERT
@time: 2023/4/21f
"""
import numpy as np
import os
import jieba
import pandas as pd
import pickle

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf

gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

import tensorflow as tf
from transformers import TFBertModel, BertTokenizer, BertConfig

from setting import logger
from utils.my_utils import my_cosine_similarity,cosine_similarity
from tqdm import tqdm
from model.BM25.BM25 import HNSWModel


import torch
from transformers import AutoTokenizer
from transformers import LlamaConfig
from module.Retrieval.modeling import LlamaModelEmbedding
max_len = 512
model_path = os.path.join(setting.MAIN_DIR,'pretrain_model','embedding-model-llama-1b-neg4-mean')

import torch
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(device,'asdasdasdasdasdasdasd')

class GPTModelRetrieval:
    def __init__(self):
        self.max_len = 512
        self.config = LlamaConfig.from_pretrained(model_path, num_labels=1)
        self.config.normalized = True
        self.tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
        self.tokenizer.pad_token = self.tokenizer.unk_token
        self.tokenizer.padding_side = "right"
        self.model = LlamaModelEmbedding.from_pretrained(model_path, config=self.config,ignore_mismatched_sizes=True)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        self.models = {}

    def train(self, model_id, all_text, save_dir):
        os.makedirs(save_dir, exist_ok=True)
        # self.train_tfidf(texts)
        embeddings_norm = self.encode_sentences(all_text)
        search = HNSWModel(all_text, embeddings_norm)
        with open(os.path.join(save_dir, "hnsw.pkl"), 'wb') as f:
            pickle.dump(search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)

    def load_model(self, model_id, save_dir):
        self.models[model_id] = dict()
        with open(os.path.join(save_dir, "hnsw.pkl"), 'rb') as f:
            bert = pickle.load(f)
        with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
            all_text = pickle.load(f)

        self.models[model_id]['all_text'] = all_text
        self.models[model_id]['hnsw'] = bert

    def predict(self, model_id, query,topn=10):
        bert = self.models[model_id]['hnsw']
        all_text = self.models[model_id]['all_text']

        emb = self.encode_sentences(texts=[query])
        recall_text, _ = bert.search(emb, min(len(all_text), topn))
        #精排
        score = cosine_similarity(emb,_)
        result = [recall_text[i] for i in score[0].argsort()][::-1]
        logger.debug(f'query:{query},bert检索结果:{dict(zip(recall_text,score[0].tolist()))}')
        return result

    def offline_model(self, model_id):
        msg = ''
        try:
            logger.debug('[{}] 模型开始下线'.format(model_id))
            del self.models[model_id]
            logger.debug('[{}] 模型下线成功'.format(model_id))
        except Exception as e:
            logger.error('[{}] 模型下线报错,{}'.format(model_id,e))
            msg = e
        return msg

    def encode_sentences(self, texts):
        embeddings = list()

        for start_idx in tqdm(range(0, len(texts), 8)):
            end_idx = min(start_idx + 8, len(texts))
            input_ids = self.tokenizer(texts[start_idx:end_idx],
                return_tensors='pt',
                padding=True,
                truncation=True,
                max_length=max_len,
            ).to(self.device)
            emb = self.model.encode(input_ids)
            embeddings.append(emb.detach().numpy())

        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings



if '__main__' == __name__:
    # doc = open('../doc/新建文本文档.txt','r').readlines()
    from utils.filechat_utils import ultra_extract
    doc = ultra_extract(r"/data/sammy/project/NLP_Model/检索测试/Retriveval/files/青牛智胜-员工手册-第二版.pdf")
    # model = ArcCSE(doc)
    # print(model.search('尤丽芳'))
    # print(model.search('方代康'))
    # print(model.search('吴家舒'))
    # print(model.search('申甲'))
    # print(model.search('易滨滨'))
    #
    # print(model.search('代少兵'))
    # print(model.search('黄超凡'))

    # doc = open('../doc/新建文本文档.txt','r').readlines()
    # from rank.BERT_with_weight import ArcCSE
    # from utils.filechat_utils import ultra_extract
    # doc = ultra_extract(r'D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2023-09\青牛智胜-员工手册-第二版.pdf')
    output_path = r'D:\develop\NLP_Model\test'
    model_id = 'test'
    model = GPTModelRetrieval()
    model.train(model_id,doc,output_path)
    model.load_model(model_id,output_path)
    model.predict(model_id,'财务管理制度。', 10)
