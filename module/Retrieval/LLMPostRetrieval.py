#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file: BERT
@time: 2023/4/21f
"""
import numpy as np
import os
import jieba
import pandas as pd
import pickle

import setting
import requests
import json
import tensorflow as tf

import tensorflow as tf
from transformers import TFBertModel, <PERSON><PERSON><PERSON><PERSON>, Bert<PERSON>onfig

from setting import logger
from utils.my_utils import my_cosine_similarity, cosine_similarity
from tqdm import tqdm
from model.BM25.BM25 import HNSWModel


class LLMPostRetrieval:
    def __init__(self):
        self.url = f"{setting.LLM_EMBEDDING_IP_PORT}embedding"
        self.models = {}

    def train(self, model_id, all_text, save_dir):
        os.makedirs(save_dir, exist_ok=True)
        embeddings_norm = self.encode_sentences(all_text)
        search = HNSWModel(all_text, embeddings_norm)
        with open(os.path.join(save_dir, "hnsw_search.pkl"), 'wb') as f:
            pickle.dump(search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)

    def load_model(self, model_id, save_dir):
        self.models[model_id] = dict()
        with open(os.path.join(save_dir, "hnsw_search.pkl"), 'rb') as f:
            search = pickle.load(f)
        with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
            all_text = pickle.load(f)

        self.models[model_id]['all_text'] = all_text
        self.models[model_id]['search'] = search

    def predict(self, model_id, query, topn=10):
        search = self.models[model_id]['search']
        all_text = self.models[model_id]['all_text']

        emb = self.encode_sentences(texts=[query])
        recall_text, _ = search.search(emb, min(len(all_text), topn))
        # 精排
        score = cosine_similarity(emb, _)
        result = [recall_text[i] for i in score[0].argsort()][::-1]
        logger.debug(f'query:{query},bert检索结果:{dict(zip(recall_text, score[0].tolist()))}')
        return result

    def offline_model(self, model_id):
        msg = ''
        try:
            logger.debug('[{}] 模型开始下线'.format(model_id))
            del self.models[model_id]
            logger.debug('[{}] 模型下线成功'.format(model_id))
        except Exception as e:
            logger.error('[{}] 模型下线报错,{}'.format(model_id, e))
            msg = e
        return msg

    def search(self, text, topn=10):
        emb = self.encode_sentences(texts=[text])
        scores = my_cosine_similarity(self.embeddings_norm, emb.astype(np.float16))[:, 0]
        index = np.argsort(scores)
        index = index[::-1][:topn]
        out = list(self.texts[index])
        return out

    def encode_sentences(self, texts):
        embeddings = list()
        for start_idx in tqdm(range(0, len(texts), 16)):
            end_idx = min(start_idx + 16, len(texts))

            payload = json.dumps({
                "model_name": "embedding-model-llama-1b-neg4-1028-gpt-zhihu-qa-mean-11500",
                "text_list": texts[start_idx:end_idx]
            })
            headers = {
                'Content-Type': 'application/json'
            }
            response = requests.post(self.url, headers=headers, data=payload)
            res = json.loads(response.text)
            embeddings.append(np.array(res["embedding"]))
        embeddings = np.concatenate(embeddings, axis=0)
        return embeddings


if '__main__' == __name__:
    from utils.filechat_utils import ultra_extract

    doc = ultra_extract(r"/data/cbk/temp/test2.txt")
    output_path = r'/data/cbk/NLP_Model/test'
    model_id = 'test'
    model = LLMPostRetrieval()
    model.train(model_id, doc, output_path)
    model.load_model(model_id, output_path)
    res = model.predict(model_id, '薪资结构', 10)
    print("wait")
