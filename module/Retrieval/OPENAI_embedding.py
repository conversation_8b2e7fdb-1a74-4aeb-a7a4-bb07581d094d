from sklearn.metrics.pairwise import cosine_similarity
import openai,pickle
import numpy as np
import requests
import os
from setting import logger
from tqdm import tqdm
from model.BM25.BM25 import HNSWModel
from openai.embeddings_utils import get_embedding
openai.api_key = "sk-j5axpBIXNNHI3WTSC3B5E030E2724a089578C426A016B046"
openai.api_base = "https://lonlie.plus7.plus/v1"


class OPENAI_embedding:
    def __init__(self):
        self.models = {}

    def train(self, model_id, all_text, save_dir):
        os.makedirs(save_dir, exist_ok=True)
        # self.train_tfidf(texts)
        embeddings_norm = self.encode_sentences(all_text)
        search = HNSWModel(all_text, embeddings_norm)
        with open(os.path.join(save_dir, "bert.pkl"), 'wb') as f:
            pickle.dump(search, f, pickle.HIGHEST_PROTOCOL)
        with open(os.path.join(save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)

    def load_model(self, model_id, save_dir):
        self.models[model_id] = dict()
        with open(os.path.join(save_dir, "bert.pkl"), 'rb') as f:
            bert = pickle.load(f)
        with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
            all_text = pickle.load(f)

        self.models[model_id]['all_text'] = all_text
        self.models[model_id]['bert'] = bert

    def predict(self, model_id, query,topn=10):
        bert = self.models[model_id]['bert']
        all_text = self.models[model_id]['all_text']

        emb = self.encode_sentences(texts=[query])
        recall_text, _ = bert.search(emb, min(len(all_text), topn))
        #精排
        score = cosine_similarity(emb,_)
        result = [recall_text[i] for i in score[0].argsort()][::-1]
        logger.debug(f'query:{query},bert检索结果:{dict(zip(recall_text,score[0].tolist()))}')
        return result

    def offline_model(self, model_id):
        msg = ''
        try:
            logger.debug('[{}] 模型开始下线'.format(model_id))
            del self.models[model_id]
            logger.debug('[{}] 模型下线成功'.format(model_id))
        except Exception as e:
            logger.error('[{}] 模型下线报错,{}'.format(model_id,e))
            msg = e
        return msg

    def encode_sentences(self,texts, engine=None):
        output = []
        for i in tqdm(texts,desc='openai正在embedding'):
            embedding = [0] * 1563
            for _ in range(5):
                try:
                    embedding = get_embedding(i, engine="text-embedding-ada-002",timeout=10)
                    break
                except:
                    pass
            output.append(embedding)
        output = np.array(output)
        return output


if __name__=='__main__':
    # path = r'../doc/qnzs介绍.txt'
    # # path = r"D:\work\jupyter_notebook\hongan\手工标注filechat文档问题\text\专业影视广告公司策划书.txt"
    # doc1 = open(path, 'r', encoding='utf-8').readlines()
    # doc1 = [i.strip() for i in doc1 if i.strip()]
    import sys, os
    # sys.path.insert(0, r'D:\develop\aicc-nlp-nlu')
    from utils.filechat_utils import ultra_extract
    # doc1 = ultra_extract(r"C:\Users\<USER>\Desktop\智能质检竞品调研报告-20230804.pdf")
    # doc2 = ultra_extract(r"C:\Users\<USER>\Desktop\报告解读指南第二版.pdf")
    # doc = doc1 + doc2
    model_id = 'asd'
    save_path = r"C:\Users\<USER>\Desktop\新建文件夹"
    doc = ['测试一下','你好']
    model = OPENAI_embedding()
    model.train(model_id,doc,save_path)
    model.load_model(model_id,save_path)
    result = model.predict(model_id,'您最近怎么样')




