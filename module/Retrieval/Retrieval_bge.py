import time

from setting import logger

# from module.LLM.llama.llama_tokenzier import llama_lener
from module.Retrieval.BEGRetrieval import BEG<PERSON><PERSON>rieval
# from module.Retrieval.BERTRetrieval import BERTRetrieval
# from module.Retrieval.GPTModelRetrieval import GPTModelRetrieval as B<PERSON>TR<PERSON>rieval
# from module.Retrieval.OPENAI_embedding import OPENAI_embedding as BERTR<PERSON>rieval
# from module.Retrieval.LLMPostRetrieval import LLMPostRetrieval as BERTRetrieval
# from main.DocKvExtractMain import DocKvExtractMain
# kv_model = DocKvExtractMain()

use_kv = True
class Retrieval:
    def __init__(self):
        self.models = {}
        self.bert_retrieval = BEGRetrieval()

    def train(self,model_id,doc_dicts):
        start_time = time.time()
        code = 0
        msg = '文档检索训练成功'
        try:
            # start_time = time.time()
            # self.es.train(model_id, stack_doc)
            # logger.debug(f"训练耗时统计,model_id:{model_id},检索训练es耗时:{time.time() - start_time}")

            # start_time = time.time()
            # bm25 = BM25model(stack_doc)
            # with open(os.path.join(save_dir, "bm25.pkl"), 'wb') as f:
            #     pickle.dump(bm25, f, pickle.HIGHEST_PROTOCOL)
            # logger.debug(f"训练耗时统计,model_id:{model_id},检索训练bm25耗时:{time.time() - start_time}")

            start_time = time.time()
            self.bert_retrieval.train(model_id, doc_dicts)

            logger.debug(f"训练耗时统计,model_id:{model_id},检索训练bert耗时:{time.time() - start_time}")

        except Exception as e:
            logger.error('[{}] 文档检索训练报错:{}'.format(model_id, e),exc_info=True)
            code = 1
            msg = f'训练错误:{e}'
        return code, msg


    def predict(self,model_id,query,topn=5):
        bert_result = self.bert_retrieval.predict(model_id,query,topn)
        return bert_result


    def load_model(self,model_id):
        self.bert_retrieval.load_model(model_id)


    def offline_model(self, model_id):
        self.bert_retrieval.offline_model(model_id)
        del self.models[model_id]

    @staticmethod
    def remove_son(input_item):
        bad = []

        def exclude_son(a, start):
            candidate = a[start]
            for i in a:
                if candidate in i:
                    if i == candidate: continue
                    #             bad.append(candidate)
                    bad.append({candidate: i})
                    break

        for start in range(len(input_item)):
            exclude_son(input_item, start)
        for i in bad:
            input_item.remove(list(i.keys())[0])
        return input_item


if __name__=='__main__':
    # from rank.BOTH_kv import BOTHmodel
    start = time.time()
    from utils.filechat_utils import ultra_extract
    # doc = [['instructgpt就是这样的一个东西','instruct gpt就是这样的一个东西','你好']]
    doc = ultra_extract(r"D:\develop\NLP_Model\检索测试\Retriveval\files\青牛智胜-员工手册-第二版.pdf")
    model_id = '检索测试'
    save_path = r'D:\develop\NLP_Model\检索测试\Retriveval'
    model = Retrieval()
    model.train(model_id,['测试文档.txt'],[doc],save_path)
    model.load_model(model_id,save_path)
    result = model.predict(model_id,'早上几点上班',need_detail=False)
    # pd.DataFrame(result).to_csv('check.csv',encoding='utf-8-sig')
