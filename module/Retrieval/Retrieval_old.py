import time
import os
import pickle

import pandas as pd
import json
from utils.my_utils import <PERSON><PERSON><PERSON><PERSON>
from setting import logger, SAVE_MODEL_DIR, CLIENT
from collections import defaultdict

from model.BM25.BM25 import B<PERSON>25model
from model.BM25.ES_search import ES
from module.LLM.llama.llama_tokenzier import llama_lener
# from module.Retrieval.BERTRetrieval import BERTR<PERSON>rieval
# from module.Retrieval.GPTModelRetrieval import GPTModelRetrieval as BERTRetrieval
from module.Retrieval.OPENAI_embedding import OPENAI_embedding as BERTRetrieval
from main.DocKvExtractMain import DocKvExtractMain
kv_model = DocKvExtractMain()

use_kv = True
class Retrieval:
    def __init__(self):
        self.models = {}
        self.text_kvs = {}
        self.raw_text_kvs = {}
        self.text_to_raws = {}
        self.es = ES()
        self.bert_retrieval = BERTRetrieval()

    def train(self,model_id,filenames,docs,save_dir):
        start_time = time.time()
        source_map_kvs = defaultdict(list)
        code = 0
        msg = '文档检索训练成功'
        try:
            stack_doc = [j for i in docs for j in i]
            stack_doc = list(set(stack_doc))
            os.makedirs(save_dir,exist_ok=True)

            text_kvs = defaultdict(list)
            raw_text_kvs = defaultdict(list)
            text_to_raws = defaultdict(list)
            if use_kv:

                for filename,doc in zip(filenames,docs):
                    text_kv = {}
                    raw_text_kv = {}
                    # if len(doc)<8000:
                    #     text_kv,raw_text_kv,text_to_raw,check = kv_model.predict(doc)
                    # text_kv1,raw_text_kv1,text_to_raw,check = kv_model.predict_classification(doc)
                    # #优先使用序列模型的结果
                    # text_kv1.update(text_kv)
                    # raw_text_kv1.update(raw_text_kv)
                    # text_kv,raw_text_kv = text_kv1,raw_text_kv1

                    #使用窗口
                    text_kv,raw_text_kv,text_to_raw,check = kv_model.predict_windows_kv(doc)


                    #回溯k+v
                    source_map_kv = {f'{item[0]}\n{item[1]}':{'filename': filename, 'index': index, 'text': f'{item[0]}\n{item[1]}'} for index,item in enumerate(zip(list(raw_text_kv.keys()),
                                                                                                                                                                     list(raw_text_kv.values())))}
                    for i in source_map_kv:
                        source_map_kvs[i].append(source_map_kv[i])

                    # 回溯v
                    source_map_kv = {f'{item[1]}':{'filename': filename, 'index': index, 'text': f'{item[1]}'} for index,item in enumerate(zip(list(raw_text_kv.keys()),
                                                                                                                                                                   list(raw_text_kv.values())))}
                    for i in source_map_kv:
                        source_map_kvs[i].append(source_map_kv[i])

                    for i in text_kv:
                        text_kvs[i].append(text_kv[i])
                    for i in raw_text_kv:
                        raw_text_kvs[i].append(raw_text_kv[i])
                    for i in text_to_raw:
                        text_to_raws[i].append(text_to_raw[i])
            logger.debug(f"训练耗时统计,model_id:{model_id},检索训练kv_model耗时:{time.time() - start_time}")
            
            with open(os.path.join(save_dir, 'text_kvs.json'), 'w', encoding='utf-8') as f:
                json.dump(text_kvs, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)
            with open(os.path.join(save_dir, 'raw_text_kvs.json'), 'w', encoding='utf-8') as f:
                json.dump(raw_text_kvs, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)
            with open(os.path.join(save_dir, 'text_to_raws.json'), 'w', encoding='utf-8') as f:
                json.dump(text_to_raws, fp=f, ensure_ascii=False, indent=2, cls=MyEncoder)

            #es检索增加对V的检索
            start_time = time.time()
            stack_doc += list([j for i in raw_text_kvs.values() for j in i])
            stack_doc = list(set(stack_doc))

            #只检索滑窗value
            key_value = []
            for i, j in raw_text_kvs.items():
                for k in j:
                    key_value.append(i + '\n' + k)

            stack_doc = key_value
            self.es.train(model_id, stack_doc)
            logger.debug(f"训练耗时统计,model_id:{model_id},检索训练es耗时:{time.time() - start_time}")

            start_time = time.time()
            bm25 = BM25model(stack_doc)
            with open(os.path.join(save_dir, "bm25.pkl"), 'wb') as f:
                pickle.dump(bm25, f, pickle.HIGHEST_PROTOCOL)
            logger.debug(f"训练耗时统计,model_id:{model_id},检索训练bm25耗时:{time.time() - start_time}")

            start_time = time.time()
            if list(raw_text_kvs.keys()):
                # key_value = []
                # for i, j in raw_text_kvs.items():
                #     for k in j:
                #        key_value.append(i + '\n' + k)
                # for_bert = list(raw_text_kvs.keys()) + stack_doc + key_value
                # for_bert = list(set(for_bert))
                # for_bert = [i.strip() for i in for_bert if len(i.strip()) > 1]
                # for_bert = [i for i in for_bert if '' not in i]
                for_bert = key_value
                self.bert_retrieval.train(model_id, for_bert,save_dir=save_dir)
            else:
                self.bert_retrieval.train(model_id, ['这句话没意义'], save_dir=save_dir)
            logger.debug(f"训练耗时统计,model_id:{model_id},检索训练bert耗时:{time.time() - start_time}")

        except Exception as e:
            logger.error('[{}] 文档检索训练报错:{}'.format(model_id, e),exc_info=True)
            code = 1
            msg = f'训练错误:{e}'
        return code, msg,source_map_kvs


    def predict(self,model_id,query,topn=5,need_detail=False):
        bm25 = self.models[model_id]['bm25']
        if use_kv:
            raw_text_kv = self.raw_text_kvs[model_id]
            text_kv = self.text_kvs[model_id]
            text_to_raw = self.text_to_raws[model_id]
        else:
            raw_text_kv = {}
            text_kv = {}
            text_to_raw = {}

        bert_result = self.bert_retrieval.predict(model_id,query,topn)
        bm_result = bm25.search(query,topn)
        es_resut = self.es.search(model_id,query,topn)
        #结果聚合
        # final_result = []
        # for i,j in zip(bm_result,bert_result):
        #     final_result += [i,j]
        # final_result = pd.Series(final_result).drop_duplicates().tolist()
        # print('map前:',final_result)
        #
        # final_result_with_kv_map = [f"{i}\n{self.kv.get(i,'')}" if self.kv.get(i,'') else i for i in final_result]
        # final_result_with_kv_map = pd.Series(final_result_with_kv_map).drop_duplicates().tolist()

        es_resut_with_kv_map = []
        for i in es_resut:
            vs = raw_text_kv.get(i, '')
            if vs:
                for j in vs:
                    es_resut_with_kv_map.append(f"{i}\n{j}")
            else:
                es_resut_with_kv_map.append(i)

        bm_result_with_kv_map = []
        for i in bm_result:
            vs = raw_text_kv.get(i, '')
            if vs:
                for j in vs:
                    bm_result_with_kv_map.append(f"{i}\n{j}")
            else:
                bm_result_with_kv_map.append(i)

        bert_result_with_kv_map = []
        for i in bert_result:
            i_s = text_to_raw.get(i,[i])
            for i in i_s:
                vs = raw_text_kv.get(i, '')
                if vs:
                    for j in vs:
                        bert_result_with_kv_map.append(f"{i}\n{j}")
                else:
                    bert_result_with_kv_map.append(i)
        bert_result_with_kv_map = pd.Series(bert_result_with_kv_map).drop_duplicates().tolist()

        es_bm_bert_with_kv = es_resut_with_kv_map[:2] +  bert_result_with_kv_map[:2] + es_resut_with_kv_map[2:]
        #追踪排序来源
        source_track = []
        source_track.append({'text':es_resut_with_kv_map[:2],'source':['es']*len(es_resut_with_kv_map[:2])})
        source_track.append({'text':bert_result_with_kv_map[:2],'source':['bert']*len(bert_result_with_kv_map[:2])})
        source_track.append({'text':es_resut_with_kv_map[2:],'source':['es']*len(es_resut_with_kv_map[2:])})
        source_track = pd.concat([pd.DataFrame(i) for i in source_track])
        source_track.columns = ['text','source']

        es_bm_bert_with_kv = pd.Series(es_bm_bert_with_kv).drop_duplicates().tolist()
        #有item是其他item的子集的问题，需要去掉
        # es_bm_bert_with_kv = ['你好','我你好']

        # #字数限制
        max_word_num = 2000
        es_bm_bert_with_kv_cp = es_bm_bert_with_kv
        import numpy as np
        cum = np.cumsum([llama_lener([i]) for i in es_bm_bert_with_kv])

        final_result = []
        for index,i in enumerate(es_bm_bert_with_kv):
            if llama_lener(final_result + [i]) >= max_word_num: #如果超字数再到去子集
                add_no_son = self.remove_son(final_result + [i])
                if llama_lener(add_no_son) <= max_word_num: #如果去了子集没超,就用子集
                    final_result = add_no_son
                else:                                       #否则我们就用结果了
                    final_result = self.remove_son(final_result)
                    index-=1
                    break
            else:
                final_result.append(i)

        final_result = self.remove_son(final_result)
        es_bm_bert_with_kv = final_result

        # 超长的item也根据最大字数放进来
        # now_len = llama_lener(es_bm_bert_with_kv)
        # if index != len(es_bm_bert_with_kv_cp) - 1:
        #     if now_len < max_word_num:
        #         tail_index = cum[cum < max_word_num + 1].shape[0]
        #         es_bm_bert_with_kv += [es_bm_bert_with_kv_cp[tail_index][
        #                                :(max_word_num - now_len) // 2]]  # 只能按一半的长度补字数,不然要用llama的tokenzier
        #         # 超长来源补充
        #         source_track = pd.concat([source_track, pd.DataFrame({"text": es_bm_bert_with_kv[-1],
        #                                                               "source": source_track[
        #                                                                   source_track['text'] == es_bm_bert_with_kv_cp[
        #                                                                       tail_index]]['source'].tolist()[0]},index=[0])])

        item = {'bm_result':bm_result,
                'es_result': es_resut,
                'bert_result':bert_result,
                'bm_result_with_kv_map':bm_result_with_kv_map,
                'es_resut_with_kv_map':es_resut_with_kv_map,
                'bert_result_with_kv_map':bert_result_with_kv_map,
                'es_bm_bert_with_kv':es_bm_bert_with_kv}
        # return final_result_with_kv_map
        if need_detail:
            return item,source_track
        return es_bm_bert_with_kv,source_track


    def load_model(self,model_id,save_dir):
        with open(os.path.join(save_dir, "bm25.pkl"), 'rb') as f:
            bm25 = pickle.load(f)

        self.models[model_id] = dict()
        self.models[model_id]['bm25'] = bm25
        self.bert_retrieval.load_model(model_id,save_dir)

        if use_kv:
            with open(os.path.join(save_dir, 'text_kvs.json'), 'r', encoding='utf-8') as f:
                self.text_kvs[model_id] = json.load(f)
            with open(os.path.join(save_dir, 'raw_text_kvs.json'), 'r', encoding='utf-8') as f:
                self.raw_text_kvs[model_id] = json.load(f)
            with open(os.path.join(save_dir, 'text_to_raws.json'), 'r', encoding='utf-8') as f:
                self.text_to_raws[model_id] = json.load(f)

    def offline_model(self, model_id):
        self.bert_retrieval.offline_model(model_id)
        del self.models[model_id]

    @staticmethod
    def remove_son(input_item):
        bad = []

        def exclude_son(a, start):
            candidate = a[start]
            for i in a:
                if candidate in i:
                    if i == candidate: continue
                    #             bad.append(candidate)
                    bad.append({candidate: i})
                    break

        for start in range(len(input_item)):
            exclude_son(input_item, start)
        for i in bad:
            input_item.remove(list(i.keys())[0])
        return input_item


if __name__=='__main__':
    # from rank.BOTH_kv import BOTHmodel
    start = time.time()
    doc = [['instructgpt就是这样的一个东西','instruct gpt就是这样的一个东西','你好']]
    model_id = '检索测试'
    save_path = r'D:\develop\NLP_Model\检索测试\Retriveval'
    model = Retrieval()
    model.train(model_id,'测试文档',doc,save_path)
    model.load_model(model_id,save_path)
    result = model.predict(model_id,'您最近怎么样',need_detail=False)
    # pd.DataFrame(result).to_csv('check.csv',encoding='utf-8-sig')
