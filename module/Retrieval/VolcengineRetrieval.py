#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file: BERT
@time: 2023/4/21f
"""
import re
import time

import numpy as np
import os
import jieba
import pandas as pd
import pickle
import shutil
import requests
from collections import defaultdict
from functools import lru_cache
from utils.filechat_utils import get_list_of_string

from volcengine.viking_knowledgebase import VikingKnowledgeBaseService, Collection, Doc, Point
from volcengine.viking_knowledgebase.common import Field, FieldType, IndexType, EmbddingModelType

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf

gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

import tensorflow as tf
from transformers import TFBertModel, BertTokenizer, BertConfig
import setting
from setting import logger
from setting import MAIN_DIR
from utils.my_utils import my_cosine_similarity
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.metrics.pairwise import normalize
from tqdm import tqdm
from model.BM25.BM25 import HNSWModel
from utils.my_utils import MyEncoder
from database.MINIO import MINIO
from database.REDIS import REDIS
minio = MINIO()
redis = REDIS()


import os
import time

import pandas as pd

from volcengine.viking_knowledgebase import VikingKnowledgeBaseService, Collection, Doc, Point
from volcengine.viking_knowledgebase.common import Field, FieldType, IndexType, EmbddingModelType





import json

class VolcengineRetrieval:
    def __init__(self):
        self.predict_models = {}

    def load_model(self, model_id, model_save_dir,need_info = False):
        self.predict_models[model_id] = {}

        with open(f"{model_save_dir}/model_name.json", "r", encoding="utf-8") as f:
            model_name = json.load(f)[0]
            self.predict_models[model_id]["model_name"] = model_name

        with open(f"{model_save_dir}/t_f_dict.json", "r", encoding="utf-8") as f:
            t_f_dict = json.load(f)
            self.predict_models[model_id]["t_f_dict"] = t_f_dict

        try:
            with open(f"{model_save_dir}/t_u_dict.json", "r", encoding="utf-8") as f:
                t_u_dict = json.load(f)
                self.predict_models[model_id]["t_u_dict"] = t_u_dict
        except:
            t_u_dict = {}
            self.predict_models[model_id]["t_u_dict"] = t_u_dict

        with open(f"{model_save_dir}/hnsw_model.pkl", 'rb') as f:
            hnsw_model = pickle.load(f)
            self.predict_models[model_id]["hnsw_model"] = hnsw_model

        # if os.path.exists(f"{model_save_dir}/bm25_model.pkl"):
        #     with open(f"{model_save_dir}/bm25_model.pkl", 'rb') as f:
        #         bm25_model = pickle.load(f)
        #         self.predict_models[model_id]["bm25_model"] = bm25_model

        if os.path.exists(f"{model_save_dir}/info.json"):
            info = json.load(open(f"{model_save_dir}/info.json", "r", encoding="utf-8"))
        else:
            info = {}
        self.predict_models[model_id]["info"] = info

        if os.path.exists(f"{model_save_dir}/filename2seg_infos.json"):
            filename2seg_infos = json.load(open(f"{model_save_dir}/filename2seg_infos.json", "r", encoding="utf-8"))
        else:
            filename2seg_infos = {}
        self.predict_models[model_id]["filename2seg_infos"] = filename2seg_infos

        if os.path.exists(f"{model_save_dir}/doc_sim_answer.json"):
            doc_sim_answer = json.load(open(f"{model_save_dir}/doc_sim_answer.json", "r", encoding="utf-8"))
            image_map_url = {}
            for i in doc_sim_answer:
                image_map_url.update(i.get('image_map_url',{}))
        else:
            image_map_url = {}
        self.predict_models[model_id]["image_map_url"] = image_map_url

        #预测不使用的信息
        if need_info:
            faq_embedding = np.load(f"{model_save_dir}/faq_embedding.npy")
            self.predict_models[model_id]["faq_embedding"] = faq_embedding
            with open(f"{model_save_dir}/f_a_dict.json", "r", encoding="utf-8") as f:
                f_a_dict = json.load(f)
                self.predict_models[model_id]["f_a_dict"] = f_a_dict
            with open(f"{model_save_dir}/all_text.json", "r", encoding="utf-8") as f:
                all_text = json.load(f)
                self.predict_models[model_id]["all_text"] = all_text
            with open(f"{model_save_dir}/t_a_dict.json", "r", encoding="utf-8") as f:
                t_a_dict = json.load(f)
                self.predict_models[model_id]["t_a_dict"] = t_a_dict
            with open(f"{model_save_dir}/t_q_dict.json", "r", encoding="utf-8") as f:
                t_q_dict = json.load(f)
                self.predict_models[model_id]["t_q_dict"] = t_q_dict
            with open(f"{model_save_dir}/doc_sim_answer.json", "r", encoding="utf-8") as f:
                doc_sim_answer = json.load(f)
                self.predict_models[model_id]["doc_sim_answer"] = doc_sim_answer

            output_dict = {"model_name":model_name,
                           "t_f_dict":t_f_dict,
                           "t_u_dict":t_u_dict,
                           "hnsw_model":hnsw_model,
                           "info":info,
                            "filename2seg_infos":filename2seg_infos,
                            "faq_embedding":faq_embedding,
                            "f_a_dict":f_a_dict,
                            "all_text":all_text,
                            "t_a_dict":t_a_dict,
                            "t_q_dict":t_q_dict,
                           'doc_sim_answer':doc_sim_answer
                           }
            return output_dict



    def offline_model(self, model_id,info=False):
        msg = ''
        try:
            if info:
                logger.debug('[{}] 模型开始下线'.format(model_id))
            del self.predict_models[model_id]
            if info:
                logger.debug('[{}] 模型下线成功'.format(model_id))
        except Exception as e:
            logger.error('[{}] 模型下线报错,{}'.format(model_id,e))
            msg = e
        return msg


    def train_from_fileids(self,model_id,fileids:list,**kwargs):
        code = 0
        result = {'fail_list':fileids,'success_list':[]}
        try:
            doc_sim_answer = []
            is_list = ['doc_sim_answer']

            for fileid in fileids:
                save_dir = os.path.join(setting.FILECHAT_FILE_DIR, fileid, 'Retrieval')
                output_dict = self.load_model(fileid, save_dir, need_info=True)
                for key in is_list:
                    cmd = f"""{key}.extend(output_dict["{key}"])"""
                    exec(cmd)
                self.offline_model(fileid)

            model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id, 'Retrieval')
            os.makedirs(model_save_dir,exist_ok=True)
            urls = []
            for i in doc_sim_answer:
                urls.append(i['url'])
            result = self.final_train(model_id,fileids,urls,**kwargs)

        except:
            code = 1
            logger.error(f'绑定失败:{model_id}', exc_info=True)

        return result

    def get_vikingservice(self):
        config = redis.get_data('llm_rag_config',need_json=True)
        volcengine_config = config['volcengine']
        volcengine_config = json.loads(volcengine_config)
        # volcengine_config = {'sk': 'WVRBeU4yRTVaVEprTXpBd05ERTNORGt3T0RObU1XTmhPVGRqTXpobVlUTQ==',
        #                      'host': 'api-knowledgebase.mlp.cn-beijing.volces.com',
        #                      'ak': 'AKLTOTBhMzk5YjZkYjMyNDllOGEzMjVjZjA4MDFlYmM2Y2I'}
        logger.info('volcengine_config:{}'.format(volcengine_config))
        ak = volcengine_config['ak']
        sk = volcengine_config['sk']
        host = volcengine_config.get('host', "api-knowledgebase.mlp.cn-beijing.volces.com")

        viking_knowledgebase_service = VikingKnowledgeBaseService(host=host,scheme="https")
        viking_knowledgebase_service.set_ak(ak)
        viking_knowledgebase_service.set_sk(sk)
        return viking_knowledgebase_service

    def train(self,viking_knowledgebase_service,model_id,file_ids,urls,**kwargs):

        # collection_name = "eval07311000" #
        collection_name = model_id
        description = model_id
        cut_len = kwargs.get('cut_len', 1000)
        embedding_model = kwargs.get('embedding_model', 'bge-m3')
        # 自定义index配置、preprocess文档配置构建知识库
        index_type = IndexType.HNSW if embedding_model!='doubao-embedding-and-m3' else IndexType.HNSW_HYBRID
        index = {
            "index_type": index_type,
            "index_config": {
                "fields": [{
                    "field_name": "chunk_len",
                    "field_type": FieldType.Int64,
                    "default_val": 0
                }],
                "cpu_quota": 1,
                "embedding_model": embedding_model
            }
        }
        preprocessing = {"chunk_length": cut_len}
        my_collection = viking_knowledgebase_service.create_collection(collection_name=collection_name,
                                                                       description=description, index=index,
                                                                       preprocessing=preprocessing)

        # 获取collection详细信息

        # 由url上传doc
        check_list = []
        for index, file_id, url in zip(range(len(file_ids)),file_ids,urls):
            file = f'a{file_id}'
            doc_type = url.split('.')[-1]
            my_collection.add_doc(add_type='url', doc_id=file, doc_name=file, doc_type=doc_type, url=url)
            check_list.append(file)

        return check_list

    def wait_to_finish(self,viking_knowledgebase_service,collection_name):
        for i in range(60):
            query = '1'
            logger.info(f'火山rag等待{collection_name}完成index')
            try:
                viking_knowledgebase_service.search_collection(collection_name=collection_name, query=query,limit=1)
                logger.info(f'火山rag训练完成:{collection_name}')
                return True
            except:
                time.sleep(10)
        return False

    def good_bad_return(self,viking_knowledgebase_service,collection_name,check_list,file_ids):
        my_collection = viking_knowledgebase_service.get_collection(collection_name=collection_name)
        good = []
        bad = []
        for check,file_id in zip(check_list,file_ids):
            a = my_collection.get_doc(doc_id=check)
            logger.info(f"collection_name:{collection_name},file_id:{file_id},status:{a.status}")
            """
            0 表示处理完成
            1 表示处理失败
            5 表示删除中
            其他表示处理中
            """
            status = a.status['process_status']
            if status in [0,2]:
                good.append(file_id)
            else:
                bad.append(file_id)
        return {'fail_list':bad,'success_list':good}


    def drop_collection(self,model_id,embedding_models):
        for embedding_model in embedding_models:
            try:
                viking_knowledgebase_service = self.get_vikingservice()
                collection_name = f"""a{model_id}{embedding_model.replace('-','')}"""
                result = viking_knowledgebase_service.drop_collection(collection_name=collection_name)
                logger.info('火山rag删除collection成功:{}'.format(collection_name))
            except Exception as e:
                result = {"model_id":model_id,'msg':e}
                logger.error('错误',exc_info=True)
        return result

    def predict(self,model_id, query, top_k, is_text_len=None,embedding_model=''):
        model_id = f"""a{model_id}{embedding_model.replace('-','')}"""
        result = {"filename": [], "match_text": [],"score": [],'url':[]}
        viking_knowledgebase_service = self.get_vikingservice()
        limit = top_k
        text_len = None
        if is_text_len is not None:
            text_len = top_k*1000
            limit = 20

        points = viking_knowledgebase_service.search_collection(collection_name=model_id, query=query,limit=limit)

        for point in points:
            each_item_max_len = 1000000
            # if self.predict_models[model_id]["t_a_dict"][search_docs[p_i]] in result["answer"]:
            #     continue
            if len(point.content.strip()) < 1:
                continue
            if point.content[:each_item_max_len] in result["match_text"]:
                continue
            if text_len != None and len(
                    '\n'.join(result["match_text"] + [point.content[:each_item_max_len]])) > text_len:
                each_item_max_len = text_len - len('\n'.join(result["match_text"][:each_item_max_len]))
                if each_item_max_len < 100:  # 如果最后一个文本不满足字数,太小了,就不要了
                    break
            filename = point.doc_info.doc_name
            result["filename"].append(filename)
            result["match_text"].append(point.content[:each_item_max_len])
            result['score'].append(point.score)
            result['url'].append(' ')
            if text_len == None and len(result["filename"]) >= top_k:  # topk返回
                break

        return result


    def final_train(self,model_id,file_ids,urls,**kwargs):
        try:
            viking_knowledgebase_service = self.get_vikingservice()
            model_id = f"""a{model_id}{kwargs["embedding_model"].replace('-','')}"""
            try:
                viking_knowledgebase_service.get_collection(model_id)
                exist = True
            except:
                exist = False
            check_list = self.train(viking_knowledgebase_service,model_id,file_ids,urls,**kwargs)
            self.wait_to_finish(viking_knowledgebase_service,model_id)
            fail_list = self.good_bad_return(viking_knowledgebase_service,model_id,check_list,file_ids)
            return fail_list
        except:
            logger.error('火山rag训练报错',exc_info=True)
            try:
                if not exist:
                    viking_knowledgebase_service.drop_collection(collection_name=model_id)
                    logger.info(f'火山rag训练失败，删除collection成功:{model_id}')
                else:
                    logger.info(f'火山rag训练失败，已存在的不删除,model_id:{model_id}')

            except:
                pass
            return {'fail_list':file_ids,'success_list':[]}


if '__main__' == __name__:
    model = VolcengineRetrieval()
    model_id = '1907000289446105090'
    fileids = ['1906983607465246720']
    cut_len = 400
    # embedding_model = 'bge-m3'
    # result = model.train_from_fileids(model_id=model_id,fileids=fileids,cut_len=cut_len,embedding_model=embedding_model)
    # model.drop_collection(model_id)
    model_id = '1916770416269312000bgem3'
    model.drop_collection(model_id)
    1+'1'
    query = '智能外呼平台模块化明细说明'
    top_k = 5

    embedding_model = 'bge-m3'
    result = model.predict(model_id=model_id, query=query, top_k=top_k,embedding_model=embedding_model)



    viking_knowledgebase_service = model.get_vikingservice()
    model_id = '1916770416269312000'
    collection_name = f"""a{model_id}{embedding_model.replace('-', '')}"""
    check_list = ['a1916375273657712640']
    file_ids = ['1916375273657712640']
    fail_list = model.good_bad_return(viking_knowledgebase_service, collection_name, check_list, file_ids)



    model.good_bad_return()