import numpy as np
import os
import jieba
import pandas as pd
import pickle

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE



from transformers import LlamaModel, LlamaConfig, LlamaTokenizer
from typing import List, Optional, Tuple, Union
from torch import nn, Tensor
from dataclasses import dataclass
from torch import nn
from typing import Dict
import torch
from transformers.file_utils import ModelOutput


@dataclass
class EncoderOutput(ModelOutput):
    q_reps: Optional[Tensor] = None
    p_reps: Optional[Tensor] = None
    loss: Optional[Tensor] = None
    scores: Optional[Tensor] = None

class LlamaModelEmbedding(LlamaModel):
    def __init__(self, config: LlamaConfig, **kwargs):
        super().__init__(config, **kwargs)
        self.forward_c = 0
        self.cross_entropy = nn.CrossEntropyLoss(reduction='mean')
        self.dense_layer = nn.Linear(self.config.hidden_size, 1536)
        
    def sentence_embedding(self, hidden_state, mask):
        if self.config.sentence_pooling_method == 'mean':
            s = torch.sum(hidden_state * mask.unsqueeze(-1).float(), dim=1)
            d = mask.sum(axis=1, keepdim=True).float()
            return s / d
        elif self.config.sentence_pooling_method == 'cls':
            return hidden_state[:, 0]

    def encode(self, features):
        if features is None:
            return None

        print([features[i].device for i in features],'asdasdsadsad')
        psg_out = super().forward(**features, return_dict=True)
        output = self.dense_layer(psg_out.last_hidden_state)
        p_reps = self.sentence_embedding(output, features['attention_mask'])
        if self.config.normalized:
            p_reps = torch.nn.functional.normalize(p_reps, dim=-1)
        return p_reps.contiguous()

    def compute_similarity(self, q_reps, p_reps):
        if len(p_reps.size()) == 2:
            return torch.matmul(q_reps, p_reps.transpose(0, 1))
        return torch.matmul(q_reps, p_reps.transpose(-2, -1))

    def compute_loss(self, scores, target):
        return self.cross_entropy(scores, target)

    def forward(self, query: Dict[str, Tensor] = None,
                passage: Dict[str, Tensor] = None, teacher_score: Tensor = None):
        self.forward_c += 1
        q_reps = self.encode(query)
        p_reps = self.encode(passage)
        if self.training:
            scores = self.compute_similarity(q_reps, p_reps)
            scores = scores / self.config.temperature
            scores = scores.view(q_reps.size(0), -1)

            target = torch.arange(scores.size(0), device=scores.device, dtype=torch.long)
            target = target * (p_reps.size(0) // q_reps.size(0))
            loss = self.compute_loss(scores, target)
        else:
            scores = self.compute_similarity(q_reps, p_reps)
            loss = None

        return EncoderOutput(
            loss=loss,
            scores=scores,
            q_reps=q_reps,
            p_reps=p_reps,
        )

    # def _dist_gather_tensor(self, t: Optional[torch.Tensor]):
    #     if t is None:
    #         return None
    #     t = t.contiguous()
    #
    #     all_tensors = [torch.empty_like(t) for _ in range(self.world_size)]
    #     dist.all_gather(all_tensors, t)
    #
    #     all_tensors[self.process_rank] = t
    #     all_tensors = torch.cat(all_tensors, dim=0)
    #
    #     return all_tensors

    def save(self, output_dir: str):
        state_dict = self.model.state_dict()
        state_dict = type(state_dict)(
            {k: v.clone().cpu()
             for k,
                 v in state_dict.items()})
        self.model.save_pretrained(output_dir, state_dict=state_dict)

