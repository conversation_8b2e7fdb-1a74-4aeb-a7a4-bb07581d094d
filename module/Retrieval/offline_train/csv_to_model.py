from module.Retrieval.BEGRetrieval import BEGRetrieval
import setting
import pandas as pd
import os
model = BEGRetrieval()
# model.get_embedding(setting.FILECHAT_EMBEDDING_MODEL_NAME, ['你好'])
# 1+"1"




model_id = '0731演示_1000_ck1552'
model_id = '0715演示_500_xiaobu-57w-ck976-mix-ori_8_2'

model_id = '1891396006230999040_100'
# model_id = '1891772354672562178_50'
model_id = '0731演示_1000_xiaobu_GPTGen4w_ck61_mix_ori_8_2'
model_save_dir = rf'/data/sammy/serve/NLP_Model/{model_id}/Retrieval'
model_save_dir = rf'D:\develop\NLP_Model/{model_id}/Retrieval'

df = pd.read_csv(r"D:\develop\NLP_Model\0731演示\Retrieval\1810490696196001793.csv")
doc_dicts = df.to_dict(orient='records')
# data_path = r'/data/sammy/serve/NLP_Model/0715所有文档_500/Retrieval/doc_sim_answer.json'
# import json
# doc_dicts = json.loads(open(data_path,'r',encoding='utf8').read())

# doc_dicts = pd.read_csv(r"C:\Users\<USER>\Desktop\doc_sim_answer.json")
# doc_dicts['answer'] = doc_dicts['answer'].apply(eval)
# doc_dicts['sim'] = doc_dicts['sim'].apply(eval)
# doc_dicts = doc_dicts.to_dict(orient='records')

import json
# doc_dicts = json.loads(open(r"C:\Users\<USER>\Desktop\doc_sim_answer无答案.json",encoding='utf-8').read())
# doc_dicts = json.loads(open(r"C:\Users\<USER>\Desktop\doc_sim_answer有答案.json",encoding='utf-8').read())
for i in doc_dicts:
    i['sim'] = eval(str(i['sim']))
    i.pop('cut_len')
    # i['window_size'] = 1000
# setting.FILECHAT_EMBEDDING_MODEL_NAME = 'm3-law-filechat-116w_ck2562'e
setting.FILECHAT_EMBEDDING_MODEL_NAME = 'xiaobu_GPTGen4w_ck61_mix_ori_8_2'
# setting.FILECHAT_EMBEDDING_MODEL_NAME = '0523_people_gptlabel_gptgen_keywordFB_doc_gptgenq_gptextq_gptsum_gptans_filechat_lawsearch_fineindex_manual_simhash5_score05_pos_batch4_gpu3_epoch1_d0523_273304'
setting.FEILCHAT_EMBEDDING_URL = 'http://192.168.1.239:6012/embeddings'



seg_lens = [1000]
model.train(model_id,doc_dicts,model_save_dir=model_save_dir,seg_lens=seg_lens)


# model.load_model(model_id,model_save_dir)
# model.predict(model_id,'能不能提供公文格式相关的资料给我',5,rerank=None)
# model.predict(model_id,'我希望你提供多一些合同模板以及其他相关的资料给我',5,rerank=None)
# set(model.predict(model_id,'请发给我所有合同样板',10,rerank=None)['filename'])
# set(model.predict(model_id,'采购的合同模板有吗',10,rerank=None)['filename'])
#
# model.predict(model_id,'能不能提供公文格式相关的资料给我',5,rerank=None)
#
# model.predict(model_id,'费用报销单',5,rerank=None)
# model.predict(model_id,'董事会的会议授权书',5,rerank=None)
# model.predict(model_id,'公司的采购合同范本',5,rerank=None)
#
# model.predict(model_id,'董事会的会议授权书文件',5,rerank=None)








compare_rank_prompt = """
片段一
@片段一

片段二
@片段二

用户问题:@用户问题
对于片段一和片段二,哪个片段能完美回答用户问题,并且针对两个片段出打分
"""
question = """"""
sec_1 = """"""
sec_2 = """"""
compare_rank_prompt = compare_rank_prompt.replace('@片段一',sec_1).replace('@片段二',sec_2).replace('@用户问题',question)

