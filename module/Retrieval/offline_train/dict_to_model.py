from module.Retrieval.BEGRetrieval import BEGRetrieval
import setting
import os
import json
model = BEGRetrieval()



model_id = '0731演示_1000_ck1552'
model_id = '0715演示_500_xiaobu'
model_id = '0715演示_500_voyage-3'
model_id = '0715演示_500_m3-law-filechat-23w-seg1000-batch2_ck793'
model_id = '演示1219'
model_id = '0731演示'
t_f_dict_path = r'/data/sammy/serve/NLP_Model/0731演示/Retrieval/t_f_dict.json'
t_f_dict_path = r'D:\develop\NLP_Model\0731演示\Retrieval/t_f_dict.json'
# t_f_dict_path = r"C:\Users\<USER>\Desktop\t_f_dict.json"


setting.FEILCHAT_EMBEDDING_URL = 'http://192.168.1.239:6012/embeddings'
model_name = 'xiaobu-embedding-v2'
model_name = 'voyage-3'
model_name = 'm3-law-filechat-23w-seg1000-batch2_ck793'
model_name = 'xiaobu-57w-ck976-mix-ori_8_2'
model_name = 'm3-law-filechat-116w_ck2562'
model_name = 'xiaobu_GPTGen4w_ck61_mix_ori_8_2'
model_name = 'doubao-embedding-large-text-240915'


model_id+='_'+model_name
model.get_embedding(model_name,['你好'])

# with open(t_f_dict_path, "r", encoding="utf-8") as f:
#     t_f_dict = json.load(f)
# all_text = list(t_f_dict.keys())


import json
# doc_dicts = json.loads(open(r"C:\Users\<USER>\Desktop\doc_sim_answer无答案.json",encoding='utf-8').read())

t_f_dict = json.loads(open(t_f_dict_path,encoding='utf-8').read())




model.train_from_t_f_dict(model_id,t_f_dict,model_name)
import setting
model_save_dir = os.path.join(setting.SAVE_MODEL_DIR,f'{model_id}/Retrieval')
# model_save_dir = rf'/data/sammy/serve/NLP_Model/{model_id}/Retrieval'
model.load_model(model_id,model_save_dir)
model.predict(model_id,'你好',3)

print(model_id,'model_idmodel_idmodel_id')




#
# aaa = open(r"C:\Users\<USER>\Desktop\all_text.json",'r',encoding='utf-8-sig').read()
# aaa = json.loads(aaa)
# for i in t_f_dict:
#     if i not in aaa:
#         print(i)
#         break