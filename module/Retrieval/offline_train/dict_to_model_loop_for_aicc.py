import sys
sys.path.append('/app')
from module.Retrieval.BEGRetrieval import BEGRetrieval
import setting
import os
import json
model = BEGRetrieval()


# file_id_list = ["1897488060094726144", "1897488060098920448", "1897488060149252096", "1897488060149252097", "1897488060149252098", "1897488060149252099", "1897488060149252100", "1825782361258573824", "1825782361266962432", "1825782361266962433", "1825782361266962434", "1825782361271156736", "1825782361275351040", "1825782361275351041", "1825782361279545344", "1825782361279545345", "1825782361279545346", "1825782361313099776", "1825783025036541952", "1825783025044930560", "1825783025044930561", "1825783025044930562", "1825783025044930563", "1825783025049124864", "1825783025049124865", "1825783025049124866", "1825783025049124867", "1825783025049124868", "1825783025049124869", "1825783025049124870", "1825783025049124871", "1825783025053319168", "1825783025053319169", "1825783025053319170", "1825783025053319171", "1825783025053319172", "1825783025053319173", "1825783025053319174", "1825783025053319175", "1825783226262470656", "1825783226266664960", "1825783226266664961", "1825783226270859264", "1825783226270859265", "1825783226270859266", "1825783226270859267", "1825783226270859268", "1825783407846473728", "1825783407880028160", "1825783407880028161", "1825783407880028162", "1825783407884222464", "1825783407884222465", "1825783407884222466", "1825783407884222467", "1825783407884222468", "1825783407884222470", "1825783407884222471", "1825863230677696512", "1825783463727185920", "1825783463727185921", "1825783463727185922", "1825783463727185923", "1825783463727185924", "1825783463731380225", "1825863356586508288", "1825873971812089856", "1825783732124893184", "1825783732129087488", "1825783732129087489", "1825783732129087490", "1825783732158447616", "1825783732162641920", "1825783732162641921", "1825783732162641922", "1825783732162641923", "1825783732162641924", "1825783732162641925", "1825783732162641926", "1825783732162641927", "1825783732166836224", "1825783732166836225", "1825783785635823616", "1825783785640017920", "1825783785640017921", "1825783785640017922", "1825783785640017923", "1825783824504438784", "1825783824512827392", "1825783824512827393", "1825783824512827394", "1825783824512827395", "1825783824512827396", "1825783824512827397", "1825783824512827398", "1825783824517021696", "1825783824517021697", "1825783824517021698", "1825783862701965312", "1825783862706159616", "1825783862710353920", "1825783862710353921", "1825783862710353922", "1825783862710353923", "1825783862710353924", "1825783895144906752", "1825783895149101056", "1825783895149101057", "1825784119645028352", "1825784119649222656", "1825784119653416960", "1825784393310781440", "1825784393310781441", "1825784393310781442", "1825784393310781443", "1825784393310781444", "1825863582676271104", "1825784451796156416", "1825784451800350720", "1825784451800350721", "1825784451804545024", "1825784451804545025", "1825784451804545026", "1825784485384142848", "1825784485388337152", "1825784485388337153", "1825785609893822464", "1825785609902211078", "1825785609902211079", "1825785609906405376", "1825785609906405377", "1825785609906405378", "1825785609906405379", "1825785609906405380", "1825785609906405381", "1825785609906405382", "1825785609906405383", "1825785609906405384", "1825785609910599680", "1825785609910599681", "1825785609939959808", "1825785609939959809", "1825785609939959810", "1825785609944154112", "1825785609944154113", "1825785609944154114", "1825785609944154115", "1825785609944154116", "1825785609944154117", "1825785609944154118", "1825785609944154119", "1825785609948348416", "1825785609948348417", "1825785609948348418", "1825785609948348419", "1825785609948348420", "1825785609948348421", "1825785609948348423", "1825785609952542720", "1825785609952542721", "1825785609952542722", "1825785609952542723", "1825863872846610432", "1825863983584624640", "1825864147024068608", "1825864147028262912", "1825864147028262913", "1825864345162989568", "1825864345200738304", "1825864345200738305", "1825864345200738306", "1825864345200738307", "1825786588513026048", "1825786588546580480", "1825786588546580481", "1825786588546580482", "1825786588546580483", "1825786588546580484", "1825786588546580485", "1825786588550774784", "1825786588550774785", "1825786588550774786", "1825786588550774787", "1825786588550774788", "1825786588550774790", "1825786588550774791", "1825786588550774792", "1825786588550774793", "1825786588550774794", "1825786588550774795", "1825786588550774796", "1825786588554969088", "1825786588554969089", "1825786588554969090", "1825786588554969091", "1825786588554969092", "1825786588554969093", "1825786588559163393", "1825786588584329216", "1825786588584329217", "1825786588584329219", "1825786588584329220", "1825786588584329221", "1825786588584329223", "1825786588588523520", "1825864511408422912", "1825864639489884160", "1825864639494078464", "1825864639494078465", "1825864639494078466", "1825864707475357696", "1825864866951184384", "1825864866955378688", "1825864866955378689", "1825864866959572992", "1825787805540663299", "1825787805540663298", "1825787805540663297", "1825787805540663296", "1825787805536468993", "1825787805536468992", "1825787805498720256", "1825788555037622272", "1825788555033427968", "1825788554995679232", "1825788554991484930", "1825788554991484929", "1825788554991484928", "1825788554987290624", "1825788681495887875", "1825788681495887874", "1825788681495887873", "1825788681495887872", "1825788681491693576", "1825788681491693575", "1825788681491693574", "1825788681491693573", "1825788681491693572", "1825788681491693571", "1825788681491693570", "1825788681491693569", "1825788681491693568", "1825788681487499269", "1825788681487499268", "1825788681487499267", "1825788681487499266", "1825788681487499265", "1825788681487499264", "1825788681483304960", "1825788883250298880"]
file_id_list = os.listdir('/NLP_Model/FILECHAT_MODEL')
from tqdm import tqdm

for model_id in tqdm(file_id_list):
    try:
        t_f_dict_path = rf'/NLP_Model/FILECHAT_MODEL/{model_id}/Retrieval/t_f_dict.json'
        model_save_dir = rf'/NLP_Model/FILECHAT_MODEL/{model_id}/Retrieval'
        t_f_dict = json.loads(open(t_f_dict_path,encoding='utf-8').read())
        model.train_from_t_f_dict(model_id,t_f_dict,setting.FILECHAT_EMBEDDING_MODEL_NAME,model_save_dir)
    except:
        import logging
        logging.error('错误',exc_info=True)
    # model_save_dir = rf'/data/sammy/serve/NLP_Model/{model_id}/Retrieval'
    # model.load_model(model_id,model_save_dir)
    # model.predict(model_id,'你好',3)
    #
    # print(model_id,'model_idmodel_idmodel_id')




#
# aaa = open(r"C:\Users\<USER>\Desktop\all_text.json",'r',encoding='utf-8-sig').read()
# aaa = json.loads(aaa)
# for i in t_f_dict:
#     if i not in aaa:
#         print(i)
#         break