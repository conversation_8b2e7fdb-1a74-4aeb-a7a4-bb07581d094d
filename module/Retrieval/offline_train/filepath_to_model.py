import re
# import sys
# sys.path.insert(0,'/data/sammy/serve/aicc-nlp-nlu')
from module.Retrieval.BEGRetrieval import BEGRetrieval
import setting
import os
import pandas as pd
model = BEGRetrieval()
# model.get_embedding(setting.FILECHAT_EMBEDDING_MODEL_NAME, ['你好'])
# 1+"1"

from utils.filechat_utils import ultra_extract

# root_path = r'/data/sammy/serve/aicc-nlp-nlu/module/FileChatBot/badcase_check/0715演示/0715所有文档'
root_path = r'/data/sammy/serve/aicc-nlp-nlu/module/FileChatBot/badcase_check/中国人寿nlp跨文档_result/pdf'
# root_path = r'/data/sammy/NLP_Model/跨文档/pdf'

model_id = '中国人寿nlp跨文档测试_500_ck2562'
model_id = '中国人寿nlp跨文档测试_400_ck2562'
# model_id = '中国人寿nlp跨文档测试_kv'
# model_id = '中国人寿nlp跨文档测试_kv_400'
# model_id = '中国人寿nlp跨文档测试_100'
model_id = '中国人寿nlp跨文档测试_kv_ck1830'
model_id = '中国人寿nlp跨文档测试_400_ck2562'
model_id = '中国人寿nlp跨文档测试_400_ck1830'
model_id = '中国人寿nlp跨文档测试_kv_cut'
model_id = '中国人寿nlp跨文档测试_kv_cut'

model_id = '中国人寿nlp跨文档测试_kv_ck1552'
model_id = '中国人寿nlp跨文档测试_400_ck1552'
model_id = '中国人寿nlp跨文档测试_400_ck1552_2'
model_id = '中国人寿nlp跨文档测试_modelkv_400'
# model_id = '中国人寿nlp跨文档测试_modelkv_400_2'
# model_id = '中国人寿nlp跨文档测试_4okv_400'
model_id = '中国人寿nlp跨文档测试_modelkv_ck1552'
model_id = '中国人寿nlp跨文档测试_model25kv'
model_id = '中国人寿nlp跨文档测试_model25kv_2'#kv_分总_pdf_parse_new
model_id = '中国人寿nlp跨文档测试_model25kv_3'#kv_分总_pdf_parse_old
model_id = '中国人寿nlp跨文档测试_kv_mean'
model_id = '中国人寿nlp跨文档测试_kv_2'
model_id = '中国人寿nlp跨文档测试_400_mean'
# model_id = '中国人寿nlp跨文档测试_400_2'
# model_id = '中国人寿nlp跨文档测试_kv'
# model_id = '中国人寿nlp跨文档测试_kv_xiaobu-100w-batch256-step2_ck793'
model_id = '中国人寿nlp跨文档测试_400_xiaobu-57w-batch128_ck976'
model_id = '中国人寿nlp跨文档测试_kv'


# model_id = '0715_model25kv'#kv_分总_pdf_parse_old
# model_id = '中国人寿nlp跨文档测试_400_xiaobu-law-filechat-23w-batch2_ck671'
# model_id = '中国人寿nlp跨文档测试_400_m3-law-filechat-44w-refined_ck872'
#
# model_id = '中国人寿nlp跨文档测试_kv_voyage-3'
# model_id = '中国人寿nlp跨文档测试_400_voyage-3'
#
# model_id = '中国人寿nlp跨文档测试_400_c'

# model_id = '中国人寿nlp跨文档测试_model25kv_ck1552'
# model_id = '中国人寿nlp跨文档测试_kv_ck1552_2'

# model_id = '中国人寿nlp跨文档测试_kv'
# model_save_dir = rf'/data/servetest/NLP_Model/{model_id}/Retrieval'
# setting.FILECHAT_EMBEDDING_MODEL_NAME = 'm3-law-filechat-116w_ck2562'
# setting.FILECHAT_EMBEDDING_MODEL_NAME = 'm3-law-filechat-135w_ck1098'


# embedding_model_list = ["xiaobu-21w-batch4_ck12000","xiaobu-57w-ck976-mix-ori_8_2", "xiaobu-21w-ck12000-mix-ori_8_2","xiaobu-57w-batch128_ck976"]
# embedding_model_list = ['xiaobu-57w-ck366','xiaobu-57w-ck366-mix-ori_8_2']
# setting.FILECHAT_EMBEDDING_MODEL_NAME = '0523_people_gptlabel_gptgen_keywordFB_doc_gptgenq_gptextq_gptsum_gptans_filechat_lawsearch_fineindex_manual_simhash5_score05_pos_batch4_gpu3_epoch1_d0523_273304'
# embedding_model_list = ["m3_GPTGen8w_ck168_mix_ori_8_2","xiaobu_med15w_ck760_mix_ori_8_2","xiaobu_mix3data18w_ck605_mix_ori_8_2","xiaobu_GPTGen4w_ck61_mix_ori_8_2"]
embedding_model_list = ['xiaobu_GPTGen4w_ck61_mix_ori_8_2']
for embedding_model in embedding_model_list:
    model_id = '中国人寿nlp跨文档测试_kv' + f'_{embedding_model}_second'
    # model_id = '0715_kv' + f'_{embedding_model}'
    print(model_id)
    setting.FEILCHAT_EMBEDDING_URL = 'http://192.168.1.238:6013/embeddings'
    # model_id += '_' + setting.FILECHAT_EMBEDDING_MODEL_NAME .split('_')[-1]
    setting.FILECHAT_EMBEDDING_MODEL_NAME = embedding_model
    model_save_dir = rf'/data/sammy/serve/NLP_Model/{model_id}/Retrieval'
    # setting.FILECHAT_EMBEDDING_MODEL_NAME = '0523_people_gptlabel_gptgen_keywordFB_doc_gptgenq_gptextq_gptsum_gptans_filechat_lawsearch_fineindex_manual_simhash5_score05_pos_batch4_gpu3_epoch1_d0523_273304'



    # root_path = r'C:\Users\<USER>\Desktop\新建文件夹'
    # model_id = 'test'
    # setting.FILECHAT_EMBEDDING_MODEL_NAME = 'm3-law-filechat-116w_ck2562'
    # model_save_dir = r'C:\Users\<USER>\Desktop\test'

    # root_path = r'C:\Users\<USER>\Desktop\pdf测试\files'
    # model_save_dir = r'C:\Users\<USER>\Desktop\pdf测试'
    from tqdm import tqdm


    filenames = []
    docs = []
    for file in tqdm(os.listdir(root_path)):
        if not file.endswith('.pdf'):
            continue
        filename = os.path.join(root_path, file)
        doc = ultra_extract(filename,True)
        # doc = '\n'.join(doc)
        filenames.append(filename)
        docs.append(doc)


    doc_dicts = []
    for filename, doc in zip(tqdm(filenames), docs):
        # if '伙人、律师简历汇总表' in filename:continue
        sim_answer_dict = {}
        sim_answer_dict["file_name"] = filename.split('/')[-1]
        # sim_answer_dict['file_name'] =  re.findall(r'[\u4e00-\u9fa5]+', sim_answer_dict["file_name"])[0]
        suffix = sim_answer_dict["file_name"].split('.')[-1].lower()

        sim_answer_dict["answer"] = doc
        sim_answer_dict["question"] = ''
        sim_answer_dict["url"] = filename
        sim_answer_dict["sim"] = ['\n'.join(doc)] if suffix not in ['csv', 'xlsx'] else doc
        sim_answer_dict['suffix'] = filename.split('.')[-1].lower()




        if '_kv' in model_id:
            # kv
            from model.FileChatAction.KVextract.ttt import extract_level, old_level_to_text_slide_value, old_level_to_text, \
                text_2_kv

            #
            # check,level_result = extract_level(fit_doc)
            # check['level_result'] = str(level_result)
            # check_path = os.path.join(f'/data/servetest/NLP_Model/中国人寿nlp跨文档测试_kv',sim_answer_dict["file_name"].replace('.pdf','_raw.csv'))
            # acc100的kv
            # check_path = os.path.join(f'/data/sammy/NLP_Model/跨文档/kv_label',
            #                           sim_answer_dict["file_name"].replace('.pdf', '.csv'))
            check_path = os.path.join(f'/data/sammy/serve/NLP_Model/跨文档/kv_label',
                                      sim_answer_dict["file_name"].replace('.pdf', '.csv'))
            check = pd.read_csv(check_path)
            # check.to_csv(check_path,encoding='utf-8-sig')
            kv_text = old_level_to_text(check)


            # ttt_node
            # from model.FileChatAction.KVextract.ttt_node import old_level_to_text
            # # check_path = os.path.join(f'/data/sammy/serve/aicc-nlp-nlu/module/FileChatBot/badcase_check/中国人寿nlp跨文档_result/pdf/kv_分总_pdf_parse_new/',sim_answer_dict["file_name"].replace('.pdf','').strip(),'level_map_check.csv')
            # # check_path = os.path.join(f'/data/sammy/serve/aicc-nlp-nlu/module/FileChatBot/badcase_check/中国人寿nlp跨文档_result/pdf/kv_分总_pdf_parse_old/',sim_answer_dict["file_name"].replace('.pdf','').strip(),'level_map_check.csv')
            # # check_path = '/data/sammy/serve/aicc-nlp-nlu/module/FileChatBot/badcase_check/中国人寿nlp跨文档_result/pdf/kv_分总_pdf_parse_new/国寿个人税收优惠型健康保险（万能型）A款（2016版）/level_map_check.csv'
            # # check_path = os.path.join(f'/data/sammy/serve/aicc-nlp-nlu/module/FileChatBot/badcase_check/0715演示/0715所有文档/kv_分总',sim_answer_dict["file_name"].split('.')[0],'level_map_check.csv')
            # if os.path.exists(check_path):
            #     check = pd.read_csv(check_path)
            #     kv_text = old_level_to_text(check)
            # else:
            #     kv_text = []
            #     print(f'kv文件不存在:{check_path}')

            sim_answer_dict['add'] = kv_text
        doc_dicts.append(sim_answer_dict)

    seg_lens = [400]

    # import json
    # doc_dicts_path = '/data/servetest/aicc-nlp-nlu/module/FileChatBot/badcase_check/跨文档doc_sim_answer.json'
    # doc_dicts = json.load(open(doc_dicts_path,'r'))
    if '_kv' in model_id:
        for index,i in enumerate(doc_dicts):
            # 去除sim和answer
            for _ in ['sim','answer']:
                # if 'xlsx' in i['file_name'] or not i['add']:
                #     continue
                i[_] = []
            filename = re.sub(r'\.\w+$', '', i['file_name']).strip()
            for j in range(len(i['add'])):
                if filename not in i['add'][j]:
                # if True:
                    i['add'][j] =  re.sub(r'_\d{13}', '', filename) +'_' + i['add'][j]
        # if index==1:
        #     break

    #[i for i in doc_dicts if '植德上海officetour' in i['file_name']]
    # 1+'1'
    model.train(model_id, doc_dicts, model_save_dir=model_save_dir, seg_lens=seg_lens)
    print(model_id)
# model.load_model(model_id, model_save_dir)
# model.predict(model_id, '如果我加班到0:20，第二天几点到', 3)





