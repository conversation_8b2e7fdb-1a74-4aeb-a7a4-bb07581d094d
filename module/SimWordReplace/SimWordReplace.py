# -*- coding: utf-8 -*-
import re
import time
import threading
from database.REDIS import REDIS
from setting import logger
import json


class SimWordReplace:
    def __init__(self):
        self.data_key = "similar_word_lib"
        self.redis = REDIS()
        self.stand2sim_dict = {}
        self.thread_lock = threading.Lock()
        self.thread = threading.Thread(target=self.get_stand2sim)
        self.thread.start()

    def get_stand2sim(self):
        while True:
            try:
                logger.debug(f"获取相似词线程开始")
                redis_db = self.redis.get_redis()
                enterprise_key_list = redis_db.hkeys(self.data_key)
                logger.debug(f"获取相似词线程,enterprise_key_list:{enterprise_key_list}")
                stand2sim_dict_temp = {}
                for key in enterprise_key_list:
                    enterprise_data = redis_db.hget(self.data_key, key)
                    enterprise_data = json.loads(enterprise_data)
                    stand2sim = {}
                    for item in enterprise_data:
                        stand_word = item.get("standard", "").strip()
                        similar_words = item.get("similar", [])
                        if len(stand_word) == 0 or len(similar_words) == 0:
                            continue
                        stand2sim[stand_word] = []
                        similar_words.append(stand_word)
                        for similar_word in similar_words:
                            if len(similar_word.strip()) == 0:
                                continue
                            similar_word = f"{similar_word.strip().lower().replace('.*', '.*?')}"
                            if similar_word not in stand2sim[stand_word]:
                                stand2sim[stand_word].append(similar_word)
                        stand2sim[stand_word].sort(key=lambda x: -len(x))
                        stand2sim[stand_word] = re.compile("("+'|'.join(stand2sim[stand_word])+")")
                    stand2sim_dict_temp[key] = stand2sim

                # 上锁替换
                self.thread_lock.acquire()
                try:
                    self.stand2sim_dict = stand2sim_dict_temp
                finally:
                    self.thread_lock.release()
                logger.debug(f"获取相似词线程结束")
                time.sleep(60)
            except Exception as e:
                #logger.warning(f"获取相似词线程异常,{e}")
                logger.warning(f"获取相似词线程异常,{e}", exc_info=True)
                time.sleep(60)

    def predict(self, query, enterprise_id=""):
        error_msg = ""
        if isinstance(query, str):
            result_query_list = [query]
        else:
            result_query_list = query
        if len(enterprise_id) == 0:
            return result_query_list, error_msg

        try:
            enterprise_id = f"{enterprise_id}_nl"
            if enterprise_id not in self.stand2sim_dict:
                return result_query_list, f"相似词替换出错,enterprise_id:{enterprise_id},不存在"

            self.thread_lock.acquire()
            try:
                # 查找匹配的词
                match_simwords = []
                stand_word_set = set()
                sim_word_set = set()
                for result_query in result_query_list:  # 兼容多个输入的语句
                    result_query = result_query.lower()  # 转小写
                    # ！规则兼容#号，相似词还没有用到这个功能，暂时没写
                    for stand_word, sim_regexs in self.stand2sim_dict[enterprise_id].items():
                        re_result = sim_regexs.findall(result_query)
                        for r in re_result:
                            sim_word = r.strip()
                            if len(sim_word) and stand_word not in stand_word_set and sim_word not in sim_word_set:
                                stand_word_set.add(stand_word)
                                sim_word_set.add(sim_word)
                                match_simwords.append([r.strip(), stand_word])
                match_simwords.sort(key=lambda x: -len(x[0]))
            finally:
                self.thread_lock.release()

            # 替换
            for index in range(len(result_query_list)):
                for sim_word, stand_word in match_simwords:
                    if sim_word in result_query_list[index]:
                        result_query_list[index] = result_query_list[index].replace(sim_word, stand_word)
            logger.debug(f"相似词替换结束, query: {query}, 返回结果: {result_query_list}")
        except Exception as e:
            error_msg = f"相似词匹配失败, query: {query}, 错误:{e}"
            logger.warning(error_msg)
            return query, error_msg
        return result_query_list, error_msg

if __name__ == "__main__":
    model = SimWordReplace()
    time.sleep(2)

    # 预测
    start = time.time()
    for _ in range(10):
        query = "什麼是重點研發專項資助有限期內"
        result_query, error_msg = model.predict(query=query, enterprise_id="826853126")
        print(result_query, error_msg)
    print(f"耗时: {(time.time()-start)/10}")

    model.stand2sim_dict['92455954_nl'] = {'客车': re.compile('(suv|mpv|小车|轿车|跑车|越野|面包|客车)'),
                                           '货车': re.compile('(货车|货|好的)'),
                                            "所有车":re.compile("(客车|货车)")}
    result_query, error_msg = model.predict(query='好的', enterprise_id="92455954")
    print(result_query, error_msg)


