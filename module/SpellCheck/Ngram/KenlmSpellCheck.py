# -*- coding: UTF-8 -*-

import json
import os,itertools
import pickle

import setting

from database.REDIS import REDIS
from model.Corrector import correct
import kenlm
from model.Kenlm_LMmodel.train_kenlm import train_kenlm,save_kenlm_text
from setting import logger
from utils.my_utils import MyEncoder, complete_match_text, my_cosine_similarity, RankReturnObject


class KenlmSpellCheck:
    def __init__(self):
        self.models = dict()

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        logger.debug(f"准备开始训练 - model_id: {model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        #处理数据
        all_text = self.get_data_generator(data)
        all_text_len = len(all_text)
        #保存txt给kenlm

        save_kenlm_text(all_text,f'{save_dir}/for_kenlm.txt')
        logger.debug(f"数据加载完成 - 训练集数据量: {all_text_len}")

        # 训练模型
        logger.debug(f"开始训练 - model_id: {model_id}")
        train_kenlm(f'{save_dir}/for_kenlm.txt',f'{save_dir}/LM.arpa',f'{save_dir}/LM.klm')


        #保存模型
        with open(os.path.join(save_dir, "all_text.pkl"), 'wb') as f:
            pickle.dump(all_text, f, pickle.HIGHEST_PROTOCOL)

        logger.debug(f"训练成功 - model_id: {model_id}")
        return 1


    def load_model(self, model_id, save_dir):
        try:
            lm = kenlm.Model(f'{save_dir}/LM.klm')

            with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
                all_text = pickle.load(f)


            # START (兼容旧模型) 检查是否有 纠错语言模型 文件，如果不存在要重新生成
            if not os.path.exists(os.path.join(save_dir, "LM.klm")):
                save_kenlm_text(all_text, f'{save_dir}/for_kenlm.txt')
                train_kenlm(f'{save_dir}/for_kenlm.txt', f'{save_dir}/LM.arpa', f'{save_dir}/LM.klm')
            # END (兼容旧模型) 检查是否有 search 文件，如果不存在要重新生成

            self.models[model_id] = dict()
            self.models[model_id]['all_text'] = all_text
            self.models[model_id]['lm'] = lm

            self.predict(model_id=model_id, query="信用卡测试",)
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, query):
        result = ''
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": ""
        }

        if model_id not in self.models:
            try:
                is_exist = self.check_model_file_exist(model_id=model_id)
                if is_exist:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91024"
                    error_dict["error_msg"] = "预测错误: 模型未加载，请重新上线"
                else:
                    error_dict["error_type"] = 1
                    error_dict["error_code"] = "NLU91025"
                    error_dict["error_msg"] = "预测错误: 模型未加载，模型文件缺失，请重新训练"
            except Exception as ee:
                error_dict["error_type"] = 0
                error_dict["error_code"] = "NLU91017"
                error_dict["error_msg"] = f"预测错误: 模型未加载, {ee}"
            logger.error(f"预测失败 [{model_id}], 错误信息: {error_dict['error_msg']}")
            return result, error_dict

        lm = self.models[model_id]['lm']

        if len(query)>setting.MAX_SENTENCE_LEN: #如果超过,直接不纠
            return [query,[]],error_dict
        result = correct(query,lm)
        return result, error_dict

    def get_data(self, data):
        pass

    @staticmethod
    def get_data_generator(data):
        # 整理数据
        title = [i['title'] for i in data]
        labelData = [i['labelData'].split("||") for i in data]
        labelData = list(itertools.chain.from_iterable(labelData))
        all_text = title + labelData
        return  all_text

    def check_model_file_exist(self, model_id):
        model_file_exist = True
        save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{self.__class__.__name__}/")

        try:
            with open(os.path.join(save_dir, "all_text.pkl"), 'rb') as f:
                all_text = pickle.load(f)
            lm = kenlm.Model(f'{save_dir}/LM.klm')
        except:
            model_file_exist = False
        return model_file_exist

        # num_batches: 300, epoch: 2, time: 1669.3783600330353  ratio=80/300/2=3
        # num_batches: 118, epoch: 2, time: 415.29477167129517  ratio=450/118/2=1.9067796610169492
        # num_batches: 170, epoch: 2, time: 623.330991268158    ratio=650/170/2=1.911764705882353

if __name__ == "__main__":
    import time

    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    ranker = KenlmSpellCheck()
    R = REDIS()
    data_faq = R.get_data(f'faq_model1_all')

    # 训练 faq 模型
    start = time.time()
    model_id = "optimization"
    model_save_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{ranker.__class__.__name__}/")
    # score = ranker.train(model_id=model_id, data=data_faq, save_dir=model_save_dir)
    # print(f"训练得分: {score}, 耗时: {time.time()-start}")

    # # 加载模型
    ranker.load_model(model_id=model_id, save_dir=model_save_dir)
    #
    # # search faq
    query_list = ['障碍是牛熊证', '我想开通信拥卡','浙江金融社爸卡换卡','ATM是不是坏了']
    for query in query_list:
        texts = ranker.predict(model_id=model_id, query=query)[0]
        print(query,texts)

    #
    # # # search labeling
    # # labeling_data = R.get_data(f'faq_labeling_test_data')
    # # texts = []
    # # text_ids = []
    # # for data_dict in labeling_data:
    # #     texts.append(data_dict["text"])
    # #     text_ids.append(data_dict["id"])
    # # label_result = ranker.labeling(model_id=model_id, texts=texts, text_ids=text_ids)
    # # print(label_result)
    # #
    # # # 测试1 粗排 top_k 准确率
    # # ranker.test_acc(model_id=model_id, data=data_faq, topk=5, model_save_dir=model_save_dir)
    # #
    # # # 测试阈值
    # # data_chat = R.get_data(f'faq_chat_model1_all')
    # # ranker.test_threshold(model_id=model_id, data_pos=data_faq, data_neg=data_chat, model_save_dir=model_save_dir)
