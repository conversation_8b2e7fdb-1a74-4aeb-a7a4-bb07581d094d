# -*- coding: UTF-8 -*-
import json
import os
import sys

# MAIN_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
# sys.path.append(MAIN_DIR)

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from transformers import BertTokenizer, BertConfig, TFBertForSequenceClassification
import numpy as np
from setting import logger
from utils.onnx_utils import create_session, inference_with_onnx, convert_tf2onnx
from utils.my_utils import MyEncoder
from tqdm import tqdm


class BertTextPairSimPointPredict:
    def __init__(self):
        self.pretrain_model_id = "baike_v3_100000_transv3_eda0.9_simlength_removelastsymbol_shortsample_0722"
        self.threshold = 0.1
        
        # self.pretrain_model_id = "baike_zhenyue_1429212_eda0.9_simlength_removelastsymbol_shortsample_231127"
        # self.threshold = 0.1
        
        self.save_dir = os.path.join(setting.MAIN_DIR, "module/TextPairSim/BertTextPairSimPoint/model")
        self.pretrain_models = {}
        self.models = {}
        self.max_len = 512
        self.batch_size = 20
        self.load_pretrain_model(self.pretrain_model_id)

    @staticmethod
    def get_train_data(data):
        norm_point_to_all_points = {}

        for data_dict in data:
            score_data_point = data_dict.get("scoreDataPoint", [])
            if isinstance(score_data_point, str):
                score_data_point = score_data_point.strip()
                score_data_point_new = []
                for p in score_data_point.split("||"):
                    p = p.strip()
                    if len(p):
                        score_data_point_new.append([p])
                score_data_point = score_data_point_new
            else:
                score_data_point_new = []
                for point in score_data_point:
                    if point is None:
                        continue
                    temp_point_list = []
                    for p in point.split("||"):
                        p = p.strip()
                        if len(p) and p not in temp_point_list:
                            temp_point_list.append(p)
                    if len(temp_point_list):
                        score_data_point_new.append(temp_point_list)
                score_data_point = score_data_point_new
            if len(score_data_point) == 0:
                continue

            text_norm_points = []
            for temp_point_list in score_data_point:
                text_norm_points.append(temp_point_list[0])
                for temp_point in temp_point_list:
                    norm_point_to_all_points[temp_point] = temp_point_list

        return norm_point_to_all_points

    def train(self, model_id, data, save_dir):
        logger.debug(f"准备开始训练, model_id:{model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载数据
        norm_point_to_all_points = self.get_train_data(data=data)

        # 保存模型
        with open(os.path.join(save_dir, 'norm_point_to_all_points.json'), 'w', encoding='utf-8') as f:
            json.dump(norm_point_to_all_points, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        logger.debug(f"保存模型成功")

    def load_model(self, model_id, save_dir):
        try:
            if os.path.exists(os.path.join(save_dir, "norm_point_to_all_points.json")):
                with open(os.path.join(save_dir, 'norm_point_to_all_points.json'), 'r', encoding='utf-8') as f:
                    norm_point_to_all_points = json.load(f)
            else:
                norm_point_to_all_points = {}
            self.models[model_id] = dict()
            self.models[model_id]['norm_point_to_all_points'] = norm_point_to_all_points
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def load_pretrain_model(self, pretrain_model_id):
        try:
            model_save_dir = os.path.join(self.save_dir, f"{pretrain_model_id}")
            tokenizer = BertTokenizer.from_pretrained(model_save_dir)
            config = BertConfig.from_pretrained(model_save_dir)
            model = TFBertForSequenceClassification(config=config)
            inputs = tokenizer.encode_plus("测试一下", add_special_tokens=True, max_length=self.max_len,
                                           padding="max_length", truncation=True, return_tensors="tf")
            model(inputs)
            model.load_weights(os.path.join(model_save_dir, 'best_model.h5'))
            onnx_model_path = os.path.join(model_save_dir, f"best_model_{os.getpid()}.onnx")
            convert_tf2onnx(model, onnx_model_path, input_signature=None, enable_overwrite=True)
            session = create_session(onnx_model_path=onnx_model_path)

            self.pretrain_models[pretrain_model_id] = dict()
            self.pretrain_models[pretrain_model_id]['tokenizer'] = tokenizer
            self.pretrain_models[pretrain_model_id]['config'] = config
            self.pretrain_models[pretrain_model_id]['model'] = model
            self.pretrain_models[pretrain_model_id]['session'] = session
        except Exception as e:
            logger.error(f"加载预训练模型失败 - pretrain_model_id: {pretrain_model_id}, 错误信息: {e}")
            # raise Exception(f"加载预训练模型失败 - pretrain_model_id: {pretrain_model_id}, 错误信息: {e}")

    @staticmethod
    def remove_last_symbol(text):
        # return text

        # new_text = text
        # all_symbol = '，。？！：；,.?!:;、()（）[]【】{}{}\n.'
        # for s in all_symbol:
        #     new_text = new_text.replace(s, "")
        # return new_text

        try:
            new_text = text
            for _ in range(10):
                if new_text[-1] in '，。？！：；,.?!:;、':
                    new_text = new_text[:-1]
                else:
                    return new_text
            return new_text
        except:
            return text

    def predict(self, model_id, query, point_list):
        if self.pretrain_model_id not in self.pretrain_models:
            logger.error(f"预测错误, 预训练模型未加载 - pretrain_model_id: {self.pretrain_model_id}, query: {query}, point_list: {point_list}")
            raise Exception(f"预测错误, 预训练模型未加载 - pretrain_model_id: {self.pretrain_model_id}")

        if query is None or query == "":
            return [0] * len(point_list), self.threshold
        if point_list is None or len(point_list) == 0:
            return [0], self.threshold

        tokenizer = self.pretrain_models[self.pretrain_model_id]['tokenizer']
        model = self.pretrain_models[self.pretrain_model_id]['model']
        session = self.pretrain_models[self.pretrain_model_id]['session']

        query = self.remove_last_symbol(query)
        max_len_index = int(np.argmax([len(p) for p in point_list]))
        inputs = tokenizer.encode_plus(point_list[max_len_index], query, add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)
        cur_max_len = np.sum(np.array(inputs["input_ids"]) != 0)

        input_ids = np.zeros(shape=(len(point_list), cur_max_len), dtype=np.int32)
        token_type_ids = np.zeros(shape=(len(point_list), cur_max_len), dtype=np.int32)
        attention_mask = np.zeros(shape=(len(point_list), cur_max_len), dtype=np.int32)

        for i, p in enumerate(point_list):
            inputs = tokenizer.encode_plus(p, query, add_special_tokens=True, max_length=cur_max_len, padding="max_length", truncation=True)
            input_ids[i, :] = inputs["input_ids"]
            attention_mask[i, :] = inputs["attention_mask"]
            token_type_ids[i, :] = inputs["token_type_ids"]

        inputs = {
            'input_ids': input_ids,
            'token_type_ids': token_type_ids,
            'attention_mask': attention_mask
        }
        probs = inference_with_onnx(session, inputs)[0]
        # probs = model(inputs, training=False)[0]
        probs = tf.nn.softmax(probs)
        probs = probs.numpy()[:, 1]
        probs = probs.tolist()

        return probs, self.threshold

    # 下面的代码是默认Go只传标准要点的
    # def predict(self, model_id, query, point_list):
    #     threshold = 0.5
    #     if self.pretrain_model_id not in self.pretrain_models:
    #         logger.error(f"预测错误, 预训练模型未加载 - pretrain_model_id: {self.pretrain_model_id}, query: {query}, point_list: {point_list}")
    #         raise Exception(f"预测错误, 预训练模型未加载 - pretrain_model_id: {self.pretrain_model_id}")
    #     if model_id not in self.models:
    #         logger.error(f"预测错误, 模型未加载 - model_id: {model_id}, query: {query}, point_list: {point_list}")
    #         raise Exception(f"预测错误, 模型未加载 - model_id: {model_id}")
    #
    #     if query is None or query == "":
    #         return [0] * len(point_list), threshold
    #     if point_list is None or len(point_list) == 0:
    #         return [0], threshold
    #
    #     tokenizer = self.pretrain_models[self.pretrain_model_id]['tokenizer']
    #     model = self.pretrain_models[self.pretrain_model_id]['model']
    #     session = self.pretrain_models[self.pretrain_model_id]['session']
    #     norm_point_to_all_points = self.models[model_id]['norm_point_to_all_points']
    #
    #     query = self.remove_last_symbol(query)
    #     point_dict = {}
    #     sim_point_list = []
    #     for p in point_list:
    #         point_dict[p] = {}
    #         sim_points = norm_point_to_all_points.get(p.strip(), [p.strip()])
    #         point_dict[p]["sim_points"] = [self.remove_last_symbol(s_p) for s_p in sim_points]
    #         point_dict[p]["sim_points_scores"] = [0] * len(sim_points)
    #         sim_point_list.extend(point_dict[p]["sim_points"])
    #
    #     max_len_index = int(np.argmax([len(p) for p in sim_point_list]))
    #     inputs = tokenizer.encode_plus(sim_point_list[max_len_index], query, add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)
    #     cur_max_len = np.sum(np.array(inputs["input_ids"]) != 0)
    #
    #     input_ids = np.zeros(shape=(len(sim_point_list), cur_max_len), dtype=np.int32)
    #     token_type_ids = np.zeros(shape=(len(sim_point_list), cur_max_len), dtype=np.int32)
    #     attention_mask = np.zeros(shape=(len(sim_point_list), cur_max_len), dtype=np.int32)
    #
    #     for i, p in enumerate(sim_point_list):
    #         inputs = tokenizer.encode_plus(p, query, add_special_tokens=True, max_length=cur_max_len, padding="max_length", truncation=True)
    #         input_ids[i, :] = inputs["input_ids"]
    #         attention_mask[i, :] = inputs["attention_mask"]
    #         token_type_ids[i, :] = inputs["token_type_ids"]
    #
    #     inputs = {
    #         'input_ids': input_ids,
    #         'token_type_ids': token_type_ids,
    #         'attention_mask': attention_mask
    #     }
    #     probs = inference_with_onnx(session, inputs)[0]
    #     # probs = model(inputs, training=False)[0]
    #     probs = tf.nn.softmax(probs)
    #     probs = probs.numpy()[:, 1]
    #     probs = probs.tolist()
    #
    #     for p_key in point_list:
    #         temp_length = len(point_dict[p_key]["sim_points"])
    #         assert len(point_dict[p_key]["sim_points_scores"]) == len(probs[:temp_length])
    #         point_dict[p_key]["sim_points_scores"] = probs[:temp_length]
    #         probs = probs[temp_length:]
    #
    #     # 返回结果
    #     scores = []
    #     for p in point_list:
    #         scores.append(max(point_dict[p]["sim_points_scores"]))
    #
    #     return scores, 0.1

    def my_test(self):
        import pandas as pd

        data = pd.read_csv(f"{setting.MAIN_DIR}/module/TextPairSim/BertTextPairSimPoint/测试数据.csv", index_col=False, encoding="gbk")
        short_texts = data["short_text"].tolist()
        answer_texts = data["text"].tolist()
        labels = data["label"].tolist()
        scores = []
        for i in tqdm(range(len(short_texts))):
            score, _ = self.predict(model_id="", query=answer_texts[i], point_list=[short_texts[i]])
            scores.append(score)

        threshold_acc_list = []
        for temp_threshold in np.arange(0.0, 1, 0.01):
            predict_labels = [1 if s[0] >= temp_threshold else 0 for s in scores]
            right = 0
            for i in range(len(predict_labels)):
                right += int(predict_labels[i]) == int(labels[i])
            threshold_acc_list.append([temp_threshold, right/len(predict_labels)])  
            if temp_threshold == self.threshold:
                print(f"模型:{self.pretrain_model_id}, 阈值:{temp_threshold}, 准确率:{right/len(predict_labels)}")
        threshold_acc_list.sort(key=lambda x: x[1], reverse=True)

        print(f"模型:{self.pretrain_model_id}, 阈值:{threshold_acc_list[0][0]}, 准确率:{threshold_acc_list[0][1]}")
        
        
if __name__ == "__main__":
    from database.REDIS import REDIS
    import time
    import pandas as pd

    model = BertTextPairSimPointPredict()
    Redis = REDIS()

    # query是学员回答，point_list是回答的要点
    scores, threshold = model.predict(model_id="", query="您好，请问要办理什么业务", point_list=["客户您好", "要办啥业务"])
    print(scores)
    scores, threshold = model.predict(model_id="", query="苹果多少钱一斤", point_list=["请问买一斤apple要多少钱？"])
    print(scores)
    scores, threshold = model.predict(model_id="", query="请问买一斤apple要多少钱？", point_list=["苹果多少钱一斤"])
    print(scores)

    # 批量测试
    model.my_test()  
    print("wait")

"""
模型:baike_v3_100000_transv3_eda0.9_simlength_removelastsymbol_shortsample_0722, 阈值:0.1, 准确率:0.9376597836774828
模型:baike_v3_100000_transv3_eda0.9_simlength_removelastsymbol_shortsample_0722, 阈值:0.99, 准确率:0.9838741396263521
"""