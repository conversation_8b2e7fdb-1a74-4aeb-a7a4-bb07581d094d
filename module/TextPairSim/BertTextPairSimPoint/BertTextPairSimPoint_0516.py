# -*- coding: UTF-8 -*-

import json
import os

import setting

os.environ['CUDA_VISIBLE_DEVICES'] = setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.model_selection import train_test_split
from transformers import BertTokenizer, BertConfig, TFBertForSequenceClassification

from module.TextPairSim.BertTextPairSimPoint.DataGenerator import DataGenerator
from setting import logger
from utils.my_utils import MyEncoder


class BertTextPairSimPoint:
    def __init__(self):
        self.save_dir = "./model"
        self.models = {}
        self.max_len = 512
        self.batch_size = 20

    def train(self, model_id, data, text2eda=None):
        logger.debug(f"准备开始训练 - model_id: {model_id}")

        # 模型保存地址
        model_save_dir = os.path.join(self.save_dir, f"{model_id}")
        os.makedirs(model_save_dir, exist_ok=True)

        # 加载预训练模型
        config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        model = TFBertForSequenceClassification.from_pretrained(setting.PRETRAIN_BERT_DIR)
        tokenizer.save_pretrained(model_save_dir)
        config.save_pretrained(model_save_dir)

        # 加载数据
        data_train, data_valid = self.get_data_generator(texts=data, text2eda=text2eda, tokenizer=tokenizer)

        # 训练模型
        model_file = os.path.join(model_save_dir, 'best_model.h5')
        opt = tf.keras.optimizers.Adam(learning_rate=8e-5, epsilon=1e-08)
        loss = tf.keras.losses.CategoricalCrossentropy(from_logits=True)
        metrics = [tf.keras.metrics.CategoricalAccuracy("accuracy")]
        model.compile(optimizer=opt, loss=loss, metrics=metrics)
        callbacks = [
            tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_accuracy', save_best_only=True, save_weights_only=True),
            # tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=10),
            # tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=5, verbose=0), # 当 patience 次迭代损失未改善，Keras停止训练
        ]
        model.compile(optimizer=opt, loss=loss, metrics=metrics)

        history = model.fit(
            data_train,
            epochs=20,
            validation_data=data_valid,
            callbacks=callbacks
        )
        model.load_weights(model_file)

        # 保存模型
        with open(os.path.join(model_save_dir, 'train_history.json'), 'w', encoding='utf-8') as f:
            json.dump(history.history, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

        # self.models[model_id] = dict()
        # self.models[model_id]['tokenizer'] = tokenizer
        # self.models[model_id]['model'] = model
        #
        # logger.debug(f"训练成功 - model_id: {model_id}")
        #
        # best_idx = np.argmax(history.history["val_sim_auc"])
        # return {
        #     "acc": history.history["val_sim_acc"][best_idx],
        #     "auc": history.history["val_sim_auc"][best_idx],
        #     "precision": history.history["val_sim_pre"][best_idx],
        #     "recall": history.history["val_sim_rec"][best_idx],
        #     "f1": history.history["val_sim_f1"][best_idx]
        # }

    def load_model(self, model_id):
        try:
            model_save_dir = os.path.join(self.save_dir, f"{model_id}")
            tokenizer = BertTokenizer.from_pretrained(model_save_dir)
            config = BertConfig.from_pretrained(model_save_dir)
            model = TFBertForSequenceClassification(config=config)
            inputs = tokenizer.encode_plus("测试一下", add_special_tokens=True, max_length=self.max_len,
                                           padding="max_length", truncation=True, return_tensors="tf")
            model(inputs)
            model.load_weights(os.path.join(model_save_dir, 'best_model.h5'))

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['config'] = config
            self.models[model_id]['model'] = model
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, text_point, text_answer):
        if model_id not in self.models:
            logger.error(f"预测错误, 模型未加载 - model_id: {model_id}, text_point: {text_point}, text_answer: {text_answer}")
            raise Exception(f"预测错误, 模型未加载 - model_id: {model_id}")

        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        inputs = tokenizer.encode_plus(text_point, text_answer, add_special_tokens=True, max_length=self.max_len,
                                       padding="max_length", truncation=True, return_tensors="tf")
        probs = model(inputs, training=False)
        probs = tf.nn.softmax(probs[0])
        probs = probs.numpy()[0][1]
        return probs

    def get_data(self, data):
        pass

    def get_data_generator(self, texts, text2eda, tokenizer=None):
        # 划分数据集
        text_list = list(texts.keys())
        texts_train, texts_valid = train_test_split(text_list, test_size=0.2, random_state=setting.SEED)

        # # 找相近负样本
        # corpos_train = [' '.join(list(t)) for t in texts_train]
        # tfidf_vectorizer = TfidfVectorizer(token_pattern=r"(?u)\b\w+\b")
        # tfidf_train = tfidf_vectorizer.fit_transform(corpos_train)
        # texts_train_neg_samples = {}
        # for i in tqdm(range(tfidf_train.shape[0])):
        #     texts_train_neg_samples[texts_train[i]] = []
        #     sim_scores = cosine_similarity(tfidf_train[i:i+1, :], tfidf_train)
        #     sim_scores = sim_scores[0, :]
        #     sim_scores[i] = -100
        #     index = sim_scores.argsort()[::-1]
        #     for idx in index[:10]:
        #         texts_train_neg_samples[texts_train[i]].append(texts_train[idx])
        #
        # corpos_valid = [' '.join(list(t)) for t in texts_valid]
        # tfidf_vectorizer = TfidfVectorizer(token_pattern=r"(?u)\b\w+\b")
        # tfidf_valid = tfidf_vectorizer.fit_transform(corpos_valid)
        # texts_valid_neg_samples = {}
        # for i in tqdm(range(tfidf_valid.shape[0])):
        #     texts_valid_neg_samples[texts_valid[i]] = []
        #     sim_scores = cosine_similarity(tfidf_valid[i:i+1, :], tfidf_valid)
        #     sim_scores = sim_scores[0, :]
        #     sim_scores[i] = -100
        #     index = sim_scores.argsort()[::-1]
        #     for idx in index[:10]:
        #         texts_valid_neg_samples[texts_valid[i]].append(texts_valid[idx])

        data_gen_train = DataGenerator(text_list=texts_train, text2subtext_trans=texts, text2eda=text2eda, texts_neg_samples=None, tokenizer=tokenizer, batch_size=self.batch_size, max_len=self.max_len)
        data_gen_valid = DataGenerator(text_list=texts_valid, text2subtext_trans=texts, text2eda=text2eda, texts_neg_samples=None, tokenizer=tokenizer, batch_size=self.batch_size, max_len=self.max_len)
        return data_gen_train, data_gen_valid

    def my_test(self, model_id):
        import pandas as pd

        data = pd.read_csv("../../../data/对练话术/要点提取.csv", index_col=False)
        short_texts = data["short_text"].tolist()
        answer_texts = data["text"].tolist()
        labels = data["label"].tolist()
        predict_labels = []
        scores = []
        for i in range(len(short_texts)):
            score = self.predict(model_id=model_id, text_point=short_texts[i], text_answer=answer_texts[i])
            if score >= 0.5:
                predict_labels.append(1)
            else:
                predict_labels.append(0)
            scores.append(score)

        model_save_dir = os.path.join(self.save_dir, f"{model_id}")
        data_predict = {"point_text": short_texts, "answer_text": answer_texts, "label": labels, "predict_label": predict_labels, "score": scores}
        data_predict = pd.DataFrame(data_predict)
        data_predict.to_csv(os.path.join(model_save_dir, "pred_test.csv"), encoding="utf-8-sig")
        right = 0
        for i in range(len(predict_labels)):
            right += predict_labels[i] == labels[i]
        print(f"准确率: {right/len(predict_labels)}")

if __name__ == "__main__":
    model = BertTextPairSimPoint()
    model_id = "baike_v3_100000_transv3_eda_simlength_removelastsymbol_shortsample" # trans eda simnegtext

    # 训练
    with open("./text2subtext_trans_100000_trans_v3.json", "r", encoding="utf-8") as f:
        text2subtext_trans = json.load(f)
    with open(f"./text2eda_100000_remove_space.json", "r", encoding="utf-8") as f:
        text2eda = json.load(f)
    model.train(model_id=model_id, data=text2subtext_trans, text2eda=text2eda)

    # 加载模型
    model.load_model(model_id=model_id)

    # 测试
    model.my_test(model_id=model_id)
    print("wait")

    # 预测
    text_list = ["R7的电机技术获国家科学技术创新奖，该奖项是国家技术创新最高奖项",
                 "我们拥有全国最好的技术，过去也获得了大量国家奖项",
                 "您好我们的技术是国家领先的，曾经获得过多个国家奖励",
                 "小明写的作文获国家文学创新奖，该奖项是文学领域最高奖项",
                 "R7的电机技术获国家科学技术创新奖，V2X就是车子跟万物互联，可以提前预知道路情况，并计算出最优的驾驶操作",
                 "可以提前预知道路情况，并计算出最优的驾驶操作"]
    text_answer = "您好我们的技术是国家领先的，曾经获得过多个国家奖励，最厉害的就是我们的电机，效率高达百分之九十七，今年新出的电车比之前的燃油车厉害了好几个量级"
    for text_point in text_list:
        score = model.predict(model_id=model_id, text_point=text_point, text_answer=text_answer)
        print(f"{text_point}: {score}")
    print("wait")
