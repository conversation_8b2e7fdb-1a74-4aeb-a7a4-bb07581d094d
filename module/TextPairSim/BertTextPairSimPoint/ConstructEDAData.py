#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:ConstructEDAData.py
@time:2022/05/05
"""

import json
import threading
import time
from tqdm import tqdm
import random

with open(f"text2subtext_trans_855530_trans_v4.json", "r", encoding="utf-8") as f:
    text2subtext_trans = json.load(f)

# 需要多线程运行的函数
text2eda = {}
def fun(thread_id, text_list):
    from utils.EDA import eda
    start_time = time.time()
    if thread_id == 0:
        for i, text in tqdm(enumerate(text_list)):
            text2eda[text] = []
            random.shuffle(text2subtext_trans[text])
            for subtext_list in text2subtext_trans[text][:5]:
                eda_list = eda(subtext_list[0], num_aug=8)
                text2eda[text].append(eda_list[:-1])
            if i % 1000 == 0:
                print(f"线程{thread_id}运行了{i}/{len(text_list)}, 耗时:{time.time() - start_time}")
    else:
        for i, text in enumerate(text_list):
            text2eda[text] = []
            random.shuffle(text2subtext_trans[text])
            for subtext_list in text2subtext_trans[text][:5]:
                eda_list = eda(subtext_list[0], num_aug=8)
                text2eda[text].append(eda_list[:-1])
            if i % 1000 == 0:
                print(f"线程{thread_id}运行了{i}/{len(text_list)}, 耗时:{time.time() - start_time}")
    print(f"线程{thread_id}结束")

start_time = time.time()
text_list = text2subtext_trans.keys()
text_list = list(text_list)
num_thread = 20
num_text_per_thread = len(text_list) // num_thread
thread_list = []
for thread_id in range(num_thread):
    if thread_id == num_thread-1:
        t = threading.Thread(target=fun, args=(thread_id, text_list[thread_id*num_text_per_thread:]))
    else:
        t = threading.Thread(target=fun, args=(thread_id, text_list[thread_id*num_text_per_thread:(thread_id+1)*num_text_per_thread]))
    thread_list.append(t)
    t.start()
for t in thread_list:
    t.join()


with open("text2eda_855530_v4.json", "w", encoding="utf-8") as f:
    json.dump(text2eda, f, ensure_ascii=False, indent=2)

with open(f"text2eda_855530_v4.json", "r", encoding="utf-8") as f:
    text2eda = json.load(f)

for text in text2eda.keys():
    for i in range(len(text2eda[text])):
        text2eda[text][i] = [t.replace(" ", "") for t in text2eda[text][i]]

with open("text2eda_855530_remove_space_v4.json", "w", encoding="utf-8") as f:
    json.dump(text2eda, f, ensure_ascii=False, indent=2)

print("wait")
