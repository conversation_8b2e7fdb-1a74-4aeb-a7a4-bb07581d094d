# -*- coding: UTF-8 -*-

import random

import numpy as np
from tensorflow import keras
from faker import Faker

fake = Faker(locale='zh_CN')


class DataGenerator(keras.utils.Sequence):
    def __init__(self, text_list, text2subtext_trans, text2eda, texts_neg_samples, tokenizer, batch_size=64, max_len=300):
        self.text_list = text_list
        self.text2subtext_trans = text2subtext_trans
        self.text2eda = text2eda
        self.texts_neg_samples = texts_neg_samples
        self.tokenizer = tokenizer
        self.batch_size = batch_size
        self.max_len = max_len
        self.trans_dict = {}

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return len(self.text_list)//self.batch_size

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        start_idx = index * self.batch_size
        end_idx = min((index + 1) * self.batch_size, len(self.text_list))
        X, Y = self.__data_generation(self.text_list[start_idx:end_idx])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        pass

    def get_sub_text(self, text):
        try:
            if self.text2eda is not None and text in self.text2eda and random.random() > 0.6:
                # EDA
                if random.random() > 0.99:
                    # 采样长度最接近的
                    best_t = ""
                    best_length = 10000
                    for eda_sentences in self.text2eda[text]:
                        for t in eda_sentences:
                            if abs(len(text)-len(t)) < best_length:
                                best_t = t
                                best_length = abs(len(text)-len(t)) < best_length
                    return best_t
                else:
                    eda_sentences = random.choice(self.text2eda[text])
                    eda_sent = random.choice(eda_sentences)
                    return eda_sent
            else:
                # 互译或者原句
                if random.random() > 0.99:
                    # 采样长度最接近的
                    best_t = ""
                    best_length = 10000
                    for sub_text_list in self.text2subtext_trans[text]:
                        t = sub_text_list[1]
                        if abs(len(text)-len(t)) < best_length:
                            best_t = t
                            best_length = abs(len(text)-len(t)) < best_length
                    return best_t
                else:
                    sub_text_list = random.choice(self.text2subtext_trans[text])
                    return sub_text_list[1]
        except:
            # 互译或者原句
            sub_text_list = random.choice(self.text2subtext_trans[text])
            return sub_text_list[1]

    def get_short_sub_text(self, text):
        # 采样比较短的句子
        all_length = [len(t[0]) for t in self.text2subtext_trans[text]]
        index = np.argsort(all_length)
        if random.random() > 0.7:
            index = index[:max(1, int(len(all_length)*0.1))]
        idx = random.choice(index)
        return self.text2subtext_trans[text][idx][0]

    def get_neg_text(self, text):
        if self.texts_neg_samples is not None and random.random() >= 0.9:
            text2 = random.choice(self.texts_neg_samples[text])
        else:
            text2 = random.choice(self.text_list)
            while text2 == text:
                text2 = random.choice(self.text_list)
        return text2

    @staticmethod
    def remove_last_symbol(text):
        try:
            new_text = text
            for _ in range(10):
                if new_text[-1] in '，。？！：；,.?!:;、':
                    new_text = new_text[:-1]
                else:
                    return new_text
            return new_text
        except:
            return text

    def __data_generation(self, text_list):
        """
        生成 cur_id 的数据。
        """
        input_ids = np.zeros(shape=(len(text_list)*5, self.max_len), dtype=np.int32)
        token_type_ids = np.zeros(shape=(len(text_list)*5, self.max_len), dtype=np.int32)
        attention_mask = np.zeros(shape=(len(text_list)*5, self.max_len), dtype=np.int32)
        labels = np.zeros(shape=(len(text_list)*5, 2))
        index = 0

        for text in text_list:
            # 正样本
            pos_text = self.get_sub_text(text)
            inputs = self.tokenizer.encode_plus(self.remove_last_symbol(pos_text), self.remove_last_symbol(text), add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)
            input_ids[index, :] = inputs["input_ids"]
            attention_mask[index, :] = inputs["attention_mask"]
            token_type_ids[index, :] = inputs["token_type_ids"]
            labels[index, :] = [0, 1]
            index += 1

            # 负样本
            text2 = self.get_neg_text(text)
            neg_text = self.get_sub_text(text2)
            temp_point = random.choice([neg_text, pos_text+neg_text, neg_text+pos_text])
            inputs = self.tokenizer.encode_plus(self.remove_last_symbol(temp_point), self.remove_last_symbol(text), add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)
            input_ids[index, :] = inputs["input_ids"]
            attention_mask[index, :] = inputs["attention_mask"]
            token_type_ids[index, :] = inputs["token_type_ids"]
            labels[index, :] = [1, 0]
            index += 1

            # 短文本样本
            short_sub_text_pos = self.get_short_sub_text(text)
            short_sub_text_neg = self.get_short_sub_text(text2)
            short_sub_text_pos_eda = self.my_eda(short_sub_text_pos)

            if random.random() > 0.5:
                inputs = self.tokenizer.encode_plus(self.remove_last_symbol(short_sub_text_pos), self.remove_last_symbol(short_sub_text_pos_eda), add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)
            else:
                inputs = self.tokenizer.encode_plus(self.remove_last_symbol(short_sub_text_pos), self.remove_last_symbol(text), add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)
            input_ids[index, :] = inputs["input_ids"]
            attention_mask[index, :] = inputs["attention_mask"]
            token_type_ids[index, :] = inputs["token_type_ids"]
            labels[index, :] = [0, 1]
            index += 1

            if random.random() > 0.5:
                inputs = self.tokenizer.encode_plus(self.remove_last_symbol(short_sub_text_pos), self.remove_last_symbol(short_sub_text_neg), add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)
            else:
                inputs = self.tokenizer.encode_plus(self.remove_last_symbol(short_sub_text_pos), self.remove_last_symbol(text2), add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)

            input_ids[index, :] = inputs["input_ids"]
            attention_mask[index, :] = inputs["attention_mask"]
            token_type_ids[index, :] = inputs["token_type_ids"]
            labels[index, :] = [1, 0]
            index += 1

            temp_point = random.choice([
                text,
                text,
                short_sub_text_pos+short_sub_text_neg,
                short_sub_text_neg+short_sub_text_pos,
                short_sub_text_pos_eda+short_sub_text_neg,
                short_sub_text_neg+short_sub_text_pos_eda
            ])
            temp_answer = random.choice([
                short_sub_text_pos_eda,
                short_sub_text_pos
            ])
            inputs = self.tokenizer.encode_plus(self.remove_last_symbol(temp_point), self.remove_last_symbol(temp_answer), add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)
            input_ids[index, :] = inputs["input_ids"]
            attention_mask[index, :] = inputs["attention_mask"]
            token_type_ids[index, :] = inputs["token_type_ids"]
            labels[index, :] = [1, 0]
            index += 1

        return (
            {
                'input_ids': input_ids,
                'token_type_ids': token_type_ids,
                'attention_mask': attention_mask
            },
            labels
        )

    def my_eda(self, text):
        eda_text = text
        if random.random() > 0.5:
            # 删除
            del_length = random.randint(1, max(int(len(eda_text) * 0.1), 1))
            del_idx = random.randint(0, len(eda_text) - del_length)
            eda_text = eda_text[:del_idx] + eda_text[del_idx + del_length:]

        if random.random() > 0.5:
            # 交换
            eda_text = list(eda_text)
            replace_num = random.randint(1, max(int(len(eda_text) * 0.1), 1))
            for _ in range(replace_num):
                idx_a = random.randint(0, len(eda_text) - 1)
                idx_b = random.randint(0, len(eda_text) - 1)
                c = eda_text[idx_a]
                eda_text[idx_a] = eda_text[idx_b]
                eda_text[idx_b] = c
            eda_text = "".join(eda_text)

        if random.random() > 0.5:
            # 插入
            word_num = random.randint(1, max(int(len(eda_text) * 0.1) // 2, 1))
            for _ in range(word_num):
                word = fake.word()
                idx = random.randint(0, len(eda_text))
                eda_text = eda_text[:idx] + word + eda_text[idx:]

        if random.random() > 0.5:
            # 替换单个字
            eda_text = list(eda_text)
            word_num = random.randint(1, max(int(len(eda_text) * 0.1), 1))
            for _ in range(word_num):
                word = fake.word()
                idx = random.randint(0, len(eda_text) - 1)
                eda_text[idx] = word[0]
            eda_text = "".join(eda_text)

        return eda_text

if __name__ == '__main__':
    text = "123,,为,"
    A = DataGenerator.remove_last_symbol(text)
    print("wait")