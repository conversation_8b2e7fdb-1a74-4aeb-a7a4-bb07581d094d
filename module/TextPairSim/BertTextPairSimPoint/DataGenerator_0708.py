# -*- coding: UTF-8 -*-

import random

import numpy as np
from tensorflow import keras
from faker import Faker

fake = Faker(locale='zh_CN')
fake_english = Faker()


class DataGenerator(keras.utils.Sequence):
    def __init__(self, text_list, text2subtext_trans, texts_neg_samples, tokenizer, batch_size=64, max_len=300):
        self.text_list = text_list
        self.text2subtext_trans = text2subtext_trans
        self.texts_neg_samples = texts_neg_samples
        self.tokenizer = tokenizer
        self.batch_size = batch_size
        self.max_len = max_len
        self.trans_dict = {}

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return len(self.text_list)//self.batch_size

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        start_idx = index * self.batch_size
        end_idx = min((index + 1) * self.batch_size, len(self.text_list))
        X, Y = self.__data_generation(self.text_list[start_idx:end_idx])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        pass

    def get_short_sub_text(self, text):
        if random.random() > 0.99:
            # 采样尾部句子
            start_idx = len(text)//10
            end_idx = len(text) - start_idx
            return text[random.randint(start_idx, end_idx):]
        else:
            all_length = [len(t[0]) for t in self.text2subtext_trans[text]]
            index = np.argsort(all_length)
            if random.random() > 0.9:
                # 采样比较短的句子
                index = index[:max(1, int(len(all_length)*0.1))]
            idx = random.choice(index)
            return self.text2subtext_trans[text][idx][0]

    def get_neg_text(self, text):
        text2 = random.choice(self.text_list)
        while text2 == text:
            text2 = random.choice(self.text_list)
        return text2

    @staticmethod
    def remove_last_symbol(text):
        try:
            new_text = text
            for _ in range(10):
                if new_text[-1] in '，。？！：；,.?!:;、()（）[]【】{}{}\n.':
                    new_text = new_text[:-1]
                else:
                    return new_text
            return new_text
        except:
            return text

    def __data_generation(self, text_list):
        """
        生成 cur_id 的数据。
        """
        index = 0
        input_ids = np.zeros(shape=(len(text_list)*5, self.max_len), dtype=np.int32)
        token_type_ids = np.zeros(shape=(len(text_list)*5, self.max_len), dtype=np.int32)
        attention_mask = np.zeros(shape=(len(text_list)*5, self.max_len), dtype=np.int32)
        labels = np.zeros(shape=(len(text_list)*5, 2))
        train_list = []

        for pos_text in text_list:
            neg_text = self.get_neg_text(pos_text)
            pos_sub_text = self.get_short_sub_text(pos_text)
            neg_sub_text = self.get_short_sub_text(neg_text)
            pos_text_eda = self.my_eda(pos_text)
            neg_text_eda = self.my_eda(neg_text)
            pos_sub_text_eda = self.my_eda(pos_sub_text)
            neg_sub_text_eda = self.my_eda(neg_sub_text)

            # 短+长 正样本
            temp_point = random.choice([pos_sub_text, pos_sub_text_eda])
            temp_answer = random.choice([pos_text, pos_text_eda])
            train_list.append([temp_point, temp_answer, 1])

            # 短+长 负样本
            temp_point = random.choice([neg_sub_text, neg_sub_text_eda])
            temp_answer = random.choice([pos_text, pos_text_eda])
            train_list.append([temp_point, temp_answer, 0])

            # 短(混合)+长 负样本
            if random.random() > 0.5:
                temp_point = random.choice([pos_sub_text, pos_sub_text_eda]) + random.choice([neg_sub_text, neg_sub_text_eda])
            else:
                temp_point = random.choice([neg_sub_text, neg_sub_text_eda]) + random.choice([pos_sub_text, pos_sub_text_eda])
            temp_answer = random.choice([pos_text, pos_text_eda])
            train_list.append([temp_point, temp_answer, 0])

            # 短+短 正样本
            temp_point = random.choice([pos_sub_text, pos_sub_text_eda])
            temp_answer = random.choice([pos_sub_text, pos_sub_text_eda])
            train_list.append([temp_point, temp_answer, 1])

            # 短+短 负样本
            temp_point = random.choice([pos_sub_text, pos_sub_text_eda])
            temp_answer = random.choice([neg_sub_text, neg_sub_text_eda])
            if random.random() > 0.9:
                # 取更短的文本
                rand_len = random.randint(1, 6)
                if len(temp_point) > rand_len:
                    start_idx = random.randint(0, len(temp_point)-rand_len)
                    temp_point = temp_point[start_idx:start_idx+rand_len]
                rand_len = random.randint(1, 6)
                if len(temp_answer) > rand_len:
                    start_idx = random.randint(0, len(temp_answer) - rand_len)
                    temp_answer = temp_answer[start_idx:start_idx + rand_len]
            train_list.append([temp_point, temp_answer, 0])

            # 长+长 正样本
            temp_point = random.choice([pos_text, pos_text_eda])
            temp_answer = random.choice([pos_text, pos_text_eda])
            train_list.append([temp_point, temp_answer, 1])

            # 长+长 负样本
            temp_point = random.choice([pos_text, pos_text_eda])
            temp_answer = random.choice([neg_text, neg_text_eda])
            train_list.append([temp_point, temp_answer, 0])

            # 长+短 负样本
            temp_point = random.choice([pos_text, pos_text_eda])
            temp_answer = random.choice([pos_sub_text, pos_sub_text_eda, neg_sub_text, neg_sub_text_eda])
            train_list.append([temp_point, temp_answer, 0])

            # 短(混合)+短 负样本
            if random.random() > 0.5:
                temp_point = random.choice([pos_sub_text, pos_sub_text_eda]) + random.choice([neg_sub_text, neg_sub_text_eda])
            else:
                temp_point = random.choice([neg_sub_text, neg_sub_text_eda]) + random.choice([pos_sub_text, pos_sub_text_eda])
            temp_answer = random.choice([pos_sub_text, pos_sub_text_eda, neg_sub_text, neg_sub_text_eda])
            train_list.append([temp_point, temp_answer, 0])

        random.shuffle(train_list)
        for i in range(len(text_list)*5):
            temp_point = train_list[i][0]
            temp_answer = train_list[i][1]
            temp_label = [0, 1] if train_list[i][2] == 1 else [1, 0]
            inputs = self.tokenizer.encode_plus(self.remove_last_symbol(temp_point), self.remove_last_symbol(temp_answer), add_special_tokens=True, max_length=self.max_len, padding="max_length", truncation=True)
            input_ids[i, :] = inputs["input_ids"]
            attention_mask[i, :] = inputs["attention_mask"]
            token_type_ids[i, :] = inputs["token_type_ids"]
            labels[i, :] = temp_label

        return (
            {
                'input_ids': input_ids,
                'token_type_ids': token_type_ids,
                'attention_mask': attention_mask
            },
            labels
        )

    def my_eda(self, text):
        eda_text = text
        if random.random() > 0.5:
            # 删除
            del_length = random.randint(1, max(int(len(eda_text) * 0.1), 1))
            del_idx = random.randint(0, len(eda_text) - del_length)
            eda_text = eda_text[:del_idx] + eda_text[del_idx + del_length:]

        if random.random() > 0.5:
            # 交换
            eda_text = list(eda_text)
            replace_num = random.randint(1, max(int(len(eda_text) * 0.1), 1))
            for _ in range(replace_num):
                idx_a = random.randint(0, len(eda_text) - 1)
                idx_b = random.randint(0, len(eda_text) - 1)
                c = eda_text[idx_a]
                eda_text[idx_a] = eda_text[idx_b]
                eda_text[idx_b] = c
            eda_text = "".join(eda_text)

        if random.random() > 0.5:
            # 插入
            word_num = random.randint(1, max(int(len(eda_text) * 0.1) // 2, 1))
            for _ in range(word_num):
                word = fake.word()
                idx = random.randint(0, len(eda_text))
                eda_text = eda_text[:idx] + word + eda_text[idx:]

        if random.random() > 0.5:
            # 替换单个字
            eda_text = list(eda_text)
            word_num = random.randint(1, max(int(len(eda_text) * 0.1), 1))
            for _ in range(word_num):
                word = fake.word()
                idx = random.randint(0, len(eda_text) - 1)
                eda_text[idx] = word[0]
            eda_text = "".join(eda_text)

        if random.random() > 0.9:
            # 随机插入符号
            symbol_num = random.randint(1, max(int(len(eda_text) * 0.1) // 2, 1))
            for _ in range(symbol_num):
                symbol = random.choice("，。？！：；,.?!:;、()（）[]【】{}{}\n.")
                idx = random.randint(0, len(eda_text))
                eda_text = eda_text[:idx] + symbol + eda_text[idx:]

        if random.random() > 0.9:
            # 随机插入英文
            word_num = random.randint(1, max(int(len(eda_text) * 0.1) // 2, 1))
            for _ in range(word_num):
                word = fake_english.word()
                idx = random.randint(0, len(eda_text))
                eda_text = eda_text[:idx] + word + eda_text[idx:]

        return eda_text
