#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:ExtractData.py
@time:2022/04/26
"""

import json
import random

import pandas as pd
from tqdm import tqdm
from utils.my_utils import convert_en_char_and_digit_to_normal
from googletrans import Translator
import threading
import time

# # 抽取子句
# def remove_repeat_symbol(text):
#     symbols = "?？!！。，.~"
#     text = text.strip()
#     text = text.replace(" ", "").replace("\t", "")
#     for s in symbols:
#         while f"{s}{s}" in text:
#             text = text.replace(f"{s}{s}", "")
#     return text
# 
# text_list = []
# with open(f"../../../data/对练话术/baike_qa_train.json", "r", encoding="utf-8") as f:
#     for line in f:
#         data = json.loads(line)
#         text = data["answer"].strip().replace("\r", "").replace("\n", "")
#         text = remove_repeat_symbol(text)
#         if len(text) >= 20 and len(text) < 200:
#             text_list.append(text)
# 
# def get_sub_text(text):
#     index = [-1]
#     for i, c in enumerate(text):
#         if c in ",，。？?！!":
#             index.append(i)
# 
#     span_can_use = []
#     for i in range(len(index)):
#         for j in range(i + 1, len(index)):
#             if 50 >= (index[j] - index[i]) >= 2 and (i != 0 or j != len(index) - 1):
#                 span_can_use.append([index[i] + 1, index[j] + 1])
# 
#     sub_text = []
#     if len(span_can_use):
#         for span in span_can_use:
#             sub_text.append(text[span[0]:span[1]])
#     else:
#         for _ in range(2):
#             l = random.randint(10, len(text) - 5)
#             start_idx = random.randint(0, len(text) - l)
#             sub_text.append(text[start_idx:start_idx + l])
#     return sub_text
# 
# text2subtext = {}
# for text in tqdm(text_list):
#     sub_text = get_sub_text(text)
#     text2subtext[text] = sub_text
# 
# with open("text2subtext.json", "w", encoding="utf-8") as f:
#     json.dump(text2subtext, f)
# 
# # 写入初始子串未回译数据
# text2subtext_trans = {}
# for text, subtexts in text2subtext.items():
#     text2subtext_trans[text] = []
#     for subt in subtexts:
#         text2subtext_trans[text].append([subt, subt])
# with open(f"text2subtext_trans_{len(text2subtext_trans)}.json", "w", encoding="utf-8") as f:
#     json.dump(text2subtext_trans, f, ensure_ascii=False, indent=2)


# 抽取子句，粤语
def remove_repeat_symbol(text):
    symbols = "?？!！。，.~,."
    text = text.strip()
    for s in symbols:
        while f"{s}{s}" in text:
            text = text.replace(f"{s}{s}", f"{s}")
    text = text.replace("  ", " ")
    text = text.replace("\r", "").replace("\n", "").replace("\t", "")
    return text

text_list = []
for csv_file in ["/data/cbk/NLP_Platform/data/wiki/train.csv", "/data/cbk/NLP_Platform/data/wiki/test.csv"]:
    df = pd.read_csv(csv_file)
    for i in range(df.shape[0]):
        text = remove_repeat_symbol(df["text"][i])
        if len(text) >= 5:
            text_list.append(text)

def get_sub_text(text):
    index = [-1]
    for i, c in enumerate(text):
        if c in ",，。？?！!":
            index.append(i)

    span_can_use = []
    for i in range(len(index)):
        for j in range(i + 1, len(index)):
            if 50 >= (index[j] - index[i]) >= 2 and (i != 0 or j != len(index) - 1):
                span_can_use.append([index[i] + 1, index[j] + 1])

    sub_text = []
    if len(span_can_use):
        for span in span_can_use:
            sub_text.append(text[span[0]:span[1]])
    else:
        if len(text) < 10:
            sub_text.append(text[:len(text)//2])
            sub_text.append(text[len(text)//2:])
        else:
            for _ in range(2):
                l = random.randint(4, len(text) - 4)
                start_idx = random.randint(0, len(text) - l)
                sub_text.append(text[start_idx:start_idx + l])
    return sub_text

text2subtext = {}
for text in tqdm(text_list):
    sub_text = get_sub_text(text)
    text2subtext[text] = sub_text

with open(f"text2subtext_yuewiki_{len(text2subtext)}_1127.json", "w", encoding="utf-8") as f:
    json.dump(text2subtext, f)

# 写入初始子串未回译数据
text2subtext_trans = {}
for text, subtexts in text2subtext.items():
    text2subtext_trans[text] = []
    for subt in subtexts:
        text2subtext_trans[text].append([subt, subt])
with open(f"text2subtext_trans_yuewiki_{len(text2subtext_trans)}_1127.json", "w", encoding="utf-8") as f:
    json.dump(text2subtext_trans, f, ensure_ascii=False, indent=2)

# # 抽取子句，英文wiki数据
# def remove_repeat_symbol(text):
#     symbols = "?？!！。，.~,."
#     text = text.strip()
#     for s in symbols:
#         while f"{s}{s}" in text:
#             text = text.replace(f"{s}{s}", f"{s}")
#     text = text.replace("  ", " ")
#     text = text.replace("\r", "").replace("\n", "")
#     return text
#
# text_list = []
# data_frame = pd.read_csv("/data/cbk/NLP_Platform/data/wiki/English_balence_no45_train.csv")
# for i in tqdm(range(data_frame.shape[0])):
#     try:
#         docs = data_frame["docs"][i]
#         docs = eval(docs)
#         docs = [convert_en_char_and_digit_to_normal(d) for d in docs]
#         docs = [remove_repeat_symbol(d) for d in docs]
#         docs = [d for d in docs if 400 >= len(d) >= 60]
#         random.shuffle(docs)
#         text_list.extend(docs[:3])
#     except Exception as e:
#         print(f"清理数据报错:{e}")
#
# def get_sub_text(text):
#     index = [-1]
#     for i, c in enumerate(text):
#         if c in ",，。？?！!":
#             index.append(i)
#
#     span_can_use = []
#     for i in range(len(index)):
#         for j in range(i + 1, len(index)):
#             if 300 >= (index[j] - index[i]) >= 20 and (i != 0 or j != len(index) - 1):
#                 span_can_use.append([index[i] + 1, index[j] + 1])
#
#     sub_text = []
#     if len(span_can_use):
#         for span in span_can_use:
#             sub_text.append(text[span[0]:span[1]])
#     else:
#         space_idx = [i for i, c in enumerate(text) if c == " " or i == 0]
#         space_idx.append(len(text))
#         if len(space_idx) >= 8:
#             for _ in range(2):
#                 start_idx = random.randint(0, len(space_idx)-4)
#                 end_idx = random.randint(start_idx+3, len(space_idx)-1)
#                 start_idx = space_idx[start_idx]
#                 end_idx = space_idx[end_idx]
#                 sub_text.append(text[start_idx:end_idx].strip())
#         else:
#             mid_idx = space_idx[len(space_idx)//2]
#             sub_text = [text[:mid_idx].strip(), text[mid_idx:].strip()]
#             sub_text = [t for t in sub_text if len(t) > 0]
#     return sub_text
#
# text2subtext = {}
# for text in tqdm(text_list):
#     sub_text = get_sub_text(text)
#     text2subtext[text] = sub_text
# with open(f"text2subtext_enwiki_{len(text2subtext)}_1127.json", "w", encoding="utf-8") as f:
#     json.dump(text2subtext, f)
#
# # 写入初始子串未回译数据
# text2subtext_trans = {}
# for text, subtexts in text2subtext.items():
#     text2subtext_trans[text] = []
#     for subt in subtexts:
#         text2subtext_trans[text].append([subt, subt])
# with open(f"text2subtext_trans_enwiki_{len(text2subtext_trans)}_1127.json", "w", encoding="utf-8") as f:
#     json.dump(text2subtext_trans, f, ensure_ascii=False, indent=2)
#


# """  回译  """
# # 加载回译数据
# with open(f"text2subtext_trans_855530_trans_v3.json", "r", encoding="utf-8") as f:
#     text2subtext_trans = json.load(f)
#
# # 需要多线程运行的函数
# def fun(thread_id, text_list):
#     start_time = time.time()
#     fail_times = 0
#     trans = Translator(service_urls=['translate.google.cn'])
#     for i, text in enumerate(text_list):
#         for j in range(len(text2subtext_trans[text])):
#             if text2subtext_trans[text][j][0] != text2subtext_trans[text][j][1] or fail_times >= 10:
#                 continue
#             if random.random() >= 0.999:
#                 try:
#                     result = trans.translate(text2subtext_trans[text][j][0], dest='en')
#                     result = trans.translate(result.text, dest='zh-cn')
#                     text2subtext_trans[text][j][1] = result.text
#                     print(text2subtext_trans[text][j])
#                 except:
#                     fail_times += 1
#                     if fail_times >= 10:
#                         print(f"线程{thread_id}翻译接口失败了，{fail_times}")
#         if i % 1000 == 0:
#             print(f"线程{thread_id}运行了{i}/{len(text_list)}, 耗时:{time.time()-start_time}")
#         if fail_times >= 10:
#             break
#     print(f"线程{thread_id}结束")
#
# start_time = time.time()
# text_list = text2subtext_trans.keys()
# text_list = list(text_list)
# num_thread = 5
# num_text_per_thread = len(text_list) // num_thread
# thread_list = []
# for thread_id in range(num_thread):
#     if thread_id == num_thread-1:
#         t = threading.Thread(target=fun, args=(thread_id, text_list[thread_id*num_text_per_thread:]))
#     else:
#         t = threading.Thread(target=fun, args=(thread_id, text_list[thread_id*num_text_per_thread:(thread_id+1)*num_text_per_thread]))
#     thread_list.append(t)
#     t.start()s
# for t in thread_list:
#     t.join()
# with open("text2subtext_trans_855530_trans_v4.json", "w", encoding="utf-8") as f:
#     json.dump(text2subtext_trans, f, ensure_ascii=False, indent=2)
# print(f"耗时:{time.time()-start_time}")

# # 单进程#
# start_time = time.time()
# trans = Translator(service_urls=['translate.google.cn'])
# text2subtext_trans = {}
# for text, subtext in tqdm(text2subtext.items()):
#     text2subtext_trans[text] = []
#     for subt in subtext:
#         try:
#             if random.random() >= 0.5:
#                 result = trans.translate(subt, dest='en')
#                 result = trans.translate(result.text, dest='zh-cn')
#                 text2subtext_trans[text].append([subt, result.text])
#             else:
#                 text2subtext_trans[text].append([subt, subt])
#         except:
#             text2subtext_trans[text].append([subt, subt])
# with open("text2subtext_trans.json", "w", encoding="utf-8") as f:
#     json.dump(text2subtext_trans, f)
# print(f"耗时:{time.time()-start_time}")


# 合并中英文数据
with open("./text2subtext_trans_855530_trans_v4.json", "r", encoding="utf-8") as f:
    data_zh = json.load(f)
with open("./text2subtext_trans_enwiki_450592_1127.json", "r", encoding="utf-8") as f:
    data_en = json.load(f)
with open("./text2subtext_trans_yuewiki_123090_1127.json", "r", encoding="utf-8") as f:
    data_yue = json.load(f)
for key in data_en:
    data_zh[key] = data_en[key]
for key in data_yue:
    data_zh[key] = data_yue[key]

with open(f"text2subtext_trans_zhenyue_{len(data_zh)}_1127.json", "w", encoding="utf-8") as f:
    json.dump(data_zh, f, ensure_ascii=False, indent=2)
print("wait")

# 101 文本 10 线程 耗时:185.4991865158081 耗时:155.77428531646729
# 101 文本 1 线程 耗时:996.5300228595734


