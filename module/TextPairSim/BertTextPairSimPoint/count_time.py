#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:count_time.py
@time:2022/05/17
"""

import time
import json
import random
from faker import Faker
import jieba
# import synonyms

fake = Faker(locale='zh_CN')

with open("./text2subtext_trans_100000_trans_v3.json", "r", encoding="utf-8") as f:
    text2subtext_trans = json.load(f)
with open(f"./text2eda_100000_remove_space.json", "r", encoding="utf-8") as f:
    text2eda = json.load(f)
text_list = list(text2subtext_trans.keys())


def my_eda(text):
    eda_text = text
    if random.random() > 0.5:
        # 删除
        del_length = random.randint(1, max(int(len(eda_text) * 0.1), 1))
        del_idx = random.randint(0, len(eda_text) - del_length)
        eda_text = eda_text[:del_idx] + eda_text[del_idx + del_length:]

    if random.random() > 0.5:
        # 交换
        eda_text = list(eda_text)
        replace_num = random.randint(1, max(int(len(eda_text) * 0.1), 1))
        for _ in range(replace_num):
            idx_a = random.randint(0, len(eda_text) - 1)
            idx_b = random.randint(0, len(eda_text) - 1)
            c = eda_text[idx_a]
            eda_text[idx_a] = eda_text[idx_b]
            eda_text[idx_b] = c
        eda_text = "".join(eda_text)

    if random.random() > 0.5:
        # 插入
        word_num = random.randint(1, max(int(len(eda_text) * 0.1) // 2, 1))
        for _ in range(word_num):
            word = fake.word()
            idx = random.randint(0, len(eda_text))
            eda_text = eda_text[:idx] + word + eda_text[idx:]

    if random.random() > 0.5:
        # 替换单个字
        eda_text = list(eda_text)
        word_num = random.randint(1, max(int(len(eda_text) * 0.1), 1))
        for _ in range(word_num):
            word = fake.word()
            idx = random.randint(0, len(eda_text)-1)
            eda_text[idx] = word[0]
        eda_text = "".join(eda_text)

    return eda_text

start_time = time.time()
for i in range(10000):
    text = text_list[i]
    if random.random() > 0.99:
        # 采样长度最接近的
        best_t = ""
        best_length = 10000
        for sub_text_list in text2subtext_trans[text]:
            t = sub_text_list[1]
            if abs(len(text) - len(t)) < best_length:
                best_t = t
                best_length = abs(len(text) - len(t)) < best_length
    else:
        sub_text_list = random.choice(text2subtext_trans[text])
        best_t = sub_text_list[1]
print(f"耗时:{time.time()-start_time}")

start_time = time.time()
for i in range(10000):
    text = text_list[i]
    if text2eda is not None and text in text2eda and random.random() > 0.6:
        # EDA
        if random.random() > 0.99:
            # 采样长度最接近的
            best_t = ""
            best_length = 10000
            for eda_sentences in text2eda[text]:
                for t in eda_sentences:
                    if abs(len(text) - len(t)) < best_length:
                        best_t = t
                        best_length = abs(len(text) - len(t)) < best_length
        else:
            eda_sentences = random.choice(text2eda[text])
            eda_sent = random.choice(eda_sentences)
print(f"耗时:{time.time()-start_time}")


start_time = time.time()
for i in range(10000):
    text = text_list[i]
    word_list = jieba.lcut(text)
    synonyms_words = cnsyn.search(random.choice(word_list), topK=10)
    aug_sentences = my_eda(text)
    # print(text, "  ", aug_sentences)
print(f"耗时:{time.time()-start_time}")

print("wait")

"""
耗时:0.1440114974975586
耗时:0.12078499794006348
my_eda jieba 耗时:2.830677032470703
my_eda jieba synonyms 耗时:824.7290465831757
"""