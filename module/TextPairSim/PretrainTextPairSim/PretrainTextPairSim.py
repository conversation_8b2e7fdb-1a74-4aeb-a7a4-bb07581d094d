from sklearn.metrics.pairwise import cosine_similarity

import setting
from model.TextRepresentationPretrainBert.BertTextRepresentation import BertTextRepresentation
from utils.my_utils import remove_symbol


class PretrainTextPairSim:
    def __init__(self):
        self.model = BertTextRepresentation(model_path=setting.PRETRAIN_BERT_DIR,
                                            emb_layer=setting.PRETRAIN_TEXT_PAIR_SIM_EMB_LAYER,
                                            max_len=setting.PRETRAIN_TEXT_PAIR_SIM_MAX_LEN)

    def predict(self, query, text_list):
        if query is None or query == "":
            return [0]
        if text_list is None or len(text_list) == 0:
            return [0]

        is_full_match = [False] * len(text_list)
        query = remove_symbol(query.strip().lower())
        for i, text in enumerate(text_list):
            text = remove_symbol(text.strip().lower())
            text_list[i] = text
            if text == query:
                is_full_match[i] = True

        embedding = self.model.encode_sentences([query] + text_list)
        query_embedding = embedding[:1, :]
        text_embedding = embedding[1:, :]
        scores = cosine_similarity(query_embedding, text_embedding)
        for i, is_match in enumerate(is_full_match):
            if is_match:
                scores[0, i] = 1

        return scores

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        pass

    def load_model(self, model_id, save_dir):
        pass

    def offline_model(self, model_id):
        pass

    def get_data(self, data):
        pass

    @staticmethod
    def get_data_generator(data, tokenizer=None):
        pass

    def my_test(self, model_id, data):
        pass


if __name__ == "__main__":
    from tqdm import tqdm
    from matplotlib import pyplot as plt
    import random

    model = PretrainTextPairSim()

    # read data
    # text_list = ["万用金款项仅限个人消费，不得用于购房>、理财、股票银证转账、生产经营等投资类领域、也不得偿还债务或通过第三方支付平台转入非消费领域。",
    #              "北京时间6月30日03:00，欧洲杯1/8决赛最后一场，由E组第1的瑞典对阵C组第3的乌克兰。津琴科首开纪录，福斯贝里扳平后在13分钟内2次中柱，"]
    text_list = []
    with open("../../../data/对练话术/data.txt", "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if len(line):
                text_list.append(line)
    scores = []
    for i in tqdm(range(len(text_list))):
        return_scores = model.predict(text_list[i], text_list[i+1:])
        if isinstance(return_scores, list):
            scores.extend(return_scores)
        else:
            if len(return_scores.shape) == 2:
                return_scores = return_scores[0]
            scores.extend(return_scores.tolist())

    plt.figure()
    plt.hist(scores, bins=500)
    plt.xlim(0.4,1)
    plt.show()
    plt.savefig(f"./{setting.PRETRAIN_TEXT_PAIR_SIM_EMB_LAYER}_all.jpg")

    ratio = 0.5
    scores = []
    for i in tqdm(range(len(text_list))):
        for _ in range(4):
            l = int(len(text_list[i]) * ratio)

            start_idx = random.randint(0, len(text_list[i]) - l)
            return_scores = model.predict(text_list[i], [text_list[i][start_idx:start_idx+l]])
            if isinstance(return_scores, list):
                scores.extend(return_scores)
            else:
                if len(return_scores.shape) == 2:
                    return_scores = return_scores[0]
                scores.extend(return_scores.tolist())

    plt.figure()
    plt.hist(scores, bins=500)
    plt.xlim(0.8,1)
    plt.ylim(0,10)
    plt.savefig(f"./{setting.PRETRAIN_TEXT_PAIR_SIM_EMB_LAYER}_{ratio}.jpg")
    plt.show()
    print("wait")