# -*- coding: UTF-8 -*-

import random

import numpy as np
from tensorflow import keras

import setting


class DataGenerator(keras.utils.Sequence):
    def __init__(self, text_list, tokenizer, batch_size, max_len):
        self.text_list = text_list
        self.idx_list = [i for i in range(len(text_list))]
        self.tokenizer = tokenizer
        self.batch_size = batch_size
        self.num_text = len(text_list)
        self.max_len = max_len
        self.num_batches = max(self.num_text//self.batch_size, 1)
        random.seed(setting.SEED)

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return self.num_batches

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        X, Y = self.__data_generation(self.idx_list[index*self.batch_size:(index+1)*self.batch_size])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        random.shuffle(self.idx_list)

    def __data_generation(self, idx_list):
        """
        生成 cur_id 的数据。
        """
        input_ids_1 = np.zeros(shape=(self.batch_size*6, self.max_len), dtype=np.int32)
        token_type_ids_1 = np.zeros(shape=(self.batch_size*6, self.max_len), dtype=np.int32)
        attention_mask_1 = np.zeros(shape=(self.batch_size*6, self.max_len), dtype=np.int32)
        input_ids_2 = np.zeros(shape=(self.batch_size*6, self.max_len), dtype=np.int32)
        token_type_ids_2 = np.zeros(shape=(self.batch_size*6, self.max_len), dtype=np.int32)
        attention_mask_2 = np.zeros(shape=(self.batch_size*6, self.max_len), dtype=np.int32)
        labels = np.zeros(shape=(self.batch_size*6, 1))
        index = 0

        for idx in idx_list:
            # positive
            text_1 = self.text_list[idx]
            text_2 = self.random_cut(text_1, is_negative=False)
            if random.random() > 0.5:
                temp = text_2
                text_2 = text_1
                text_1 = temp
            inputs_1 = self.tokenizer.encode_plus(text_1, add_special_tokens=True, truncation=True, max_length=self.max_len, padding="max_length")
            inputs_2 = self.tokenizer.encode_plus(text_2, add_special_tokens=True, truncation=True, max_length=self.max_len, padding="max_length")
            input_ids_1[index, :] = inputs_1["input_ids"]
            attention_mask_1[index, :] = inputs_1["attention_mask"]
            token_type_ids_1[index, :] = inputs_1["token_type_ids"]
            input_ids_2[index, :] = inputs_2["input_ids"]
            attention_mask_2[index, :] = inputs_2["attention_mask"]
            token_type_ids_2[index, :] = inputs_2["token_type_ids"]
            labels[index, 0] = 1
            index += 1

            # neg cut
            text_1 = self.text_list[idx]
            text_2 = self.random_cut(text_1, is_negative=True)
            if random.random() > 0.5:
                temp = text_2
                text_2 = text_1
                text_1 = temp
            inputs_1 = self.tokenizer.encode_plus(text_1, add_special_tokens=True, truncation=True, max_length=self.max_len, padding="max_length")
            inputs_2 = self.tokenizer.encode_plus(text_2, add_special_tokens=True, truncation=True, max_length=self.max_len, padding="max_length")
            input_ids_1[index, :] = inputs_1["input_ids"]
            attention_mask_1[index, :] = inputs_1["attention_mask"]
            token_type_ids_1[index, :] = inputs_1["token_type_ids"]
            input_ids_2[index, :] = inputs_2["input_ids"]
            attention_mask_2[index, :] = inputs_2["attention_mask"]
            token_type_ids_2[index, :] = inputs_2["token_type_ids"]
            labels[index, 0] = 0
            index += 1

            # neg
            for _ in range(4):
                text_1 = self.text_list[idx]
                idx2 = random.choice(self.idx_list)
                while idx2 == idx:
                    idx2 = random.choice(self.idx_list)
                text_2 = self.text_list[idx2]
                if random.random() > 0.5:
                    temp = text_2
                    text_2 = text_1
                    text_1 = temp
                inputs_1 = self.tokenizer.encode_plus(text_1, add_special_tokens=True, truncation=True, max_length=self.max_len, padding="max_length")
                inputs_2 = self.tokenizer.encode_plus(text_2, add_special_tokens=True, truncation=True, max_length=self.max_len, padding="max_length")
                input_ids_1[index, :] = inputs_1["input_ids"]
                attention_mask_1[index, :] = inputs_1["attention_mask"]
                token_type_ids_1[index, :] = inputs_1["token_type_ids"]
                input_ids_2[index, :] = inputs_2["input_ids"]
                attention_mask_2[index, :] = inputs_2["attention_mask"]
                token_type_ids_2[index, :] = inputs_2["token_type_ids"]
                labels[index, 0] = 0
                index += 1

        return (
            {
                'input_ids_1': input_ids_1,
                'token_type_ids_1': token_type_ids_1,
                'attention_mask_1': attention_mask_1,
                'input_ids_2': input_ids_2,
                'token_type_ids_2': token_type_ids_2,
                'attention_mask_2': attention_mask_2
            },
            labels
        )

    @staticmethod
    def random_cut(text, is_negative):
        if is_negative:
            ratios = [i/100.0 for i in range(30, 71)]
        else:
            ratios = [i/100.0 for i in range(90, 101)]
        l = int(len(text) * random.choice(ratios))
        start_idx = random.randint(0, len(text)-l)
        return text[start_idx:start_idx+l]