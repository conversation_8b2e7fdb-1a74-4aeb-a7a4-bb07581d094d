import json
import os
import random

import numpy as np
from tqdm import tqdm

import setting

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.model_selection import train_test_split
from transformers import BertTokenizer

from database.REDIS import REDIS
from model.SentenceBert.SentenceBert import SentenceBert
from setting import logger
from utils.my_utils import MyEncoder, remove_symbol


class SentBertTextPairSim:
    def __init__(self):
        self.models = dict()
        self.max_len = setting.SENT_BERT_TEXT_PAIR_SIM_MAX_LEN
        self.batch_size = setting.SENT_BERT_TEXT_PAIR_SIM_BATCH_SIZE

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        logger.debug(f"准备开始训练 - model_id: {model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载预训练模型
        model_config = {
            "model_path": setting.PRETRAIN_BERT_DIR,
            "emb_layer": setting.SENT_BERT_TEXT_PAIR_SIM_EMB_LAYER,
            "output_type": setting.SENT_BERT_TEXT_PAIR_SIM_OUTPUT,
            "max_len": self.max_len
        }
        model = SentenceBert(**model_config)
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)

        # 加载数据
        data_train, len_train, data_valid, len_valid = self.get_data_generator(data=data, tokenizer=tokenizer)
        data_train = data_train.shuffle(setting.SEED).batch(self.batch_size)
        data_valid = data_valid.batch(self.batch_size)
        logger.debug(f"数据加载完成 - 训练集数据量: {len_train}, 验证集数据量: {len_valid}")

        # 训练模型
        model_file = os.path.join(save_dir, 'best_model.ckpt')
        model.save_weights(model_file)

        opt = tf.keras.optimizers.Adam(learning_rate=setting.SENT_BERT_TEXT_PAIR_SIM_LEARNING_RATE, epsilon=1e-08)
        loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=False)
        metrics = [tf.keras.metrics.SparseCategoricalAccuracy("acc")]
        callbacks = [
            tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_acc', save_best_only=True),
            tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=3),
            tf.keras.callbacks.EarlyStopping( monitor='val_loss', patience=3, verbose=0)
        ]
        model.compile(optimizer=opt, loss=loss, metrics=metrics)

        logger.debug(f"开始训练 - model_id: {model_id}")
        history = model.fit(
            data_train,
            epochs=setting.SENT_BERT_TEXT_PAIR_SIM_EPOCHS,
            validation_data=data_valid,
            callbacks=callbacks
        )

        with open(os.path.join(save_dir, 'train_history.json'), 'w', encoding='utf-8') as f:
            json.dump(history.history, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, 'model_config.json'), 'w', encoding='utf-8') as f:
            json.dump(model_config, fp=f, ensure_ascii=False, indent=2)

        # self.models[model_id] = dict()
        # self.models[model_id]['model'] = model
        # self.models[model_id]['tokenizer'] = tokenizer

        logger.debug(f"训练成功 - model_id: {model_id}")

        return np.max(history.history["val_acc"])

    def load_model(self, model_id, save_dir):
        try:
            tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
            with open(os.path.join(save_dir, 'model_config.json'), 'r', encoding='utf-8') as f:
                model_config = json.load(f)
                model_config["model_path"] = setting.PRETRAIN_BERT_DIR
            model = SentenceBert(**model_config)
            model.load_weights(os.path.join(save_dir, 'best_model.ckpt')).expect_partial()

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['model'] = model
            self.predict(model_id=model_id, text_list1="测试一下", text_list2="测试一下")
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, text_list1, text_list2):
        """ text_list1 长度要等于 text_list2 """
        if model_id not in self.models:
            logger.error(f"预测错误, 模型未加载 - model_id: {model_id}, text_list1: {text_list1}, text_list2: {text_list2}")
            raise Exception(f"预测错误, 模型未加载 - model_id: {model_id}")

        if isinstance(text_list1, str):
            text_list1 = [text_list1]
        if isinstance(text_list2, str):
            text_list2 = [text_list2]

        if len(text_list1) == 0 or len(text_list2) == 0 or len(text_list1) != len(text_list2):
            logger.error(f"预测错误, 文本列表长度出错: model_id: {model_id}, text_list1: {len(text_list1)}, text_list2: {len(text_list2)}")
            return None

        if len(text_list1) == 1 and len(text_list2) == 1:
            text1 = remove_symbol(text_list1[0].strip())
            text2 = remove_symbol(text_list2[0].strip())
            if text2 == text1:
                return 1

        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        probs = []

        for start_idx in range(0, len(text_list1), self.batch_size):
            end_idx = min(start_idx+self.batch_size, len(text_list1))
            inputs_1 = tokenizer.batch_encode_plus(
                text_list1[start_idx:end_idx],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                return_tensors="tf"
            )
            inputs_2 = tokenizer.batch_encode_plus(
                text_list2[start_idx:end_idx],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                return_tensors="tf"
            )
            features = {
                "input_ids_1": inputs_1["input_ids"],
                "token_type_ids_1": inputs_1["token_type_ids"],
                "attention_mask_1": inputs_1["attention_mask"],
                "input_ids_2": inputs_2["input_ids"],
                "token_type_ids_2": inputs_2["token_type_ids"],
                "attention_mask_2": inputs_2["attention_mask"],
            }
            probs.append(model(features).numpy())
        prob = np.clip(np.vstack(probs), a_min=0, a_max=1)
        return prob[:, 1]

    def get_data(self, data):
        pass

    @staticmethod
    def split_train_valid(data):
        # 划分数据集
        text_list_train = []
        text_list_valid = []
        for data_dict in data:
            norm_query = data_dict['title']
            texts = set()
            texts.add(norm_query)

            for text in data_dict['labelData'].split("||"):
                text_strip = text.strip()
                if len(text_strip):
                    texts.add(text_strip)
            texts = list(texts)

            if len(texts) <= 0:
                continue
            elif len(texts) == 1:
                text_list_train.append(texts)
            else:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                text_list_train.append(texts_train)
                text_list_valid.append(texts_valid)

        return text_list_train, text_list_valid

    def get_data_generator(self, data, tokenizer):
        text_list_train, text_list_valid = self.split_train_valid(data)

        # 生成器
        def get_features(text_list):
            features = []
            for i, texts in enumerate(text_list):
                for text_1 in texts:
                    # 正样本
                    label = 1
                    text_2 = random.choice(texts)
                    inputs_1 = tokenizer.encode_plus(text_1, add_special_tokens=True, max_length=self.max_len, padding="max_length")
                    inputs_2 = tokenizer.encode_plus(text_2, add_special_tokens=True, max_length=self.max_len, padding="max_length")
                    features.append(({
                                         "input_ids_1": inputs_1["input_ids"],
                                         "token_type_ids_1": inputs_1["token_type_ids"],
                                         "attention_mask_1": inputs_1["attention_mask"],
                                         "input_ids_2": inputs_2["input_ids"],
                                         "token_type_ids_2": inputs_2["token_type_ids"],
                                         "attention_mask_2": inputs_2["attention_mask"],
                                     }, label))

                    # 负样本
                    if len(text_list) == 1:
                        continue
                    label = 0
                    for _ in range(5):
                        idx = random.randint(0, len(text_list)-1)
                        while idx == i:
                            idx = random.randint(0, len(text_list)-1)
                        text_2 = random.choice(text_list[idx])
                        inputs_1 = tokenizer.encode_plus(text_1, add_special_tokens=True, max_length=self.max_len, padding="max_length")
                        inputs_2 = tokenizer.encode_plus(text_2, add_special_tokens=True, max_length=self.max_len, padding="max_length")

                        features.append(({
                                             "input_ids_1": inputs_1["input_ids"],
                                             "token_type_ids_1": inputs_1["token_type_ids"],
                                             "attention_mask_1": inputs_1["attention_mask"],
                                             "input_ids_2": inputs_2["input_ids"],
                                             "token_type_ids_2": inputs_2["token_type_ids"],
                                             "attention_mask_2": inputs_2["attention_mask"],
                                         }, label))
            return features

        features_train = get_features(text_list_train)
        features_valid = get_features(text_list_valid)

        # 生成器
        def gen_train():
            for feature in features_train:
                yield feature
        def gen_valid():
            for feature in features_valid:
                yield feature

        return tf.data.Dataset.from_generator(
            gen_train,
            (
                {
                    "input_ids_1": tf.int32, "token_type_ids_1": tf.int32, "attention_mask_1": tf.int32,
                    "input_ids_2": tf.int32, "token_type_ids_2": tf.int32, "attention_mask_2": tf.int32
                },
                tf.int64
            ),
            (
                {
                    "input_ids_1": tf.TensorShape([None]), "token_type_ids_1": tf.TensorShape([None]), "attention_mask_1": tf.TensorShape([None]),
                    "input_ids_2": tf.TensorShape([None]), "token_type_ids_2": tf.TensorShape([None]), "attention_mask_2": tf.TensorShape([None]),
                },
                tf.TensorShape(None),
            )
        ), len(features_train), tf.data.Dataset.from_generator(
            gen_valid,
            (
                {
                    "input_ids_1": tf.int32, "token_type_ids_1": tf.int32, "attention_mask_1": tf.int32,
                    "input_ids_2": tf.int32, "token_type_ids_2": tf.int32, "attention_mask_2": tf.int32
                },
                tf.int64
            ),
            (
                {
                    "input_ids_1": tf.TensorShape([None]), "token_type_ids_1": tf.TensorShape([None]), "attention_mask_1": tf.TensorShape([None]),
                    "input_ids_2": tf.TensorShape([None]), "token_type_ids_2": tf.TensorShape([None]), "attention_mask_2": tf.TensorShape([None])
                },
                tf.TensorShape(None),
            )
        ), len(features_valid)

    def my_test(self, model_id, data):
        import Levenshtein
        random.seed(setting.SEED)
        _, text_list_valid = self.split_train_valid(data)


        # 测试 AUC，随机负采样，正负样本 1:1
        y_true = []
        y_pred = []
        auc_metric = tf.keras.metrics.AUC()
        auc_score = 0
        pbar = tqdm(range(len(text_list_valid)))
        for i in pbar:
            text_list = text_list_valid[i]
            pbar.set_description('测试进度')
            for text1 in text_list:
                # 正样本
                text2 = random.choice(text_list)
                score = self.predict(model_id=model_id, text_list1=text1, text_list2=text2)[0]
                y_pred.append(score)
                y_true.append(1)

                # 负样本
                j = random.randint(0, len(text_list_valid)-1)
                while j == i:
                    j = random.randint(0, len(text_list_valid)-1)
                text2 = random.choice(text_list_valid[j])
                score = self.predict(model_id=model_id, text_list1=text1, text_list2=text2)[0]
                y_pred.append(score)
                y_true.append(0)

                auc_metric.reset_states()
                auc_metric.update_state(y_true=y_true, y_pred=y_pred)
                auc_score = float(auc_metric.result().numpy())
                pbar.set_postfix({"auc": auc_score})
        print(f"随机负采样 1：1 AUC: {auc_score}")


        # 测试 AUC，难样本负采样，正负样本 1:1
        y_true = []
        y_pred = []
        auc_metric = tf.keras.metrics.AUC()
        auc_score = 0
        pbar = tqdm(range(len(text_list_valid)))
        for i in pbar:
            text_list = text_list_valid[i]
            pbar.set_description('测试进度')
            for text1 in text_list:
                # 正样本
                text2 = random.choice(text_list)
                score = self.predict(model_id=model_id, text_list1=text1, text_list2=text2)[0]
                y_pred.append(score)
                y_true.append(1)

                # 负样本
                best_score = 0
                best_neg_text = ''
                for j in range(len(text_list_valid)):
                    if j != i:
                        for neg_text in text_list_valid[j]:
                            neg_score = Levenshtein.ratio(text1, neg_text)
                            if neg_score > best_score:
                                best_score = neg_score
                                best_neg_text = neg_text
                score = self.predict(model_id=model_id, text_list1=text1, text_list2=best_neg_text)[0]
                y_pred.append(score)
                y_true.append(0)

                auc_metric.reset_states()
                auc_metric.update_state(y_true=y_true, y_pred=y_pred)
                auc_score = float(auc_metric.result().numpy())
                pbar.set_postfix({"auc": auc_score})
        print(f"难样本负采样 1：1 AUC: {auc_score}")


        # 测试检索 top1 准确率，正负样本 1:50，当正样本得分最高时才正确
        acc = 0
        total = 0
        neg_samples = 50
        pbar = tqdm(range(len(text_list_valid)))
        for i in pbar:
            text_list = text_list_valid[i]
            pbar.set_description('测试进度')
            for text1 in text_list:
                texts_1 = []
                texts_2 = []

                # 正样本
                pos_text = random.choice(text_list)
                texts_1.append(text1)
                texts_2.append(pos_text)

                # 负样本
                for _ in range(neg_samples):
                    j = random.randint(0, len(text_list_valid)-1)
                    while j == i:
                        j = random.randint(0, len(text_list_valid)-1)
                    neg_text = random.choice(text_list_valid[j])
                    texts_1.append(text1)
                    texts_2.append(neg_text)

                prob = self.predict(model_id=model_id, text_list1=texts_1, text_list2=texts_2)
                if float(prob[0]) > float(np.max(prob[1:])):
                    acc += 1
                total += 1
                pbar.set_postfix({"acc": acc/total})
        print(f"检索 top 1：{neg_samples} ACC: {acc/total}")


if __name__ == "__main__":
    model = SentBertTextPairSim()
    Redis = REDIS()

    model_id = f"sentbert_dense_cls_dataset"
    data_key = "faq_model1_all"
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), model.__class__.__name__)

    # 训练
    data = Redis.get_data(data_key)
    score = model.train(model_id=model_id, data=data, save_dir=save_dir)
    print(f"训练得分: {score}")

    # 加载微调过的模型
    model.load_model(model_id=model_id, save_dir=save_dir)
    # 预测
    text_1 = "有什么办法可以测试文本相似度？"
    text_2 = "文本相似度的测量方法"
    text_3 = "这是一段测试代码"
    print(model.predict(model_id=model_id, text_list1=text_1, text_list2=text_2))
    print(model.predict(model_id=model_id, text_list1=text_2, text_list2=text_3))

    # 测试
    data = Redis.get_data(data_key)
    model.my_test(model_id=model_id, data=data)