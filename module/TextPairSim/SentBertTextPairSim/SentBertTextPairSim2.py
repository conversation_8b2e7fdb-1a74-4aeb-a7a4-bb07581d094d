import json
import os
import random

import numpy as np
from tqdm import tqdm

import setting

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.model_selection import train_test_split
from transformers import BertTokenizer

from database.REDIS import REDIS
from model.SentenceBert.SentenceBert import SentenceBert
from setting import logger
from utils.my_utils import MyEncoder, remove_symbol
from module.TextPairSim.SentBertTextPairSim.DataGenerator import DataGenerator


class SentBertTextPairSim:
    def __init__(self):
        self.models = dict()
        self.max_len = setting.SENT_BERT_TEXT_PAIR_SIM_MAX_LEN
        self.batch_size = setting.SENT_BERT_TEXT_PAIR_SIM_BATCH_SIZE

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        logger.debug(f"准备开始训练 - model_id: {model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载预训练模型
        model_config = {
            "model_path": setting.PRETRAIN_BERT_DIR,
            "emb_layer": setting.SENT_BERT_TEXT_PAIR_SIM_EMB_LAYER,
            "output_type": setting.SENT_BERT_TEXT_PAIR_SIM_OUTPUT,
            "max_len": self.max_len
        }
        model = SentenceBert(**model_config)
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)

        # 加载数据
        data_train, data_valid = self.get_data_generator(data=data, tokenizer=tokenizer)

        # 训练模型
        model_file = os.path.join(save_dir, 'best_model.h5')
        model.save_weights(model_file)

        opt = tf.keras.optimizers.Adam(learning_rate=setting.SENT_BERT_TEXT_PAIR_SIM_LEARNING_RATE, epsilon=1e-08)
        loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=False)
        metrics = [tf.keras.metrics.SparseCategoricalAccuracy("acc")]
        callbacks = [
            tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_acc', save_best_only=True, save_weights_only=True),
            tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=3),
            tf.keras.callbacks.EarlyStopping( monitor='val_loss', patience=3, verbose=0)
        ]
        model.compile(optimizer=opt, loss=loss, metrics=metrics)

        logger.debug(f"开始训练 - model_id: {model_id}")
        history = model.fit(
            data_train,
            epochs=setting.SENT_BERT_TEXT_PAIR_SIM_EPOCHS,
            validation_data=data_valid,
            callbacks=callbacks
        )

        with open(os.path.join(save_dir, 'train_history.json'), 'w', encoding='utf-8') as f:
            json.dump(history.history, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, 'model_config.json'), 'w', encoding='utf-8') as f:
            json.dump(model_config, fp=f, ensure_ascii=False, indent=2)

        self.models[model_id] = dict()
        self.models[model_id]['model'] = model
        self.models[model_id]['tokenizer'] = tokenizer

        logger.debug(f"训练成功 - model_id: {model_id}")

        return np.max(history.history["val_acc"])

    def load_model(self, model_id, save_dir):
        try:
            tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
            with open(os.path.join(save_dir, 'model_config.json'), 'r', encoding='utf-8') as f:
                model_config = json.load(f)
                model_config["model_path"] = setting.PRETRAIN_BERT_DIR
            model = SentenceBert(**model_config)

            inputs_1 = tokenizer.batch_encode_plus(
                ["测试一下"],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                return_tensors="tf"
            )
            inputs_2 = tokenizer.batch_encode_plus(
                ["测试一下"],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                return_tensors="tf"
            )
            features = {
                "input_ids_1": inputs_1["input_ids"],
                "token_type_ids_1": inputs_1["token_type_ids"],
                "attention_mask_1": inputs_1["attention_mask"],
                "input_ids_2": inputs_2["input_ids"],
                "token_type_ids_2": inputs_2["token_type_ids"],
                "attention_mask_2": inputs_2["attention_mask"],
            }
            model(features, training=False)

            # model.load_weights(os.path.join(save_dir, 'best_model.ckpt')).expect_partial()
            model.load_weights(os.path.join(save_dir, 'best_model.h5'))

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['model'] = model
            self.predict(model_id=model_id, text_list1="测试一下", text_list2="测试一下")
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, text_list1, text_list2):
        """ text_list1 长度要等于 text_list2 """
        if model_id not in self.models:
            logger.error(f"预测错误, 模型未加载 - model_id: {model_id}, text_list1: {text_list1}, text_list2: {text_list2}")
            raise Exception(f"预测错误, 模型未加载 - model_id: {model_id}")

        if isinstance(text_list1, str):
            text_list1 = [text_list1]
        if isinstance(text_list2, str):
            text_list2 = [text_list2]

        if len(text_list1) == 0 or len(text_list2) == 0 or len(text_list1) != len(text_list2):
            logger.error(f"预测错误, 文本列表长度出错: model_id: {model_id}, text_list1: {len(text_list1)}, text_list2: {len(text_list2)}")
            return None

        if len(text_list1) == 1 and len(text_list2) == 1:
            text1 = remove_symbol(text_list1[0].strip())
            text2 = remove_symbol(text_list2[0].strip())
            if text2 == text1:
                return 1

        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        probs = []

        for start_idx in range(0, len(text_list1), self.batch_size):
            end_idx = min(start_idx+self.batch_size, len(text_list1))
            inputs_1 = tokenizer.batch_encode_plus(
                text_list1[start_idx:end_idx],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                return_tensors="tf"
            )
            inputs_2 = tokenizer.batch_encode_plus(
                text_list2[start_idx:end_idx],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                return_tensors="tf"
            )
            features = {
                "input_ids_1": inputs_1["input_ids"],
                "token_type_ids_1": inputs_1["token_type_ids"],
                "attention_mask_1": inputs_1["attention_mask"],
                "input_ids_2": inputs_2["input_ids"],
                "token_type_ids_2": inputs_2["token_type_ids"],
                "attention_mask_2": inputs_2["attention_mask"],
            }
            probs.append(model(features, training=False).numpy())
        prob = np.clip(np.vstack(probs), a_min=0, a_max=1)
        return prob[:, 1]

    def get_data(self, data):
        pass

    def get_data_generator(self, data, tokenizer):
        text_list_train, text_list_valid = train_test_split(data, test_size=0.2, random_state=setting.SEED)
        data_gen_train = DataGenerator(text_list=text_list_train, tokenizer=tokenizer, batch_size=self.batch_size, max_len=self.max_len)
        data_gen_valid = DataGenerator(text_list=text_list_valid, tokenizer=tokenizer, batch_size=self.batch_size, max_len=self.max_len)
        return data_gen_train, data_gen_valid


if __name__ == "__main__":
    model = SentBertTextPairSim()
    Redis = REDIS()

    model_id = f"sentbert_dense_baike_temp_6"
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), model.__class__.__name__)

    # load data
    text_list = []
    with open(f"../../../data/对练话术/baike_qa_train.json", "r", encoding="utf-8") as f:
        for line in f:
            data = json.loads(line)
            text = data["answer"].strip().replace("\r", "").replace("\n", "")
            if len(text) >= 20 and len(text) < 300:
                text_list.append(text)
    text_list = text_list[:100000]
    # text_list = text_list[:100]

    # 训练
    score = model.train(model_id=model_id, data=text_list, save_dir=save_dir)
    print(f"训练得分: {score}")

    # 加载微调过的模型
    model.load_model(model_id=model_id, save_dir=save_dir)

    # 预测
    text_1 = "有什么办法可以测试文本相似度？"
    text_2 = "文本相似度的测量方法"
    text_3 = "这是一段测试代码"
    print(model.predict(model_id=model_id, text_list1=text_1, text_list2=text_2))
    print(model.predict(model_id=model_id, text_list1=text_2, text_list2=text_3))

    """ threshold """
    # load test data
    from matplotlib import pyplot as plt
    texts_train, texts_valid = train_test_split(text_list, test_size=0.2, random_state=setting.SEED)
    text_list = texts_valid

    # threshold and accuracy
    ratio = 0.5
    scores_5 = []
    texts_1 = []
    texts_2 = []
    for i in tqdm(range(200)):
        for _ in range(4):
            l = int(len(text_list[i]) * ratio)
            start_idx = random.randint(0, len(text_list[i]) - l)
            texts_1.append(text_list[i])
            texts_2.append(text_list[i][start_idx:start_idx+l])
    return_scores = model.predict(model_id=model_id, text_list1=texts_1, text_list2=texts_2)
    scores_5 = return_scores.tolist()

    ratio = 0.8
    scores_8 = []
    texts_1 = []
    texts_2 = []
    for i in tqdm(range(200)):
        for _ in range(4):
            l = int(len(text_list[i]) * ratio)
            start_idx = random.randint(0, len(text_list[i]) - l)
            texts_1.append(text_list[i])
            texts_2.append(text_list[i][start_idx:start_idx+l])
    return_scores = model.predict(model_id=model_id, text_list1=texts_1, text_list2=texts_2)
    scores_8 = return_scores.tolist()

    scores_5 = np.array(scores_5)
    scores_8 = np.array(scores_8)
    best_threshold = 0
    best_acc = -100
    for threshold_int in range(0, 101):
        threshold = threshold_int / 100.0
        correct = np.sum(scores_5 < threshold) + np.sum(scores_8 >= threshold)
        acc = correct / (scores_5.shape[0] + scores_8.shape[0])
        if acc > best_acc:
            best_threshold = threshold
            best_acc = acc

    print("wait")

    plt.figure()
    plt.hist(scores_5, bins=50)
    plt.xlim(0.0,1)
    plt.show()


    plt.figure()
    plt.hist(scores_8, bins=50)
    plt.xlim(0.0,1)
    plt.show()
    print("wait")

    scores_all = []
    texts_1 = []
    texts_2 = []
    for i in tqdm(range(200)):
        for _ in range(4):
            texts_1.append(text_list[i])
            idx = random.randint(0, len(text_list)-1)
            while idx == i:
                idx = random.randint(0, len(text_list)-1)
            texts_2.append(text_list[idx])
    return_scores = model.predict(model_id=model_id, text_list1=texts_1, text_list2=texts_2)
    scores_all = return_scores.tolist()

    plt.figure()
    plt.hist(scores_all, bins=50)
    plt.xlim(0.0,1)
    plt.show()
    print("wait")