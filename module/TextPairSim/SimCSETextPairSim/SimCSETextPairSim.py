# -*- coding: UTF-8 -*-

import json
import os
import random

import numpy as np
from tqdm import tqdm

import setting

os.environ['CUDA_VISIBLE_DEVICES']=setting.GPU_DIVICE
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
for gpu in gpus:
    tf.config.experimental.set_memory_growth(gpu, True)

from sklearn.metrics.pairwise import cosine_similarity
from sklearn.model_selection import train_test_split
from transformers import BertTokenizer, BertConfig

from database.REDIS import REDIS
from model.SimCSE.SimCSE import SimCSE, SimLoss2N, SimLossN, SimMetric2N, SimMetricN
from module.TextPairSim.SimCSETextPairSim.DataGenerator import DataGenerator
from setting import logger
from utils.my_utils import MyEncoder


class SimCSETextPairSim:
    def __init__(self):
        self.models = dict()
        self.max_len = setting.SIMCSE_TEXT_PAIR_SIM_MAX_LEN
        self.batch_size = setting.SIMCSE_TEXT_PAIR_SIM_BATCH_SIZE

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        logger.debug(f"准备开始训练 - model_id: {model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载预训练模型
        config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
        config.emb_layer = setting.SIMCSE_PRE_RANK_EMB_LAYER
        config.max_len = self.max_len
        tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)
        model = SimCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)

        # 加载数据
        data_train, len_train, data_valid, len_valid = self.get_data_generator(data=data, tokenizer=tokenizer)
        logger.debug(f"数据加载完成 - 训练集数据量: {len_train}, 验证集数据量: {len_valid}")

        # 训练模型
        opt = tf.keras.optimizers.Adam(learning_rate=setting.SIMCSE_TEXT_PAIR_SIM_LEARNING_RATE, epsilon=1e-08)
        if setting.SIMCSE_TEXT_PAIR_SIM_LOSS == "2n":
            loss = SimLoss2N(name="sim_loss")
            metrics = [
                SimMetric2N(metric_type="auc", name="sim_auc", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD),
                SimMetric2N(metric_type="acc", name="sim_acc", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD),
                SimMetric2N(metric_type="precision", name="sim_pre", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD),
                SimMetric2N(metric_type="recall", name="sim_rec", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD),
                SimMetric2N(metric_type="f1", name="sim_f1", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD)
            ]
        elif setting.SIMCSE_TEXT_PAIR_SIM_LOSS == "n":
            loss = SimLossN(name="sim_loss")
            metrics = [
                SimMetricN(metric_type="auc", name="sim_auc", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD),
                SimMetricN(metric_type="acc", name="sim_acc", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD),
                SimMetricN(metric_type="precision", name="sim_pre", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD),
                SimMetricN(metric_type="recall", name="sim_rec", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD),
                SimMetricN(metric_type="f1", name="sim_f1", threshold=setting.SIMCSE_TEXT_PAIR_SIM_THRESHOLD)
            ]
        else:
            raise Exception(f"参数有误 SIMCSE_TEXT_PAIR_SIM_LOSS:{setting.SIMCSE_TEXT_PAIR_SIM_LOSS}")
        model.compile(optimizer=opt, loss=loss, metrics=metrics)

        model_file = os.path.join(save_dir, 'best_model.ckpt')
        model.save_weights(model_file)
        callbacks = [
            tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_sim_auc', save_best_only=True),
            tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=3),
            tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=3, verbose=0), # 当 patience 次迭代损失未改善，Keras停止训练
        ]
        logger.debug(f"开始训练 - model_id: {model_id}")
        history = model.fit(
            data_train,
            epochs=setting.SIMCSE_TEXT_PAIR_SIM_EPOCHS,
            validation_data=data_valid,
            callbacks=callbacks
        )
        model.load_weights(model_file).expect_partial()

        # 保存模型
        tokenizer.save_pretrained(save_dir)
        config.save_pretrained(save_dir)
        with open(os.path.join(save_dir, 'train_history.json'), 'w', encoding='utf-8') as f:
            json.dump(history.history, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)

        # self.models[model_id] = dict()
        # self.models[model_id]['tokenizer'] = tokenizer
        # self.models[model_id]['model'] = model

        logger.debug(f"训练成功 - model_id: {model_id}")

        best_idx = np.argmax(history.history["val_sim_auc"])
        return {
            "acc": history.history["val_sim_acc"][best_idx],
            "auc": history.history["val_sim_auc"][best_idx],
            "precision": history.history["val_sim_pre"][best_idx],
            "recall": history.history["val_sim_rec"][best_idx],
            "f1": history.history["val_sim_f1"][best_idx]
        }

    def load_model(self, model_id, save_dir):
        try:
            tokenizer = BertTokenizer.from_pretrained(save_dir)
            config = BertConfig.from_pretrained(save_dir)
            model = SimCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)
            model.load_weights(os.path.join(save_dir, 'best_model.ckpt')).expect_partial()

            self.models[model_id] = dict()
            self.models[model_id]['tokenizer'] = tokenizer
            self.models[model_id]['model'] = model
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, text_list1, text_list2):
        if model_id not in self.models:
            logger.error(f"预测错误, 模型未加载 - model_id: {model_id}, text_list1: {text_list1}, text_list2: {text_list2}")
            raise Exception(f"预测错误, 模型未加载 - model_id: {model_id}")

        if isinstance(text_list1, str):
            text_list1 = [text_list1]
        if isinstance(text_list2, str):
            text_list2 = [text_list2]

        if len(text_list1) == 0 or len(text_list2) == 0 or len(text_list1) != len(text_list2):
            logger.error(f"预测错误, 文本列表长度出错: model_id: {model_id}, text_list1: {len(text_list1)}, text_list2: {len(text_list2)}")
            return None

        tokenizer = self.models[model_id]['tokenizer']
        model = self.models[model_id]['model']
        probs = []

        for start_idx in range(0, len(text_list1), self.batch_size):
            end_idx = min(start_idx+self.batch_size, len(text_list1))
            inputs_1 = tokenizer.batch_encode_plus(
                text_list1[start_idx:end_idx],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                return_tensors="tf"
            )
            inputs_2 = tokenizer.batch_encode_plus(
                text_list2[start_idx:end_idx],
                add_special_tokens=True,
                max_length=self.max_len,
                padding="max_length",
                return_tensors="tf"
            )
            emb_1 = model(inputs_1).numpy()
            emb_2 = model(inputs_2).numpy()
            probs.append(np.diag(cosine_similarity(emb_1, emb_2)))
        prob = np.clip(np.hstack(probs), a_min=0, a_max=1)
        return prob

    def get_data(self, data):
        pass

    @staticmethod
    def split_train_valid(data):
        # 划分数据集
        text_list_train = []
        text_list_valid = []
        for data_dict in data:
            norm_query = data_dict['title']
            texts = set()
            texts.add(norm_query)

            for text in data_dict['labelData'].split("||"):
                text_strip = text.strip()
                if len(text_strip):
                    texts.add(text_strip)
            texts = list(texts)

            if len(texts) <= 0:
                continue
            elif len(texts) == 1:
                text_list_train.append(texts)
            else:
                texts_train, texts_valid = train_test_split(texts, test_size=0.2, random_state=setting.SEED)
                text_list_train.append(texts_train)
                text_list_valid.append(texts_valid)

        return text_list_train, text_list_valid

    def get_data_generator(self, data, tokenizer=None):
        # 划分数据集
        text_list_train, text_list_valid = self.split_train_valid(data)
        num_train_text = np.sum([len(l) for l in text_list_train])
        num_valid_text = np.sum([len(l) for l in text_list_valid])
        data_gen_train = DataGenerator(text_list=text_list_train, tokenizer=tokenizer)
        data_gen_valid = DataGenerator(text_list=text_list_valid, tokenizer=tokenizer)
        return data_gen_train, num_train_text, data_gen_valid, num_valid_text

    # def get_data_generator(self, data, tokenizer=None):
    #     # 划分数据集
    #     text_list_train, text_list_valid = self.split_train_valid(data)
    #
    #     setting.SIMCSE_TEXT_PAIR_SIM_BATCH_SIZE = min(len(text_list_valid) * 2, setting.SIMCSE_TEXT_PAIR_SIM_BATCH_SIZE)
    #     # 生成器
    #     label = 0
    #     def get_features(text_list):
    #         features = []
    #         for _ in range(setting.SIMCSE_TEXT_PAIR_SIM_NUM_BATCHS):
    #             random.shuffle(text_list)
    #             for i in range(setting.SIMCSE_TEXT_PAIR_SIM_BATCH_SIZE//2):
    #                 # 连续两行是正样本对
    #                 text1 = random.choice(text_list[i])
    #                 text2 = random.choice(text_list[i])
    #                 inputs_1 = SimCSE.get_data(text1, tokenizer=tokenizer, max_len=setting.SIMCSE_TEXT_PAIR_SIM_MAX_LEN, return_tensor=False)
    #                 inputs_2 = SimCSE.get_data(text2, tokenizer=tokenizer, max_len=setting.SIMCSE_TEXT_PAIR_SIM_MAX_LEN, return_tensor=False)
    #                 for key, value in inputs_1.items():
    #                     inputs_1[key] = np.reshape(value, newshape=(-1, ))
    #                 for key, value in inputs_2.items():
    #                     inputs_2[key] = np.reshape(value, newshape=(-1, ))
    #                 features.append((inputs_1, label))
    #                 features.append((inputs_2, label))
    #         return features
    #     features_train = get_features(text_list_train)
    #     features_valid = get_features(text_list_valid)
    #
    #     # 生成器
    #     def gen_train():
    #         for feature in features_train:
    #             yield feature
    #     def gen_valid():
    #         for feature in features_valid:
    #             yield feature
    #
    #     data_train = tf.data.Dataset.from_generator(
    #         gen_train,
    #         ({
    #              "input_ids": tf.int32, "token_type_ids": tf.int32, "attention_mask": tf.int32
    #          },tf.int64),
    #         ({
    #              "input_ids": tf.TensorShape([None]), "token_type_ids": tf.TensorShape([None]), "attention_mask": tf.TensorShape([None])
    #          }, tf.TensorShape(None))
    #     )
    #     data_train = data_train.batch(setting.SIMCSE_TEXT_PAIR_SIM_BATCH_SIZE)
    #     data_valid = tf.data.Dataset.from_generator(
    #         gen_valid,
    #         ({
    #              "input_ids": tf.int32, "token_type_ids": tf.int32, "attention_mask": tf.int32
    #          },tf.int64),
    #         ({
    #              "input_ids": tf.TensorShape([None]), "token_type_ids": tf.TensorShape([None]), "attention_mask": tf.TensorShape([None])
    #          }, tf.TensorShape(None))
    #     )
    #     data_valid = data_valid.batch(setting.SIMCSE_TEXT_PAIR_SIM_BATCH_SIZE)
    #     return data_train, len(features_train), data_valid, len(features_valid)

    def my_test(self, model_id, data):
        import Levenshtein
        random.seed(setting.SEED)
        _, text_list_valid = self.split_train_valid(data)


        # 测试 AUC，随机负采样，正负样本 1:1
        y_true = []
        y_pred = []
        auc_metric = tf.keras.metrics.AUC()
        auc_score = 0
        pbar = tqdm(range(len(text_list_valid)))
        for i in pbar:
            text_list = text_list_valid[i]
            pbar.set_description('测试进度')
            for text1 in text_list:
                # 正样本
                text2 = random.choice(text_list)
                score = self.predict(model_id=model_id, text_list1=text1, text_list2=text2)[0]
                y_pred.append(score)
                y_true.append(1)

                # 负样本
                j = random.randint(0, len(text_list_valid)-1)
                while j == i:
                    j = random.randint(0, len(text_list_valid)-1)
                text2 = random.choice(text_list_valid[j])
                score = self.predict(model_id=model_id, text_list1=text1, text_list2=text2)[0]
                y_pred.append(score)
                y_true.append(0)

                auc_metric.reset_states()
                auc_metric.update_state(y_true=y_true, y_pred=y_pred)
                auc_score = float(auc_metric.result().numpy())
                pbar.set_postfix({"auc": auc_score})
        print(f"随机负采样 1：1 AUC: {auc_score}")


        # 测试 AUC，难样本负采样，正负样本 1:1
        y_true = []
        y_pred = []
        auc_metric = tf.keras.metrics.AUC()
        auc_score = 0
        pbar = tqdm(range(len(text_list_valid)))
        for i in pbar:
            text_list = text_list_valid[i]
            pbar.set_description('测试进度')
            for text1 in text_list:
                # 正样本
                text2 = random.choice(text_list)
                score = self.predict(model_id=model_id, text_list1=text1, text_list2=text2)[0]
                y_pred.append(score)
                y_true.append(1)

                # 负样本
                best_score = 0
                best_neg_text = ''
                for j in range(len(text_list_valid)):
                    if j != i:
                        for neg_text in text_list_valid[j]:
                            neg_score = Levenshtein.ratio(text1, neg_text)
                            if neg_score > best_score:
                                best_score = neg_score
                                best_neg_text = neg_text
                score = self.predict(model_id=model_id, text_list1=text1, text_list2=best_neg_text)[0]
                y_pred.append(score)
                y_true.append(0)

                auc_metric.reset_states()
                auc_metric.update_state(y_true=y_true, y_pred=y_pred)
                auc_score = float(auc_metric.result().numpy())
                pbar.set_postfix({"auc": auc_score})
        print(f"难样本负采样 1：1 AUC: {auc_score}")


        # 测试检索 top1 准确率，正负样本 1:50，当正样本得分最高时才正确
        acc = 0
        total = 0
        neg_samples = 50
        pbar = tqdm(range(len(text_list_valid)))
        for i in pbar:
            text_list = text_list_valid[i]
            pbar.set_description('测试进度')
            for text1 in text_list:
                texts_1 = []
                texts_2 = []

                # 正样本
                pos_text = random.choice(text_list)
                texts_1.append(text1)
                texts_2.append(pos_text)

                # 负样本
                for _ in range(neg_samples):
                    j = random.randint(0, len(text_list_valid)-1)
                    while j == i:
                        j = random.randint(0, len(text_list_valid)-1)
                    neg_text = random.choice(text_list_valid[j])
                    texts_1.append(text1)
                    texts_2.append(neg_text)

                prob = self.predict(model_id=model_id, text_list1=texts_1, text_list2=texts_2)
                if float(prob[0]) > float(np.max(prob[1:])):
                    acc += 1
                total += 1
                pbar.set_postfix({"acc": acc/total})
        print(f"检索 top 1：{neg_samples} ACC: {acc/total}")


if __name__ == "__main__":
    model = SimCSETextPairSim()
    Redis = REDIS()

    model_id = f"model1"
    data_key = "faq_model1_all"
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), SimCSETextPairSim.__name__)

    # 训练
    data = Redis.get_data(data_key)
    score = model.train(model_id=model_id, data=data, save_dir=save_dir)
    print(f"训练得分: {score}")

    # 加载微调过的模型
    model.load_model(model_id=model_id, save_dir=save_dir)

    # 预测
    query = '请问余额理财赎回到账时间？'
    texts = ['你能告诉我什么时候能查到我快速赎回的余额理财到账的钱吗',
             '快速赎回的余额理财我什么时候能收到钱',
             'IE浏览器登录个人网银显示空白页如何处理',
             '数字牛熊证介绍',
             'ATM扫码取款支持怎样的账户啊',
             "请问余额理财赎回到账时间"]
    for text in texts:
        print(f"\n{query}\n{text}")
        print(model.predict(model_id=model_id, text_list1=query, text_list2=text))

    # 测试
    data = Redis.get_data(data_key)
    model.my_test(model_id=model_id, data=data)