import json
import os
import random

import jieba
import joblib
import numpy as np
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.feature_extraction.text import TfidfTransformer
from sklearn.metrics.pairwise import cosine_similarity

import setting
from database.REDIS import REDIS
from setting import logger
from utils.my_utils import MyEncoder


class TfidfTextPairSim:
    def __init__(self):
        self.models = dict()

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        logger.debug(f"准备开始训练, model_id:{model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载数据
        text_list = self.get_text_list(data=data)
        corpos = [' '.join(jieba.cut(text)) for text in text_list]
        logger.debug(f"数据加载完成, 数据量:{len(text_list)}")

        # 训练模型
        vectorizer = CountVectorizer(token_pattern=r"(?u)\b\w+\b")
        transformer = TfidfTransformer()
        tfidf = transformer.fit_transform(vectorizer.fit_transform(corpos))
        # tfidf_norm = normalize(tfidf, copy=True)
        threshold_dict = self.get_threshold(transformer, vectorizer, text_list, tfidf)
        logger.debug(f"训练完成, threshold:{threshold_dict['threshold']}, acc:{threshold_dict['acc']}")

        # 保存模型
        joblib.dump(vectorizer, os.path.join(save_dir, 'vectorizer.pkl'))
        joblib.dump(transformer, os.path.join(save_dir, 'transformer.pkl'))
        with open(os.path.join(save_dir, 'threshold_dict.json'), 'w', encoding='utf-8') as f:
            json.dump(threshold_dict, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        logger.debug(f"保存模型成功")
        return threshold_dict['threshold']

    def load_model(self, model_id, save_dir):
        try:
            vectorizer = joblib.load(os.path.join(save_dir, 'vectorizer.pkl'))
            transformer = joblib.load(os.path.join(save_dir, 'transformer.pkl'))
            with open(os.path.join(save_dir, 'threshold_dict.json'), 'r', encoding='utf-8') as f:
                threshold_dict = json.load(f)
            self.models[model_id] = dict()
            self.models[model_id]['threshold_dict'] = threshold_dict
            self.models[model_id]['vectorizer'] = vectorizer
            self.models[model_id]['transformer'] = transformer
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, query, text_list):
        threshold = self.models[model_id]['threshold_dict']['threshold']
        vectorizer = self.models[model_id]['vectorizer']
        transformer = self.models[model_id]['transformer']

        if query is None or query == "":
            return [0], threshold
        if text_list is None or len(text_list) == 0:
            return [0], threshold

        is_full_match = [False] * len(text_list)
        query = query.strip().lower()
        for i, text in enumerate(text_list):
            text = text.strip().lower()
            text_list[i] = text
            if text == query:
                is_full_match[i] = True

        corpos = [' '.join(jieba.cut(text)) for text in [query] + text_list]
        tfidf = transformer.transform(vectorizer.transform(corpos))
        scores = cosine_similarity(tfidf[:1, :], tfidf[1:, :])
        scores = scores[0, :]
        for i, is_match in enumerate(is_full_match):
            if is_match:
                scores[i] = 1

        return scores, threshold

    def get_data(self, data):
        pass

    @staticmethod
    def get_text_list(data):
        text_list = []
        for data_dict in data:
            texts = set()
            # norm_query = data_dict['title'].strip()
            # texts.add(norm_query)
            for text in data_dict.get('scoreData', '').split("||"):
                text_strip = text.strip().lower()
                if len(text_strip):
                    texts.add(text_strip)
            texts = list(texts)
            text_list.extend(texts)
        return text_list

    def get_threshold(self, transformer, vectorizer, text_list, tfidf):
        result = {
            "scores_short": [],
            "scores_long": [],
            "threshold": 0.83,
            "acc": 0
        }
        if len(text_list) < 1:
            return result

        scores_long = []
        scores_short = []
        for i, text in enumerate(text_list):
            texts_short = []
            texts_long = []
            for _ in range(4):
                # short
                l = int(len(text) * 0.7)
                start_idx = random.randint(0, len(text) - l)
                cut_text = text_list[i][start_idx:start_idx+l]
                texts_short.append(cut_text)

                # long
                l = int(len(text) * 0.8)
                start_idx = random.randint(0, len(text) - l)
                cut_text = text_list[i][start_idx:start_idx+l]
                texts_long.append(cut_text)

            cut_tfidf = transformer.transform(vectorizer.transform([' '.join(jieba.cut(t)) for t in texts_short+texts_long]))
            return_scores = cosine_similarity(tfidf[i], cut_tfidf)
            return_scores = return_scores[0].tolist()
            scores_short.extend(return_scores[:4])
            scores_long.extend(return_scores[4:])

        scores_short = np.array(scores_short)
        scores_long = np.array(scores_long)
        threshold_start = int(np.min(scores_short) * 100)
        threshold_end = int(np.max(scores_long) * 100) + 1
        best_threshold = 0
        best_acc = -100
        for threshold in range(threshold_start, threshold_end):
            acc = self.get_acc(threshold / 100.0, scores_small=scores_short, scores_big=scores_long)
            if acc > best_acc:
                best_threshold = threshold / 100.0
                best_acc = acc

        return {
            "scores_short": scores_short,
            "scores_long": scores_long,
            "threshold": best_threshold,
            "acc": best_acc
        }

    @staticmethod
    def get_acc(threshold, scores_small, scores_big):
        correct = np.sum(scores_small < threshold) + np.sum(scores_big >= threshold)
        return correct / (len(scores_small) + len(scores_big))

if __name__ == "__main__":
    import time
    model = TfidfTextPairSim()
    Redis = REDIS()

    model_id = f"model2"
    data_key = "score_test_data3"
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), TfidfTextPairSim.__name__)

    # 训练
    data = Redis.get_data(data_key)
    start_time = time.time()
    threshold = model.train(model_id=model_id, data=data, save_dir=save_dir)
    print(f"threshold:{threshold}, time:{time.time()-start_time}")

    model.load_model(model_id=model_id, save_dir=save_dir)

    query = "X先生/小姐你好，我是XX公司的，我姓X，今天我们打电话给你是想了解和确认你的账户还款情况，你"
    text_list = ["x先生/小姐你好，我是xx公司的，我姓x，今天我们打电话给你是想了解和确认你的账户还款情况，你现在总欠款为xx元，我们已经多次电话联系你还款，请问你为什么到现在还没有及时处理你的欠款？"]
    query = "我在外地出差等我回去就还了，不就几千块钱嘛，"
    text_list = ['我在外地出差等我回去就还了，不就几千块钱嘛，我会还的，不要天天打电话烦了。13']
    print(model.predict(model_id=model_id, query=query, text_list=text_list))
    print(model.predict(model_id=model_id, query=query, text_list=text_list[:1]))


"""
data 0 
threshold:0.83, acc:0.929324894514768  threshold:0.83, time:15.441554069519043
data 1
threshold:0.83, acc:0.9295039164490861 threshold:0.83, time:13.557525396347046

data0
threshold:0.83, acc:0.9214135021097046 threshold:0.83, time:3.6608407497406006
data1
threshold:0.83, acc:0.9298302872062664 threshold:0.83, time:4.859501838684082
"""
