import json
import os
import random

import joblib
import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

import setting
from database.REDIS import REDIS
from setting import logger
from utils.my_utils import MyEncoder


class TfidfTextPairSimPoint:
    def __init__(self):
        self.models = dict()

    def build_model(self):
        pass

    def train(self, model_id, data, save_dir):
        logger.debug(f"准备开始训练, model_id:{model_id}")

        # 模型保存地址
        os.makedirs(save_dir, exist_ok=True)

        # 加载数据
        train_text_list, norm_point_to_all_points, text_norm_points_list = self.get_train_data(data=data)
        corpos = [' '.join(list(point.lower())) for point in train_text_list]
        logger.debug(f"数据加载完成, 要点文本数据量:{len(train_text_list)}")

        # 训练模型
        tfidf_vectorizer = TfidfVectorizer(token_pattern=r"(?u)\b\w+\b")
        tfidf = tfidf_vectorizer.fit_transform(corpos)
        result = self.get_threshold(tfidf_vectorizer, text_norm_points_list)
        logger.debug(f"训练完成, threshold:{result['threshold']}, acc:{result['acc']}")

        # 保存模型
        joblib.dump(tfidf_vectorizer, os.path.join(save_dir, 'tfidf_vectorizer.pkl'))
        with open(os.path.join(save_dir, 'result.json'), 'w', encoding='utf-8') as f:
            json.dump(result, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        with open(os.path.join(save_dir, 'norm_point_to_all_points.json'), 'w', encoding='utf-8') as f:
            json.dump(norm_point_to_all_points, cls=MyEncoder, fp=f, ensure_ascii=False, indent=2)
        data_frame = pd.DataFrame(result)
        data_frame.to_csv(os.path.join(save_dir, 'result.csv'), encoding='utf-8-sig', index=False)
        logger.debug(f"保存模型成功")
        return result

    def load_model(self, model_id, save_dir):
        try:
            tfidf_vectorizer = joblib.load(os.path.join(save_dir, 'tfidf_vectorizer.pkl'))
            with open(os.path.join(save_dir, 'result.json'), 'r', encoding='utf-8') as f:
                train_result = json.load(f)
            if os.path.exists(os.path.join(save_dir, "norm_point_to_all_points.json")):
                with open(os.path.join(save_dir, 'norm_point_to_all_points.json'), 'r', encoding='utf-8') as f:
                    norm_point_to_all_points = json.load(f)
            else:
                norm_point_to_all_points = {}
            self.models[model_id] = dict()
            self.models[model_id]['threshold'] = train_result['threshold']
            self.models[model_id]['tfidf_vectorizer'] = tfidf_vectorizer
            self.models[model_id]['norm_point_to_all_points'] = norm_point_to_all_points
        except Exception as e:
            logger.error(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")
            raise Exception(f"加载模型失败 - model_id: {model_id}, 错误信息: {e}")

    def offline_model(self, model_id):
        try:
            self.models.pop(model_id)
        except:
            pass
        logger.debug(f"下线模型成功 - model_id: {model_id}")

    def predict(self, model_id, query, point_list):
        threshold = self.models[model_id]['threshold']
        tfidf_vectorizer = self.models[model_id]['tfidf_vectorizer']
        norm_point_to_all_points = self.models[model_id]['norm_point_to_all_points']

        if query is None or query == "":
            return [0] * len(point_list), threshold
        if point_list is None or len(point_list) == 0:
            return [0], threshold

        point_dict = {}
        for p in point_list:
            point_dict[p] = {}
            sim_points = norm_point_to_all_points.get(p.strip(), [p.strip()])
            point_dict[p]["sim_points"] = sim_points
            point_dict[p]["sim_points_scores"] = [0] * len(sim_points)

        query = self.remove_symbol_and_lower(query)
        sim_point_list = []
        sim_point_keys = []
        for p in point_list:
            for i in range(len(point_dict[p]["sim_points"])):
                temp_sim_p = self.remove_symbol_and_lower(point_dict[p]["sim_points"][i])
                point_dict[p]["sim_points"][i] = temp_sim_p
                if temp_sim_p in query:
                    point_dict[p]["sim_points_scores"][i] = 1
            if max(point_dict[p]["sim_points_scores"]) < 1:
                sim_point_list.extend(point_dict[p]["sim_points"])
                sim_point_keys.append(p)

        if len(sim_point_list):
            corpos = [' '.join(list(t)) for t in [query] + sim_point_list]
            tfidf = tfidf_vectorizer.transform(corpos)
            query_tfidf = tfidf[:1, :]
            points_tfidf = tfidf[1:, :]
            scores = [0] * len(sim_point_list)
            for i in range(points_tfidf.shape[0]):
                point_tfidf = points_tfidf[i:i + 1, :]
                index = point_tfidf > 0
                if point_tfidf[index].size and query_tfidf[index].size:
                    scores[i] = cosine_similarity(point_tfidf[index], query_tfidf[index])[0][0]
                else:
                    scores[i] = 0.0
            for p_key in sim_point_keys:
                temp_length = len(point_dict[p_key]["sim_points"])
                assert len(point_dict[p_key]["sim_points_scores"]) == len(scores[:temp_length])
                point_dict[p_key]["sim_points_scores"] = scores[:temp_length]
                scores = scores[temp_length:]

        # 返回结果
        scores = []
        for p in point_list:
            scores.append(max(point_dict[p]["sim_points_scores"]))
        return scores, threshold

    def get_data(self, data):
        pass

    @staticmethod
    def get_train_data(data):
        train_text_list = []
        norm_point_to_all_points = {}
        text_norm_points_list = []

        for data_dict in data:
            score_data_point = data_dict.get("scoreDataPoint", [])
            if isinstance(score_data_point, str):
                score_data_point = score_data_point.strip()
                score_data_point_new = []
                for p in score_data_point.split("||"):
                    p = p.strip()
                    if len(p):
                        score_data_point_new.append([p])
                score_data_point = score_data_point_new
            else:
                score_data_point_new = []
                for point in score_data_point:
                    if point is None:
                        continue
                    temp_point_list = []
                    for p in point.split("||"):
                        p = p.strip()
                        if len(p) and p not in temp_point_list:
                            temp_point_list.append(p)
                    if len(temp_point_list):
                        score_data_point_new.append(temp_point_list)
                score_data_point = score_data_point_new
            if len(score_data_point) == 0:
                continue

            text_norm_points = []
            for temp_point_list in score_data_point:
                text_norm_points.append(temp_point_list[0])
                for temp_point in temp_point_list:
                    if temp_point not in train_text_list:
                        train_text_list.append(temp_point)
                    norm_point_to_all_points[temp_point] = temp_point_list
            text_norm_points_list.append(text_norm_points)

        return train_text_list, norm_point_to_all_points, text_norm_points_list

    def get_threshold(self, tfidf_vectorizer, text_norm_points_list):
        result = {
            "points": [],
            "answer_short": [],
            "answer_long": [],
            "scores_short": [],
            "scores_long": [],
            "threshold": 0.83,
            "acc": 0
        }
        if len(text_norm_points_list) < 1:
            return result

        for text_norm_points in text_norm_points_list:
            for i, point in enumerate(text_norm_points):
                texts_short = []
                texts_long = []
                temp_text_norm_points = text_norm_points.copy()
                for _ in range(4):
                    # short
                    l = int(len(point) * 0.5)
                    start_idx = random.randint(0, len(point) - l)
                    point_short = point[start_idx:start_idx+l]
                    temp_text_norm_points[i] = point_short
                    texts_short.append("".join(temp_text_norm_points))

                    # long
                    l = int(len(point) * 0.8)
                    start_idx = random.randint(0, len(point) - l)
                    point_long = point[start_idx:start_idx+l]
                    temp_text_norm_points[i] = point_long
                    texts_long.append("".join(temp_text_norm_points))

                tfidf = tfidf_vectorizer.transform([' '.join(list(t)) for t in [point]+texts_short+texts_long]).toarray()
                point_tfidf = tfidf[0, :]
                other_tfidf = tfidf[1:, :]
                index = point_tfidf > 0
                if np.sum(index) == 0:
                    continue
                scores = cosine_similarity(point_tfidf[index].reshape(1, -1), other_tfidf[:, index])[0].tolist()
                result["points"].extend([point]*len(texts_short))
                result["answer_short"].extend(texts_short)
                result["answer_long"].extend(texts_long)
                result["scores_short"].extend(scores[:len(texts_short)])
                result["scores_long"].extend(scores[len(texts_short):])

        scores_short = np.array(result["scores_short"])
        scores_long = np.array(result["scores_long"])
        threshold_start = int(np.min(scores_short) * 100)
        threshold_end = int(np.max(scores_long) * 100) + 1
        best_threshold = 0
        best_acc = -100
        for threshold in range(threshold_start, threshold_end):
            acc = self.get_acc(threshold / 100.0, scores_small=scores_short, scores_big=scores_long)
            if acc > best_acc:
                best_threshold = threshold / 100.0
                best_acc = acc
        result["threshold"] = best_threshold
        result["acc"] = best_acc
        return result

    @staticmethod
    def get_acc(threshold, scores_small, scores_big):
        correct = np.sum(scores_small < threshold) + np.sum(scores_big >= threshold)
        return correct / (len(scores_small) + len(scores_big))

    @staticmethod
    def remove_symbol_and_lower(text):
        text_lower = text.lower().strip()
        for symbol in r'，。？！：；,.?!:;、/\<>"""[]{}()-=+_*&^%$#@!~`':
            text_lower = text_lower.replace(symbol, "")
        return text_lower

if __name__ == "__main__":
    import time

    model = TfidfTextPairSimPoint()
    Redis = REDIS()

    model_id = f"pretrain"
    data_key = "pretrain"
    save_dir = os.path.join(os.path.join(setting.SAVE_MODEL_DIR, model_id), TfidfTextPairSimPoint.__name__)

    # 训练
    data = Redis.get_data(data_key)
    start_time = time.time()
    train_result = model.train(model_id=model_id, data=data, save_dir=save_dir)
    model.load_model(model_id=model_id, save_dir=save_dir)
    scores, threshold = model.predict(model_id=model_id, query="need delay", point_list=["need delay", "maybe delay", "no delay expected.", "welcome to make your choice.", "how old are you"])
    print(f"threshold:{train_result['threshold']}, acc:{train_result['acc']}, time:{time.time()-start_time}")

    # plt.figure(figsize=(21, 12))
    # plt.hist(train_result["scores_short"], bins=50)
    # plt.title("short text score distribute")
    # plt.grid()
    # plt.show()
    #
    # plt.figure(figsize=(21, 12))
    # plt.hist(train_result["scores_long"], bins=50)
    # plt.title("long text score distribute")
    # plt.grid()
    # plt.show()

    # 加载模型
    model.load_model(model_id=model_id, save_dir=save_dir)

    # 预测
    point_list = ["我们的技术是国家领先的，曾经取得多个国家大奖",
                  "R7的电机技术获国家科学技术创新奖，该奖项是国家技术创新最高奖项",
                  "小明写的作文获国家文学创新奖，该奖项是文学领域最高奖项",
                  "R7的电机技术获国家科学技术创新奖，V2X就是车子跟万物互联，可以提前预知道路情况，并计算出最优的驾驶操作",
                  "可以提前预知道路情况，并计算出最优的驾驶操作",
                  "最厉害的就是我们的电机",
                  "龙舌蓝",
                  "银翼紫"]
    query = "银翼紫您好我们的技术是国家领先的，曾经获得过多个国家奖励，最厉害的就是我们的电机，效率高达百分之九十七，今年新出的电车比之前的燃油车厉害了好几个量级"
    scores, threshold = model.predict(model_id=model_id, query=query, point_list=point_list)
    print("wait")

"""
score_test_data_keypoint 非凡R7数据
0.5-0.8  threshold:0.81, acc:0.7870762711864406

"""
