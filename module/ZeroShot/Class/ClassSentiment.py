import os
from transformers.models.bert import TFBertForSequenceClassification, BertConfig, BertTokenizer
import tensorflow as tf
from module.ZeroShot.Class.DataGenerator import DataGenerator

pretrain_dir = "/data/cbk/NLP_Platform/pretrain_model/chinese-roberta-wwm-ext"

class ClassSentiment:
    def __init__(self, need_train=False):
        self.tokenizer = BertTokenizer.from_pretrained(pretrain_dir)
        self.config = BertConfig.from_pretrained(pretrain_dir, num_labels=2)
        self.bert = TFBertForSequenceClassification.from_pretrained(pretrain_dir, config=self.config)
        self.max_len = 128

        if need_train:
            self.train()
        self.load_model()

    def train(self):
        save_dir = "./model"
        os.makedirs(save_dir, exist_ok=True)
        model_file = os.path.join(save_dir, 'tf_model.h5')
        train_gen, valid_gen = self.get_data()

        loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
        metrics = [tf.keras.metrics.SparseCategoricalAccuracy("acc")]
        opt = tf.keras.optimizers.Adam(learning_rate=8e-5, epsilon=1e-08)
        self.bert.compile(optimizer=opt, loss=loss, metrics=metrics)

        history = self.bert.fit(
            train_gen,
            epochs=10,
            validation_data=valid_gen,
            callbacks=[tf.keras.callbacks.ModelCheckpoint(model_file, monitor='val_acc', save_best_only=True)]
        )

        self.tokenizer.save_pretrained(save_dir)
        self.config.save_pretrained(save_dir)
        self.bert = TFBertForSequenceClassification.from_pretrained(model_file, config=self.config, from_pt=False)

    def load_model(self):
        save_dir = "./model"
        model_file = os.path.join(save_dir, 'tf_model.h5')
        self.tokenizer.from_pretrained(save_dir)
        self.config.from_pretrained(save_dir)
        self.bert = TFBertForSequenceClassification.from_pretrained(model_file, config=self.config, from_pt=False)


    def offline_model(self, model_id):
        pass

    def incremental_train(self, model_id, data, model_save_dir):
        pass

    def get_data(self):
        files = ["/data/cbk/NLP_Platform/data/sentiment/sentiment.train.data",
                 "/data/cbk/NLP_Platform/data/sentiment/sentiment.valid.data",
                 "/data/cbk/NLP_Platform/data/sentiment/sentiment.test.data"]
        train_texts = []
        train_labels = []
        valid_texts = []
        valid_labels = []

        for file in files:
            with open(file, "r", encoding="utf-8") as f:
                for line in f:
                    line = line.split('\t')
                    if ".test." in file:
                        valid_texts.append(line[0].strip())
                        valid_labels.append(int(line[1].strip()))
                    else:
                        train_texts.append(line[0].strip())
                        train_labels.append(int(line[1].strip()))

        train_gen = DataGenerator(texts=train_texts, labels=train_labels, tokenizer=self.tokenizer)
        valid_gen = DataGenerator(texts=valid_texts, labels=valid_labels, tokenizer=self.tokenizer)
        return train_gen, valid_gen

    def predict(self, texts):
        if isinstance(texts, str):
            texts = [texts]
        pred_labels = []

        for start_idx in range(0, len(texts), 64):
            end_idx = min(start_idx+64, len(texts))
            inputs = self.tokenizer.batch_encode_plus(texts[start_idx:end_idx],
                                                      add_special_tokens=True,
                                                      max_length=self.max_len,
                                                      padding="max_length",
                                                      truncation=True,
                                                      return_tensors="tf")
            outputs = self.bert(**inputs)[0]
            pred_labels.extend(list(tf.argmax(outputs, axis=-1).numpy()))

        return pred_labels


if __name__ == '__main__':
    model = ClassSentiment(need_train=False)

    # predict
    texts = ['不知算不算问题，就是合上本本的时候，缝隙有点大。不过也没有什么影响。', '小巧，美观']
    for text in texts:
        p = model.predict(texts=text)
        print(f'\n查询句子: {text}')
        print(f"result: {p}")

"""
loss: 0.0258 - acc: 0.9925 - val_loss: 0.2054 - val_acc: 0.9541
"""