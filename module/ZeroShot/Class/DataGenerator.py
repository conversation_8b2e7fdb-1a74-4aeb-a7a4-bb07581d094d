# -*- coding: UTF-8 -*-

import math
import random

import numpy as np
from tensorflow import keras


class DataGenerator(keras.utils.Sequence):
    def __init__(self, texts, labels, tokenizer):
        self.texts = texts
        self.labels = labels
        self.idx_list = [i for i in range(len(texts))]
        self.tokenizer = tokenizer
        self.batch_size = 64
        self.max_len = 128

    def __len__(self):
        """
        返回生成器的长度，也就是总共分批生成数据的次数。
        """
        return math.ceil(len(self.texts) / self.batch_size)

    def __getitem__(self, index):
        """
        该函数返回每次我们需要的经过处理的数据。
        """
        start_idx = index * self.batch_size
        end_idx = min(start_idx+self.batch_size, len(self.idx_list))
        X, Y = self.__data_generation(self.idx_list[start_idx:end_idx])
        return X, Y

    def on_epoch_end(self):
        """
        该函数将在训练时每一个epoch结束的时候自动执行，在这里是随机打乱索引次序以方便下一batch运行。
        """
        random.shuffle(self.idx_list)

    def __data_generation(self, idx_list):
        """
        生成 cur_id 的数据。
        """
        input_ids = np.zeros(shape=(self.batch_size, self.max_len), dtype=np.int32)
        token_type_ids = np.zeros(shape=(self.batch_size, self.max_len), dtype=np.int32)
        attention_mask = np.zeros(shape=(self.batch_size, self.max_len), dtype=np.int32)
        labels = np.zeros(shape=(self.batch_size, 1))
        index = 0

        for idx in idx_list:
            text = self.texts[idx]
            label = self.labels[idx]
            inputs = self.tokenizer.encode_plus(text, add_special_tokens=True, truncation=True, max_length=self.max_len, padding="max_length")
            input_ids[index, :] = inputs["input_ids"]
            attention_mask[index, :] = inputs["attention_mask"]
            token_type_ids[index, :] = inputs["token_type_ids"]
            labels[index, 0] = label
            index += 1

        return (
            {
                'input_ids': input_ids,
                'token_type_ids': token_type_ids,
                'attention_mask': attention_mask
            },
            labels
        )