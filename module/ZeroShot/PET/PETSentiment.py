from transformers.models.bert import TFBertForMaskedLM, BertConfig, BertTokenizer
import tensorflow as tf

pretrain_dir = "/data/cbk/NLP_Platform/pretrain_model/chinese-roberta-wwm-ext"


class PETSentiment:
    def __init__(self):
        self.tokenizer = BertTokenizer.from_pretrained(pretrain_dir)
        self.config = BertConfig.from_pretrained(pretrain_dir)
        self.bert = TFBertForMaskedLM.from_pretrained(pretrain_dir, config=self.config, from_pt=False)

        self.prefix = u'很满意。'
        self.mask_idx = 1
        self.mask_token_id = self.tokenizer.mask_token_id
        self.pos_id = self.tokenizer.convert_tokens_to_ids('很')
        self.neg_id = self.tokenizer.convert_tokens_to_ids('不')
        self.max_len = 128

    def load_model(self, model_id, model_save_dir):
        pass

    def offline_model(self, model_id):
        pass

    def train(self, model_id, data, model_save_dir):
        pass

    def incremental_train(self, model_id, data, model_save_dir):
        pass

    def predict(self, texts):
        if isinstance(texts, str):
            texts = [texts]
        texts = [self.prefix+t for t in texts]
        pred_labels = []

        for start_idx in range(0, len(texts), 64):
            end_idx = min(start_idx+64, len(texts))
            inputs = self.tokenizer.batch_encode_plus(texts[start_idx:end_idx],
                                                      add_special_tokens=True,
                                                      max_length=self.max_len,
                                                      padding="max_length",
                                                      truncation=True)
            for i in range(len(inputs["input_ids"])):
                inputs["input_ids"][i][1] = self.mask_token_id
            for key in inputs.keys():
                inputs[key] = tf.convert_to_tensor(inputs[key])

            outputs = self.bert(**inputs)[0]
            pred_labels.extend(list(tf.cast(outputs[:, 1, self.pos_id] > outputs[:, 1, self.neg_id], tf.int32).numpy()))

        return pred_labels

    def test_acc(self):
        files = ["/data/cbk/NLP_Platform/data/sentiment/sentiment.train.data",
                 "/data/cbk/NLP_Platform/data/sentiment/sentiment.valid.data",
                 "/data/cbk/NLP_Platform/data/sentiment/sentiment.test.data"]
        all_texts = []
        labels = []
        for file in files:
            with open(file, "r", encoding="utf-8") as f:
                for line in f:
                    texts = line.split('\t')
                    text = texts[0].strip()
                    label = int(texts[1].strip())
                    all_texts.append(text)
                    labels.append(label)

        pred = self.predict(texts=all_texts)
        total = len(labels)
        acc = 0
        for i in range(len(labels)):
            if labels[i] == pred[i]:
                acc += 1

        print(f"准确率: {acc/total}")


if __name__ == '__main__':
    model = PETSentiment()

    # predict
    texts = ['这本书的内容比较不错', '这本书很难看']
    for text in texts:
        p = model.predict(texts=text)
        print(f'\n查询句子: {text}')
        print(f"result: {p}")

    # 准确率
    model.test_acc()

"""
查询句子: 这本书的内容比较不错
result: [1]

查询句子: 这本书很难看
result: [0]
准确率: 0.8599857853589197
"""