import pandas as pd

from setting import logger,FILECHAT_IMG2TEXT_URL_OPANAI
import base64
import requests
import json,time
import os
from tqdm import tqdm

import base64
from PIL import Image
from io import BytesIO


def file_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
    return encoded_string



def file_to_base64_resample(image_path, max_resolution=1000):
    # 打开图片
    with Image.open(image_path) as img:
        # 检查图片的大小
        if img.width > max_resolution or img.height > max_resolution:
            # 计算缩放比例
            scale_factor = min(max_resolution / img.width, max_resolution / img.height)
            # 计算新的尺寸
            new_width = int(img.width * scale_factor)
            new_height = int(img.height * scale_factor)
            # 调整图片大小
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 创建一个BytesIO对象
        img_byte_arr = BytesIO()
        # 保存图片到BytesIO对象
        img.save(img_byte_arr, format='PNG')
        # 获取BytesIO对象的值
        img_byte_arr = img_byte_arr.getvalue()

    # 编码为base64
    encoded_string = base64.b64encode(img_byte_arr).decode('utf-8')
    return encoded_string


def img2text(image_path=None,image_base64=None):
    start = time.time()

    try:

        if image_base64 is None:
            suffix = image_path.split('.')[-1].lower()
            image_name = os.path.split(image_path)[-1]
            assert suffix in ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'webp', 'tiff', 'tif', 'jpe', 'jfif', 'jp2', 'j2k',
                              'jpf'], '图片格式不支持'
            image_base64 = file_to_base64_resample(image_path)
        else:
            image_base64 = image_base64
            image_name = 'base64.jpg'
        headers = {
            'Content-Type': 'application/json',
        }
        data = {
            "vlm_parameter": {
                "image": image_base64,
                "image_name": image_name,
                "prompt": "请抽取出图片中的所有文字信息和表格/图表信息。不要遗漏。\n\n并按照正确的位置或顺序关系输出。\n如果涉及统计图不要忽略变化关系。\n只需要返回表格/图表中的内容，不要说其他话"
            }
        }
        logger.info(f'开始图片转文字:{FILECHAT_IMG2TEXT_URL_OPANAI}')
        if FILECHAT_IMG2TEXT_URL_OPANAI:
            for i in range(2):
                try:
                    # response = requests.post(FILECHAT_IMG2TEXT_URL, json=data,timeout=60)
                    # output = response.json()['answer']
                    # print(response.text)
                    output = send_image_to_openai_api(image_base64)
                    logger.info(f'ocr图片转文字成功，耗时{time.time() - start:.2f}s')
                    break
                except:
                    logger.error('ocr错误',exc_info=True)
                    output = ''
        else:
            output = ''
            logger.info(f'不使用ocr模型')
    except:
        logger.error('ocr错误',exc_info=True)
        output = ''
    return output


def send_image_to_openai_api(image_base64):
    data = {
        "model": "InternVL2-26B-AWQ",
        "messages": [
            {
                    'role': 'system',
                    'content': [{
                        'type': 'text',
                        'text': '请尽可能详细地回答用户的问题。',
                    }],
            },
            {
                    'role': 'user',
                    'content': [{
                        'type': 'text',
                        'text': "抽取出图中所有文字。"
                        # 'text': '抽取出图中所有文字。', # 抽取出图片中所有文字并保留换行。与图片中的文字格式一致。 合并单元格请拆分。
                        # 'text': '请逻辑清晰地描述图片中的全部内容。要求：文字与图片中相同，图表描述逻辑清晰准确。', # 抽取出图片中所有文字并保留换行。与图片中的文字格式一致。 合并单元格请拆分。
                    }, {
                        'type': 'image_url',
                        'image_url': {
                            "url": f"data:image/jpeg;base64,{image_base64}",
                        },
                    }],
                }],
        "temperature": 0.01,
        "top_p": 0.95,
        "max_tokens": 4096,
        "repetition_penalty": 1.1
    }
    # 重试
    try_count = 3
    while try_count:
        try:
            response = requests.post(FILECHAT_IMG2TEXT_URL_OPANAI, data=json.dumps(data),timeout=120)
            logger.debug(response)
            logger.debug(response.json()["choices"][0]["message"]["content"])
            output = response.json()["choices"][0]["message"]["content"]
            break
        except Exception as e:
            logger.error(f"重试倒数：{try_count}, 调用OCR出错:{e}")
            output = ''
            try_count -= 1
            time.sleep(try_count*2)
    return output

from concurrent.futures import ThreadPoolExecutor
excutor = ThreadPoolExecutor(max_workers=2)
from tqdm import tqdm
def image2text_thread(image_path):
    task = excutor.submit(img2text, image_path)
    return task.result()



if __name__ == '__main__':
    data = []
    root = r'C:\Users\<USER>\Desktop\新建文件夹'
    root = r'C:\Users\<USER>\Desktop\新建文件夹\中国电信人工智能战略思考与实践-120'
    root = r'C:\Users\<USER>\Desktop\a'
    for file in tqdm(os.listdir(root)):
        image_path = os.path.join(root, file)
        output = img2text(image_path)
        data.append(output)

    # df = pd.DataFrame(data, columns=['output'])
    # df['path'] = os.listdir(root)
    # df.to_excel(r"C:\Users\<USER>\Desktop\中国电信人工智能战略思考与实践V2.xlsx", index=False)
