
import fitz
import PyPDF2
import os


def convert_pdf_to_images(pdf_path,select_page=None):
    output_path = os.path.join(os.path.dirname(pdf_path), 'images')
    os.makedirs(output_path, exist_ok=True)
    filename = os.path.basename(pdf_path).split('.')[0]

    image_paths = []
    doc = fitz.open(pdf_path)
    reader = PyPDF2.PdfReader(pdf_path)
    pageNum = len(reader.pages)
    if select_page:
        page = doc.load_page(select_page-1)
        pix = page.get_pixmap(dpi=200)
        output_name = f"{filename}_{select_page-1}.png"
        pix.save(os.path.join(output_path,output_name))
        image_paths.append(os.path.join(output_path,output_name))
        return image_paths


    for num in range(0, pageNum):
        page = doc.load_page(num)
        pix = page.get_pixmap(dpi=200)
        output_name = f"{filename}_{num}.png"
        pix.save(os.path.join(output_path,output_name))
        image_paths.append(os.path.join(output_path,output_name))
    return image_paths


if __name__=='__main__':
    a = convert_pdf_to_images(r"C:\Users\<USER>\Desktop\新建文件夹\【海棠悦府】改善户型说明书.pdf")

