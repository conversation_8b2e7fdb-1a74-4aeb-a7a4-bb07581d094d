###  

# nlp-nlu-train，nlp-nlu-predict服务配置说明（在public的yaml-base-comfig进行配置）

## 单机和多机的情况（按情况修改send_model_mode配置项）

###  单机（只有一台机器一个ip）

* 情况一：单机多个docker：需要各docker共用conf/setting.ini的ModelPath路径
* 情况二：单机一个docker：无需改动

### 多机（有多台机器多个ip）

步骤1：每台机器需按单机部署要求部署

步骤2：

**train**端**send_model_mode**需要选择下面其中一种发送方式（预测端不用管）

* **Nas**：**Nas**需确保多机共用一台**Nas**
* **Redis**：多机共用一个**Redis** 
* **Rsync**：确保系统支持**rsync**指令

模型训练完后会通过选定的发送方式同步模型到各节点

* **Nas**

```
nlp:
  send_model_mode: "Nas"
```

* Redis

```
nlp:
  send_model_mode: "Redis"
```

* **Rsync**

```
nlp:
  send_model_mode: "Rsync"
```

## 模型是否需要自动上下线的情况（按情况修改auto_online_model配置项）
### 需要稳定的预测服务，客户只有少量模型id（比如昊申、税务局），没有内存不足的问题。（不启用自动上下线）
```
nlp:
  auto_online_model: False
```
### 客户有大量模型id（比如云讯通）且内存资源不足的情况。（启用自动上下线）
```
nlp:
  auto_online_model: True
```
