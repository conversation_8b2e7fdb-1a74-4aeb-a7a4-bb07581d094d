{"return_dict": true, "output_hidden_states": false, "output_attentions": false, "torchscript": false, "torch_dtype": "float16", "use_bfloat16": false, "tf_legacy_loss": false, "pruned_heads": {}, "tie_word_embeddings": true, "chunk_size_feed_forward": 0, "is_encoder_decoder": false, "is_decoder": false, "cross_attention_hidden_size": null, "add_cross_attention": false, "tie_encoder_decoder": false, "max_length": 20, "min_length": 0, "do_sample": false, "early_stopping": false, "num_beams": 1, "num_beam_groups": 1, "diversity_penalty": 0.0, "temperature": 1.0, "top_k": 50, "top_p": 1.0, "typical_p": 1.0, "repetition_penalty": 1.0, "length_penalty": 1.0, "no_repeat_ngram_size": 0, "encoder_no_repeat_ngram_size": 0, "bad_words_ids": null, "num_return_sequences": 1, "output_scores": false, "return_dict_in_generate": false, "forced_bos_token_id": null, "forced_eos_token_id": null, "remove_invalid_values": false, "exponential_decay_length_penalty": null, "suppress_tokens": null, "begin_suppress_tokens": null, "architectures": ["JinaBertModel"], "finetuning_task": null, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "tokenizer_class": null, "prefix": null, "bos_token_id": null, "pad_token_id": 0, "eos_token_id": null, "sep_token_id": null, "decoder_start_token_id": null, "task_specific_params": null, "problem_type": null, "_name_or_path": "/root/autodl-tmp/model/jina-embeddings-v2-base-zh-new", "transformers_version": "4.44.2", "auto_map": {"AutoConfig": "/root/autodl-tmp/LLMEmbedding/CallLLM/jina_bert_implementation--configuration_bert.JinaBertConfig", "AutoModel": "/root/autodl-tmp/LLMEmbedding/CallLLM/jina_bert_implementation--modeling_bert.JinaBertModel", "AutoModelForMaskedLM": "/root/autodl-tmp/LLMEmbedding/CallLLM/jina_bert_implementation--modeling_bert.JinaBertForMaskedLM", "AutoModelForQuestionAnswering": "/root/autodl-tmp/LLMEmbedding/CallLLM/jina_bert_implementation--modeling_bert.JinaBertForQuestionAnswering", "AutoModelForSequenceClassification": "/root/autodl-tmp/LLMEmbedding/CallLLM/jina_bert_implementation--modeling_bert.JinaBertForSequenceClassification", "AutoModelForTokenClassification": "/root/autodl-tmp/LLMEmbedding/CallLLM/jina_bert_implementation--modeling_bert.JinaBertForTokenClassification"}, "gradient_checkpointing": false, "model_max_length": 8192, "model_type": "bert", "vocab_size": 61056, "hidden_size": 768, "num_hidden_layers": 12, "num_attention_heads": 12, "hidden_act": "gelu", "intermediate_size": 3072, "hidden_dropout_prob": 0.1, "attention_probs_dropout_prob": 0.1, "max_position_embeddings": 8192, "type_vocab_size": 2, "initializer_range": 0.02, "layer_norm_eps": 1e-12, "position_embedding_type": "alibi", "use_cache": true, "classifier_dropout": null, "feed_forward_type": "g<PERSON><PERSON>", "emb_pooler": "mean", "attn_implementation": null}