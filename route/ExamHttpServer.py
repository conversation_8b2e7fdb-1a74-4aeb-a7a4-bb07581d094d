import json
import random
import time
from concurrent.futures import ThreadPoolExecutor

from flask import Flask, request, Blueprint, current_app

from main.ExamMain import ExamMain
from main.SpeechTextMain import SpeechTextMain
from setting import logger
from utils.ModelManager import ModelManager
from utils.my_utils import MyEncoder, json_message, random_id
from utils.filechat_utils import write_comments
from utils.To_html import my_to_html

# app = Flask(__name__)
ppt_app = Blueprint('ppt_app', __name__)


exam_main = ExamMain()
speech_text_main = SpeechTextMain()
executor = ThreadPoolExecutor(max_workers=1)
# model_manager = current_app.config['model_manager']


@ppt_app.route("/nlp/exam/upload", methods=["POST"])
def exam_upload():
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        if "base64s" not in data:
            info_in_data = {"service": "ExamUpload", "from": data}
        else:
            info_in_data = {"service": "ExamUpload", "from": str(data)[0:100]}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        base64s = data.get('base64s', None)
        filenames = data.get('filenames', "")
        tab_txt = data.get('tab_txt', "")
        if "callback_url" in data:
            callback_url = data.get('callback_url', '')
        else:
            callback_url = data.get("callback", "")
        need_check = data.get("need_check", 0)
        prompt_data_key = data.get("prompt_data_key", "")
        ocr_switch = data.get("ocr_switch", "0")
        other_model_id = data.get("other_model_id", "")
        other_model_type = data.get("other_model_type", "")
        enterprise_id = data.get("enterprise_id", "")
        ocr_switch = True if ocr_switch == "1" or ocr_switch == 1 else False
        question_types = data.get("question_types", ["0", "1", "2", "3", "4"])
        logger.debug(f"接收到的model_id:{model_id}")
        logger.debug(f"接收到的filenames:{filenames}")
        if isinstance(filenames, str) and filenames != "":
            filenames = [filenames]

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":1, "msg":"empty model_id"}')
        if len(filenames) == 0:
            logger.warning("预测错误: empty filenames")
            return json_message('{"code":1, "msg":"empty filenames"}')
        if len(filenames):
            if base64s and type(base64s) == list:
                data_for_model_manager = [{'extract_qa': "1", 'filename': filename, "question_types": question_types, 'base64': base64_, "prompt_data_key": prompt_data_key, "need_check": need_check, "ocr_switch": ocr_switch} for filename, base64_ in zip(filenames, base64s)]
            else:
                data_for_model_manager = [{'extract_qa': "1", 'filename': filename, "question_types": question_types, "prompt_data_key": prompt_data_key, "need_check": need_check, "ocr_switch": ocr_switch} for filename, in zip(filenames)]

            data_for_model_manager[0]["other_model_id"] = other_model_id
            data_for_model_manager[0]["other_model_type"] = other_model_type
            data_for_model_manager[0]["enterprise_id"] = enterprise_id
            executor.submit(current_app.config['model_manager'].append_train_queue, model_id, data=data_for_model_manager, data_key=f"exam_train_data_{model_id}", model_list=[exam_main, speech_text_main], callback_url=callback_url, scenario="smartllm")
            result["msg"] = '上传成功!'
            result["model_id"] = model_id
            result["filenames"] = filenames
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            logger.error("请求数据出错,data:{}".format(data))
    except Exception as e:
        result["code"] = 1
        result["msg"] = '上传文件失败,报错:{}'.format(e)
        logger.error('上传文件失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "ExamUpload", "out": result, "time": end - start}
    time.sleep(2)  # 等待加入队列
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@ppt_app.route("/nlp/exam/query", methods=["POST"])
def exam_query():
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "msg": "", "finish": 0, "single_choice": [], "multi_choice": [],
              "cloze_question": [], "judge_question": [], "simple_question": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "ExamQuery", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data['model_id']

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":400, "msg":"empty doc_id"}')
        q_result = exam_main.query(model_id)
        for k, v in q_result.items():
            result[k] = v
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'抽题报错:{e}'
        logger.error(f'抽题报错:{e}', exc_info=True)

    end = time.time()
    info_out_data = {"service": "ExamQuery", "out": result, "time": end - start}
    if int(result['finish']) == 1:
        logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@ppt_app.route("/nlp/exam/query_test", methods=["POST"])
def exam_query_test():
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "msg": "", "finish": 0, "prompt": [], "llm_output": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "ExamQuery", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data['model_id']

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":400, "msg":"empty doc_id"}')
        q_result = exam_main.query_test(model_id)
        for k, v in q_result.items():
            result[k] = v
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'抽题报错:{e}'
        logger.error(f'抽题报错:{e}', exc_info=True)

    end = time.time()
    info_out_data = {"service": "ExamQuery", "out": result, "time": end - start}
    if int(result['finish']) == 1:
        logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@ppt_app.route("/nlp/ppt/upload", methods=["POST"])
def ppt_upload():
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        if "base64s" not in data:
            info_in_data = {"service": "PPTUpload", "from": data}
        else:
            info_in_data = {"service": "PPTUpload", "from": str(data)[0:100]}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        base64s = data.get('base64s', None)
        filenames = data.get('filenames', "")
        image_list = data.get("imageList", [])
        if "callback_url" in data:
            callback_url = data.get('callback_url', '')
        else:
            callback_url = data.get("callback", "")
        prompt_data_key = data.get("prompt_data_key", "")
        question_types = data.get("question_types", [])
        other_model_id = data.get("other_model_id", "")
        other_model_type = data.get("other_model_type", "")
        enterprise_id = data.get("enterprise_id", "")

        logger.debug(f"接收到的model_id:{model_id}")
        logger.debug(f"接收到的filenames:{filenames}")
        if isinstance(filenames, str):
            filenames = [filenames]
        if isinstance(image_list, str):
            image_list = [image_list]

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":1, "msg":"empty model_id"}')
        if len(filenames) == 0:
            logger.warning("预测错误: empty filenames")
            return json_message('{"code":1, "msg":"empty filenames"}')
        if len(filenames):
            if base64s and type(base64s) == list:
                data_for_model_manager = [{'speech_text': "1", 'filename': filename, "question_types": question_types, 'base64': base64_, "prompt_data_key": prompt_data_key, 'imageList': image_list} for filename, base64_ in zip(filenames, base64s)]
            else:
                data_for_model_manager = [{'speech_text': "1", 'filename': filename, "question_types": question_types, "prompt_data_key": prompt_data_key, 'imageList': image_list} for filename, in zip(filenames)]

            data_for_model_manager[0]["other_model_id"] = other_model_id
            data_for_model_manager[0]["other_model_type"] = other_model_type
            data_for_model_manager[0]["enterprise_id"] = enterprise_id
            executor.submit(current_app.config['model_manager'].append_train_queue, model_id, data=data_for_model_manager, data_key=f"speech_text_train_data_{model_id}", model_list=[exam_main, speech_text_main], callback_url=callback_url, scenario="smartllm")
            result["msg"] = '上传成功!'
            result["model_id"] = model_id
            result["filenames"] = filenames
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            logger.error("请求数据出错,data:{}".format(data))
    except Exception as e:
        result["code"] = 1
        result["msg"] = '上传文件失败,报错:{}'.format(e)
        logger.error('上传文件失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "PPTUpload", "out": result, "time": end - start}
    time.sleep(2)  # 等待加入队列
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@ppt_app.route("/nlp/ppt/query", methods=["POST"])
def ppt_query():
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "msg": "", "finish": 0, "ppt": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "PPTQuery", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data['model_id']
        result["model_id"] = model_id

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":400, "msg":"empty doc_id"}')
        q_result = speech_text_main.query(model_id)
        for k, v in q_result.items():
            result[k] = v
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'PPT演讲稿查询报错:{e}'
        logger.error(f'PPT演讲稿查询报错:{e}', exc_info=True)

    end = time.time()
    info_out_data = {"service": "PPTQuery", "out": result, "time": end - start}
    if int(result['finish']) == 1:
        logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@ppt_app.route("/nlp/ppt/single_query", methods=["POST"])
def ppt_single_query():
    start = time.time()
    result = {'code': 0, 'msg': '成功', 'model_id': "", 'finish': 0, 'result': "", "sn": ""}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "PPTSingleQuery", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        filenames = data['filenames']
        model_id = data.get('model_id', "")
        image = data.get("image", "")
        page = int(data['page'])
        prompt_data_key = data.get("prompt_data_key", "")
        callback = data.get("callback", "")
        is_new_request = data.get("is_new_request", True)  # 是否是新的请求,如果是新的请求就不用查询redis结果了
        task_type = int(data.get("task_type", 0))  # 0:重新生成,1:改写,2:缩写,3:扩写
        old_text = data.get("old_text", "")  # 之前生成的演讲稿
        other_model_dict = {
            "other_model_id": data.get("other_model_id", ""),
            "other_model_type": data.get("other_model_type", ""),
            "enterprise_id": data.get("enterprise_id", ""),
            "model_id": model_id,
        }
        result["model_id"] = model_id
        logger.debug(f"is_new_request:{type(is_new_request)},{is_new_request}")
        if task_type not in [0, 1, 2, 3]:
            error_msg = f"接口调用报错,task_type:{task_type}有误,task_type必须是0,1,2,3其中之一"
            logger.error(error_msg)
            return json_message('{"code":400, "msg":"接口调用报错,task_type必须是0,1,2,3其中之一"}')
        if model_id == '':
            model_id = random_id(use_time_stamp=True)
            logger.warning(f"PPT生成单页演讲稿,model_id为空,随机生成一个:{model_id}")
        if task_type == 0:
            if is_new_request:
                executor.submit(speech_text_main.single_page_query, filename=filenames, model_id=model_id, page=page, image_url=image, prompt_data_key=prompt_data_key, callback=callback, is_new_request=is_new_request, task_type=task_type, old_text=old_text, need_callback=True, other_model_dict=other_model_dict)
            else:
                q_result = speech_text_main.single_page_query(filename=filenames, model_id=model_id, page=page,
                                                              image_url=image, prompt_data_key=prompt_data_key,
                                                              callback=callback, is_new_request=is_new_request,
                                                              task_type=task_type, old_text=old_text,
                                                              need_callback=True, other_model_dict=other_model_dict)
                for k, v in q_result.items():
                    result[k] = v
        else:
            q_result = speech_text_main.single_page_query(filename=filenames, model_id=model_id, page=page, image_url=image, prompt_data_key=prompt_data_key, callback=callback, is_new_request=is_new_request, task_type=task_type, old_text=old_text, need_callback=False, other_model_dict=other_model_dict)
            for k, v in q_result.items():
                result[k] = v
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'PPT生成单页演讲稿报错:{e}'
        logger.error(f'PPT生成单页演讲稿报错:{e}', exc_info=True)

    end = time.time()
    info_out_data = {"service": "PPTSingleQuery", "out": result, "time": end - start}
    if int(result['finish']) == 1:
        logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@ppt_app.route("/nlp/ppt/add_comment", methods=["POST"])
def ppt_add_comment():
    start = time.time()
    result = {'code': 0, 'msg': '成功', 'url': "", "sn": ""}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "PPTAddComment", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        filenames = data["filenames"]
        upload_file_url = data.get("upload_file_url", "")
        comments = data["comments"]
        upload_res = speech_text_main.add_comments(filename=filenames, comments=comments, sn=result["sn"], upload_file_url=upload_file_url)
        result["code"] = upload_res.get("code", "")
        result["msg"] = upload_res.get("msg", "")
        result["url"] = upload_res.get("result", "")
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'PPT插入备注报错:{e}'
        logger.error(f'PPT插入备注报错:{e}', exc_info=True)

    end = time.time()
    info_out_data = {"service": "PPTAddComment", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@ppt_app.route("/nlp/to_html", methods=["POST"])
def file_to_html():
    start = time.time()
    result = {'code': 0, 'msg': '成功', 'url': "", "sn": ""}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "ToHtml", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        file_path = data.get("filename", "")
        # 回调参数
        callback = data.get("callback", "")
        # 图片识别
        ocr_switch = data.get("ocr_switch", "0")
        ocr_switch = True if ocr_switch == "1" or ocr_switch == 1 else False
        # 参数检测
        if file_path == "" or callback == "":
            result["code"] = 1
            result["msg"] = "参数不全"
            return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))
        to_html_res = my_to_html.run(file_path, callback, result["sn"], ocr_switch)
        result["code"] = to_html_res["code"]
        result["msg"] = to_html_res["msg"]
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'转html报错:{e}'
        logger.error(f'转html报错:{e}', exc_info=True)

    end = time.time()
    info_out_data = {"service": "ToHtml", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@ppt_app.route("/nlp/exam/test_callback", methods=["POST"])
def test_callback():
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "msg": ""}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "TestCallBack", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))
        result["sn"] = data.get("sn", "")
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'回调报错:{e}'
        logger.error(f'回调报错:{e}', exc_info=True)

    end = time.time()
    info_out_data = {"service": "TestCallBack", "out": result, "time": end - start}
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))

# if __name__ == '__main__':
#     app.run(host='0.0.0.0', port=5002, debug=False)
