import time,json,os
from flask import Flask, request, Blueprint, current_app
import requests
from concurrent.futures import ThreadPoolExecutor


from setting import logger
from utils.ModelManager import ModelManager
from utils.my_utils import MyEncoder, json_message, lang_symbol_to_lang_string
from main.RetrievalMain import Retrieval<PERSON><PERSON>
from main.LLMDecodeMain import Openai_Decoder
from model.LanguageJudge.LanguageJudgeRegular import LanguageJudgeRegular

# try:
#     from utils.nacos_register import NacosHelper
#     nacos_r = NacosHelper("nlp-filechat")
#     nacos_r.nacos_fun()
# except:
#     logger.error('nacos 注册失败', exc_info=True)

# app = Flask(__name__)
file_chat = Blueprint('file_chat', __name__)

retrieval_main = RetrievalMain()
chat = Openai_Decoder()
executor = ThreadPoolExecutor(max_workers=10)
# model_manager = current_app.config['model_manager']
language_judge_regular = LanguageJudgeRegular()


@file_chat.route("/nlp/filechat/upload", methods=["POST"])
def upload():
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        if "base64s" not in data:
            info_in_data = {"service": "upload", "from": data}
        else:
            info_in_data = {"service": "upload", "from": str(data)[0:100]}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        base64s = data.get('base64s', None)
        filenames = data.get('filenames', "")
        from hashlib import md5
        data_key = md5(str(data).encode()).hexdigest()
        # language: chs、cht、yue、en
        source_langs = data.get("source_langs", "chs")
        target_langs = data.get("target_langs", "chs")
        logger.debug(f"接收到的model_id:{model_id}")
        logger.debug(f"接收到的filenames:{filenames}")
        logger.debug(f"接收到的source_langs:{source_langs}")
        logger.debug(f"接收到的target_langs:{target_langs}")

        if isinstance(filenames, str):
            filenames = [filenames]
        if isinstance(source_langs, str):
            source_langs = [source_langs]
        if isinstance(target_langs, str):
            target_langs = [target_langs]
        source_langs = [lang_symbol_to_lang_string(s) for s in source_langs]
        target_langs = [lang_symbol_to_lang_string(s) for s in target_langs]
        # logger.debug(f"接收到的base64s长度:{len(base64s)},{[len(b) for b in base64s]}")

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":1, "msg":"empty model_id"}')
        if len(filenames) == 0:
            logger.warning("预测错误: empty filenames")
            return json_message('{"code":1, "msg":"empty filenames"}')
        if len(filenames) != len(source_langs):
            logger.warning("预测错误: len(filenames) != len(source_langs)")
            return json_message('{"code":1, "msg":"len(filenames) != len(source_langs)"}')
        if len(filenames) and len(filenames) == len(source_langs):
            if base64s and type(base64s) == list:
                data_for_model_manager = [{'filename': filename, 'base64': base64_, 'source_lang': source_lang_, "target_langs": target_langs}
                                          for filename, base64_, source_lang_ in zip(filenames, base64s, source_langs)]
            else:
                data_for_model_manager = [{'filename': filename, 'source_lang': source_lang_, "target_langs": target_langs}
                                          for filename, source_lang_ in zip(filenames, source_langs)]

            # retrieval_main.train(model_id,data=data_for_model_manager)
            # model_manager.append_train_queue(model_id,data=data_for_model_manager)
            executor.submit(current_app.config['model_manager'].append_train_queue, model_id, data=data_for_model_manager, model_list=[retrieval_main],data_key=data_key)
            result["msg"] = '上传成功!'
            result["model_id"] = model_id
            result["filenames"] = filenames
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            logger.error("请求数据出错,data:{}".format(data))
    except Exception as e:
        result["code"] = 1
        result["msg"] = '上传文件失败,报错:{}'.format(e)
        logger.error('上传文件失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "Upload", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@file_chat.route("/nlp/filechat/query", methods=["POST"])
def query():
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "answer": "", "end": "0"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "query", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data['model_id']
        query = data['query']
        query_id = data["query_id"]
        session_id = data['session_id']
        lang = data.get("lang", "chs")
        # lang: chs、cht、yue、en
        result['query'] = query

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":400, "msg":"empty doc_id"}')
        lang = lang_symbol_to_lang_string(lang)

        if session_id in chat.answers and query_id in chat.answers.get(session_id,[]):
            out = chat.answers[session_id][query_id]
            result['hit_filenames'] = out.get('hit_filenames', [])
            result['retrival_content'] = out.get('retrival_content', [])
            result['retrival_info'] = out.get('retrival_info', [])
            result['answer'] = out.get('answer', "")
            result['end'] = out.get('end', "0")
        else:
            if query:
                try:
                    # search_result = retrieval_main.predict(model_id,query, topn=10)
                    # executor.submit(chat.chat, query, search_result,session_id, query_id)
                    executor.submit(chat.search_and_chat, retrieval_main, model_id, query, session_id, query_id, lang)
                except Exception as e:
                    result["code"] = 1
                    result["msg"] = "请求数据出错,data:{}".format(data)
                    logger.error("请求数据出错,data:{}".format(data),exc_info=True)
            else:
                result["code"] = 1
                result["msg"] = "query为空"
                logger.error("query为空")
    except Exception as e:
        result["code"] = 1
        result["msg"] = 'gpt回答失败,报错:{}'.format(e)
        logger.error('gpt回答失败,错误：{}'.format(e), exc_info=True)


    end = time.time()
    info_out_data = {"service": "query", "out": result, "time": end - start}
    if result['end'] == '1':
        logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@file_chat.route("/filechat/language_judge", methods=["POST"])
def language_judge():
    """
    判断文本是否是给定语言的
    """
    start = time.time()
    result = {"sn": "", "code": 0, "query": "", "msg": "", "is_lang": 0}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "language_judge", "from": str(data)[0:100]}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        query = data.get('query', '')
        lang = data.get('lang', 'chs')
        list_flag = True
        if isinstance(lang, list):
            lang_list = lang
        else:
            lang_list = lang
            list_flag = False
        result["query"] = query
        # lang: chs、cht、yue、en

        if query == '':
            logger.warning("预测错误: empty query")
            return json_message('{"code":1, "msg":"empty query"}')
        is_lang_list = []
        for lang in lang_list:
            lang = lang_symbol_to_lang_string(lang)
            is_lang = language_judge_regular.language_judge(text=query, lang=lang)
            is_lang_list.append(1 if is_lang else 0)
        result["is_lang"] = is_lang_list if list_flag else is_lang_list[0]
    except Exception as e:
        result["code"] = 1
        result["msg"] = f'language_judge错误,报错:{e}'
        logger.error(f'language_judge错误,报错:{e}', exc_info=True)

    end = time.time()
    info_out_data = {"service": "language_judge", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@file_chat.route("/filechat/search", methods=["POST"])
def search():
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "upload", "from": str(data)[0:100]}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        query = data.get('query', '')

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":1, "msg":"empty model_id"}')

        output = retrieval_main.predict(model_id,query)
        result["msg"] = '上传成功!'
        result["model_id"] = model_id
        result['query'] = query
        result['data'] = output
    except Exception as e:
        result["code"] = 1
        result["msg"] = 'search错误,报错:{}'.format(e)
        logger.error('search错误,错误：{}'.format(e),exc_info=True)

    end = time.time()
    info_out_data = {"service": "FilechatSearch", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@file_chat.route("/filechat/otherpeople/query", methods=["POST"])
def otherpeople_query():
    """
    https://gpt.sealmoo.com/qa/api/question
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "answer": "", "end": "0"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "query", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        query = data.get('query', '')
        result['query'] = query

        import requests
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36 OPR/52.0.2871.99',
            'Content-Type': 'application/json'}

        url = 'https://gpt.sealmoo.com/qa/api/question'
        output = requests.post(url, data=json.dumps({'question': query}, ensure_ascii=False).encode('utf-8'),
                               headers=headers).json()
        if output['success']==False:
            output['answer'] = output['message']
        result['answer'] = output['answer']

    except Exception as e:
        result["code"] = 1
        result["msg"] = 'gpt回答失败,报错:{}'.format(e)
        logger.error('gpt回答失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "query", "out": result, "time": end - start}
    if result['end'] == '1':
        logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@file_chat.route("/filechat/upload", methods=["POST"])
def upload_2():
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "data": []}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        if "base64s" not in data:
            info_in_data = {"service": "upload", "from": data}
        else:
            info_in_data = {"service": "upload", "from": str(data)[0:100]}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data.get('model_id', '')
        base64s = data.get('base64s', None)
        filenames = data.get('filenames', "")
        # language: chs、cht、yue、en
        source_langs = data.get("source_langs", "chs")
        target_langs = data.get("target_langs", "chs")
        logger.debug(f"接收到的model_id:{model_id}")
        logger.debug(f"接收到的filenames:{filenames}")
        logger.debug(f"接收到的source_langs:{source_langs}")
        logger.debug(f"接收到的target_langs:{target_langs}")

        if isinstance(filenames, str):
            filenames = [filenames]
        if isinstance(source_langs, str):
            source_langs = [source_langs]
        if isinstance(target_langs, str):
            target_langs = [target_langs]
        source_langs = [lang_symbol_to_lang_string(s) for s in source_langs]
        target_langs = [lang_symbol_to_lang_string(s) for s in target_langs]
        # logger.debug(f"接收到的base64s长度:{len(base64s)},{[len(b) for b in base64s]}")

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":1, "msg":"empty model_id"}')
        if len(filenames) == 0:
            logger.warning("预测错误: empty filenames")
            return json_message('{"code":1, "msg":"empty filenames"}')
        if len(filenames) != len(source_langs):
            logger.warning("预测错误: len(filenames) != len(source_langs)")
            return json_message('{"code":1, "msg":"len(filenames) != len(source_langs)"}')
        if len(filenames) and len(filenames) == len(source_langs):
            if base64s and type(base64s) == list:
                data_for_model_manager = [{'filename': filename, 'base64': base64_, 'source_lang': source_lang_, "target_langs": target_langs}
                                          for filename, base64_, source_lang_ in zip(filenames, base64s, source_langs)]
            else:
                data_for_model_manager = [{'filename': filename, 'source_lang': source_lang_, "target_langs": target_langs}
                                          for filename, source_lang_ in zip(filenames, source_langs)]

            # retrieval_main.train(model_id,data=data_for_model_manager)
            # model_manager.append_train_queue(model_id,data=data_for_model_manager)
            executor.submit(current_app.config['model_manager'].append_train_queue, model_id, data=data_for_model_manager, model_list=[retrieval_main])
            result["msg"] = '上传成功!'
            result["model_id"] = model_id
            result["filenames"] = filenames
        else:
            result["code"] = 1
            result["msg"] = "请求数据出错,data:{}".format(data)
            logger.error("请求数据出错,data:{}".format(data))
    except Exception as e:
        result["code"] = 1
        result["msg"] = '上传文件失败,报错:{}'.format(e)
        logger.error('上传文件失败,错误：{}'.format(e), exc_info=True)

    end = time.time()
    info_out_data = {"service": "Upload", "out": result, "time": end - start}
    logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


@file_chat.route("/filechat/query", methods=["POST"])
def query_2():
    """
    意图分类
    """
    start = time.time()
    result = {"sn": "", "code": 0, "model_id": "", "query": "", "msg": "", "answer": "", "end": "0"}

    try:
        try:
            data = request.get_data(as_text=True)
            data = json.loads(data)
        except:
            data = request.form

        info_in_data = {"service": "query", "from": data}
        logger.info(json.dumps(info_in_data, cls=MyEncoder, ensure_ascii=False))

        result["sn"] = data.get("sn", "")
        model_id = data['model_id']
        query = data['query']
        query_id = data["query_id"]
        session_id = data['session_id']
        lang = data.get("lang", "chs")
        # lang: chs、cht、yue、en
        result['query'] = query

        if model_id == '':
            logger.warning("预测错误: empty model_id")
            return json_message('{"code":400, "msg":"empty doc_id"}')
        lang = lang_symbol_to_lang_string(lang)

        if session_id in chat.answers and query_id in chat.answers.get(session_id,[]):
            out = chat.answers[session_id][query_id]
            result['hit_filenames'] = out.get('hit_filenames', [])
            result['retrival_content'] = out.get('retrival_content', [])
            result['retrival_info'] = out.get('retrival_info', [])
            result['answer'] = out.get('answer', "")
            result['end'] = out.get('end', "0")
        else:
            if query:
                try:
                    # search_result = retrieval_main.predict(model_id,query, topn=10)
                    # executor.submit(chat.chat, query, search_result,session_id, query_id)
                    executor.submit(chat.search_and_chat, retrieval_main, model_id, query, session_id, query_id, lang)
                except Exception as e:
                    result["code"] = 1
                    result["msg"] = "请求数据出错,data:{}".format(data)
                    logger.error("请求数据出错,data:{}".format(data),exc_info=True)
            else:
                result["code"] = 1
                result["msg"] = "query为空"
                logger.error("query为空")
    except Exception as e:
        result["code"] = 1
        result["msg"] = 'gpt回答失败,报错:{}'.format(e)
        logger.error('gpt回答失败,错误：{}'.format(e), exc_info=True)


    end = time.time()
    info_out_data = {"service": "query", "out": result, "time": end - start}
    if result['end'] == '1':
        logger.info(json.dumps(info_out_data, cls=MyEncoder, ensure_ascii=False))
    return json_message(json.dumps(result, cls=MyEncoder, ensure_ascii=False))


# if __name__ == '__main__':
#     file_chat.run(host='0.0.0.0', port=5001, debug=False)
