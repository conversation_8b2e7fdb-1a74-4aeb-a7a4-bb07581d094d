#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:setting.py.py
@time:2021/02/19
"""

import configparser
import os

from utils.logHelper import log_initialize
from contextvars import ContextVar

request_id_var = ContextVar('request_id')
request_id_var.set("0")
""" 参数 """
ServerName = 'NLP_Platform'
MAIN_DIR = os.path.dirname(os.path.realpath(__file__))
config_file = os.path.join(MAIN_DIR, "conf/setting.ini")
config = configparser.ConfigParser()
config.read(config_file, encoding="utf-8")
SAVE_MODEL_DIR = config.get("common", "ModelPath")
if not SAVE_MODEL_DIR.startswith('/'):
    SAVE_MODEL_DIR = os.path.join(MAIN_DIR,SAVE_MODEL_DIR)
MODEL_RECORD_FILE = os.path.join(SAVE_MODEL_DIR, 'model_record')
TRAIN_RECORD_FILE = os.path.join(SAVE_MODEL_DIR, "train_record")

# 模型下线延迟时间,分钟
MODEL_OFFLINE_TIME = 30

# 自动模型上下线
AUTO_ONLINE_MODEL = config.getboolean("common", "auto_online_model")
LRU_MODEL_LOAD_NUM = config.getint('common','lru_model_load_num',fallback=0)

NACOS_ADDR = config.get("NACOS", "server_addresses")
# 同步模型参数
SEND_MODEL_MODE = config.get("SEND_MODEL", "send_model_mode")  # [Redis, Rsync]
PREDICT_SERVER_IPS = []  # '*************']
DOWNLOAD_MODEL_CHANNEL = "nlp_download_model_channel"
SERVICE_CHANNEL_DICT = {
    "ClassifyMain": {
        "switch_model": "nlp_classification_switch_model_channel",
        "key": "labelData",
        "need_wait": True
    },
    "RegularMain": {
        "switch_model": "nlp_regular_switch_model_channel",
        "key": "keywords",
        "need_wait": False
    },
    "SimilarityScoreMain": {
        "switch_model": "nlp_sim_score_switch_model_channel",
        "key": "labelData",
        "need_wait": True
    },
    "SearchMain": {
        "switch_model": "nlp_search_switch_model_channel",
        "key": "labelData",
        "need_wait": True
    },
    "SpellCheckMain": {
        "switch_model": "nlp_spell_check_switch_model_channel",
        "key": "labelData",
        "need_wait": True
    },
    "TfidfSimilarityScoreMain": {
        "switch_model": "nlp_tfidf_sim_score_switch_model_channel",
        "key": "scoreData",
        "need_wait": False
    },
    "TfidfSimilarityScorePointMain": {
        "switch_model": "nlp_tfidf_sim_score_point_switch_model_channel",
        "key": "scoreDataPoint",
        "need_wait": False
    },
    "BertTextPairSimPointMain": {
        "switch_model": "nlp_bert_sim_score_point_switch_model_channel",
        "key": "scoreDataPoint",
        "need_wait": False
    },
    "ExamMain": {
        "switch_model": "exam_switch_model_channel",
        "key": "extract_qa",
        "need_wait": False
    },
    "SpeechTextMain": {
        "switch_model": "speech_text_switch_model_channel",
        "key": "speech_text",
        "need_wait": False
    },
    "RetrievalMain": {
            "switch_model": "retrieval_switch_model_channel",
            "key": "filename",
            "need_wait": False
        },
"RetrievalMain_bge": {
            "switch_model": "retrieval_bge_switch_model_channel",
            "key": "filename",
            "need_wait": False
        }
}

# 共用参数
NUM_THREADS = int(config.get("common", "num_threads"))     # CPU占用个数
SEED = 2021
CLIENT = config.get("common", "client",fallback='train')
GPU_DIVICE = config.get("common", "gpu")
MAX_MODELS_TRAIN_CLIENT = 5
USE_PORMETHEUS = config.getboolean("common", "use_prometheus")
SERVICE_NAME = config.get("common", "service_name")
USE_SCENARIOS = config.get("common", "use_scenarios").split(',')
RSYNC_SEND_SPEED = config.get("SEND_MODEL", "rsync_send_speed")

#redis待训练任务队列的名称
WAITING_QUEUE = f"waiting_list_{SERVICE_NAME}"
LLM_WAITING_QUEUE = f"llm_waiting_list_{SERVICE_NAME}"
OTHER_LLM_WAITING_QUEUE = f"other_llm_waiting_list_{SERVICE_NAME}"
REDIS_TRAIN_CANCEL_HASH_KEY = "nlu_train_cancel_hash"
INCREMENT_TRAIN_WAITING_QUEUE = f"increment_train_waiting_list_{SERVICE_NAME}"

# 删除模型参数
REDIS_CHANNEL_DELETE_MODEL = f"nlu_channel_delete_model_{SERVICE_NAME}"

#外呼参数
CALLOUT_SERVICE = False
CALLOUT_MAX_LOAD_MODEL_NUMS = 20   #; 单进程最大加载模型数，8G内存可加载150个模型
CALLOUT_MODEL_EXIST_HOUR = 1   #; 模型自动下线时间
MAX_CONCURRENT_NUM = 200    #; 最大并发数配置项，单进程配置10并发数限制，tps233，CPU最大消耗4核心，平均响应时间40ms

# exam
EXAM_MODEL = config.get("EXAM", "exam_model")
OUR_MODEL_URL = config.get("EXAM", "our_model_url")
IMAGE_TO_TEXT_URL = config.get("LLM", "image_to_text_url")
OTHER_LLM_URL = config.get("LLM", "other_llm_url")

#filechat
FILECHAT_MODEL = config.get("FILECHAT", "filechat_model")
FILECHAT_LANG = config.get("FILECHAT", "filechat_lang")
FEILCHAT_EMBEDDING_URL = config.get("LLM", "embedding_url")
IMAGE_EMBEDDING_URL = config.get("LLM", "image_embedding_url",fallback='http://192.168.1.238:9775/image_embeddings')
FILECHAT_EMBEDDING_MODEL_NAME = config.get("FILECHAT", "filechat_embedding_model_name")
FILECHAT_MODEL_URL = config.get('FILECHAT', 'filechat_model_url')
FILECHAT_IMG2TEXT_URL_OPANAI = config.get('LLM', 'image_to_text_url_opanai')
FILECHAT_OCR = config.getboolean('FILECHAT', 'filechat_ocr', fallback=False)
FILECHAT_PPTX2IMA_URL = config.get('FILECHAT', 'filechat_pptx2ima_url')
FILECHAT_FILE_DIR = os.path.join(SAVE_MODEL_DIR,'FILECHAT_MODEL')
FILECHAT_TABLE_SQL = config.getboolean('FILECHAT', 'filechat_table_sql')
FILECHAT_RERANK_URL = config.get('LLM', 'rerank_url', fallback='')
FILECHAT_RERANK = eval(config.get('llm', 'filechat_rerank', fallback='None'))
FILECHAT_CUT_MERGE = config.getboolean('FILECHAT', 'filechat_cut_merge', fallback=False)


#translate
TRANSLATE_MODEL = config.get("TRANSLATE", "translate_model")
TRANSLATE_MODEL_URL = config.get("TRANSLATE", "translate_model_url")
YUE_TRANSLATE_MODEL_URL = config.get("TRANSLATE", "yue_translate_model_url")


# ppt-其他服务的IP
UPLOAD_FILE_URL = config.get("SERVER", "upload_file_url")
LLM_EMBEDDING_IP_PORT = config.get("SERVER", "llm_embedding_ip_port")

# 读取nacos配置
""" minio """
MINIO_URL = config.get("MINIO", "minio_url")
MINIO_NAME = config.get("MINIO", "minio_name")
MINIO_PASS = config.get("MINIO", "minio_pass")
BUCKETNAME = config.get("MINIO", "bucketName")

#ES配置
ES_HOST = config.get("ES", "es_host",fallback='*************:9200')
ES_USERNAME = config.get("ES", "es_username",fallback='elastic')
ES_PASSWORD = config.get("ES", "es_password",fallback='qnzs2020')

""" Redis """
USE_SINGLE_REDIS = config.getboolean("REDIS", "use_single_redis")
encrypt_redis = eval(config.get("REDIS", "redis_setting"))
# 需要解密
if config.getboolean("REDIS", "encrypt"):
    from encrypt.SM4 import sm4_key, sm4
    SM4 = sm4.SM4()
    key = sm4_key.get_key()
    encrypt_redis["password"] = SM4.decryptSM4(key, encrypt_redis["password"])
    REDIS_CLUSTER_PASSWORD = SM4.decryptSM4(key, eval(config.get("REDIS", "redis_cluster_password")))
else:
    REDIS_CLUSTER_PASSWORD = eval(config.get("REDIS", "redis_cluster_password"))
REDIS_SETTING = encrypt_redis
REDIS_CLUSTER_LIST = eval(config.get("REDIS", "redis_cluster_list"))
""" SQL """
SQL_SETTING = eval(config.get("SQL", "sql_setting"))

""" RabbitMQ """
try:
    USE_RABBIT_MQ = config.getboolean("RabbitMQ", "use_rabbit_mq")
    RABBIT_MQ_SETTING = eval(config.get("RabbitMQ", "rabbit_mq_setting"))
except:
    USE_RABBIT_MQ = False
    RABBIT_MQ_SETTING = ""

# 预训练语言模型
# PRETRAIN_BERT_NAME = 'ArcCSEPretrain'
# PRETRAIN_BERT_NAME = 'roberta_chinese_clue_tiny'
# PRETRAIN_BERT_NAME = 'roberta_chinese_clue_tiny_distill_0112_tiny_taskonlymse_e1'
# PRETRAIN_BERT_NAME = 'roberta_chinese_clue_tiny_anto'
PRETRAIN_BERT_NAME = "ft-jina-1220-153w-bidi-cosent-batch256"
# PRETRAIN_BERT_NAME = 'roberta_chinese_clue_base_distill_tiny'
PRETRAIN_BERT_DIR = f'{MAIN_DIR}/pretrain_model/{PRETRAIN_BERT_NAME}'
PRETRAIN_BERT_CONFIG = f'{MAIN_DIR}/pretrain_model/{PRETRAIN_BERT_NAME}/config.json'
PRETRAIN_BERT_VOCAB = f'{MAIN_DIR}/pretrain_model/{PRETRAIN_BERT_NAME}/vocab.txt'
PRETRAIN_BERT_MODEL_PT = f'{MAIN_DIR}/pretrain_model/{PRETRAIN_BERT_NAME}/pytorch_model.bin'
PRETRAIN_BERT_MODEL_TF = f'{MAIN_DIR}/pretrain_model/{PRETRAIN_BERT_NAME}/tf_model.h5'


classification_PRETRAIN_BERT_NAME = 'roberta_chinese_clue_tiny'
# PRETRAIN_BERT_NAME = 'roberta_chinese_clue_base_distill_tiny'
classification_PRETRAIN_BERT_DIR = f'{MAIN_DIR}/pretrain_model/{classification_PRETRAIN_BERT_NAME}'
classification_PRETRAIN_BERT_CONFIG = f'{MAIN_DIR}/pretrain_model/{classification_PRETRAIN_BERT_NAME}/config.json'
classification_PRETRAIN_BERT_VOCAB = f'{MAIN_DIR}/pretrain_model/{classification_PRETRAIN_BERT_NAME}/vocab.txt'
classification_PRETRAIN_BERT_MODEL_TF = f'{MAIN_DIR}/pretrain_model/{classification_PRETRAIN_BERT_NAME}/tf_model.h5'



# 文件
STOP_WORDS_FILE = f"{MAIN_DIR}/data/vocab/stopwords.txt"

""" PreRank 粗排 """
PRE_RANK_RETURN_NUM = 30

# 编辑距离
EDIT_PRE_RANK_METHOD = 'ratio'  # ['ratio', 'distance']

# Pretrain Bert
PRE_BERT_PRE_RANK_EMB_LAYER = [1, 4]  # 'cls' or int or list ['cls', 1, 2, 3, ..., [1, 2], [3, 4]]
PRE_BERT_PRE_RANK_BATCH_SIZE = 64
PRE_BERT_PRE_RANK_MAX_LEN = 128

# Pretrain Bert White
PRE_BERT_WHITE_PRE_RANK_EMB_LAYER = [1, 4]  # 'cls' or int or list ['cls', 1, 2, 3, ..., [1, 2], [3, 4]]
PRE_BERT_WHITE_PRE_RANK_DIM = 128
PRE_BERT_WHITE_PRE_RANK_BATCH_SIZE = 64
PRE_BERT_WHITE_PRE_RANK_MAX_LEN = 128

# SimCSE
SIMCSE_PRE_RANK_EMB_LAYER = [1, -1] # ["cls", 1, -1, [1, -1]]
SIMCSE_PRE_RANK_LEARNING_RATE = 8e-5
SIMCSE_PRE_RANK_BATCH_SIZE = 64
SIMCSE_PRE_RANK_NUM_BATCHS = 300
SIMCSE_PRE_RANK_EPOCHS = 30 # 30
SIMCSE_PRE_RANK_MAX_LEN = 128
SIMCSE_PRE_RANK_THRESHOLD = 0.7
SIMCSE_PRE_RANK_LOSS = "2n" # [n, 2n]
SIMCSE_PRE_RANK_SEARCH_METHOD = "hnsw" # [bm25, invert_index, hnsw, faiss]
SIMCSE_PRE_RANK_SEARCH_RETURN = 200

# ArcCSE
ARCCSE_PRE_RANK_EMB_LAYER = [1, -1] # ["cls", 1, -1, [1, -1]],[1, -1]
ARCCSE_PRE_RANK_USE_ARC_LOSS = True
ARCCSE_PRE_RANK_USE_TRI_LOSS = False
ARCCSE_PRE_RANK_USE_DCL_LOSS = False # Decouple Loss
ARCCSE_PRE_RANK_LOSS = "2n" # [n, 2n]
ARCCSE_PRE_RANK_LEARNING_RATE = 8e-5
ARCCSE_PRE_RANK_BATCH_SAMPLES = 64
ARCCSE_PRE_RANK_ENCODE_BATCH_SIZE = 16
ARCCSE_PRE_RANK_EPOCHS = 10 # 30
ARCCSE_PRE_RANK_MAX_LEN = int(config.get('SEARCH','arccse_pre_rank_max_len', fallback=64))
ARCCSE_PRE_RANK_THRESHOLD = 0.7
ARCCSE_PRE_RANK_SEARCH_METHOD = "hnsw" # [bm25, invert_index, hnsw, faiss]
ARCCSE_PRE_RANK_SEARCH_RETURN = 200
ARCCSE_PRE_RANK_HNSW_EF = 10000
# searchmain是否强制替换标点为空格
SEARCHMAIN_REPLACE_PUNCTUATION = True

""" Rank 精排 """
RANK_RETURN_NUM = 5

# Bert Pointwise
BERT_POINTEWISE_RANK_NUM_SAMPLE = 10
BERT_POINTEWISE_RANK_NEG_SAMPLE = 3
BERT_POINTEWISE_RANK_HARD_NEG_SAMPLE = 2
BERT_POINTEWISE_RANK_MAX_LEN = 128
BERT_POINTEWISE_RANK_LEARNING_RATE = 8e-5
BERT_POINTEWISE_RANK_EPOCHS = 10

""" TextPairSim 文本相似度打分"""
# PretrainTextPairSim
PRETRAIN_TEXT_PAIR_SIM_EMB_LAYER = "cls"
PRETRAIN_TEXT_PAIR_SIM_BATCH_SIZE = 64
PRETRAIN_TEXT_PAIR_SIM_MAX_LEN = 128

# SentBertTextPairSim
SENT_BERT_TEXT_PAIR_SIM_EMB_LAYER = "cls" # ["cls", 1, -1, [1, -1]]
SENT_BERT_TEXT_PAIR_SIM_LEARNING_RATE = 8e-5
SENT_BERT_TEXT_PAIR_SIM_BATCH_SIZE = 64
SENT_BERT_TEXT_PAIR_SIM_EPOCHS = 50
SENT_BERT_TEXT_PAIR_SIM_MAX_LEN = 128
SENT_BERT_TEXT_PAIR_SIM_OUTPUT = "dense" # [cosine, dense]

# SimCSETextPairSim
SIMCSE_TEXT_PAIR_SIM_EMB_LAYER = [1, -1] # ["cls", 1, -1, [1, -1]]
SIMCSE_TEXT_PAIR_SIM_LEARNING_RATE = 5e-5
SIMCSE_TEXT_PAIR_SIM_BATCH_SIZE = 64
SIMCSE_TEXT_PAIR_SIM_NUM_BATCHS = 600
SIMCSE_TEXT_PAIR_SIM_EPOCHS = 50
SIMCSE_TEXT_PAIR_SIM_MAX_LEN = 128
SIMCSE_TEXT_PAIR_SIM_THRESHOLD = 0.7
SIMCSE_TEXT_PAIR_SIM_LOSS = "2n" # [n, 2n]

""" 分类 """
# Bert
BERT_CLASSIFICATION_LEARNING_RATE = 8e-5
BERT_CLASSIFICATION_BATCH_SIZE = 64
BERT_CLASSIFICATION_EPOCHS = 50 # 50
BERT_CLASSIFICATION_MAX_LEN = 128

# DDrop
RDROP_CLASSIFICATION_LEARNING_RATE = 8e-5
RDROP_CLASSIFICATION_BATCH_SIZE = 32
RDROP_CLASSIFICATION_EPOCHS = 200
RDROP_CLASSIFICATION_MAX_LEN = 128
RDROP_CLASSIFICATION_DROP_RATE = 0.3
RDROP_CLASSIFICATION_ALPHA = 4

""" 聚类 """
# Single Pass
SINGLE_PASS_CLUSTER_EMB_TYPE = "bert" # ["bert", "tfidf"]
SINGLE_PASS_CLUSTER_EMB_LAYER = [1, 4] # ["cls", [1, 4]]
SINGLE_PASS_CLUSTER_USE_WHITE = False
SINGLE_PASS_CLUSTER_WHITE_DIM = 128
SINGLE_PASS_CLUSTER_MAX_LEN = 128
SINGLE_PASS_CLUSTER_BATCH_SIZE = 64
SINGLE_PASS_CLUSTER_LOW_THRESHOLD = 0.3
SINGLE_PASS_CLUSTER_HIGH_THRESHOLD = 0.7

# Birch Cluster
BIRCH_CLUSTER_EMB_TYPE = "bert" # ["bert", "tfidf"]
BIRCH_CLUSTER_EMB_LAYER = [1, 4] # ["cls", [1, 4]]
BIRCH_CLUSTER_USE_WHITE = True
BIRCH_CLUSTER_WHITE_DIM = 128
BIRCH_CLUSTER_MAX_LEN = 128
BIRCH_CLUSTER_BATCH_SIZE = 64

# Kmeans Cluster
KMEANS_CLUSTER_EMB_TYPE = "bert" # ["bert", "tfidf"]
KMEANS_CLUSTER_EMB_LAYER = [1, 4] # ["cls", [1, 4]]
KMEANS_CLUSTER_USE_WHITE = True
KMEANS_CLUSTER_WHITE_DIM = 128
KMEANS_CLUSTER_MAX_LEN = 128
KMEANS_CLUSTER_BATCH_SIZE = 64

# SCCL Cluster
SCCL_CLUSTER_EMB_LAYER = [1, 4] # ["cls", [1, 4]]
SCCL_CLUSTER_MAX_LEN = 128
SCCL_CLUSTER_BATCH_SIZE = 32
SCCL_CLUSTER_LEARNING_RATE = 1e-5
SCCL_CLUSTER_EPOCHS = 100

""" 联想输入 """
# NGram
NGRAM_CONTEXT_INPUT_N = 3
NGRAM_CONTEXT_RETURN_N = 5

""" 纠错 """
KENLM_PATH = eval(config.get("KENLM", "kenlm_path"))
MAX_SENTENCE_LEN = 50

""" 初始化日志对象 """
logPath = config.get("common", "ServerLogPath")  # 日志文件夹
ServerName = config.get("common", "ServerLogName")
logName = ServerName
logger = log_initialize(logPath, logName)
