import base64
import copy

import requests
import fcntl
import json
import os
import threading
import time
import shutil
import re

import setting
from database.REDIS import REDIS
from database.RabbitMQ import RabbitMQ
from database.MINIO import <PERSON>NI<PERSON>
from setting import SAVE_MODEL_DIR, SERVICE_CHANNEL_DICT, USE_RABBIT_MQ
from setting import logger
from utils.my_utils import MyEncoder, get_model_id_switch, get_server_ip_config, stop_thread, check_model_status
from utils.callback_utils import callback_for_http
from submodule_utils.nacos_register import <PERSON><PERSON><PERSON><PERSON>per

from concurrent.futures import ThreadPoolExecutor
LLM_executor = ThreadPoolExecutor(1)


class ModelManager:
    def __init__(self, name, main_list=None):
        logger.debug(f"启动服务,当前进程:{os.getpid()},父进程:{os.getppid()}")
        self.redis = REDIS()
        self.minio = MINIO()
        self.name = name
        self.main_list = main_list   #需要把所有可能训练的main全部放进来
        self.scenario = {}
        self.sn = {}

        self.train_record_file = setting.TRAIN_RECORD_FILE + f".{self.name}"
        self.train_record = {}

        # 模型使用时间
        self.model_user_time = {}

        self.redis_task = ["", "",""]

        if USE_RABBIT_MQ:
            self.rabbit_send = RabbitMQ()
            self.rabbit_listen = RabbitMQ()
        else:
            self.rabbit_send = None
            self.rabbit_listen = None

        self.redis.subscribe(setting.REDIS_CHANNEL_DELETE_MODEL)
        callback_dict = {
            setting.REDIS_CHANNEL_DELETE_MODEL: self._delete_model
        }
        self.redis_listen_thread = threading.Thread(target=self.redis.listen_msg, args=(callback_dict,))
        self.redis_listen_thread.start()

        if setting.CLIENT == "train":
            self.check_train_record()
            self.check_task_thread = threading.Thread(target=self.check_task_existence)
            self.check_task_thread.start()

        # if setting.CLIENT == "predict" and setting.AUTO_ONLINE_MODEL:
        #     self.offline_thread = threading.Thread(target=self.auto_offline)
        #     self.offline_thread.start()

    # 判断redis有没有待训练的任务
    def check_task_existence(self):
        while True:
            try:
                pop_task_str = self.redis.pop_key(setting.WAITING_QUEUE)
                if pop_task_str:
                    # logger.debug('从redis列表[{}] 弹出任务成功'.format(setting.WAITING_QUEUE))
                    self.redis_task = pop_task_str.split("/")
                    model_id = self.redis_task[0]
                    data_key = self.redis_task[1]
                    callback_url = self.redis_task[2]
                    logger.debug(f"训练队列{model_id},从redis获取到任务,model_id:{model_id},data_key:{data_key}")

                    # 判断是不是要取消训练
                    cancel_times = self.redis.hget(setting.REDIS_TRAIN_CANCEL_HASH_KEY, key=model_id)
                    cancel_times = 0 if cancel_times is None else int(cancel_times)
                    if cancel_times > 0:  # 训练取消任务
                        # 取消后无需写训练结果,在train_cancel函数里面写了
                        logger.debug(f"训练队列{model_id},需要取消当前训练任务,cancel_times:{cancel_times}")
                        self.delete_cancel_hash_key(model_id=model_id)
                        self.redis_task = ["", "", ""]
                    else:
                        logger.debug(f"训练队列{model_id},准备开始训练,cancel_times:{cancel_times}")
                        self.train(model_id, data_key)
                        self.redis_task = ["", "", ""]
                else:
                    self.redis_task = ["", "", ""]
                    logger.debug(f"redis任务列表目前为空")
                    time.sleep(5)
            except Exception as e:
                self.redis_task = ["", "", ""]
                logger.debug(f'check_task_existence报错, 等待5秒继续: {e}', exc_info=True)
                time.sleep(5)

    def add_cancel_to_hash_key(self, model_id):
        logger.debug(f"取消训练Hash表增加model_id{model_id},开始")
        cancel_times = self.redis.hget(setting.REDIS_TRAIN_CANCEL_HASH_KEY, key=model_id)
        cancel_times = 1 if cancel_times is None else int(cancel_times) + 1
        self.redis.hset(hash_name=setting.REDIS_TRAIN_CANCEL_HASH_KEY, key=model_id, value=cancel_times)
        logger.debug(f"取消训练Hash表增加model_id{model_id},成功,cancel_times={cancel_times}")

    def delete_cancel_hash_key(self, model_id):
        logger.debug(f"取消训练Hash表删除model_id{model_id},开始")
        cancel_times = self.redis.hget(setting.REDIS_TRAIN_CANCEL_HASH_KEY, key=model_id)
        cancel_times = 0 if cancel_times is None else int(cancel_times) - 1
        if cancel_times == 0:
            self.redis.hdel(hash_name=setting.REDIS_TRAIN_CANCEL_HASH_KEY, key=model_id)
        else:
            self.redis.hset(hash_name=setting.REDIS_TRAIN_CANCEL_HASH_KEY, key=model_id, value=cancel_times)
        logger.debug(f"取消训练Hash表删除model_id{model_id},成功,cancel_times={cancel_times}")

    def train_cancel(self, model_id):
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": ""
        }
        logger.debug(f"取消训练{model_id},准备取消")

        # 在hash表里加入待取消训练的model_id,value为该model_id需要取消的次数
        self.add_cancel_to_hash_key(model_id)
        logger.debug(f"取消训练{model_id},Hash表计数加1成功")

        try:
            redis_key = f"single_page_gen_result_{model_id}"
            if self.redis.key_exist(redis_key):
                single_page_gen_data = {
                    'code': 3,
                    'msg': '取消训练',
                    'finish': 0,
                    'content': "",
                    'model_id': model_id
                }
                self.redis.set_data(redis_key, single_page_gen_data)

            # 取消当前训练的任务
            if self.redis_task[0] == model_id and setting.CLIENT == "train":
                logger.debug(f"取消训练{model_id},当前模型正在训练,准备关闭训练进程")
                stop_thread(self.check_task_thread)
                self.delete_cancel_hash_key(model_id)
                self.redis_task = ["", "", ""]
                self.check_task_thread = threading.Thread(target=self.check_task_existence)
                self.check_task_thread.start()
                logger.debug(f"取消训练{model_id},当前模型正在训练,成功关闭训练进程")

            train_result = {"code": 1, "model_id": model_id, "task": "train", "score": 0, "error_code": "NLU91019", "error_type": 1, "msg": "取消训练"}
            train_state = {"model_id": model_id, "state": 3, "msg": "取消训练", "total_time": 20, "task_time": 20, "start_time": 0, "train_queue": []}
            self.redis.set_data(key=f"nlp_train_{model_id}", msg=train_result)
            self.redis.set_data(key=f"train_state_{model_id}", msg=train_state)
            self.redis.lrem(setting.LLM_WAITING_QUEUE, 0, model_id)
            self.dump_train_record(model_id=model_id, is_add=False)
            logger.debug(f"取消训练{model_id},写入训练结果,train_result:{train_result},train_state:{train_state}")
        except Exception as e:
            logger.debug(f"取消训练{model_id},报错:{e}")
            error_dict["error_code"] = "NLU91016"
            error_dict["error_msg"] = "取消训练任务请求报错"
        return error_dict

    def check_train_record(self):
        result = {
            "code": 1,
            "model_id": 0,
            "task": "train",
            "score": 0,
            "error_code": "NLU91019",
            "error_type": 1,
            "msg": "训练错误: 服务重启训练失败"
        }
        if os.path.exists(self.train_record_file):
            with open(self.train_record_file, "r", encoding="utf-8") as f:
                try:
                    train_record = json.load(f)
                except:
                    train_record = {}
        else:
            train_record = {}

        for model_id, flag in train_record.items():
            logger.debug(f"重启后发送训练失败消息 - model_id: {model_id}")
            # logger.debug(f"错误训练记录为:{train_record}")
            train_state = {"model_id": model_id, "state": 3, "msg": "训练错误: 服务重启训练失败", "total_time": 20, "task_time": 20, "start_time": 0, "train_queue": []}
            result["model_id"] = model_id
            result["state"] = train_state["state"]
            result["scenario"] = self.redis.hget("NLP_SCENARIO_HASH", model_id)
            logger.debug(f"训练result:{result}")
            self.redis.set_data(key=f"nlp_train_{model_id}", msg=result)
            self.redis.set_data(key=f"train_state_{model_id}", msg=train_state)
            self.redis.pop_key(setting.LLM_WAITING_QUEUE)
            if 'http' in str(flag):
                train_thread = threading.Thread(target=callback_for_http, args=(flag, result,True,2))
                train_thread.start()
            logger.debug(f"成功重置错误请求为{model_id}的训练状态")

        with open(self.train_record_file, "w", encoding="utf-8") as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            train_record = {}
            json.dump(train_record, f, ensure_ascii=False)

        # 修改训练队列状态
        while True:
            # 从列表左侧弹出一个元素
            llm_queue_model = self.redis.pop_key(setting.LLM_WAITING_QUEUE)
            if llm_queue_model is None:  # 如果列表为空，lpop 返回 None
                break
            train_state = {"model_id": llm_queue_model, "state": 3, "msg": "训练错误: 服务重启训练失败", "total_time": 0, "task_time": 0,
                           "start_time": 0, "train_queue": []}
            self.redis.set_data(key=f"train_state_{llm_queue_model}", msg=train_state)
            logger.debug(f"成功把训练状态train_state_{llm_queue_model}写入redis")

    def dump_train_record(self, model_id, is_add, callback_url = ''):
        if is_add:
            self.train_record[model_id] = callback_url if callback_url else 1
        else:
            # self.redis.set_data(key=f"train_state_{model_id}", msg={})
            if model_id in self.train_record.keys():
                self.train_record.pop(model_id)

        with open(self.train_record_file, "w", encoding="utf-8") as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.train_record, f, ensure_ascii=False)

    def url_to_json(self,model_id,url):
        save_dir = os.path.join(SAVE_MODEL_DIR, model_id,'train_data')
        os.makedirs(save_dir,exist_ok=True)
        save_filename = url.split('/')[-1]
        file_path = os.path.join(save_dir, save_filename)
        try:
            logger.info(f'正在请求下载链接{url}')
            data = requests.get(url)
            with open(file_path, 'wb') as f:
                f.write(data.content)
                f.close()
            data = json.loads(open(file_path,'r').read())
            logger.info(f'{url}下载成功')
            return data
        except Exception as e:
            logger.error(f'文件下载失败:{url}',exc_info=True)

    def data_to_url(self,model_id,data):
        save_dir = os.path.join(SAVE_MODEL_DIR, model_id,'train_data')
        os.makedirs(save_dir,exist_ok=True)
        filename = model_id + '_train_result.json'
        file_path = os.path.join(save_dir, filename)
        with open(file_path, 'w') as f:
            f.write(json.dumps(data,ensure_ascii=False))
            f.close()
        upload_down = self.minio.upload_file(filename,file_path)
        if upload_down:
            return self.minio.get_download_url(filename)
        else:
            return ''

    def append_train_queue(self, model_id, data = '' ,data_key='', data_url='', model_list = [],callback_url = '',is_inc=False, scenario="",  sn = "",**kwargs):
        try:
            logger.debug('准备加入训练队列, model_id:{}, data_key:{}'.format(model_id, data_key))
            self.scenario[model_id] = scenario
            self.sn[model_id] = sn
            other_model_id = ''
            other_model_type = ''
            enterprise_id = ''
            self.redis.hset(hash_name="NLP_SCENARIO_HASH", key=model_id, value=scenario)

            # 如果取消任务Hash表里还存在model_id的记录,直接删掉
            if self.redis.hexists(hash_name=setting.REDIS_TRAIN_CANCEL_HASH_KEY, key=model_id):
                self.redis.hdel(hash_name=setting.REDIS_TRAIN_CANCEL_HASH_KEY, key=model_id)

            # confirm type of model will be train
            if data:#直接传入数据
                data = data
                if data_key:
                    self.redis.set_data(key=data_key, msg=data)
            elif data_url:#目前仅支持url训练
                data = self.url_to_json(model_id,data_url)
                if data_key:#仅写入url
                    self.redis.set_data(key=data_key, msg=data_url,need_json=False)
            elif data_key:
                data = self.redis.get_data(key=data_key)
            logger.debug(f"[{model_id}] 训练数据:{data}")

            # 已经有请求，但还没有开始预估时长的训练状态（初始状态）, total_time是包含等待队列后的总时长，task_time是当前任务时长
            train_state = {"model_id": model_id, "state": 1, "msg": "等待训练", "total_time": 0, "task_time": 0, "start_time": 0, "train_queue": []}
            self.redis.set_data(key=f"train_state_{model_id}", msg=train_state)

            signal = {"labelData": False, "keywords": False, "scoreData": False, "scoreDataPoint": False, "extract_qa": False, "speech_text": False}
            for d in data:
                labelData = d.get("labelData", "").strip()
                scoreData = d.get("scoreData", "").strip()
                scoreDataPoint = d.get("scoreDataPoint", [])
                filename = d.get('filename','')
                if isinstance(scoreDataPoint, str):
                    scoreDataPoint = scoreDataPoint.strip()
                keywords = d.get("keywords", [])
                extract_qa = d.get("extract_qa", "")
                speech_text = d.get("speech_text", "")
                if isinstance(keywords, str):
                    keywords = keywords.strip()
                if len(labelData):
                    signal["labelData"] = True
                if len(keywords):
                    signal["keywords"] = True
                if len(scoreData):
                    signal["scoreData"] = True
                if len(scoreDataPoint):
                    signal["scoreDataPoint"] = True
                if len(filename):
                    signal["filename"] = True
                if len(extract_qa):
                    signal["extract_qa"] = True
                if len(speech_text):
                    signal["speech_text"] = True
                # 如果是文本机器人一定要训练search，否则search没有sim模型会报错
                if re.findall('smartchat|smartspeech',scenario):
                    signal["labelData"] = True
                # 其他大模型参数
                other_model_id = d.get('other_model_id', '')
                other_model_type = d.get('other_model_type', '')
                enterprise_id = d.get('enterprise_id', '')

            for model_ in model_list:
                if model_.__class__.__name__ in ['ExamMain', 'SpeechTextMain']:
                    logger.debug(f"大模型训练请求，加入大模型训练队列：{model_id}")
                    if other_model_id:
                        self.redis.push_key(setting.OTHER_LLM_WAITING_QUEUE, model_id)
                    else:
                        self.redis.push_key(setting.LLM_WAITING_QUEUE, model_id)
                    break

            task = {"model_id": model_id, "total_time": 0, "task_time": 0, "task_list": [], "is_inc": is_inc,'callback_url':callback_url,'scenario':scenario,  "sn": sn}
            train_model_list = [i for i in model_list if signal[SERVICE_CHANNEL_DICT[i.__class__.__name__]['key']]]
            #seach的增量训练可以传空数据训练
            if not data and is_inc:
                train_model_list = [i for i in model_list if i.__class__.__name__=='SearchMain']


            logger.debug('[{}] 装载训练队列, data_key:{}, class:{}'.format(model_id, data_key, [i.__class__.__name__ for i in train_model_list]))

            need_wait = False
            task_time = 0
            for m in train_model_list:
                if SERVICE_CHANNEL_DICT[m.__class__.__name__]["need_wait"]:
                    need_wait = True
            for m in train_model_list:
                if need_wait or m.__class__.__name__ in ['ExamMain', 'SpeechTextMain']:
                    task_list = copy.deepcopy(kwargs)
                    update = {"train_function": str(m), "model_id": model_id, "data_key": data_key,
                     "model_class": m.__class__.__name__,
                     "other_model_id": other_model_id, "other_model_type": other_model_type,
                     "enterprise_id": enterprise_id}
                    task_list.update(update)
                    task["task_list"].append(task_list)
                else:
                    task_list = copy.deepcopy(kwargs)
                    update = {"train_function": m, "model_id": model_id, "data_key": data_key, "model_class": m.__class__.__name__}
                    task_list.update(update)
                    task["task_list"].append(task_list)

                if hasattr(m, "train_time"):
                    train_time = m.train_time(data)
                    task_time += train_time if isinstance(train_time, int) else train_time[1]
                else:
                    task_time += 20
                logger.debug(f"[{m.__class__.__name__}]预估耗时: {round(task_time, 2)}秒,即{round(task_time+1 / 60, 3)}分钟")

            # 训练队列
            task["task_time"] = task_time
            waiting_list_task_time = self.get_waiting_list_task_time()
            # total_time = waiting_list_task_time + task_time
            # total_time = int(total_time * 1.1)
            task["total_time"] = task_time

            if len(task["task_list"]) == 0:
                final_result = {
                    "code": 1,
                    "model_id": model_id,
                    "task": "train",
                    "score": 0,
                    "error_code": "NLU91018",
                    "error_type": 1,
                    "msg": "训练错误: 数据不足",
                    "scenario": self.scenario[model_id],
                    "sn": self.sn[model_id]
                }
                logger.debug(f"训练result:{final_result}")
                train_state['state'] = 3
                train_state['msg'] = "训练错误: 数据不足"
                self.redis.set_data(key=f"train_state_{model_id}", msg=train_state)
                self.redis.set_data(key=f"nlp_train_{model_id}", msg=final_result)
                train_thread = threading.Thread(target=callback_for_http, args=(callback_url, final_result,True,2))
                train_thread.start()
            else:
                # 训练状态
                train_state["total_time"] = task["total_time"]
                train_state["task_time"] = task_time
                self.redis.set_data(f"train_state_{model_id}", str(train_state), need_json=False)
                logger.debug(f"成功把训练状态train_state_{model_id}写入redis")
                if need_wait:
                    self.redis.set_data(key=f"nlp_train_{model_id}_{data_key}", msg=str(task), need_json=False)
                    logger.debug("写入task到redis成功")
                    self.dump_train_record(model_id=model_id, is_add=True, callback_url=callback_url)
                    self.redis.push_key(setting.WAITING_QUEUE, str(model_id + "/" + data_key + '/' + callback_url))
                    logger.debug('成功把请求加入redis待训练队列, model_id:{}, data_key:{}'.format(model_id, data_key))
                else:
                    # self.train_now(task)
                    # 开启线程异步
                    self.dump_train_record(model_id=model_id, is_add=True, callback_url=callback_url)

                    for model_ in train_model_list:
                        logger.debug(f"模型函数：{model_.__class__.__name__}")
                        if model_.__class__.__name__ in ['ExamMain', 'SpeechTextMain']:
                            # logger.error(f"model_id:{model_id}使用线程池训练,线程池任务数量:{len(LLM_executor._threads)}")
                            if other_model_id:
                                train_thread = threading.Thread(target=self.train_now, args=(task, ))
                                train_thread.start()
                            else:
                                LLM_executor.submit(self.train_now, task)
                            break
                    else:
                        train_thread = threading.Thread(target=self.train_now, args=(task,))
                        train_thread.start()
        except Exception as e:
            final_result = {
                "code": 1, "model_id": model_id, "task": "train", "score": 0,
                "error_code": "NLU91018", "error_type": 1, "msg": "训练错误,加入训练队列报错",
                "scenario": self.scenario[model_id], "sn": self.sn[model_id]
            }
            logger.debug(f"训练错误,加入训练队列报错:{e}",exc_info=True)
            train_state = {"model_id": model_id, "state": 3, "msg": "训练错误,加入训练队列报错", "total_time": 20, "task_time": 20, "start_time": 0, "train_queue": []}
            self.redis.set_data(key=f"train_state_{model_id}", msg=train_state)
            self.redis.set_data(key=f"nlp_train_{model_id}", msg=final_result)
            train_thread = threading.Thread(target=callback_for_http, args=(callback_url, final_result,True,2))
            train_thread.start()

    def train(self, model_id, data_key):
        # 根据model_id,data_key的组合（唯一标识）从redis中找到长耗时任务
        task = eval(self.redis.get_data(key=f"nlp_train_{model_id}_{data_key}", need_json=False))
        self.train_now(task)
        time.sleep(10)
        self.redis.delete(f"nlp_train_{model_id}_{data_key}")

    def train_now(self, task):
        logger.debug(f'train_now 即将开始训练')
        train_state = {}
        if not check_model_status(task['model_id']):
            logger.debug(f"model_id:{task['model_id']}模型训练已取消，不再训练")
            # if self.redis.get_list(setting.LLM_WAITING_QUEUE, 0, -1):
            #     self.redis.pop_key(setting.LLM_WAITING_QUEUE)
            self.redis.lrem(setting.LLM_WAITING_QUEUE, 0, task['model_id'])
            self.redis.lrem(setting.OTHER_LLM_WAITING_QUEUE, 0, task['model_id'])

            return

        # 训练状态部分
        # train_state = {i:j for i,j in task.items()}
        # for i in train_state['task_list']:
        #     i['train_function'] = ''
        train_state["state"] = 0
        train_state["msg"] = "正在训练中"
        train_state["start_time"] = int(time.time())
        train_state["total_time"] = task['total_time']
        train_state["task_time"] = task['task_time']
        train_state['train_queue'] = []
        train_state['model_id'] = task['model_id']

        model_id = task['model_id']
        task_list = task["task_list"]
        is_inc = task["is_inc"]
        callback_url = task['callback_url']
        scenario = task['scenario']
        sn = task.get('sn', '')
        data_url = ''
        logger.debug(f'train_now 开始训练, model_id:{model_id}')
        try:
            self.redis.set_data(f"train_state_{model_id}", str(train_state), need_json=False)

            save_dir = os.path.join(SAVE_MODEL_DIR, model_id, "train_record")
            os.makedirs(save_dir, exist_ok=True)
            final_result = {"code": 0, "model_id": model_id, "task": "train", "score": 0, "msg": "训练成功"}
            train_record = []
            logger.debug(f'train_now 开始训练, model_id:{model_id}')

            need_train_main_list = []
            logger.debug(f"model_manager_name:{self.name}")
            logger.debug(self.main_list)
            for m in self.main_list:
                for t in range(len(task["task_list"])):
                    if m.__class__.__name__ == task["task_list"][t]["model_class"]:
                        task["task_list"][t]["train_function"] = m
                        need_train_main_list.append(m)
            while len(task_list):
                model_class = task_list[0]['model_class']
                train_function = task_list[0]['train_function']
                task_list[0].pop('train_function')
                data_key = task_list[0]['data_key'] #现在是一个下载地址
                logger.debug(f'train_now 训练中, model_id:{model_id}, model_class:{model_class}, data_key:{data_key}')
                if is_inc and hasattr(train_function, "increment_train"):
                    result = train_function.increment_train(**task_list[0])
                else:
                    result = train_function.train(**task_list[0])

                train_record.append({'model_id': model_id, 'model_class': model_class, 'code': result["code"]})
                if result["code"] != 0:
                    if result["msg"] == "取消训练":
                        # if self.redis.get_list(setting.LLM_WAITING_QUEUE, 0, -1):
                        # self.redis.lrem(setting.LLM_WAITING_QUEUE, 0, model_id)
                        return
                    final_result["code"] = 1
                    final_result["error_code"] = "NLU91020"
                    final_result["error_type"] = result.get("error_type", 0)
                    final_result["msg"] = "NLP模型训练出错了，请联系服务商~错误代码：NLU91020"
                    final_result["key"] = result.get("key", "")
                    if scenario == "filechat":
                        final_result["msg"] = '向量化失败'
                    logger.error(f'train_now 训练失败, model_class:{model_class}, model_id:{model_id}, error:{result.get("msg", "训练失败: 未知原因")}')
                    break
                else:
                    final_result["score"] = result["score"] if "score" in result else 0
                    final_result.update(result)
                    if "suggest" in result:
                        data_url = self.data_to_url(model_id,result["suggest"])
                    final_result["key"] = result.get("key", "")
                    task_list.pop(0)

            logger.debug(f'train_now 训练结束, model_id:{model_id}, final_result:{final_result}')
            #filechat训练了也有train_log
            if os.path.exists(os.path.join(save_dir, "train_log.json")):
                with open(os.path.join(save_dir, "train_log.json"), "r", encoding='utf-8') as f:
                    add_train_record = json.load(f)
                train_record.extend([i for i in add_train_record if i['model_class']=='RetrievalMain_bge'])

            with open(os.path.join(save_dir, "train_log.json"), "w", encoding='utf-8') as f:
                json.dump(train_record, fp=f, cls=MyEncoder, ensure_ascii=False, indent=2)
            if final_result["code"] == 0:
                send_model_code, send_model_msg = self.send_model(model_id=model_id)
                if send_model_code != 0:
                    final_result["code"] = 1
                    final_result["error_code"] = "NLU91020"
                    final_result["error_type"] = 1
                    final_result["msg"] = send_model_msg
                else:
                    # 如果训练端曾经交互测试用过这个model_id,就要从train_client_load_model_list里面移除这个model_id,下次可以重新加载
                    for m in need_train_main_list:
                        if hasattr(m, "train_client_load_model_list"):
                            if model_id in m.train_client_load_model_list:
                                m.train_client_load_model_list.remove(model_id)
            time.sleep(1)
            if "msg" in final_result and isinstance(final_result["msg"], str):
                final_result["msg"] = final_result["msg"][:100]
            logger.debug(f"训练result:{final_result}")

            train_state["state"] = 2
            train_state["msg"] = "已训练成功"
            if scenario!= "filechat":#非filechat模型才写入model_record文件
                self.dump_model_record(train_record, model_id,write_to_file=False)  # 训练端的模型自动上线,不需要写入model_record文件

        except Exception as e:
            logger.error(f"训练报错: {e}", exc_info=True)
            model_id = task['model_id']
            final_result = {"code": 1, "model_id": model_id, "task": "train", "score": 0, "error_code": "NLU91020", "error_type": 0, "msg": f"NLP模型训练出错了，请联系服务商~错误代码：NLU91020"}
            if "msg" in final_result and isinstance(final_result["msg"], str):
                final_result["msg"] = final_result["msg"][:100]
            train_state["state"] = 3
            train_state["msg"] = "NLP模型训练出错了，请联系服务商~错误代码：NLU91020"
            if scenario == "filechat":
                final_result["msg"] = '向量化失败'

        self.redis.set_data(f"train_state_{model_id}", str(train_state), need_json=False)
        self.redis.set_data(key=f"nlp_train_{model_id}", msg=final_result)
        self.dump_train_record(model_id=model_id, is_add=False)

        # 清除LLM队列的model_id
        self.redis.lrem(setting.LLM_WAITING_QUEUE, 0, model_id)
        self.redis.lrem(setting.OTHER_LLM_WAITING_QUEUE, 0, model_id)

        final_result['data_url'] = data_url
        final_result['state'] = train_state["state"]
        final_result['scenario'] = scenario
        final_result['sn'] = sn
        train_thread = threading.Thread(target=callback_for_http, args=(callback_url, final_result,True,2))
        train_thread.start()

    def get_waiting_list_task_time(self):
        try:
            task_time = 0
            redis_waiting_task = self.redis.get_list(setting.WAITING_QUEUE, 0, -1)  # 没有包括当前正在训练任务的id
            logger.debug(f"{setting.WAITING_QUEUE}为:{redis_waiting_task}")
            if redis_waiting_task:
                for i in range(len(redis_waiting_task)):
                    model_id = redis_waiting_task[i].split("/")[0]
                    wait_train_state = self.check_train_state(model_id=model_id)
                    task_time += wait_train_state["task_time"]
            return task_time
        except Exception as e:
            logger.error(f"获取训练队列所有task_time报错:{e}")
            return 0

    def check_train_state(self, model_id, other_model_type=""):
        """
        state: 0:正在训练  1:等待训练  2:已训练成功  3:训练失败
        """
        logger.debug(f'开始查询训练状态, model_id:{model_id}')
        result = {"model_id": model_id, "state": 2, "msg": "训练已成功", "total_time": 20, "task_time": 20, "start_time": 0,
                  "train_queue": []}

        check_model_id = f"train_state_{model_id}"

        if model_id != "0":
            train_state = eval(self.redis.get_data(key=check_model_id, need_json=False))
            logger.debug(f"redis中对应的训练状态为:{train_state}")
        else:
            train_state = {"total_time": 0, "task_time": 0, "msg": "统计训练时间", "state": 1, "start_time": 0, "train_queue": []}

        if train_state:
            result["total_time"] = train_state["total_time"]
            result["task_time"] = train_state["task_time"]
            result["msg"] = train_state["msg"]
            if train_state["state"] == 2:
                result["start_time"] = train_state["start_time"]  # 训练成功应该不需要看待训练队列等信息
            elif train_state["state"] == 3:
                result["state"] = 3
                result["start_time"] = 0
            else:
                if train_state["state"] == 0:
                    result["state"] = 0
                    result["start_time"] = train_state["start_time"]

                else:
                    result["state"] = 1
                    result["start_time"] = 0

                if self.redis_task[0] == model_id:
                    return result

                if other_model_type:
                    pass
                else:
                    redis_waiting_llm_task = self.redis.get_list(setting.LLM_WAITING_QUEUE, 0, -1)
                    logger.debug(f"{setting.LLM_WAITING_QUEUE}为:{redis_waiting_llm_task}")
                    if redis_waiting_llm_task:
                        for i in range(len(redis_waiting_llm_task)):
                            llm_model_id = redis_waiting_llm_task[i]
                            result["train_queue"].append(llm_model_id)
                            if llm_model_id != model_id:
                                llm_model_status = eval(
                                    self.redis.get_data(key="train_state_" + llm_model_id, need_json=False))
                                if llm_model_status:
                                    logger.warning(
                                        f"当前等待{llm_model_id}正在训练的任务时间:{llm_model_status['total_time']}")
                                    result["total_time"] += llm_model_status["total_time"]
                            elif llm_model_id == model_id:
                                break

        else:
            result["state"] = 3
            result["msg"] = "model_id不存在"
        return result

    def switch_model(self, model_id, flag, immediately_flag=1, model_suffix=0):
        error_dict = {
            "code":0,
            "error_code": 0,
            "error_type": 1,
            "error_msg": ""
        }
        logger.debug(f'模型上下线,准备开始,model_id:{model_id},flag:{flag}')
        if flag == 0 and immediately_flag == 0:
            time.sleep(60*setting.MODEL_OFFLINE_TIME)
        try:
            save_log = os.path.join(SAVE_MODEL_DIR, f"{model_id}", "train_record", 'train_log.json')
            if os.path.exists(save_log):
                with open(save_log, encoding='utf-8', mode='r') as f:
                    train_record = json.load(f)
                    logger.debug(f'模型上下线,读取train_record成功,train_record:{train_record}')
            else:
                train_record = []
                # 上线要求本地模型必须存在，下线则可以忽略
                if flag == 1:
                    error_dict['code'] = 1
                    error_dict["error_code"] = "NLU91014"
                    error_dict["error_msg"] = "NLP模型上下线报错:模型记录不存在"
                    logger.error(f'模型上下线,NLP模型发布出错,模型记录不存在,model_id:{model_id},flag:{flag}')
                    return error_dict

            server_config_dict = get_server_ip_config()
            if setting.CALLOUT_SERVICE or not model_suffix:
                model_id_switch = model_id
            else:
                model_id_switch = get_model_id_switch(model_id)
            if flag == 1 and not setting.CALLOUT_SERVICE and setting.CLIENT == "train":
                # 非外呼的上线模型需要先通知预测端拷贝模型
                result_code_list = []
                if setting.SEND_MODEL_MODE != 'Nas':
                    done_ip_list = []
                    for server_ip_dict in server_config_dict["server_ip_list"]:
                        copy_online_model_begin_time = time.time()
                        if server_ip_dict["service"] == "train":
                            # if server_ip_dict["ip"] in server_config_dict["local_ip_list"] and server_ip_dict["service"] == "train":
                            continue
                        if server_ip_dict['ip'] in done_ip_list:#重复ip不二次拷贝
                            continue
                        wrong_train_record = [i for i in train_record if i['code']==1]
                        if wrong_train_record:
                            error_dict['code'] = 1
                            error_dict["error_code"] = "NLU91016"
                            error_dict["error_msg"] = "NLP模型上下线报错:模型训练失败,无法发布"
                            logger.error(f"模型上下线,模型训练不成功,model_id:{model_id},model_class:{[i['model_class'] for i in wrong_train_record]},flag:{flag}")
                            return error_dict
                        for i in range(3):#重试3次
                            url = f"http://{server_ip_dict['ip']}:{server_ip_dict['service_port']}/nlp/copy_online_model"
                            logger.debug(f"模型上下线,通知预测端拷贝上线模型,model_id:{model_id},url:{url}")
                            payload = json.dumps({"model_id": model_id, "train_record": train_record}, ensure_ascii=False)
                            headers = {'Content-Type': 'application/json'}
                            try:
                                result = requests.request("POST", url, headers=headers, data=payload, timeout=5*60)  # 超时时间为5分钟
                                response = json.loads(result.text)
                                result_code_list.append(response["code"])
                                done_ip_list.append(server_ip_dict["ip"])
                                logger.debug(
                                    f"模型上下线,预测端拷贝上线模型结束,response:{response},model_id:{model_id},url:{url},耗时:{time.time() - copy_online_model_begin_time}")
                                break
                            except Exception as e:
                                logger.warning(f"模型上下线,通知预测端拷贝上线模型报错:{url}, {e}", exc_info=True)

                        if 1 in result_code_list or not result_code_list: #有一个拷贝错误或者没有拷贝成功
                            logger.warning(result_code_list)
                            error_dict["code"] = 1
                            error_dict["error_code"] = "NLU91014"
                            error_dict["error_msg"] = "NLP模型上下线报错:预测端拷贝模型失败"
                            return error_dict
                else:
                    self.copy_online_model(model_id, train_record)


            # 只有预测端才会监听模型上下线的消息,预测端是要调用拷贝后(即发布)的模型,model_id使用model_id_switch
            # 尽量只在ModelManager和HttpServer中控制model_id(是否加switch,只要是预测端的都要加switch)
            # train_record.append({"code":0,"model_id": model_id,"model_class": "RetrievalMain_bge"})
            if setting.CLIENT == "train":
                # nacos上线模型
                if (setting.NACOS_ADDR or os.environ.get("NACOS_ADDR")):
                    # 服务注册了nacos可以直接走路由
                    infos = []
                    for server_ip_dict in server_config_dict["server_ip_list"]:
                        if server_ip_dict["service"] == "train":
                            # if server_ip_dict["ip"] in server_config_dict["local_ip_list"] and server_ip_dict["service"] == "train":
                            continue
                        url = f"http://{server_ip_dict['ip']}:{server_ip_dict['service_port']}/nlp/predict/switch_model"
                        on_is_loading =  f"http://{server_ip_dict['ip']}:{server_ip_dict['service_port']}/nlp/is_loading_on"
                        off_is_loading =  f"http://{server_ip_dict['ip']}:{server_ip_dict['service_port']}/nlp/is_loading_off"
                        data = json.dumps({"model_id": model_id_switch, "flag": flag}, ensure_ascii=False)

                        for i in range(3):
                            try:
                                logger.debug(f"模型上下线,通知预测端上线模型,model_id:{model_id_switch},url:{url}")

                                requests.request("POST", on_is_loading, data=data,timeout=10).json()
                                result = requests.request("POST", url, data=data,
                                                          timeout=60)

                                response = json.loads(result.text)
                                infos.append({"ip": f"{server_ip_dict['ip']}:{server_ip_dict['service_port']}", "response": response,"code":response["code"]})
                                logger.debug(f'通知预测端上线模型，端口:{url},model_id:{model_id_switch},相应结果:{response}')
                                break
                            except Exception as e:
                                infos.append({"ip": f"{server_ip_dict['ip']}:{server_ip_dict['service_port']}", "response": f'{e}','code':1})
                                logger.warning(f"通知预测端上线模型,通知预测端上线模型报错:{url}, {e}", exc_info=True)
                        requests.request("POST", off_is_loading, data=data, timeout=10)
                        #延迟下一台机器下线
                        time.sleep(1)
                        logger.debug('延迟1秒再进行下一台机器成功')

                    error_dict['info'] = infos
                    #所有预测端都发布,但是其中一个有错误就算发布失败
                    if [i for i in infos if i['code']!=0]:
                        error_dict['code'] = 1
                        error_dict["error_code"] = "NLU91016"
                        error_dict["error_msg"] = "NLP模型上下线报错:模型训练失败,无法发布"
                        logger.error(f"模型上下线,存在ip预测端模型发布失败,model_id:{model_id},infos:{[i for i in infos if i['code']!=0]}")
                        return error_dict
                # redis key上线
                elif setting.AUTO_ONLINE_MODEL:
                    try:
                        self.dump_model_record(train_record, model_id_switch)
                    except:
                        logger.error(f"模型上下线,保存模型记录失败,model_id:{model_id_switch},train_record:{train_record}",exc_info=True)
                # redis channel上线
                else:
                    for record in train_record:
                        model_class = record["model_class"]
                        code = record["code"]
                        if code == 0:
                            transfer_data = json.dumps({"model_id": model_id_switch, "flag": flag})
                            if USE_RABBIT_MQ:
                                res = self.rabbit_send.publish_msg(SERVICE_CHANNEL_DICT[model_class]["switch_model"], transfer_data)
                            else:
                                res = self.redis.publish_msg(SERVICE_CHANNEL_DICT[model_class]["switch_model"], transfer_data)
                            if res == -1:
                                error_dict["code"] = 1
                                error_dict["error_code"] = "NLU91015"
                                error_dict["error_msg"] = "NLP模型上下线报错:广播上下线请求报错"
                                logger.error(f'模型上下线,广播上下线请求报错,model_id:{model_id},model_class:{model_class},flag:{flag}')
                                return error_dict
                            else:
                                logger.debug(f'模型上下线,广播上下线请求成功,model_id:{model_id},model_class:{model_class},flag:{flag},监听者数量: {res}')
                        else:
                            error_dict['code'] = 1
                            error_dict["error_code"] = "NLU91016"
                            error_dict["error_msg"] = "NLP模型上下线报错:模型训练不成功"
                            logger.error(f'模型上下线,模型训练不成功,model_id:{model_id},model_class:{model_class},flag:{flag}')
                            return error_dict
                        # if flag == 1:
                        #     self.model_user_time[model_id_switch + "_" + model_class] = time.time()
                    logger.debug(f'模型上下线,广播上下线请求完成,model_id:{model_id},flag:{flag}')

            if setting.CLIENT=='predict':
                infos = []
                for record in train_record:
                    code = record["code"]
                    model_class = record["model_class"]
                    # if model_class == 'RetrievalMain_bge': #RetrievalMain_bge直接在main上线
                    #     continue
                    info_dict = {'code':code,'model_class':model_class}
                    if code == 0: #只对code为0的模型进行上线
                        for model_main in self.main_list:  # self.main_list里面有两个SearchMain
                            if model_main.__class__.__name__ == model_class:
                                if model_class in str(infos):
                                    continue
                                main_load_code = model_main.switch_model(model_id_switch, flag)
                                if main_load_code != 0:
                                    #如果有一个上线失败就是失败
                                    error_dict['code'] = 1
                                info_dict['main_load_code'] = main_load_code
                                infos.append(info_dict)
                    else:
                        error_dict['code'] = 1
                        error_dict["error_code"] = "NLU91016"
                        error_dict["error_msg"] = "NLP模型上下线报错:模型训练不成功"
                        logger.error(f'模型上下线,模型训练不成功,model_id:{model_id},model_class:{model_class},flag:{flag}')
                        error_dict['info'] = infos
                        return error_dict
                error_dict['info'] = infos
            return error_dict
        except Exception as e:
            logger.error(f'模型上下线,NLP模型上下线报错,model_id:{model_id},flag:{flag},错误:{e}', exc_info=True)
            error_dict["code"] = 1
            error_dict["error_code"] = "NLU91016"
            error_dict["error_msg"] = "NLP模型上下线报错"
        return error_dict

    @staticmethod
    def _delete_model(model_id_list):
        logger.debug(f"删除模型,监听到redis消息,model_id_list:{model_id_list}")
        for model_id in model_id_list:
            for del_model_id in [model_id, get_model_id_switch(model_id)]:
                try:
                    logger.debug(f"删除模型,准备删除,del_model_id:{del_model_id}")
                    model_path = os.path.join(setting.SAVE_MODEL_DIR, del_model_id)
                    if os.path.exists(model_path):
                        shutil.rmtree(model_path)
                except Exception as e:
                    logger.warning(f"删除模型,del_model_id:{del_model_id},报错:{e}")
            logger.debug(f"删除模型,成功删除model_id:{model_id}")
        logger.debug(f"删除模型,完成,model_id_list:{model_id_list}")

    def delete_model(self, model_id_list):
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": ""
        }
        logger.debug(f'删除模型,准备开始,model_id_list:{model_id_list}')
        try:
            if setting.CLIENT != "train":
                error_dict["error_code"] = "NLU91014"
                error_dict["error_msg"] = "NLP删除模型报错:删除请求必须调训练端"
                logger.error(f'删除模型出错,请求必须调训练端,model_id_list:{model_id_list}')
                return error_dict
            for model_id in model_id_list:
                error_dict = self.switch_model(model_id, flag=0)
                logger.debug(f"删除模型前下线model_id:{model_id},结果:{error_dict}")
            transfer_data = json.dumps({"model_id_list": model_id_list})
            res = self.redis.publish_msg(setting.REDIS_CHANNEL_DELETE_MODEL, transfer_data)
            if res == -1:
                error_dict["error_code"] = "NLU91015"
                error_dict["error_msg"] = "NLP删除模型报错:广播删除请求报错"
                logger.error(f'删除模型,广播删除请求报错,model_id_list:{model_id_list}')
                return error_dict
            else:
                logger.debug(f'删除模型,广播删除请求成功,model_id_list:{model_id_list},监听者数量: {res}')
            return error_dict
        except Exception as e:
            logger.error(f'删除模型,NLP删除模型报错,model_id_list:{model_id_list},错误:{e}')
            error_dict["error_code"] = "NLU91016"
            error_dict["error_msg"] = "NLP删除模型报错"
        return error_dict

    def copy_online_model(self, model_id, train_record):
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": "拷贝上线模型成功"
        }
        try:
            copy_online_model_begin_time = time.time()
            model_id_switch = get_model_id_switch(model_id)
            model_id_save_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id)
            model_id_switch_save_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id_switch)

            logger.debug(f"拷贝上线模型,准备开始,model_id:{model_id},model_id_switch:{model_id_switch}")
            if os.path.exists(model_id_switch_save_dir):
                logger.debug(f"拷贝上线模型,{model_id_switch}的模型文件夹已存在,先删除,model_id:{model_id},model_id_switch:{model_id_switch}")
                shutil.rmtree(model_id_switch_save_dir)

            shutil.copytree(model_id_save_dir, model_id_switch_save_dir)

            # 写model_record
            for record in train_record:
                model_class = record["model_class"]

                if setting.SEND_MODEL_MODE != "Nas": #是否有风险
                    for model_main in [model for model in self.main_list if model.__class__.__name__ == model_class]:
                        model_main.dump_record()

            logger.debug(f"拷贝上线模型,拷贝成功,model_id:{model_id},model_id_switch:{model_id_switch},耗时:{time.time()-copy_online_model_begin_time}")
        except Exception as e:
            logger.debug(f"拷贝上线模型,拷贝失败,model_id:{model_id},错误:{e}")
            error_dict["code"] = 1
            error_dict["error_code"] = "NLU91016"
            error_dict["error_msg"] = "拷贝上线模型报错"
        return error_dict

    def download_model_redis(self, model_id, all_dirs, dir_to_keys):
        error_dict = {
            "error_code": 0,
            "error_type": 1,
            "error_msg": "从Redis下载模型成功"
        }
        try:
            logger.debug(f"从Redis下载模型,准备开始,model_id:{model_id},all_dirs:{all_dirs},dir_to_keys:{dir_to_keys}")
            model_id_save_dir = os.path.join(setting.SAVE_MODEL_DIR, model_id)
            for a_dir in all_dirs:
                pattern = f"nlp_model_{model_id}_{a_dir}_"
                keys = dir_to_keys[a_dir]
                save_path = os.path.join(model_id_save_dir, a_dir)
                os.makedirs(save_path, exist_ok=True)
                for key in keys:
                    file_name = key.replace(pattern, "")
                    file_path = os.path.join(save_path, file_name)
                    with open(file_path, "wb") as f:
                        base64_msg = self.redis.get_data(key=key, need_json=False)
                        f.write(base64.b64decode(base64_msg))
            logger.debug(f"从Redis下载模型,下载成功,model_id:{model_id},all_dirs:{all_dirs},dir_to_keys:{dir_to_keys}")
        except Exception as e:
            logger.debug(f"从Redis下载模型,下载失败,model_id:{model_id},all_dirs:{all_dirs},dir_to_keys:{dir_to_keys},错误:{e}")
            error_dict["code"] = 1
            error_dict["error_code"] = "NLU91016"
            error_dict["error_msg"] = "从Redis下载模型失败"
        return error_dict

    @staticmethod
    def _scp_send_model(model_id):
        code = 0
        msg = "Scp发送模型成功"
        try:
            if len(setting.PREDICT_SERVER_IPS) == 0:
                logger.debug(f"ModelManager - PREDICT_SERVER_IPS 为空, Scp 无需发送模型 - model_id: {model_id}")
                return code, msg

            logger.debug(f"ModelManager - Scp 开始发送 - model_id: {model_id}")
            save_model_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}")

            for ip in setting.PREDICT_SERVER_IPS:
                logger.debug(f"ModelManager - Scp 正在发送 - ip: {ip}")
                send_success = False
                send_times = 1
                while send_times <= 3 and not send_success:
                    scp_command = f"scp -r {save_model_dir}  root@{ip}:{setting.SAVE_MODEL_DIR}"
                    res = os.system(scp_command)
                    if res == 0:
                        send_success = True
                    else:
                        logger.error(f"ModelManager - Scp 发送模型第{send_times}次失败 - ip: {ip}, scp_cmd: {scp_command}")
                        send_times += 1
                if not send_success:
                    logger.error(f"ModelManager - Scp 发送模型失败 - model_id: {model_id}")
                    code = 1
                    msg = "Scp发送模型失败"
                    return code, msg
            logger.debug(f"ModelManager - Scp 发送模型成功 - model_id: {model_id}")
        except Exception as e:
            logger.error(f"ModelManager - Scp 发送模型失败 - model_id: {model_id},错误:{e}")
            code = 1
            msg = "Scp发送模型失败"
        return code, msg

    @staticmethod
    def _rsync_send_model(model_id):
        code = 0
        msg = "Rsync发送模型成功"
        try:
            server_config_dict = get_server_ip_config()
            logger.debug(f"Rsync发送模型,开始发送模型,model_id:{model_id},server_config_dict:{server_config_dict}")
            save_model_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}")
            send_record = []
            for ip_service in server_config_dict["server_ip_list"]:
                ip, user, ssh_port, service_port, service_type = ip_service["ip"], ip_service["user"], ip_service.get("ssh_port", 22), ip_service.get("service_port", 22), ip_service.get("service")
                if ip in server_config_dict["local_ip_list"]:
                    if service_type == "train":
                        logger.debug(f"Rsync发送模型,同个train服务不用发送,ip:{ip},user:{user},ssh_port:{ssh_port},service_port:{service_port},service_type:{service_type}")
                        continue  # 同个服务不用发送
                    elif server_config_dict["service_model_path_dict"]["train"] == server_config_dict["service_model_path_dict"]["predict"]:
                        logger.debug(f"Rsync发送模型,训练和预测在同一台服务并且模型路径相同不用发送,ip:{ip},user:{user},ssh_port:{ssh_port},service_port:{service_port},service_type:{service_type}")
                        continue  # 训练和预测在同一台服务并且模型路径相同不用发送
                if (ip, service_type) in send_record:
                    logger.debug(f"Rsync发送模型,同个服务器相同服务,ip:{ip},user:{user},ssh_port:{ssh_port},service_port:{service_port},service_type:{service_type}")
                    continue
                send_record.append((ip, service_type))
                logger.debug(f"Rsync发送模型,开始发送到,ip:{ip},user:{user},ssh_port:{ssh_port},service_port:{service_port},service_type:{service_type}")
                send_success = False
                send_times = 1
                while send_times <= 3 and not send_success:
                    rsync_command = f"rsync --bwlimit={setting.RSYNC_SEND_SPEED} --partial -avz {save_model_dir} {user}@{ip}:{server_config_dict['service_model_path_dict'][service_type]}"
                    logger.debug(f"Rsync发送模型,发送命令:{rsync_command}")
                    res = os.system(rsync_command)
                    if res == 0:
                        send_success = True
                    else:
                        logger.error(f"Rsync发送模型,第{send_times}次失败,ip:{ip},model_id:{model_id},rsync_cmd:{rsync_command}")
                        send_times += 1
                if not send_success:
                    logger.error(f"Rsync发送模型,发送失败,ip:{ip},model_id:{model_id}")
                    raise Exception("Rsync发送模型出错")
            logger.debug(f"Rsync发送模型成功,model_id:{model_id}")
        except Exception as e:
            logger.error(f"Rsync发送模型失败,model_id:{model_id},错误:{e}")
            code = 1
            msg = "Rsync发送模型失败"
        return code, msg

    def _redis_send_model(self, model_id):
        code = 0
        msg = "Redis发送模型成功"
        logger.debug(f"Redis发送模型,准备发送到Redis,model_id:{model_id}")
        try:
            redis_send_begin_time = time.time()
            model_id_path = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}")
            all_dirs = os.listdir(model_id_path)
            dir_to_keys = {}
            for a_dir in all_dirs:
                dir_to_keys[a_dir] = []
                a_dir_path = os.path.join(model_id_path, a_dir)
                all_files = os.listdir(a_dir_path)
                for a_file in all_files:
                    if os.path.isfile(os.path.join(a_dir_path, a_file)):
                        with open(os.path.join(a_dir_path, a_file), "rb") as f:
                            self.redis.set_data(key=f"nlp_model_{model_id}_{a_dir}_{a_file}",
                                                msg=base64.b64encode(f.read()), need_json=False, expire_time=1800)
                            dir_to_keys[a_dir].append(f"nlp_model_{model_id}_{a_dir}_{a_file}")
            logger.debug(f"Redis发送模型,成功发送到Redis,model_id:{model_id},耗时:{time.time()-redis_send_begin_time}")
            server_config_dict = get_server_ip_config()
            send_record = []
            for server_ip_dict in server_config_dict["server_ip_list"]:
                download_begin_time = time.time()
                ip, user, ssh_port, service_port, service_type = server_ip_dict["ip"], server_ip_dict["user"], server_ip_dict.get("ssh_port", 22), server_ip_dict["service_port"], server_ip_dict["service"]
                if ip in server_config_dict["local_ip_list"]:
                    logger.debug(f"Redis发送模型,同个ip服务不用发送,ip:{ip},user:{user},ssh_port:{ssh_port},service_port:{service_port},service_type:{service_type}")
                    continue  # 同个服务不用下载
                if (ip, service_type) in send_record:
                    logger.debug(f"Redis发送模型,同个服务器相同服务,ip:{ip},user:{user},ssh_port:{ssh_port},service_port:{service_port},service_type:{service_type}")
                    continue
                send_record.append((ip, service_type))
                url = f"http://{ip}:{service_port}/nlp/download_model_redis"
                logger.debug(f"Redis发送模型,通知其他服务下载,model_id:{model_id},url:{url}")
                payload = json.dumps({"model_id": model_id, "all_dirs": all_dirs, "dir_to_keys": dir_to_keys}, ensure_ascii=False)
                headers = {'Content-Type': 'application/json'}
                try:
                    response = requests.request("POST", url, headers=headers, data=payload, timeout=1*60*60)  # 超时时间为1个小时
                    logger.debug(f'download_model_redis返回信息{response.text}')
                    response = json.loads(response.text)
                    if response["code"] != 0:
                        code = 1
                        msg = "Redis发送模型失败"
                        break
                except:
                    #请求某些关闭服务的端口会超时报错
                    logger.error(f'Redis发送模型下载报错:url:{url}')
                logger.debug(f"Redis发送模型,其他服务下载完成,model_id:{model_id},url:{url},耗时:{time.time()-download_begin_time}")
        except Exception as e:
            logger.error(f"ModelManager - Redis 发送失败 - model_id: {model_id}, 错误信息: {e}",exc_info=True)
            code = 1
            msg = "Redis发送模型失败"
        return code, msg

    def send_model(self, model_id):
        if setting.SEND_MODEL_MODE == "Nas":
            return 0, "NAS不用发送"  # Nas 不用发送模型
        elif setting.SEND_MODEL_MODE == "Scp":
            code, msg = self._scp_send_model(model_id=model_id)
        elif setting.SEND_MODEL_MODE == "Rsync":
            code, msg = self._rsync_send_model(model_id=model_id)
        elif setting.SEND_MODEL_MODE == "Redis":
            code, msg = self._redis_send_model(model_id=model_id)
        else:
            logger.error(f"ModelManager - SEND_MODEL_MODE 参数错误 - SEND_MODEL_MODE: {setting.SEND_MODEL_MODE}")
            code = 1
            msg = "发送模型失败,参数错误"
        return code, msg

    def copy_old2new_and_send(self, model_id, old_model_id, module_name):
        # 外呼只训练正则,模型复制
        source_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{old_model_id}/{module_name}/")
        target_dir = os.path.join(setting.SAVE_MODEL_DIR, f"{model_id}/{module_name}/")
        assert os.path.exists(source_dir)
        os.system(f"rm -rf {target_dir}")
        shutil.copytree(source_dir, target_dir)
        self.send_model(model_id)

    def info_record(self):
        # 信息录入
        key_name = setting.SERVICE_NAME
        server_info = self.redis.get_data(key_name)
        server_ip = NacosHelper.get_first_ip()
        server_port = int(os.environ.get('NLP_PORT'))
        client = setting.CLIENT
        if server_info == "{}":
            server_info = []
        if server_info:
            if server_ip not in [i["ip"] for i in server_info] or server_port not in [i["service_port"] for i in server_info]:
                server_info.append({"ip": server_ip, "service_port": server_port, "service": client})
        else:
            server_info.append({"ip": server_ip, "service_port": server_port, "service": client})
        logger.debug(setting.USE_SINGLE_REDIS)
        if setting.USE_SINGLE_REDIS:  # 集群不能用watch
            logger.info(f'{client+ "_" + str(os.getpid())}获取 redis 锁成功！')
            identifier = self.redis.acquire_lock()
            if identifier:
                logger.info(f'{client + "_" + str(os.getpid())}获取 redis 锁成功！')
                self.redis.set_data(key_name, server_info)
                res = self.redis.release_lock(identifier)
                logger.info(f'{client+ "_" + str(os.getpid())} 释放状态: {res}')
            else:
                logger.error(f'{client+ "_" + str(os.getpid())} 获取 redis 锁失败, 其他线程正在使用')

    # def auto_offline(self):
    #     while True:
    #         to_delete = []
    #         for model_id, timestamp in self.model_user_time.items():
    #             if time.time() - timestamp >= 24 * 60 * 60:
    #             # if time.time() - timestamp >= 30:
    #                 to_delete.append(model_id)
    #
    #         for model_id in to_delete:
    #             logger.info(f"模型定时下线: {model_id}")
    #             if model_id in self.model_user_time:
    #                 logger.warning(model_id)
    #                 self.model_user_time.pop(model_id)
    #                 logger.warning(self.model_user_time)
    #                 self.switch_model(model_id, 0)
    #         time.sleep(60)
    #
    # def auto_online(self, main_fun, model_id):
    #     try:
    #         class_name = main_fun.__class__.__name__
    #         model_class_id = model_id + "_" + class_name
    #         if setting.AUTO_ONLINE_MODEL:
    #             # 判断是否需要上线(模型使用时间超过1天)
    #             logger.debug(f"自动上线:{model_class_id}\n{self.model_user_time}")
    #             # logger.warning(model_class_id not in self.model_user_time)
    #             # logger.warning(time.time() - self.model_user_time[model_class_id])
    #             # logger.warning(time.time())
    #             # logger.warning(self.model_user_time[model_class_id])
    #             if model_class_id not in self.model_user_time or time.time() - self.model_user_time[model_class_id] >= 24 * 60 * 60:
    #                 logger.debug(f"模型上线时间：[{model_class_id}]：[{time.time()}]")
    #                 self.model_user_time[model_class_id] = time.time()
    #                 main_fun._load_model(model_id)
    #                 time.sleep(1)
    #     except Exception as e:
    #         logger.error(f"自动上线报错:{e}", exc_info=True)
    #         logger.error(f"当前上线模型记录:{self.model_user_time}")
    #         main_fun._load_model(model_id)
    #         # for filename in os.listdir(SAVE_MODEL_DIR):
    #         #     if filename.startswith("model_record"):  # 只处理以 "model_record" 开始的文件
    #         #         with open(os.path.join(SAVE_MODEL_DIR, f"{model_id}, filename), 'r'")) as f:
    #         #             content = f.read()
    #         # # 外呼预测自动上线逻辑
    #         # self.auto_online(model_id, 1)
    #         # #自动下线逻辑
    #         # self.model_update_time[model_id] = time.time()

    def dump_model_record(self, train_record, model_id, write_to_file=True):
        if setting.CLIENT == "train":
            for record in train_record:
                model_class = record["model_class"]
                try:
                    #写入reids
                    model_record_redis_key = 'model_record' + f".{model_class.replace('Main', '')}"
                    model_record_redis = self.redis.get_data(model_record_redis_key)
                    if model_record_redis== "{}":
                        model_record_redis = {}
                    set_time = int(time.time())
                    model_record_redis[model_id] = set_time
                    logger.debug(f'model_record_redis:{model_id},{set_time}')
                    self.redis.set_data(model_record_redis_key, model_record_redis)

                    #写入model_record文件
                    if write_to_file:
                        model_record_file = setting.MODEL_RECORD_FILE + f".{model_class.replace('Main', '')}"
                        if not os.path.exists(model_record_file):
                            os.makedirs(os.path.dirname(model_record_file), exist_ok=True)
                            with open(model_record_file, 'w') as file:
                                file.write("{}")
                            print(f"文件 {model_record_file} 已创建")
                        with open(model_record_file, 'r+', encoding='utf-8') as f:
                            # 加锁
                            fcntl.flock(f, fcntl.LOCK_EX)
                            try:
                                data = json.load(f)  # 尝试读取文件内容
                            except json.JSONDecodeError:
                                data = {}  # 如果文件内容不是有效的 JSON，则初始化为空对象
                            data[model_id] = int(time.time())
                            # 清空文件并写入修改后的内容
                            f.seek(0)
                            f.truncate()
                            json.dump(data, f, ensure_ascii=False)
                            fcntl.flock(f, fcntl.LOCK_UN)
                except Exception as e:
                    logger.error(f"保存已上线模型记录出错：{e}")
            logger.debug(f'保存已上线模型记录:{model_id}')

    def check_task_list(self):
        task_list = ""
        try:
            task_list = self.redis.get_list(setting.WAITING_QUEUE)
            if self.redis.now_task:
                task_list.append(self.redis.now_task)
            logger.debug(f"当前训练队列为:{task_list}")
            return task_list
        except Exception as e:
            logger.error(f"获取训练队列报错:{e}, {task_list}")
            return None


if __name__ == "__main__":
    os.makedirs(setting.SAVE_MODEL_DIR, exist_ok=True)
    if setting.SEND_MODEL_MODE == "Redis":
        model_manager = ModelManager(name="test")
    elif setting.SEND_MODEL_MODE == "Scp":
        model_manager = ModelManager(name="test")
    else:
        raise Exception(f"SEND_MODEL_MODE参数错误:{setting.SEND_MODEL_MODE}")
