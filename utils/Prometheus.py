#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
@author:admin
@file:Prometheus.py
@time:2022/05/31
"""

import prometheus_client
from prometheus_client import Gauge
from prometheus_client.core import CollectorRegistry
from database.REDIS import REDIS
from flask import Response


class Prometheus:
    def __init__(self, key2msg, use_prometheus):
        self.use_prometheus = use_prometheus
        if self.use_prometheus:
            self.redis = REDIS()
            self.registry = CollectorRegistry()
            self.key2object = dict()
            for key, msg in key2msg.items():
                self.key2object[key] = Gauge(key, msg, registry=self.registry)
                self.redis.set_data(key, 0, need_json=False)

    def get_metric(self):
        if self.use_prometheus:
            for key, obj in self.key2object.items():
                temp = self.redis.get_count(key)
                if temp is not None:
                    obj.set(temp)
            return Response(prometheus_client.generate_latest(self.registry), mimetype="text/plain")
        else:
            return None

    def add_count(self, key):
        if self.use_prometheus:
            self.redis.count_add(key, 1)
