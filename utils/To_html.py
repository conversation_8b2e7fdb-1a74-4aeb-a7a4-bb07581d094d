import time
import os
import re
import requests
import html
from pydocx import PyDocX
import openpyxl
from pdf2docx import Converter
from markdownify import markdownify as md
from setting import logger
import threading
from utils import filechat_utils
from module.img2text.img2text import img2text


class To_Html:
    def __init__(self):
        self.file_type = None
        self.html_path = None
        self.file_path = None
        self.download_file_path = None
        self.call_back_url = None
        self.sn = None
        self.ocr_switch = False

    def run(self, path, call_back_url, sn, ocr_switch="0"):
        try:
            self.sn = sn
            self.download_file_path = path
            # 判断文件类型
            self.file_type = self.download_file_path.split('.')[-1]
            self.html_path = self.download_file_path.replace(self.file_type, 'html')
            self.call_back_url = call_back_url
            # 文件下载到本地
            self.download()
            # 图像识别开关
            self.ocr_switch = True if ocr_switch == "1" or ocr_switch == 1 else False
            fun_dict = {"docx": self.word_to_html, "txt": self.txt_to_html, "pdf": self.pdf_to_html, "html": self.html_to_mark, "xlsx": self.xlsx_to_html}

            threading.Thread(target=fun_dict[self.file_type]).start()

            return {"code": "0", "msg": "success"}
        except Exception as e:
            logger.error(f'文件转换出错:{self.download_file_path}', exc_info=True)
            return {"code": "1", "msg": "defeat"}

    def word_to_html(self):
        try:
            # word转html
            html_text = PyDocX.to_html(self.file_path)
            # 图片识别
            if self.ocr_switch:
                html_text = self.image_ocr(html_text)
            self.call_back(self.call_back_url, html_text.encode('utf-8'))
        except Exception as e:
            self.call_back(self.call_back_url, "")
            logger.error(f'word转换失败:{self.download_file_path}', exc_info=True)
        # return html_text.encode('utf-8')

        # with open('D:\\周\\tmp\\123.html', 'w', encoding='utf-8-sig') as f:
        #     f.write(html_text)
        #     f.close()

    def pdf_to_html(self):
        try:

            # pdf转html
            start = time.time()
            # Keeping the PDF's location in a separate variable
            pdf_file = self.file_path
            # Maintaining the Document's path in a separate variable
            # docx_file = os.path.join(self.file_path, self.download_file_path.split('/')[-1].replace('pdf', 'docx'))
            docx_file = pdf_file.replace('.pdf', '.docx')

            # Using the built-in function, convert the PDF file to a document file by saving it in a variable.
            logger.debug("开始pdf转换")
            cv = Converter(pdf_file)
            # Storing the Document in the variable's initialised path
            cv.convert(docx_file)
            # Conversion closure through the function close()
            cv.close()
            logger.debug(f'pdf转换成功，耗时{time.time() - start:.2f}s')
            # 保存html结果
            html_text = PyDocX.to_html(docx_file)
            if self.ocr_switch:
                html_text = self.image_ocr(html_text)
            self.call_back(self.call_back_url, html_text.encode('utf-8'))
            # return html_text.encode('utf-8')
            # with open('【钉钉版】酷学院学员操作指南（V7.9.23）.html', 'w', encoding='utf-8-sig') as f:
            #     f.write(html_txt)
            #     f.close()
            # print(time.time() - start)
        except Exception as e:
            self.call_back(self.call_back_url, "")
            logger.error(f'pdf转换失败:{self.download_file_path}', exc_info=True)

    def txt_to_html(self):
        try:
            # txt转htmml
            lines = filechat_utils.txt_extract(self.file_path)

            # 使用列表推导式来创建每个段落，并用html.escape来处理可能存在的特殊字符
            paragraphs = [f"<p>{html.escape(line.strip())}</p>" for line in lines]
            html_content = f"""
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>Text to HTML</title>
                        </head>
                        <body>
                        {''.join(paragraphs)}
                        </body>
                        </html>
                    """
            self.call_back(self.call_back_url, html_content.encode('utf-8'))
        except Exception as e:
            self.call_back(self.call_back_url, "")
            logger.error(f'txt转换失败:{self.download_file_path}', exc_info=True)

    def xlsx_to_html(self):
        try:
            # 读取 Excel 文件
            workbook = openpyxl.load_workbook(self.file_path)
            sheet = workbook.active

            # 生成 HTML 表格
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Excel to HTML</title>
                <style>
                    table {border-collapse: collapse; width: 100%;}
                    th, td {border: 1px solid black; padding: 8px; text-align: left;}
                </style>
            </head>
            <body>
                <table>
            """

            # 读取表格数据并生成 HTML 行
            for row in sheet.iter_rows(values_only=True):
                html_content += "<tr>"
                for cell in row:
                    if cell is None:
                        html_content += "<td></td>"
                    else:
                        html_content += f"<td>{html.escape(str(cell))}</td>"
                html_content += "</tr>"

            html_content += """
                </table>
            </body>
            </html>
            """

            # 调用回调函数，传递生成的 HTML 内容
            self.call_back(self.call_back_url, html_content.encode('utf-8'))
        except Exception as e:
            self.call_back(self.call_back_url, "")
            logger.error(f'xlsx转换失败: {self.file_path}', exc_info=True)

    def save_html(self):
        pass

    def html_to_mark(self):
        with open(self.download_file_path, 'r') as f:
            html_content = f.read()  # > '**Yay** [GitHub](http://github.com)'
        output = md(html_content)
        return output

    def download(self, save_path="./to_html_file"):
        try:
            save_filename = self.download_file_path.split('/')[-1]
            os.makedirs(save_path, exist_ok=True)
            self.file_path = os.path.join(save_path, save_filename)
            if os.path.exists(self.file_path):
                logger.info(f'文件已存在: {self.file_path}')
                return '已下载'
            logger.info(f'正在请求下载链接: {self.download_file_path}')
            data = requests.get(self.download_file_path, timeout=30)
            data.raise_for_status()  # 检查请求是否成功
            with open(self.file_path, 'wb') as f:
                f.write(data.content)
            logger.info(f'文件下载完成: {self.file_path}')
            return '下载完成'
        except requests.RequestException as e:
            logger.error(f'文件下载失败: {self.download_file_path}', exc_info=True)
            return e
        except Exception as e:
            logger.error(f'文件下载失败: {self.download_file_path}', exc_info=True)
            return e

    @staticmethod
    def image_ocr(html_text):
        try:
            # 提取包含图片base64的整个元素
            all_pattern = '<img[^>]*src="data:image\/[a-zA-Z]+;base64,([^"]+)"[^>]*>'
            lable_images = re.finditer(all_pattern, html_text)
            lable_images = [i.group() for i in lable_images]
            if not lable_images:
                return html_text
            # 提取图片base64
            pattern = r'data:image\/[a-zA-Z]+;base64,([^"]+)'
            images_element_list = [re.findall(pattern, i)[0] for i in lable_images]
            # logger.debug(f'图片元素:{images_element_list}')
            images_base64_list = []
            images_word_list = []
            for i in images_element_list:
                images_base64_list.extend(i.split(",")[-1:])
            # 图片识别
            for index, image_base64 in enumerate(images_base64_list):
                txt_content = img2text(image_base64=image_base64)
                logger.debug(f'图片识别结果:{txt_content}')
                # 图片元素尾部加上识别结果<p>xxxxxxx</p>
                images_word_list.append(lable_images[index] + f'<p>{txt_content}</p>')
            # 替换原本图片元素
            for i in range(len(images_element_list)):
                html_text = html_text.replace(lable_images[i], images_word_list[i])
            return html_text
        except Exception as e:
            logger.error(f'图片识别出错', exc_info=True)
            return html_text

    def call_back(self, call_back_url, binary_html_content):
        try:
            if call_back_url is None:
                logger.debug(f'回调地址为空')
                return
            logger.debug(f'sn:{self.sn}, 回调地址:{call_back_url}, "回调数据type：{type(binary_html_content)}')
            # 使用 requests.post() 函数发送二进制数据
            response = requests.post(call_back_url, data=binary_html_content, timeout=30)
            logger.debug(f'回调response:{response}')
        except Exception as e:
            logger.error("回调出错", exc_info=True)


my_to_html = To_Html()

if __name__ == '__main__':
    # my_to_html.run("D:\周\\tmp\pdf\\1.docx",None, "")
    a = """
<html><head><meta charset="utf-8" /><style>.pydocx-caps {text-transform:uppercase}.pydocx-center {text-align:center}.pydocx-comment {color:blue}.pydocx-delete {color:red;text-decoration:line-through}.pydocx-hidden {visibility:hidden}.pydocx-insert {color:green}.pydocx-left {text-align:left}.pydocx-list-style-type-cardinalText {list-style-type:decimal}.pydocx-list-style-type-decimal {list-style-type:decimal}.pydocx-list-style-type-decimalEnclosedCircle {list-style-type:decimal}.pydocx-list-style-type-decimalEnclosedFullstop {list-style-type:decimal}.pydocx-list-style-type-decimalEnclosedParen {list-style-type:decimal}.pydocx-list-style-type-decimalZero {list-style-type:decimal-leading-zero}.pydocx-list-style-type-lowerLetter {list-style-type:lower-alpha}.pydocx-list-style-type-lowerRoman {list-style-type:lower-roman}.pydocx-list-style-type-none {list-style-type:none}.pydocx-list-style-type-ordinalText {list-style-type:decimal}.pydocx-list-style-type-upperLetter {list-style-type:upper-alpha}.pydocx-list-style-type-upperRoman {list-style-type:upper-roman}.pydocx-right {text-align:right}.pydocx-small-caps {font-variant:small-caps}.pydocx-strike {text-decoration:line-through}.pydocx-tab {display:inline-block;width:4em}.pydocx-underline {text-decoration:underline}body {margin:0px auto;width:49.61em}</style></head><body><p><img height="181px" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAy0AAAEKCAYAAAAfJrqhAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEE0AABBNAWeMAeAAAFBYSURBVHhe7bsLkiTJjmv5Nvk2eTf5Zlyq2YWLBEiqmf8iEiNyZJQ4oJq5R2Z29HTN//n//5//F0IIIYQQQghfjAxDCCGEEEII4VuQYQghhBBCCCF8CzIMIYQQQgghhG9BhiGEEEIIIYTwLcgwhBBCCCGEEL6Ffw7/+c9//gsovA33XH431evcO3DPd/kruPKMd75fB77Hp9/Fod7rHe+Mz3j1s17Jq9/9HferfEO9m7tj8n8z+N1c+Y7u7heb3Tv3X6Weiajeq/j08/8GNt/tt373+O6n76h2C9W/wubuzm3Y7t95xifZfL4nfrZ/Dk+88BLu+So/6b6TT77XnWe84/22qHfBTPl30D33le+k7n7l817Jq9/7lfc/4+7pjmc847ehvpOT7+nuflE73d6m82zU8975Dp9+/t/E9L1+4/eu3unkPU+6V1D3c6Y6J2z3q3fnWZ9kevcnfrZ/Dk+88BLu+So/6b6TT77XnWe84/22qHfBTPl30D333e/07ueF53zn0x3PeMZvQ30nd7+n0/3qd3ubzrv49Dt8w3fwG/kt3+vJ53j1Z1b3Y6b8Cdv96m3738gb3/2fg3sg5nXmHuanXrkH7Gt22SZH7nh0iPOYq05lyNYr9wD3J3BH7VeGKMeZ6rBj2OMOg3sO7KpdnjmrMzP1lUfYca+8YtPZop55cj9266x2ncPMdTa4XcymjvLKPej20SF3vQJ36ox38Oyyjuoj7LinfJc5uFvzdl9xdd/tYe46DrWL+zy7bOMU2K8z7vPsMmTyCHbrzLuYs8dZec6V71A7buacu6rDs8u6HB17zFTndJ7yidO9q88p3H7lymOm/Ambfexs+kj1ccfNnHNXdXh2WZejc/6Qfw4nD3Rn7m98l3FeZ8xcd5tNnrniKi/nztzf+C7bUru1787cx/OE22emvvITvIMzu5OMXXl35v52xky5O/C9jNphuOt2t7nrTbg9znFmd5Iput50x+QdvIczO5c5NvvdzM5lDuzWGbMtd3YLtcuZ6kx0d7BzWZdP8B7O7KZMuQnewZmdyrqZncsc1XW4fpfhzM5lXV5s93DunMtUZ8OVvdphVNfBO25GOveg/ITafcDezRPVd7h+l+HMzmVdXkz+gH8O3YXscGZ36ruM8zpj5rrbTHmmOkjXm3Kc2alMdZhNx8G7OLPrvDp31H2IcpgpP8E7OLM7yZzDmZ3KppmZPFN9Bl13nuCu293mrjfh9jifZkXXKYd0PeUeTN7Bezizc1nH1GePMzuV1cwop/ZPubqv9ipTTB32amZ3km3hXZzZuQxhXzMz9ZVTWTfXWcF9xt3lzsgjLzjDmZ3LurzY7uHcOaRy5yeu7imu3FU7vMszZ5OfmPbrrMAdB3anM1L73OWZncu6vJj8Af8cugtPHWaT7zLO64yZ624z5RH2Xd85znFmN2Wcs1dugndxZtd5dcZsg+pjpvwE7+DM7iRzDmd2KptmZvIn4F3uPMFdt7vNXW/C7XGuepVxzn7KXW9yDybv4D2c2blsonbUHuc4s3OZo7rYP9lXXN3f7F25m3dwZqcy1Tmhu4+dy5DJM9zHmZ3KupndKe6uOmPGHZfhzM5lXV5s93DuHNO5jqt7jme+x5RNfuJ0v3MK7KszZtxxGc7sXNblxeQP+OfQXXjqMJt8l3FeZ9VDf5Ipj7Dv+s5xjjM7l02u25ngXZzZdV6dMdvAfbyDwb0O7uPM7iRzDmd2KptmZvIn4F3uPMFdt7vNXW/C7XHuep3b5q43uQeTd/AezuxctmVzH87sXOa4u6+4ur/Zu3I37+DMbuNP4TtwZucyZPIM93Fmp7JuZneKu6vOmHHHZTizc1mXF9s9nDuHVO6847S/4cqdtcO7PHM2+YnT/c4psK/OmHHHZTizc1mXF5M/4J9Dd+Gpw2zyKnPnCdWdslPPM+Ic5zizO/UqU76D+ziz67w6Y1aorHOYKT/BOziz22Tsu5mdyrqZncuugne58wR33e42d70Jt8c5zuw2mTurGencg8k7eA9ndi5zbPa7mZ3LHCf7267KuryY/INNh+EdnNmd+is5zuxO/Qbu48xOZd3MzmUO7KozZtxxGc7sXNblxXYP5865THUcqvvO/Qfcx5ndqZ843T91mKkzZtxxGc7sXNblxeQP+OdQF/LFLke4w73Jq57LlVedzt3xOLsOui5THXYbr3rKK9SOmxHVnc5I7Redx0z5idpBJv8AO9zbZKrDruvzjJS7C97nzhPVRaZO59hv6PY5czNSDnGe86nDbuM7eJf3lX+AnY5p94p/gJ2O7a5zuOc62HM5M/XYO3iPd5V/0LkHeAd2XY5M/sF2f4J33IyU6/o8I+UmsD+dlTuZmfKqg27ynLm5MndGXM5UT6H6imfsdnmdEdV1fmK7P3VUjtl0Vu5kZsqrDrqNP0SGIfwaTv+iPOkv1l/D3e8r3/c9Tr+/Z3/fn37+T+fT39+nnx/+m3y/4Zv5gj+fMgzh13Dyl+wL/kL+OO5+Z/nO73Hy/XG3ZgfuOl7V/Vv49Pf36eeHf/lp32+9r0PtIGoHUTuI2kHUzjNRz0TUzk/mSz6XDEP4FeA/IOovG3vVCZ67393d/b+d6ftjrzp3mO5mrzp/M9N3w1517jDdzV51wnXy/YZv5kv/fMowhBBCCCGEEL4FGYYQQgghhBDCtyDDEEIIIYQQQvgWZPgSXvXfxOF/b+ee0bkN2/07z1CcPnfqhRBCCCGE8AOR4dN51S/V6k7OVOeE7X717jxLcfp85UIIIYQQQvjByPCp1C/Tr/ilWt2JmfInbPert+2fsL3zFc8OIYQQQgjh4+AvunXGX3xP5y533Q63U7nymCl/wmYfO5s+4vqYuzPTuRBCCCGEEH4wf/6yi3PnXKY6XT7Be25GOveg/ITafcDezROuj3mdecZ+l4cQQgghhPDD+fOXXZw7h1TuPHaUm8C78Q6eOZv8xLRfZwXuOFwXc+7wPOUhhBBCCCH8cPpfijvHdO7B5CfU/pRNfuJ0v3MK7G/Oap7yEEIIIYQQfjj9L8WdQyp3HjvKTeDdeAfPnE1+4nS/cwrsb85qnvIQQgghhBB+OP0vxZ1zmep0+QTv4czu1E+c7p+6yhxqj+cpDyGEEEII4Ycz/1Jcc2XujKgOwzsK18W8zojqOj+x3Z862xznOjvPmXIhhBBCCCH8cGQYQgghhBBCCN+CDN8G/18IGLXzTNQzEbUTQgghhBBCeCsyDCGEEEIIIYRvQYYhhBBCCCGE8C3IMIQQQgghhBC+BRmGEEIIIYQQwnfwn//7f/7fAxYq73oMe7WjMnY8T5ly7+LVz/7kZ7tLvXv3/t/++Taf4Q7T3crVTre34Vn33GF69qffb0K9W73zM977mz/7hmd9DyfUM7ds71A9x7TfeZc/i1fe/dNQ38FP+X6+4T3vvMOd3S2vuB/fG1HdCbV3985n8i3vsf5fWmo+6eLZeUTllWF+Or+LVz/3U5/rLvje3Wfo3Lfwynfs7r7qTnjWPVfYPPuT7zfRvdsz3vsZd3yaT3wGfObmzCjX9Zlpf/Ku80xeff9PoPsOfsr38w3veecdXv3+r7qf773ynG7nyn2v4FveY/W/tLiz6vJcZ5VVv1B5ZejcGXGZysNr4e/9J/8MPvVnqHvus97pWfdc4ZPPfgbd+z/jsz3jjr8R/M74O6wz54hyXb8oP+1P3nWeyavv/wl038FP+X6+4T3vvMM3vP8V+L2vfI5u58p9vxr3hVSuPGfdXGdF9dUeZwq34zjpFrhTZ3WHc5i5zoZuF53rdEz7ncdZee443K5y7jzNdUbPufKqw97h+pzXzF3MEdXh8ylut3Lkju9cgR3udY47OCvPufIdaveB6vCZ/cYV2OnAvtvHfPI481nNnLPDbOo4v8XtTndOnuH+tH/X3wXvV8+qDGGHfc7rjKgegnmdkalXfgJ3ENXhM3sE/cS0P3nuKedQd7oZKec6nLsZca5yhDsTbhcz15ngHZzZOc9MfeURdtwrv6XbRec6T8U9BF+APWfYc071qqO6KquzynDn2fAz3DO3uetNqL1t5pj2Jz/NdcbMoTqcnc6YVe7O3FczuwnXV7nrbl35rtuh9qZs8oxz3U6hOpusm9m5bKLbKVd+093mG2q39t2Z+8qfzohzlZdzZ+5jtsHtbe6rzrbncrff+W7vGfCz3Zn7zmOuPGbKY648Z9xjv6HbKVfenbmPmWPan/wm76idCbWj5s6pGVFum21we5y7nqP6CDvVnzJ25d2Z+9t5i9rbZk8FH6DOHXUJzp3DGTN2XVZzZeqM2TPg+9z929z1Jtze1fseTLvKc3Y6O1SPs9N56tRZMe1ucH2Vu+6p67odam/KlGeqg7gO54jqbLLT+QrdHezcjJTjDucbeBfnOium3c2MOMc5znVWVH+L2zu5r+tu7pk6yk87d+H7cWa38Zh3PGMfu27e0O2ww7nOiupPTP3tfdsegjvqjNm0c2VGlKtMgb0Nbo9z13N0feW2mXM4s1PZNG9xe1fvuww+UJ0xKzjrZnYuc/mUTf4Z8H3u/m3uehPdXrnTe6cd5Tk7nR2qx9np7LKNe8B+6jPYd2fVZU5c1+1Qe1OmPMLe9V2OqM4mm2bMON/S7bLDuXOIyzd0u9O97E9nxLkrO1dwd50+Q/VP7pi67Kf+Xbrnseu8OmOmcB53lUc2nYnuDnY4s7tK3ePumvyDyStwR50xm3auzIhyXf8Udxfnrufo+sptM+dwZqeyad7S7ZW7cu8x+LAOXOKsm9m5zOVTNvlNPsF77p5t7noTm73Tu6e+8pydzg7V4+x0dtnGPWA/9Rnsu7PqMieu63aovSlTHmHv+i5HVGeTTTPSuY6TO3HuHOLyDd3udC/70xlx7srOFdxdKq/ZOcxUp3OYTb7r4FyZyid4D2d2nVdnzBzcwZmdYtOZ6O5ghzO7u0z3dX7aVeCOOmM27VyZEeW6/inuLs5dz9H1ldtmzuHMTmXTvGWzd/XuQ/58EM7sVNbN7Fzm8k02zVM+wXvunm3uehNqb5s5pv3JX5kdqsfZ6eyyzmHGnueJ6jtUFzPluDfNW9TelJ16nlW+6Zxk3czOZRO4w/vd3DkEc9dxdH3lMGN/OiPOne50/Q63xznO7DYeUR6zyW/mKZ/gPZzZOT/h9jljx7PKVOcUvIPv62Z2LnNM+5Pf5B24o86YTTtXZkS5bbbB7XHueo6ur9yUse9mdiqb5i1qb5s9mT8fgjM757GjfLkpY8fzlFXO/g54dzF1Osd+Q7fPjv3EtL/1aq4zZoqux67zOKtd3Cu4wz3OVaeD+2q/MuVUZ5PVvAHv4V12dzzO2FE959izc36akXInqP0ucx32rrcBd9wed7h34nF2HXQuR7jjeh1un3Ok66Crs4N3eafzyhV4B3Y571D3uRkp1/V5RsohzuEed1x+BXVPl6kOuw3T/uRVh70D+9MZqX32vLvx3Jkc+w3dPmeq46hut8OdouttMtVh1/WxM1E7apcd+xcgwwDc/UG86Qf5V5DvMoTPkb9/30l+LiH8DPJ39TYyDMDdP2T5Q3od/u7yXYbwOfL3L4QQrpF/P5+CDMP/UH/Irv5hu7sf8h2G8A3k72EIIezhfzPz7+ZTkGEIIYQQQgghfAsyDCGEEEIIIYRvQYYhhBBCCCGE8C3I8BLf/t/rdf9NYbnTz7Dd2/ZCCCGEEEIIfyDDY37CL+TTO179DNu9q/eHEEIIIYTwlyPDY37DL+RXP8N27+r9IYQQQggh/NXgL9J15l+sMWev3APcn+h2VcZ5nTFjJv/Aedy94gvncdd1QgghhBBC+Iv58xdlnNmdZFfge9y9p/nkHkz+gepgpnyh3DYLIYQQQgjhL+bPX5JxZneSbaldhB32Oa8zwn3sKffA+coR5d3c5ZUpsBdCCCGEEMJfzJ+/JOPM7iTbwHvTzDl7npHOPVCes6sdl7tuCCGEEEII4X/58xdnnNmdZBt4T93TdTrHdO6B8pxd7bjcdUMIIYQQQgj/y5+/OOPMbpMp7+Cu262c/TQjnXugPGdXOy7fZl0eQgghhBDCL+fPX4bdjJRDJu/gPXfHlJdTPeywmzzn3EHPznU6x557yoUQQgghhPCLkWEIIYQQQgghfAsyDCGEEEIIIYRvQYYhhBBCCCGE8C3IMIQQQgghhBC+BRmGEEIIIYQQwrcgwxBCCCGEEEL4Dv7zn//8P4Us/3B+82f7yeCfuys/o7v7xWb3zv1XqWciqvcqPv38v4HNd/ut3z2+++k7qt1C9a+wubtzG7b7d57xSTaf79Of7VXPr3uRrsNuw3b/zjMUp8+deiG8gT//IvzWP5y/9XP9ZNTP5OTndHe/qJ1ub9N5Nup573yHTz//b2L6Xr/xe1fvdPKeJ90rqPs5U50TtvvVu/OsTzK9+yc/Wz372c9Xd3KmOids96t351mK0+crF8Ib+fMPY/5wfp6/5ftXf9bu/vk73a9+t7fpvItPv8M3fAe/kd/yvZ58jld/ZnU/ZsqfsN2v3rb/jXzru+P3+uz3U3dipvwJ2/3qbfsnbO98xbNDuMCffxhxVueauf+tnjsq33QqV51n4p5V55rZd1lH9RF23FO+yxzcrXm7r7i67/Ywdx2H2sV9nl22cQrs1xn3eXYZMnkEu3XmXczZ46w858p3qB03c85d1eHZZV2Ojj1mqnM6T/nE6d7V5xRuv3LlMVP+hM0+djZ9pPq442bOuas6PLusy9Gxx0x1Tucud90Ot1O58pgpf8JmHzubPuL6mLsz07kQ3si/fxgRLHHuztz/Bn8lx7lzr0DdzxnO7Fzm2Ox3MzuXObBbZ8y23Nkt1C5nqjPR3cHOZV0+wXs4s5sy5SZ4B2d2Kutmdi5zVNfh+l2GMzuXdXmx3cO5cy5TnQ1X9mqHUV0H77gZ6dyD8hNq9wF7N09U3+H6XYYzO5d1ebHdw7lzLlOdLp/gPTcjnXtQfkLtPmDv5gnXx7zOPGO/y0N4M/MfRvY4s9t4pLyCO7iHTL5wPc5x7tyzcXdzjjM7l3VMffY4s1NZzYxyav+Uq/tqrzLF1GGvZnYn2RbexZmdyxD2NTNTXzmVdXOdFdxn3F3ujDzygjOc2bmsy4vtHs6dQyp3fuLqnuLKXbXDuzxzNvmJab/OCtxxYHc6I7XPXZ7ZuazLi+0ezp1DKnceO8pN4N14B8+cTX5i2q+zAnccros5d3ie8hDezPyHsfPKYaY8Mvmieq47+QfOc45z555Jdy87nNm5bKJ21B7nOLNzmaO62D/ZV1zd3+xduZt3cGanMtU5obuPncuQyTPcx5mdyrqZ3Snurjpjxh2X4czOZV1ebPdw7hzTuY6re45nvseUTX7idL9zCuyrM2bccRnO7FzW5cV2D+fOMZ17MPkJtT9lk5843e+cAvubs5qnPIQ3M/9h7LxymCmPTJ65c59znOPcuWcx3ckeZ3Yu27K5D2d2LnPc3Vdc3d/sXbmbd3Bmt/Gn8B04s3MZMnmG+zizU1k3szvF3VVnzLjjMpzZuazLi+0ezp1DKnfecdrfcOXO2uFdnjmb/MTpfucU2FdnzLjjMpzZuazLi+0ezp1DKnceO8pN4N14B8+cTX7idL9zCuxvzmqe8hDezPyHsfPKYfZpfyXHuXOnqN3NfdzBmZ3LHJv9bmbnMsfJ/rarsi4vJv9g02F4B2d2p/5KjjO7U7+B+zizU1k3s3OZA7vqjBl3XIYzO5d1ebHdw7lzLlMdh+q+c/8B93Fmd+onTvdPHWbqjBl3XIYzO5d1ebHdw7lzLlOdLp/gPZzZnfqJ0/1TV5lD7fE85SG8lfqD6P5ATl51uPdprzrKTTNS7gS1u7mr9pDJP8BOx7R7xT/ATsd21znccx3suZyZeuwdvMe7yj/o3AO8A7suRyb/YLs/wTtuRsp1fZ6RchPYn87KncxMedVBN3nO3FyZOyMuZ6qnUH3FM3a7vM6I6jo/sd2fOirHbDordzIz5VUH3eQ5c3Nl7oyoDsM7CtfFvM6I6jo/sd2fOtsc5zo7z5lyIbwZGQbDJ/7Snj7z2e/46ef/dD79/X36+eG/yfcbvpn8+QwhfDEyDP8D/wP+iX/QT57J3ZoduOt4Vfdv4dPf36efH/7lp32/9b4OtYOoHUTtIGoHUTvPRD0TUTs/md/2ufBnpVA7z0Q9E1E7IYQWGQbgk//QTM9mrzp3mO5mrzp/M9N3w1517jDdzV51wnXy/YZvJn8+Qwg/DBmGEEIIIYQQwrcgwxBCCCGEEEL4FmQYQgghhBBCCN+CDH8ln/xvdr/hvxl+9Tu8+vPdubvezd0x+Q3PuKNjulu52un2trz6/nCP3/5zUJ+tPvNv/tzF9Dnvfhd39xWvuFPxqueoO1/xnFdT7/zT3vuZvPrz37m73s3dMfkNd3YnnvF+B8jwV/LGL1Xy6ec/eOU7fPvd0x3veMYduruvui2vvj/c5zf/HLrP9ps/N7L5nHe/i7v7ilfcqXjXu7/iOa8E3/envfszeeVnf8bd0x13nnFndwLvfuVzABmGF/CmH2jLN7zDFZ7x3tMd73jGq+ie+4x3evX9IXTkz9+Ou9/FK77LV9ypeNe7v+I5r4Tf9ye9+0/hGX8mpjvuPOPO7gTf/arn/C/4wDrjQ3l2WUf1EXbcU77LJmpH7aFznY7trvKY1dl1th7d1OHZZRO10901dZRX7kG3jw656itHpg57h+tzXjN3MUdUh88bqs+oDp/ZI+gnpv2t40x12E24PuZ1RqZeec6V507nuIOz8htwT+2yVx2H2n2gOnxmj6DvwL7anzzn7Hl2ec2qy5477NirDnuH62PuztxFTjz3lLuKupOzmhHVd/7VTM/E9+IuzspzfsV3YN/tY648d1S+6Siv3INuHx1y1ytqh5k66CemHbx36i7586E4s3OZY7PfzexctmF7l8q2dLvOVV7OnbmPWecwY8+zyza4Pc5xZneSKbredIfymwxndhOur3LX3bryXdfR7ZQr787cx8xxus+u5onujg7XrVx5zrjnztzHzOWbbJpP2Nylsolup1x5d+Y+Zh3cd3Nl7sx9512+6XKH56nDbsL1Ma8zz3wuTvwmv4O6E7O7/tXUs9wzVc5ZzZW5M/c3fkP1a8eduY/ZSY4zu5NM0fWmOybf4XZV7rqK6rodlbvuAX9egjM7ldXMuD7T9dm5bIPbu3pfUftI15tynOus4H7ntrPLNrg9zqdZ0XXKIV1PuQfKV6ZQOzxPuL7KXffUdV1Ht8MO5zorqj8x9fFO7uKszh11R4fr4j0O7m5nh+ptsmmeqD6iPGandHeww7nOiupPcP9kZrfxLt9kblZs9idcH3PuKKfYeMTld1B3cqY67BSqr1C7D059zYhynHUzO6a8YuqwV3OdFdVXe10+zYquUw7peso9mHyH21W56zq6vnJdf8mfl+DMzmUTtaP2OMeZncs2dHvlTu/lne4O567sFOzvzi7b4PY4V73KOGc/5a43uQfKn+5MfQb77qy6zInruo5uhx3O7K5S9/BdnHWzOmN2BbePdyuPnHS6nvKbbJo7truVK7eh22WHM7sr8B3TjCiHmfIu32TTzJz2GexvzpNjJo+cdLeoO7ts03033Tsox5nqbNyDyW/o7ji533U5V73KOGc/5a43uQeT73C7KnddR9dXrusv+fMSnNm5bMvmPpzZuWzDZu/0bu53+85d2SnY351dtsHtce56ndvmrje5B8qf7kx9BvvurLrMieu6jm6HHc7s7sL3nczqjNlV+A6c2Sk2naLrKrfJprnjdHfyjm6PHc7srsB3TDOiHGfT3OWcTTNz2mewvzlPjpk8ctLdou7snsOu676L7h2U40x1Nu7B5Dd0d5zc77qcu17ntrnrTe7B5Dvcrspd19H1lev6S/68BGd2LnNs9ruZncs2bO9SmYO73a5zpzuYsb87u2yD2+McZ3abzJ3VjHTugfJTxp7nieo7VBcz5bg3zRtwh/e7mZ3LHNM++25WZ8zUzha8C3d5VpnqdM71t13Oprlj2uXZZRO4w/vdzM5lHdyfZkS5LlOOO102zZyx53mi+g7s8I5yp36TF5NXqB3M7vp30D1POc5Up3OYTX5D11fO9bc5zuw2mTurGencg8l34K47q+6Grq9c11/y7yUIlpR/gJ2OafeKf4CdiW6XHfsJ3lN3YAedyxHucI9z7px4pPyGbpczNyPlEOc5nzrsJs+u8zhjp4P7ar8y5VRnk9W8pbtLZarDbsO0z/nVM/LIT3G7eC93XI5wR/U6z855N09Uv3Z4Hz27E9R+l6kOuw3dvssR7ky9zqkO59xBz449ztjp4D7OdXaeO+w2XnXYY0e5DrxX3XHXv5LNc7mDPZcj3OHe5Ds2e9xRvc5z5makHOI851OH3cZvcPuYK9+x2eOO6x3w76UkLE968P/y6eeH/ybfbwgh/EP+PQwhhK/h7B/lV/wD/unnh3/J9xtCCP+SfxNDCOFLqH+Q3T/M7FXnDtPd7FUnXCffbwghaPJvYwghfBUyDCGEEEIIIYRvQYYhhBBCCCGE8C3IMIQQQgghhBC+BRn+Sl793yXfubvezd0x+Q3T7t37X0m92533U7vPuPcO73r+u54TQgghhPAiZPgreeUvbc+4e7rjzjM2u3fufyX4Xlffsdu7euezeNfzP/05QwghhBBuIMNwyDN+IZzuuPOMO7ufht/9yufoPn/n3sG7nv/pzxlCCCGEcBn8RabO/IsN5lf8RLeLmetscLuYTR3llXvQ7aNDrvrKlWdXYId7neMOzspvwD21r7IteCeiOnxmj6DvcH3M3Zm7yInnnnIhhBBCCF/Of//SgzOfixN/yuY+1dng9jjHmd1Jpuh60x2Tf+A6V3c32TR3qC5mdcbsCt0+3+/O3MfM4bqY15lnPhcnfpOHEEIIIfwA/vxlBmd2THmF6iumXc5UZ4Pb43yaFV2nHNL1lHvgfOWI63COqM4mm+YO1eVMdU7p7mCHc50V1e9wXcy5o5xi4xGXhxBCCCH8APpfZqZfdCY/wfvqvk1ng9vjXPUq45z9lLve5B4oz5nqdDmiOptsmjtUlzPVOaW7gx3O7E5xd7nz5JjJIyfdEEIIIYQvo/9lZvpFZ/ITvK/u23Q2uD3OXa9z29z1JvdAec5Up8sR1dlk09yhupypzindHexwZneKu8udJ8dMHjnphhBCCCF8Gf0vM8phNvkJ7qrdTWeD2+McZ3abzJ3VjHTugfKcqQ7nm85JNs0dqsuZ6pyCd/B93czOZY7qOrDDO8qd+k1eTD6EEEII4WPULyrdLyzc4d7kJ3iP75jmDbWjdjlzM1IOcZ7zqcNu8pyrjuo5x56d826eqD7iHO6dou7pMtVht4F3cK6z89xht/Gqwx47yoUQQgghfBgZhhBCCCGEEMK3IMMQQgghhBBC+BZkGEIIIYQQQgjfggxDCCGEEEII4VuQYQghhBBCCCF8CzIMIbyR//zff1DuG/j29wshhBDCL8f9MqLyrsewVzsqY8fzlCl3B763UN1X88lnb/j295tQ716f6dWf6x3PmOie/w3vF15H/XynPwMq3zLdf4W689n3Fq++/yfx6u/ht3/P9fm+8TNO7zb5B5OfmHbv3n+FeubV597dP+Fdz/k47oNyXvNJF8/OIyqvDPPT+S6vvn/Lp5675dvfr6N793d8rnc8o2N6/qffL7ye7mf8jJ//M+5QvOre4tX3/xTyPd/nGz8jvlP3fp17MPmOze6d++9w97nveu93PefjuA+KuTurLs91Vln1C5VXhs6dEZepfIL3rt4TvpfuZ/rqn/er79/QvcM3vF94PT/1z8Cr3+2bP3v4WXzjnyV+J/d+r3z3V959l7vv9s2f7UfivtDKleesm+usqL7a40zhdhwnXYT3cO6c65547rHDbOpUrjoOtad2ncPMdSZwT+2yVx2H2n2gOnxmj6C/A97n7sf81Cv3APfvwPep+ytDth5n5TfgntrfOO5hp2Ozj/mpn2ZEucoY7Gxwu5i5zgTu8S5mU2fy04yorvITm33M2ePceZXV7HrKqc7kudM57uCsPOfKT3S7mLkOO+U7eJf3O4+z8tzp6HrllEfHHeUe4D73VL7pVK46Hbiv9thzB7POdw4zldfcdVXnZK4z+gnXP73nv5guVZ4z7DmnetVRXZXVWWW482zwmfy8ad50pvk0x7lzW7Z3bHPX27C5S2UT3U658u7MfczuUPfVne7M/Y3vsmfA904zZ5PHuTL2Harb7W/6qtNR/dpxZ+5f8TwjV90WdwfnrufAvtqtTHXwzH01d85l0zzR7bNTWc2VqbOj7uC9bTb5Kd9k3czOZVs293UzuwnVx2zy01xnzBxTR/k7GeM6nOPcuS24w/s8d53K2CPsXFflVzL2PGNWueo4XPfkjj/AZXXuqEtw7hzOmLHrsporU2fMngHex3dP86Yzzac5zp3bsr1jm7ueo/qI8pid0t3BDuc6K6p/F74PZ3anvsueAd+rnqMydgruuLlj060OorybJ7r9OivcPnKnu3Vb3B2cu55j2u9mdiqrmXPlEO64eaLbr7PiZL87Fyrr8mLyheptstP5lNpHlFdz5zZMfeU5O50dU0/5aafY9FyHc5w7t6W7o86KzT7OiOt2+ZWMPc/bjsN1T+74A1xWZ8wKzrqZnctcPmWTfwZ8H86dc9npfJrj3Lkt2zu2uesptruVK7eh22WHM7tX0D1DOcwm32XPgO91z6mcnesjm45j2mWv+ptOR9ff3lU97nKmOnfdFncH567nmPZ5RpTjrGbOVVfBnc0O0u2zU3QddO6suky5q/6B8ptsmjHjfIJ31B1dp3Mbpr7ynJ3Ojqmn/LRTbHquwznOndvS3cFOwR2cO6fmLr+SsefZZVtw152PqeUJXOKsm9m5zOVTNvlNPsF7OHfOZafzaY5z57Zs79jmrqc43Z28o9tjhzO7V9A9QznMJt9lz4DvnZ5z2n+w6TimXfaqv+l0dP27d00zctVtcXdw7nqOaZ9nRDnOcO6c48oO0u2zU3QddO6suo6p03nlNtk0I51TcF/td53ObZj6ynN2OjumnvLTTrHpuQ7nOHduS3cHOwV3cO6cy1TH5VPGnmeXbcFdd77AnxfgzE5l3czOZS7fZNM85RO8h3PnXHY6n+Y4d27L9o5t7nqKaZdnl03gDu93MzuX3aG7TznMJq8y5a/Cd00zZ5N3nS1qFzP2PG87HV1fOcxOPc/I1nW9DrfHues5pn2eEeU462Z2KpvmiW6fncpURzl3Vt1tNvkp32TdzM5lDu6q3a7TuQ2qj9nkr8yOqaf8lUz5kxznzm3p7mCnsm7uHFK589g5ydjz7LIttetQOwv+vABnds5jR/lyU8aO5ymrnP1V+F7MXMf5qzNmnZtmpNyGzX7n2bGf4D2+Az27E9R+l6kOu7ts7uUO9yavespfhe/lZ6BnN3mXn8B38D2cc2eaO6rb7XCHeyceZ9dhpzrKd+Au73OmOh3VR5zDPdfBXpepDruuj52O6iNb73L23" width="554px" /><br /></p><p>Asdfasd </p></body></html>
"""
    my_to_html.image_ocr(a)
