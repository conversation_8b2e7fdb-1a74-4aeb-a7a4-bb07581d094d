import time

import setting
import os
import json
from setting import logger
import fcntl
import requests
import copy
import threading
mutex = threading.Lock()


def callback_for_http(url, data, wait=False,retry_times=1):
    if url:
        logger.debug(f'回调地址:{url},回调数据:' + str(data))
        # response = requests.post(url, data=json.dumps(data), timeout=3).text
        for i in range(retry_times): #所有回调给java的东西都try两遍，间隔10min
            try:
                response = requests.post(url, data=json.dumps(data, ensure_ascii=False).encode('utf-8'), timeout=10,headers={"content-type": "application/json"}).text
                logger.debug(f'{data}:回调response:{response}')
                return response
            except:
                logger.error(f"回调{url}失败", exc_info=True)
                if retry_times!=1:
                    logger.error(f'回调{url}失败,尝试重试', exc_info=True)
                    time.sleep(10*60)
    else:
        logger.debug(f'空url无法回调数据:{data}')


class RECODER():
    def __init__(self,name = ''):
        self.train_record_file = setting.TRAIN_RECORD_FILE  + name
        self.train_record = {}
        os.makedirs(setting.SAVE_MODEL_DIR,exist_ok=True)

    def check_train_record(self):
        if os.path.exists(self.train_record_file):
            with open(self.train_record_file, "r", encoding="utf-8") as f:
                try:
                    train_record = json.load(f)
                except:
                    train_record = {}
        else:
            train_record = {}

        for data, flag in train_record.items():
            try:
                logger.debug(f"重启后发送训练失败消息{data}")
                data = eval(str(data))
                data['code'] = 1
                data['msg'] = '服务重启'
                callback_for_http(data.get('callbackurl',data['callback_url']),data)
            # logger.debug(f"错误训练记录为:{train_record}")
            except:
                logger.error('重启失败发送失败',exc_info=True)

        with open(self.train_record_file, "w", encoding="utf-8") as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            train_record = {}
            json.dump(train_record, f, ensure_ascii=False)

    def dump_train_record(self, data, is_add):
        key = json.dumps(data)
        mutex.acquire()
        if is_add:
            self.train_record[key] = 1
        else:
            if key in self.train_record.keys():
                self.train_record.pop(key)
        #对self.train_record加锁
        with open(self.train_record_file, "w", encoding="utf-8") as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            json.dump(self.train_record, f, ensure_ascii=False)
        mutex.release()
