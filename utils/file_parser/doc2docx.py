import subprocess
import os
# doc转docx
def doc2docx(docPath):
    docxPath = os.path.dirname(docPath)
    # cmd = '/bin/libreoffice6.1 --headless --convert-to docx'.split() + [docPath] + ['--outdir'] + [docxPath]
    cmd = 'libreoffice --headless --convert-to docx'.split() + [docPath] + ['--outdir'] + [docxPath]
    p = subprocess.Popen(cmd, stderr=subprocess.PIPE, stdout=subprocess.PIPE)
    p.wait(timeout=30)
    stdout, stderr = p.communicate()
    if stderr:
        raise subprocess.SubprocessError(stderr)
    else:
        return os.path.join(docxPath, os.path.basename(docPath).replace('.doc', '.docx'))

def ppt2pptx(pptPath):
    pptxPath = os.path.dirname(pptPath)
    cmd = 'libreoffice --headless --convert-to pptx'.split() + [pptPath] + ['--outdir'] + [pptxPath]
    p = subprocess.Popen(cmd, stderr=subprocess.PIPE, stdout=subprocess.PIPE)
    p.wait(timeout=30)
    stdout, stderr = p.communicate()
    if stderr:
        raise subprocess.SubprocessError(stderr)
    else:
        return os.path.join(pptxPath, os.path.basename(pptPath).replace('.ppt', '.pptx'))


if __name__=='__main__':
    docPath = '/data/sammy/dddtttt.doc'
    doc2docx(docPath)
