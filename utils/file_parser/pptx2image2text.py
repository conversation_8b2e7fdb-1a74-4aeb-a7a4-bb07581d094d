#upload
# from setting import logger
from minio import <PERSON>o
import os


from setting import logger
from setting import MINIO_URL,MINIO_NAME,MINIO_PASS,BUCKETNAME
from setting import FILECHAT_PPTX2IMA_URL

class MINIO:
    def __init__(self):
        self.client= Minio(
            MINIO_URL,
            access_key=MINIO_NAME,
            secret_key=MINIO_PASS,secure=False
        )

    def upload_file(self,bucketName,filename,filepath,delete=False):
        # Create a client with the MinIO server playground, its access key
        # and secret key.
        # Make 'asiatrip' bucket if not exist.
        found = self.client.bucket_exists(bucketName)
        if not found:
            print(f'bucketName:{bucketName}不存在,开始创建')
            self.client.make_bucket(bucketName)
            print('新建桶{}成功！'.format(bucketName))
        # Upload '/home/<USER>/Photos/asiaphotos.zip' as object name
        # 'asiaphotos-2015.zip' to bucket 'asiatrip'.
        self.client.fput_object(
            bucketName, filename, filepath
        )
        if delete:
            os.remove(filepath)
        url = self.client.presigned_get_object(bucketName, filename)
        return f'http://{MINIO_URL}/{bucketName}/{filename}'
        # return url


    def download_file(self,bucketName,filename,output_path):
        print(f'{bucketName,filename,output_path},开始下载')
        output_filename = filename.split('/')[-1]
        if os.path.exists(os.path.join(output_path,output_filename)):
            return
        data = self.client.get_object(bucketName, filename)
        with open(os.path.join(output_path,output_filename), "wb") as fp:
            for d in data.stream(1024):
                fp.write(d)


import time
import os
import requests
minio = MINIO()

# 上传


def ppt2image_main(pptpath,dpi):
    total_start = time.time()
    start = time.time()
    headers = {'User-Agent':'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36 OPR/52.0.2871.99'}
    dir_name = pptpath.split(".pptx")[0] + f"-{dpi}"
    # if os.path.exists(dir_name):
    #     print(f"exits: {pptpath}")
    #     return
    ppt_url = minio.upload_file(BUCKETNAME,os.path.split(pptpath)[-1],pptpath)
    print(ppt_url,'ppt_urlppt_url')

    data = {'url':ppt_url,'dpi':dpi}
    print('上传耗时',time.time()-start)

    start = time.time()
    try:
        wb_data = requests.post(FILECHAT_PPTX2IMA_URL, headers=headers, data=data)
        logger.info(f"ppt转图：{FILECHAT_PPTX2IMA_URL}, {data}, {wb_data.text}")
    except Exception as e:
        logger.error(f"ppt转图失败：{e}", exc_info=True)
    print('转图片耗时',time.time()-start)
    print("转图片结果： ", wb_data.text)

    minio_data = wb_data.json()
    minio_urls = minio_data['result']['images']

    print(minio_urls,'minio_urlsminio_urls')
    start = time.time()
    from tqdm import tqdm
    return_image_path = []
    for url in tqdm(minio_urls):
        # bucket_name = os.path.split(url)[0].replace('/minio','')
        bucket_name = 'filetrans'
        print("url: ", url)
        print("dir_name: ", dir_name)
        filename = '/'.join(url.split('/')[-2:])
        page = int(filename.split('/')[-1].split('_')[0]) + 1
        ppt_name = filename.split('/')[0]
        os.makedirs(dir_name, exist_ok=True)
        print("filename: ", filename)
        print("dir_name: ", dir_name)
        print("ppt_name: ", ppt_name)
        minio.download_file(bucket_name, filename, dir_name)
        from PIL import Image
        image_save_path = os.path.join(dir_name, filename.split("/")[-1])
        print("image_save_path: ", image_save_path)
        resolution = Image.open(image_save_path).size
        resolution = f'{resolution[0]}x{resolution[1]}'
        

        ultra_filename = f'{ppt_name}_{resolution}_{page}页' + '.' + filename.split('.')[-1]
        ulrta_image_save_path = os.path.join(dir_name, ultra_filename)
        try:
            os.rename(image_save_path, ulrta_image_save_path)
        except:
            logger.error('ppt转image重命名失败')
        return_image_path.append(ulrta_image_save_path)
    print('下载耗时',time.time() - start)
    logger.info(f"ppt转图片耗时：{time.time() - total_start}")
    return return_image_path

from module.img2text.img2text import img2text
from tqdm import tqdm
def imagepath2text(imagepath):
    result = []
    for i in tqdm(imagepath,desc='pptx图片转文字'):
        text = img2text(i)
        result.append(text)
        # os.remove(i)
    return result


def pptx2image2text(pptpath):
    imagepath = ppt2image_main(pptpath, 120)
    text = imagepath2text(imagepath)
    return text



if __name__=='__main__':
    pptpath = r"C:\Users\<USER>\Desktop\文件解析测试文件\指针大模型v1.pptx"

    # ppt2image_main(pptpath, imgpath, 400)
    # ppt2image_main(pptpath, 120)
    text_result = pptx2image2text(pptpath)









