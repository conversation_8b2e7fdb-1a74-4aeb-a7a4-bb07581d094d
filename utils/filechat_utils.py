import os
import requests
import re
# from pdfminer.layout import LAParams, LTTextBox,LTCurve
# from pdfminer.pdfpage import PDFPage
# from pdfminer.pdfinterp import PDFResourceManager
# from pdfminer.pdfinterp import PDFPageInterpreter
# from pdfminer.converter import PDFPageAggregator
import itertools
import numpy as np
import openpyxl
import pptx
import pandas as pd
import Levenshtein
import chardet
from setting import logger
from openpyxl import load_workbook
import pdfplumber
from hashlib import md5



def old_pdf_text_extract(pdf_path):
    fp = open(pdf_path, 'rb')
    rsrcmgr = PDFResourceManager()
    laparams = LAParams()
    device = PDFPageAggregator(rsrcmgr, laparams=laparams)
    interpreter = PDFPageInterpreter(rsrcmgr, device)
    pages = PDFPage.get_pages(fp)
    coordinate_extract = []
    for index,page in enumerate(pages):
        print('Processing next page...')
        interpreter.process_page(page)
        layout = device.get_result()
        for lobj in layout:
            if isinstance(lobj, LTTextBox):
                x, y, text = lobj.bbox[0], lobj.bbox[3], lobj.get_text()
    #             print('At %r is text: %s' % ((index+1,x, y), text))
                coordinate_extract.append([(index+1,x, y), text])
    return_text = [i[-1].split('\n \n') for i in coordinate_extract if i[-1].strip()]
    return_text = list(itertools.chain.from_iterable(return_text))
    fp.close()
    return_text = [i.replace('\n','') for i in return_text]
    return return_text

def pdf_text_extract(path, need_extract_table=True):
    """
    pdfminer结构化成都高,但是有些文档抽不出来

    """
    def get_max_score_of_list(a_t, t_list):
        return max([Levenshtein.ratio(a_t, t_l) for t_l in t_list])

    if need_extract_table:
        pdf_2020 = pdfplumber.open(path)
        table_result = []
        text_result = []
        for page in pdf_2020.pages:
            table = page.extract_tables()
            result = []
            for t in table:
                result.append(pd.DataFrame(t).fillna(""))
            table_result.append(result)
            text_result.append(page.extract_text())

        all_text = []
        for page_i in range(len(table_result)):
            if len(table_result[page_i]) == 0:
                all_text.extend(text_result[page_i].split("\n"))
            else:
                text_split_list = text_result[page_i].split("\n")
                for a_table in table_result[page_i]:
                    table_str_temp = []
                    for row in range(a_table.shape[0]):
                        for col in range(a_table.shape[1]):
                            if len(a_table.iloc[row, col].strip()):
                                table_str_temp.append(a_table.iloc[row, col].strip())
                        table_str_temp.append(" ".join([a_table.iloc[row, col].strip() for col in range(a_table.shape[1]) if len(a_table.iloc[row, col].strip())]))

                    flag_score = [get_max_score_of_list(t, table_str_temp) for t in text_split_list]
                    flag_str = ''.join(["1" if score > 0.3 else "0" for score in flag_score])
                    start_idx = flag_str.find("1")
                    end_idx = flag_str.rfind("1")
                    if start_idx != -1 and end_idx != -1 and end_idx >= start_idx:
                        all_text.extend(text_split_list[:start_idx])
                        table_str = "------\n"
                        for row in range(a_table.shape[0]):
                            table_str = table_str + "|".join([a_table.iloc[row, jj] for jj in range(a_table.shape[1])]) + "\n------\n"
                        all_text.append(table_str)
                        text_split_list = text_split_list[end_idx+1:]
                    else:
                        table_str = "------\n"
                        for row in range(a_table.shape[0]):
                            table_str = table_str + "|".join([a_table.iloc[row, jj] for jj in range(a_table.shape[1])]) + "\n------\n"
                        all_text.append(table_str)
                all_text.extend(text_split_list)
        return all_text
    else:
        import pdfminer.high_level
        text1 = pdfminer.high_level.extract_text(path).split('\n\n')
        text1 = [i.replace('\n', '') for i in text1]

        if len(text1)<10:
            import fitz
            output = []
            doc = fitz.open(path)  # open a document
            for page in doc:  # iterate the document pages
                text = page.get_text()  # get plain text (is in UTF-8)
                output.extend(text.split('\n'))
            output = [i for i in output if i.strip()]
            if (len(output)/len(text1))>5:
                return output
        return text1


import docx
from docx.table import _Cell, Table
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.image.image import Image
from docx.parts.image import ImagePart
from docx.oxml.shape import CT_Picture
from module.img2text.img2text import img2text
from io import BytesIO
from docx.text.paragraph import Paragraph
from docx import Document

from docx.opc.pkgreader import _SerializedRelationships, _SerializedRelationship
from docx.opc.oxml import parse_xml


def load_from_xml_v2(baseURI, rels_item_xml):
    """
    Return |_SerializedRelationships| instance loaded with the
    relationships contained in *rels_item_xml*. Returns an empty
    collection if *rels_item_xml* is |None|.
    """
    srels = _SerializedRelationships()
    if rels_item_xml is not None:
        rels_elm = parse_xml(rels_item_xml)
        for rel_elm in rels_elm.Relationship_lst:
            if rel_elm.target_ref in ('../NULL', 'NULL'):
                continue
            srels._srels.append(_SerializedRelationship(baseURI, rel_elm))
    return srels


_SerializedRelationships.load_from_xml = load_from_xml_v2



def iter_block_items(parent):
    """
    Yield each paragraph and table child within *parent*, in document order.
    Each returned value is an instance of either Table or Paragraph. *parent*
    would most commonly be a reference to a main Document object, but
    also works for a _Cell object, which itself can contain paragraphs and tables.
    """
    if isinstance(parent, docx.document.Document):
        parent_elm = parent.element.body
    elif isinstance(parent, _Cell):
        parent_elm = parent._tc
    else:
        raise ValueError("something's not right")

    for child in parent_elm.iterchildren():
        if isinstance(child, CT_P):
            yield Paragraph(child, parent)
        elif isinstance(child, CT_Tbl):
            yield Table(child, parent)
        # elif isinstance(child, CT_SectPr):
        #     yield Paragraph(child, parent)

def text_table_sequence(path,doc,ocr=False):
    output = []
    output_image_paths = []
    for idd,block in enumerate(iter_block_items(doc)):
        try:
            if hasattr(block, "table"):
                table_str = "------\n" if len(block.rows) else ""
                for row in block.rows:
                    row_texts = [c.text for c in row.cells]
                    table_str = table_str + "|".join(row_texts) + "\n------\n"
                output.append(table_str)
            else:
                output.append(block.text)
                #图片ocr
                ocr_result,output_image_path = docx_get_image_ocr_result(path,doc,block,ocr=ocr)
                output_image_paths += output_image_path
                output.append(ocr_result)
        except:
            output.append(block)
            import logging
            logging.error('错误',exc_info=True)
    return output,output_image_paths

def docx_get_image_ocr_result(path,document,paragraph,ocr=False):
    save_dir = path.replace('.docx','')
    os.makedirs(save_dir,exist_ok=True)
    images = paragraph._element.xpath('.//pic:pic')
    result = ''
    output_image_paths = []
    for index,i in enumerate(images):
        img: CT_Picture = i
        embed = img.xpath('.//a:blip/@r:embed')[0]
        related_part: ImagePart = document.part.related_parts[embed]
        image: Image = related_part.image
        # 如果图片分辨率很小,则去掉
        if image.width < 50 * 10000 or image.height < 50 * 10000:
            continue
        # 后缀
        ext = image.ext
        # 二进制内容
        blob = image.blob
        # Image.open(BytesIO(blob)).show()
        bytes_io = BytesIO(blob)
        binary_data = bytes_io.getvalue()
        base64_encoded = base64.b64encode(binary_data)
        base64_string = base64_encoded.decode('utf-8')
        # img_name = f"citation_image_{idd}_{index}"
        img_name =f"citation_image_{md5(str(base64_encoded).encode()).hexdigest()[0:5]}"
        save_img_path = os.path.join(save_dir,img_name + '.png')
        output_image_paths.append(save_img_path)

        with open(save_img_path, 'wb') as f:
            f.write(binary_data)
        if ocr:
            ocr_result = img2text(image_base64=base64_string)
            result += f'\n{ocr_result}\n'
        else:
            result += f'\n{img_name}\n'
    return result,output_image_paths


def GetParagraphText(paragraph):
    def GetTag(element):
        return "%s:%s" % (element.prefix, re.match("{.*}(.*)", element.tag).group(1))

    text = ''
    runCount = 0
    for child in paragraph._p:
        tag = GetTag(child)
        if tag == "w:r":
            text += paragraph.runs[runCount].text
            runCount += 1
        if tag == "w:hyperlink":
            for subChild in child:
                if GetTag(subChild) == "w:r":
                    text += subChild.text
    return text

# def doc_extract(path):
#     document = Document(path)  # 读入文
#     output = []
#     for i in document.paragraphs:
#         output.append(GetParagraphText(i))
#     output = [i for i in output if i]
#     return output

def doc_extract(path,ocr=False,need_image_paths=False):
    document = Document(path)  # 读入文
    element,output_image_paths = text_table_sequence(path,document,ocr=ocr)
    raw_string = [str(i) for i in element if i]
    if need_image_paths:
        return raw_string,output_image_paths
    return raw_string

def detect_file_encoding(file_path):
    with open(file_path, 'rb') as f:
        result = chardet.detect(f.read())
    return result['encoding']


def txt_extract(path):
    texts = []
    encode = detect_file_encoding(path)
    with open(path, 'r', encoding=encode, errors='ignore') as f:
        for line in f.readlines():
            if line.strip():
                texts.append(line.strip())
    return texts


def html_extract(path):
    # encode = detect_file_encoding(path)
    with open(path, 'r', encoding="utf-8") as f:
        html_content = f.read()
    if "selection-highlight" not in html_content:
        paragraphs = re.findall(r'<p>(.*?)</p>', html_content, re.S)
        texts = [re.sub(r'<[^>]+>', '', paragraph).strip() for paragraph in paragraphs]
    else:
        texts = re.findall(r'class="selection-highlight"[^>]*>(.*?)</span>', html_content, re.S)
    return texts

import openpyxl
import pandas as pd


def extract_data_from_excel(path):
    workbook = openpyxl.load_workbook(filename=path, data_only=True)
    data = []
    for sheet_name in workbook.sheetnames:
        sheet = workbook[sheet_name]
        sheet_data = []
        for row in sheet.iter_rows(values_only=True):
            sheet_data.append(list(row))
        df = pd.DataFrame(sheet_data)
        data.append((sheet_name, df))
    return data

def excelpath_to_filldf(path):
    """如果有多个sheet，只返回第一个sheet的df"""
    df_list = extract_data_from_excel(path)
    output_list = []
    for i in df_list:
        sheet_name, df = i
        df = pd.read_excel(path, sheet_name=sheet_name)
        if df.empty:
            continue
        wb = load_workbook(path)[sheet_name]
        df = df_cell_fill_outofcolumns(df, wb)
        # df = clean_df(df)
        return df

def df_extract(path):
    if path.lower().endswith('.xlsx'):
        df_list = extract_data_from_excel(path)
        output_list = []
        for i in df_list:
            sheet_name, df = i
            if df.empty:
                continue
            wb = load_workbook(path)[sheet_name]
            df = df_cell_fill(df,wb)
            df['sheet_name'] = sheet_name
            output_list.append(df)
    elif path.lower().endswith('.xls'):
        df = pd.read_excel(path, header=None)
        output_list = [df]
    else:
        df = pd.read_csv(path,header=None)
        output_list = [df]
    #表格预处理
    texts = []
    for df in output_list:
        df = df.replace(to_replace=[None], value=np.nan)
        df = clean_df(df)
        columns = df.columns.tolist()
        columns = [str(i) for i in columns]
        df.columns = columns
        text = [df_to_markdown(df)]
        texts.extend(text)
    #原始对齐版本md
    # for i in df.columns:
    #     df[i] = df[i].apply(lambda x:str(x).strip() if type(x)==str else x)
    # text = [df.to_markdown()]
    return texts

def df_cell_fill(df,wb):
    merged_ranges = wb.merged_cells.ranges
    for merged_range in merged_ranges:
        # 获取合并单元格的左上角和右下角的坐标
        min_col, min_row, max_col, max_row = merged_range.bounds

        # 获取合并单元格的值
        value = df.iloc[min_row - 1, min_col - 1]
        if value==None:
            value = np.nan
        # 填充合并单元格的 NaN 值
        df.iloc[min_row - 1:max_row, min_col - 1:max_col] = df.iloc[min_row - 1:max_row, min_col - 1:max_col].fillna(value = value)
    return df

def df_cell_fill_outofcolumns(df,wb):
    merged_ranges = wb.merged_cells.ranges
    for merged_range in merged_ranges:
        # 获取合并单元格的左上角和右下角的坐标
        min_col, min_row, max_col, max_row = merged_range.bounds
        min_row = min_row - 1
        max_row = max_row - 1
        # 获取合并单元格的值
        value = df.iloc[min_row - 1, min_col - 1]
        if value==None:
            value = np.nan
        # 填充合并单元格的 NaN 值
        df.iloc[min_row - 1:max_row, min_col - 1:max_col] = df.iloc[min_row - 1:max_row, min_col - 1:max_col].fillna(value = value)
    return df

def clean_df(df):
    # Remove rows where all values are NaN
    df = df.dropna(axis=0, how='all')
    df = df.dropna(axis=1, how='all')
    # Reset the index
    df = df.reset_index(drop=True)
    #判断如果第一行都是nan或者空则删除往下移一行
    for i in range(3):
        if df.iloc[0].isnull().all() or not [i for i in df.iloc[0] if str(i).strip()]:
            df = df.drop(df.index[0])
            df = df.reset_index(drop=True)

    df.columns = df.iloc[0]
    # Remove the first row
    df = df.drop(0)
    # Reset the index again
    df = df.reset_index(drop=True)
    return df

def df_to_markdown(df):
    # Convert the DataFrame to a list of lists of strings
    data = df.astype(str).values.tolist()
    # Get the column names
    columns = df.columns.tolist()
    # Combine the column names and data
    table = [columns] + data
    # Convert each row to a Markdown table row
    markdown_rows = [' | '.join(map(str, row)) for row in table]
    # Add the header separator
    separator = ' | '.join(['---'] * len(columns))
    markdown_rows.insert(1, separator)
    # Join the rows together
    markdown = '\n'.join(markdown_rows)
    return markdown





from utils.pdf_parse_for_rag import my_pdf_extract as pdf_text_extract
from setting import FILECHAT_OCR
from utils.file_parser.pptx2image2text import pptx2image2text
from utils.file_parser.doc2docx import doc2docx,ppt2pptx
def ultra_extract(file_path,use_ocr=FILECHAT_OCR):
    """返回的是list"""
    suffix = file_path.split('.')[-1].lower()
    if  suffix == 'pdf':
        texts = pdf_text_extract(file_path,ocr=use_ocr)
        # texts = pdf_text_extract(file_path)
    elif suffix in ['docx','doc']:
        if suffix == 'doc':
            docx_path = doc2docx(file_path)
            file_path = docx_path
        texts = doc_extract(file_path,ocr=use_ocr)
    elif suffix in ['pptx','ppt']:
        if suffix == 'ppt':
            pptx_path = ppt2pptx(file_path)
            file_path = pptx_path
        if use_ocr:
            try:
                texts = pptx2image2text(file_path)
            except:
                logger.error(f"pptx2image2text error:{file_path}",exc_info=True)
                texts = ppt_text_extract_page(file_path)
                texts = ["\n".join(t_l) for t_l in texts]
        else:
            texts = ppt_text_extract_page(file_path)
            texts = ["\n".join(t_l) for t_l in texts]
    elif suffix == 'html':
        texts = html_extract(file_path)
    elif suffix == 'txt':
        texts = txt_extract(file_path)
    elif suffix in ['csv','xlsx','xls']:
        texts = df_extract(file_path)
    # 解析图片
    elif suffix in ['jpg','jpeg','png','bmp']:
        texts = [img2text(file_path)]
    else:
        texts = []
    texts = [i.lower() for i in texts]  #解析英文全为小写
    texts = [i for i in texts if i.strip()] #去除一个字的东西
    # texts = [i.replace('\n','') if "------\n" not in i else i for i in texts]  #解析英文全为小写
    # texts = [re.sub('\.\.\.\.|----|____','',i) if "------\n" not in i else i for i in texts] #去除特殊符号
    # texts = [i.strip() for i in texts if len(i.strip())>1] #去除一个字的东西
    if not texts:
        logger.error(f'文件解析为空:{file_path}, {suffix}')
        raise Exception(f'文件解析为空:{file_path}')
    return texts


import base64
def ToBase64(file, txt=None):
    with open(file, 'rb') as fileObj:
        image_data = fileObj.read()
        base64_data = base64.b64encode(image_data)
        if txt is not None:
            fout = open(txt, 'w')
            fout.write(base64_data.decode())
            fout.close()
        else:
            return base64_data.decode()


def ToFile(string, file):
    ori_image_data = base64.b64decode(string)
    fout = open(file, 'wb')
    fout.write(ori_image_data)
    fout.close()

def level_to_kv(level, string,spliter='\n'):
    try:
        item = []
        rank_stack = []
        text_stack = []
        for index, rank, text in zip(range(len(level)), level, string):  # 去除层级,使用predict
            if rank == -1: continue
            if not rank_stack and rank != 0:
                rank_stack += [rank]
                text_stack += [text]
            elif rank == 0 and rank_stack:
                rank_stack += [rank]
                text_stack += [text]
            elif rank in rank_stack and rank != 0:
                # 处理1200
                cut_index = rank_stack.index(rank)
                key = f'{spliter}'.join([j for i, j in zip(rank_stack, text_stack) if i != 0])  # 非0是key  #强行自己替换家庭和个人
                value = '\n'.join([j for i, j in zip(rank_stack, text_stack) if i == 0])  # 0是value
                rank_stack = rank_stack[:cut_index]
                text_stack = text_stack[:cut_index]
                item.append({'index': index, 'key': key, 'value': value})

                # 当前入栈
                rank_stack += [rank]
                text_stack += [text]
            elif rank != 0 and 0 in rank_stack:
                # 处理100200
                cut_index = rank_stack.index(rank - 1)
                key = f'{spliter}'.join([j for i, j in zip(rank_stack, text_stack) if i != 0])  # 非0是key  #强行自己替换家庭和个人
                value = '\n'.join([j for i, j in zip(rank_stack, text_stack) if i == 0])  # 0是value
                rank_stack = rank_stack[:cut_index + 1]
                text_stack = text_stack[:cut_index + 1]
                item.append({'index': index, 'key': key, 'value': value})
                rank_stack += [rank]
                text_stack += [text]

            elif rank != 0:
                rank_stack += [rank]
                text_stack += [text]

        # 对最后剩余的stack处理
        key = f'{spliter}'.join([j for i, j in zip(rank_stack, text_stack) if i != 0])  # 非0是key
        value = '\n'.join([j for i, j in zip(rank_stack, text_stack) if i == 0])  # 0是value
        item.append({'index': len(item)+1,'key': key, 'value': value})
        return item
    except:
        print(rank, rank_stack, 'rank_stackrank_stack')
        import logging
        logging.error('level_to_kv 错误',exc_info=True)


import cn2an
cn_index = []
for i in range(1, 100):
    cn_num = cn2an.an2cn(i)
    cn_index += [f'第{cn_num}条']+[f'第{cn_num}节'] + \
                  [f'第{cn_num}章']+[f'第{cn_num}部分'] + [f'\({cn_num}\)'] + \
                  [f'（{cn_num}）'] + [f'{cn_num}'] + [f'（{i}）']+ [f'\({i}\)']

num_index_1 = [str(i) for i in range(0, 20)]
num_index_2 = [j + '\.' + str(i) for i in range(0, 20) for j in num_index_1]
num_index_3 = [j + '\.' + str(i) for i in range(0, 20) for j in num_index_2]
num_index = num_index_1 + num_index_2 + num_index_3


end_index = '\.|、|\:|\,|，|。|：|．|；'.split('|')

all_index = []
for i in cn_index + ['问题','答案','问','答'] + num_index[::-1]:
    for j in end_index:
        all_index.append(i+j)


order = """①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳⓪
❶❷❸❹❺❻❼❽❾❿⓫⓬⓭⓮⓯⓰⓱⓲⓳⓴
㊀㊁㊂㊃㊄㊅㊆㊇㊈㊉㈠㈡㈢㈣㈤㈥㈦㈧㈨㈩
⑴⑵⑶⑷⑸⑹⑺⑻⑼⑽⑾⑿⒀⒁⒂⒃⒄⒅⒆⒇
⒈⒉⒊⒋⒌⒍⒎⒏⒐⒑⒒⒓⒔⒕⒖⒗⒘⒙⒚⒛
ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩⅪⅫⅰⅱⅲⅳⅴⅵⅶⅷⅸⅹ
ⒶⒷⒸⒹⒺⒻⒼⒽⒾⒿⓀⓁⓂⓃⓄⓅⓆⓇⓈⓉⓊⓋⓌⓍⓎⓏ
ⓐⓑⓒⓓⓔⓕⓖⓗⓘⓙⓚⓛⓜⓝⓞⓟⓠⓡⓢⓣⓤⓥⓦⓧⓨⓩ
⒜⒝⒞⒟⒠⒡⒢⒣⒤⒥⒦⒧⒨⒩⒪⒫⒬⒭⒮⒯⒰⒱⒲⒳⒴⒵"""
order = list(order)
order = [i for i in order if i.strip()]

char_index = [chr(i) for i in range(ord('a'),ord('z')+1)]
char_index+=[i.upper() for i in char_index]
char_index = [i+'\)' for i in char_index]

all_index += cn_index + num_index + ['□'] + order + char_index


all_index = sorted(all_index,key=lambda x:len(x),reverse=True)

pattern = '|'.join(['\A' +i for i in all_index])
import re
def replace_index(title):
    title = re.sub(pattern, '', title)
    return title


def ppt_text_extract_page(ppt_path, need_sort=True):
    prs = pptx.Presentation(ppt_path)
    return_text = []

    def get_table_data(shape):
        table_data = []
        table = shape.table
        for row in table.rows:
            row_data = []
            for cell in row.cells:
                row_data.append(cell.text.replace('\n', ' '))
            row_lengths = [len(r.strip()) for r in row_data]
            if sum(row_lengths) != 0:
                table_data.append(row_data)

        table_str = "------\n" if len(table_data) else ""
        for row in table_data:
            table_str = table_str + "|".join(row) + "\n------\n"
        return table_data, table_str

    def process_shape(shape, slide_list):
        if shape.has_text_frame:  # Shape with text
            if shape.text.strip():
                try:
                    slide_list.append([shape.text.strip(), shape.left+shape.width//2, shape.top+shape.height//2])
                except:
                    slide_list.append([shape.text.strip(), None, None])
        if shape.has_table:
            _, shape_table_str = get_table_data(shape)
            slide_list.append([shape_table_str, None, None])

        if hasattr(shape, "shapes"):
            for shp in shape.shapes:
                process_shape(shp, slide_list)

    for i, slide in enumerate(prs.slides):
        slide_list = []
        shape_list = [s for s in slide.shapes]
        for shape in slide.shapes:
            process_shape(shape, slide_list)
        if need_sort:
            for _ in range(3):
                have_swap = False
                for i in range(len(slide_list)):
                    for j in range(i+1, len(slide_list)):
                        if slide_list[i][1] is None or slide_list[j][1] is None:
                            continue
                        try:
                            if abs(abs(slide_list[i][1]-slide_list[j][1])/min(slide_list[i][1], slide_list[j][1])) <= 0.01:
                                # 同一列
                                if slide_list[i][2] > slide_list[j][2]:
                                    temp_list = slide_list[i]
                                    slide_list[i] = slide_list[j]
                                    slide_list[j] = temp_list
                                    have_swap = True
                            elif abs(abs(slide_list[i][2]-slide_list[j][2])/min(slide_list[i][2], slide_list[j][2])) <= 0.01:
                                # 同一行
                                if slide_list[i][1] > slide_list[j][1]:
                                    temp_list = slide_list[i]
                                    slide_list[i] = slide_list[j]
                                    slide_list[j] = temp_list
                                    have_swap = True
                        except:
                            continue
                if not have_swap:
                    break
        slide_list = [s[0] for s in slide_list]
        return_text.append(slide_list)
    return return_text

def write_comments(ppt_file, save_file, coment_list):
    ppt = pptx.Presentation(ppt_file)
    slides = ppt.slides
    for i, slide in enumerate(slides):
        if i+1 > len(coment_list):
            logger.warning(f"ppt页码与数据对不上：ppt.slides:{len(slides)}, coment_list:{coment_list}")
            continue
        notes_slide = slide.notes_slide
        notes_slide.notes_text_frame.text = coment_list[i]
    ppt.save(save_file)

def download_url_file(url, download_path, exists_download=False):
    try:
        if not exists_download:
            if os.path.exists(download_path):  # 检查对应wav是否存在
                logger.info(f'已经下载过了:{url}')
                return '已下载'
        logger.info(f'正在请求下载链接:{url}')
        data = requests.get(url)
        with open(download_path, 'wb') as f:
            f.write(data.content)
            f.close()
        return '下载完成'
    except Exception as e:
        logger.error(f'文件下载失败:{url}', exc_info=True)
        return e

import json

def get_list_of_string(res_string):
    res_string = res_string.strip()
    try:
        temp = json.loads(res_string.strip())
        return temp
    except:
        pass

    try:
        temp = eval(res_string.strip())
        return temp
    except:
        pass

    try:
        res_string = res_string[res_string.find("["):res_string.rfind("]") + 1]
        temp = json.loads(res_string.strip())
        return temp
    except:
        pass

    try:
        res_string = res_string[res_string.find("["):res_string.rfind("]") + 1]
        temp = eval(res_string.strip())
        return temp
    except:
        pass

    return []

if __name__=='__main__':
    # texts = txt_extract("/data/luyu/NLP_Model/1763389835399856128/files/诛仙小说_1709259873518.txt")
    # ToBase64('doc/test2.txt','doc/base_test.txt')
    # ToFile(open('base_test.txt','r').read(),'test.docx')
    # doc = pdf_text_extract(r"D:\develop\NLP_Model\检索测试\Retriveval\files\棉花主要虫害种类及防治措施_赵晓燕.pdf")
    # path = r"C:\Users\<USER>\Desktop\文件解析测试文件\人力资源管理法务实战笔记-70.pdf"
    # path = r"C:\Users\<USER>\Desktop\新建文件夹 (2)\职务犯罪咨询指引2023年版.pdf"
    # path = r"C:\Users\<USER>\Desktop\新建文件夹 (2)\搬书匠-3001-Spring Boot 实战-2016-中文版.pdf"
    # path = r"D:\develop\aicc-llm-agent\data\刑法\刑事知识库\pdf\20240228-回归之路-服刑人员家属法律咨询服务…斌、韩若冰、吕子辉-终稿.pdf"
    # path = r"C:\Users\<USER>\Desktop\sh_data.csv"
    # path = r"D:\download\录音18_1719911203176.xlsx"
    path = r"C:\Users\<USER>\Desktop\新建文件夹 (2)\专利审查指南(2023)_1720081003175.pdf"
    path = r"C:\Users\<USER>\Desktop\pdf测试\files\植德新员工基础培训_1720441494897.pdf"
    path = r"C:\Users\<USER>\Desktop\植德新员工基础培训_1720441494897.pdf"
    path = r"D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2024-07\1.xlsx"
    path = r"D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2024-07\10000行文本题库导入模板.xlsx"
    path = r"D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2024-07\1.202304-202403争议解决部业绩表（20240521）.xlsx"
    path = r"C:\Users\<USER>\Desktop\新建文件夹 (4)\合伙人、律师简历汇总表.xlsx"
    path = r"C:\Users\<USER>\Desktop\新建文件夹 (4)\植德宣传册-2308九办公室版-压缩_1720231382974.pdf"
    # path = r"C:\Users\<USER>\Desktop\pdf测试\专利审查\专利审查指南(2023)_1720081003175_1720095341742_1720577419885.pdf"
    path = r'/NLP_Model/FILECHAT_MODEL/1813397715854098434/Retrieval/files/专利审查指南(2023)_1720081003175_1721182662598.pdf'
    path = r"C:\Users\<USER>\Desktop\新建文件夹\卓越理财尊尚迎新手册_1721263837928.pdf"
    path = r"C:\Users\<USER>\Desktop\新建文件夹\植德律所介绍（合伙人版）-20240314.pdf"
    path = r"C:\Users\<USER>\Desktop\文件解析测试文件\指针大模型v1.pptx"
    path = r"D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2024-08\邓伟方律师 - 项目建议书简历.docx"
    path = '/data/sammy/dddtttt.doc'
    # from utils.filechat_utils import ToFile, ultra_extract
    path = r"C:\Users\<USER>\Desktop\泰康健保通医院住院津贴医疗保险  .pdf"
    path = r"C:\Users\<USER>\Desktop\博物馆文档\刘氏庄园——泥塑卷(文.图).docx"

    path = r"D:\app\qchat_temp\WXWork\1688852042007731\Cache\File\2025-06\国务院办公厅关于印发“十四五”全民医疗保障规划的通知.docx"
    doc = ultra_extract(path,True)
    for i in doc: print(i);print('\n+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n')

    # print(doc,'asdasdasdasd')
    # print("wait")
    # print(doc)

