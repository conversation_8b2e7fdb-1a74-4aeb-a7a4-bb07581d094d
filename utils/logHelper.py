# -*- coding: utf-8 -*-
import os
import logging
import logging.handlers
import concurrent_log


#### 使用说明 ####
# 在自己的setting.py文件中调用log_initialize得到初始化的logger，然后其他代码中引用


#### 日志格式规范 ####

# 所有程序内部日志都采用debug
# info只用于接到请求时打印收到的内容及处理完任务后返回的内容
# info中传入日志内容必须是json格式

def path_is_exist(path):
    if not os.path.exists(path):
        os.makedirs(path)


def log_initialize(logPath, logName):
    log_format = '%(asctime)s.%(msecs)d [%(levelname)-3s] [%(filename)s:%(lineno)d] [%(process)s] [%(thread)s] %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    #日志路径
    path_is_exist(logPath)
    logging.basicConfig(level=logging.INFO, format=log_format, datefmt=date_format)
    logger = logging.getLogger(logName)
    logger.setLevel(logging.DEBUG)
    log_file_name = logPath+"/"+logName
    timefilehandler = logging.handlers.ConcurrentTimedRotatingFileHandler(log_file_name, when='MIDNIGHT', interval=1, backupCount=20,encoding='utf-8') #delay=True
    timefilehandler.suffix = "%Y-%m-%d.log"
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)
    timefilehandler.setFormatter(formatter)
    logger.addHandler(timefilehandler)
    return logger



if __name__ == "__main__":
    logger = log_initialize('./log', 'test_log')
    for i in range(10):
        logger.info('你好啊')
