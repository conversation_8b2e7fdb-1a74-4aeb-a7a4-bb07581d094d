import json
import pandas as pd

extract_service = "SimScorePredict"
df = pd.DataFrame(columns=["text1", "text2"])
text1 = []
text2 = []

with open("./EXAM", encoding="utf-8", mode="r") as f:
    for line in f:
        if line.find(extract_service) >= 0:
            line = line[line.find("{"):]
            data = json.loads(line)
            if "out" in data:
                text1.append(data["out"]["text1"])
                text2.append(data["out"]["text2"])

df["text1"] = text1
df["text2"] = text2
df.to_csv("./EXAM.csv", index=False)
