import ctypes
import inspect
import json
import random
import time
import configparser
import os
import shutil
import re
from functools import total_ordering
import psutil
import subprocess
import setting
from setting import logger
from submodule_utils.nacos_register import NacosHelper
import numpy as np
from flask import make_response
from numpy import mean
from sklearn.metrics.pairwise import normalize, safe_sparse_dot
from database.REDIS import REDIS
import threading
mutex = threading.Lock()

redis = REDIS()

symbol_re = re.compile('，|，|。|？|！|：|；|,|\.|\?|!|:|;|、|（|）|\(|\)|」|「|/')
def remove_symbol(text,replace_to_what = ''):
    if replace_to_what:
        return symbol_re.sub(replace_to_what, text)
    else:
        return symbol_re.sub("", text)

# 去掉开头和结尾的标点符号
patten_sign = re.compile('[\u4e00-\u9fa5a-zA-Z0-9﹢+()（）].{0,100}[\u4e00-\u9fa5a-zA-Z0-9﹢+()（）]')
patten_sign_ = re.compile('[\u4e00-\u9fa5a-zA-Z0-9﹢+()（）]{1,100}')  # 避免出现一个汉字的情况被过滤掉了
def remove_front_end_symbol(text, need_lower=True):
    text = text.replace("&nbsp;", "").replace("&nbsp", "").replace("<br/>", "").replace("<br>", "")
    origin_text = text
    if need_lower:
        text = text.lower().strip()
    else:
        text = text.strip()
    r = patten_sign.findall(text)
    if r:
        text = r[0]
    else:
        r_ = patten_sign_.findall(text)
        if r_:
            text = r_[0]
        else:
            text = text  # 只有一个标点符号不用去除
    if len(text) == 0:
        return origin_text
    return text

def remove_front_end_symbol_2(text, need_lower=True):
    if need_lower:
        text = text.lower().strip()
    else:
        text = text.strip()

    symbol_list = ",./<>?';:\"!@#$%^&*()_+-=/\\，。？‘’“”；：！{}【】 "
    while len(text) > 1 and text[0] in symbol_list:
        text = text[1:]
    while len(text) > 1 and text[-1] in symbol_list:
        if text[-1] == "。":
            return text
        text = text[:-1]
    return text

pre_suff_fix_replacer = {'pu_prefixs': ['请告诉我','告诉我', '你知道', '想知道', '请问', '说一下', '问问', '问下', '我问下', '你知不知道', '我想知道', '能不能告诉我', '我可以问个问题', '我有个疑问', '我在想', '你能解释一下', '你认为', '我好奇', '我不明白', '你能帮我澄清一下', '我想问一下', '我想了解', '你能帮我解答一下', '你能解读一下', '你能说说关于', '你能分享一下关于', '你能告诉我关于', '你能帮我查一下', '你能找找', '你能查查', '你知道什么关于', '你能帮我理解一下', '你能给我解释一下', '我想问', '能问你一个问题', '想问下', '可以告诉我', '能不能回答我', '回答我', '请回答我', '你理解', '可不可以回答我', '想问一下', '给我找找', '给我查查', '帮我看下', '帮我查下', '给我看下', '想问', '你能不能', '你能帮我', '查查看', '你能找到', '我需要知道', '我想找一下', '我想查一下', '我想听听', '我想问你', '你可以解释一下', '你能解答一下', '我想知道关于', '我想听听关于', '你知道关于', '你能找找关于', '我想了解关于', '你能查查关于', '你可以告诉我', '你能告诉我', '你能说明一下', '你能帮我解答', '你能帮我找找', '你能帮我查查', '你能帮我看下', '你能帮我查下', '你能帮我解释一下', '你能帮忙看下', '你能帮我理解', '我想听听你的看法', '我想听听你的意见', '我想听听你的解释', '我想听听你的答案', '我想了解一下', '我想了解你的看法', '我想了解你的意见', '我想了解你的答案', '我想了解你的解释', '我想了解你的解答', '我想了解你的观点', '我想了解你的理解', '那么', '对了', '其实', '其次', '然后', '另外', '此外', '所以', '因此', '难道', '大概', '可能', '假如', '如果', '这样', '那样', '哎呀', '嘿嘿', '哈哈', '呃呃', '嗯嗯', '哼哼', '嘛嘛', '瞧瞧', '看看', '听听', '你瞧', '你看', '你听', '你猜', '你说', '你觉得', '你感觉', '你了解', '你找找', '你查查', '你看看', '你想想', '你试试', '你帮帮', '你给我', '你可不可以', '你是不是', '你能否', '你会不会', '你可会',
                                        '你是否', '你有没有', '你有没', '你有无', '你有否', '你可有', '你曾有', '你曾否',
                                        '你曾无', '你曾没','能告诉我吗','可以麻烦你告诉我一下','能否告知','能不能告知'],
                         'yue_prefixs': ['话我知', '请问下', '你知唔知', '想知识', '讲下', '我想知识', '可以解释下', '我唔明', '我想问下', '帮我查一下', '你知唔知咩', '我想知', '可以同我讲下', '答我', '可唔可以问下', '查下', '帮我睇下', '我想寻下', '我想查下', '你可以解释下', '你能否解释下', '你能解释下', '我想知关于', '我想听下关于', '你知唔知关于', '你能唔能告诉我', '你能解释下咩系', '你能帮我解释下', '你能帮我寻寻', '你能够帮我睇下', '你能帮我查下咩', '你能帮忙睇下', '你知道咩', '你能唔能告诉我呀', '可以帮我解释', '我想听下你嘅睇法', '我想听下你嘅意见', '我想听下你嘅解释', '我想听下你嘅答覆', '我想了解下', '我想了解你嘅睇法', '我想了解你嘅意见', '我想了解你嘅答案', '我想了解你嘅解释', '我想了解你嘅解答', '我想了解你嘅观点', '我想了解你嘅理解', '大抵', '噉样', '睇下', '听下', '你睇', '你话', '你明未', '你稳下', '你睇下', '你想下', '试下', '你帮下忙', '你畀我', '你可唔可以', '你识唔识', '你睇咗未', '你听咗未', '你明唔明', '你知唔知道', '你可唔可以答我', '你听说过', '你有冇理解', '你有冇听过', '你有冇睇过', '你可以帮我查下', '你可以帮我理解下', '你可以帮我睇下', '你可以帮我听下', '我想听你讲', '我想听你的意见', '我想知道你的看法', '你能否帮我', '你能否解答我', '你能否给我解释', '你能否给我讲解', '你能否给我答覆', '你能否给我查下', '我想听下你的解释', '我想听下你的看法', '我想听下你的观点', '我想听下你的答案', '我想听下你的理解', '我想听下你的答覆', '我想听下你的意见', '我想了解下你的看法', '我想了解下你的观点', '我想了解下你的答案', '我想了解下你的理解', '我想了解下你的答覆', '我想了解下你的意见', '你可以帮我解答下', '你可以帮我解释下', '你可以帮我找下', '你可以帮我看下', '你可以帮我学下', '你可以帮我了解下', '你可以帮我研究下', '你可以帮我考察下'],
                         'shuffixs': ['啦', '啊', '呃', '呵', '嗯', '噢', '哦', '咦', '哼', '哈', '嘿', '哎', '喂', '呀', '哇', '唉', '嘛', '吧', '呢', '嗬', '嗯哼', '嘻', '噻', '哩', '喏', '咯', '咚', '咔', '哗', '哒', '哝', '哏', '哐', '哧', '啪', '啷', '嘭', '嗒', '嗵', '嗤', '嘤', '哔', '啵', '嘣', '嘞', '噼', '嚓']}

prefix = pre_suff_fix_replacer['pu_prefixs'] + pre_suff_fix_replacer['yue_prefixs']
#对prefix按长到短排序
prefix = sorted(prefix, key=lambda x: len(x), reverse=True)
prefix = [f'\A{i}' for i in prefix if i]


suffix = pre_suff_fix_replacer['shuffixs']
#对suffix按长到短排序
suffix = sorted(suffix, key=lambda x: len(x), reverse=True)
suffix = [f'{i}\Z' for i in suffix if i]


prefix_reg = re.compile('|'.join(prefix))
suffix_reg = re.compile('|'.join(suffix))
def pre_suf_replace_function(text):
    text = prefix_reg.sub('', text)
    text = suffix_reg.sub('', text)
    return text


def check_model_status(model_id):
    model_status = eval(redis.get_data(key="train_state_" + model_id, need_json=False))
    if model_status["state"] == 3 and model_status["msg"] == "取消训练":
        return False
    else:
        return True


def complete_match_text(text):
    text = text.lower().strip()
    return symbol_re.sub("", text)

def json_message(msg):
    resp = make_response(msg)
    resp.headers['Content-Type'] = 'application/json'
    return resp

def mycopyfile(srcfile, dstpath):
    """
    srcfile: 文件路径，如 E://test/test.csv
    dstpath: 目标文件夹，如 E://test
    """
    fpath, fname = os.path.split(srcfile)  # 分离文件名和路径
    shutil.copy(srcfile, os.path.join(dstpath, fname))  # 复制文件

def my_cosine_similarity(X_norm, Y, dense_output=True):
    """
    参考 sklearn.metrics.pairwise 的 cosine_similarity 实现，但是 X_norm 是已经标准化的 X 矩阵
    """
    Y_normalized = normalize(Y, copy=True)
    K = safe_sparse_dot(X_norm, Y_normalized.T, dense_output=dense_output)
    return K


def remove_smallest_overlapping_list(sentence_index):
    b = []
    for i in range(len(sentence_index)):
        if len(sentence_index[i]) < 2:
            b.append(sentence_index[i])
            continue
        end_index = sentence_index[i][1]
        for j in sentence_index:
            if sentence_index[i][0] == j[0]:
                end_index = max(end_index, j[1])
        b.append([sentence_index[i][0], end_index])
    # 去重
    b = [list(x) for x in set(tuple(x) for x in b)]
    c = []
    for i in range(len(b)):
        if len(b[i]) < 2:
            c.append(b[i])
            continue
        start_index = b[i][0]
        for j in b:
            if b[i][1] == j[1]:
                start_index = min(start_index, j[0])

        c.append([start_index, b[i][1]])
    # 去重
    c = [list(x) for x in set(tuple(x) for x in c)]
    c = sorted(c)
    return c


def _async_raise(tid, exctype):
    """raises the exception, performs cleanup if needed"""
    tid = ctypes.c_long(tid)
    if not inspect.isclass(exctype):
        exctype = type(exctype)
    res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
    if res == 0:
        raise ValueError("invalid thread id")
    elif res != 1:
        # """if it returns a number greater than one, you're in trouble,
        # and you should call it again with exc=NULL to revert the effect"""
        ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
        raise SystemError("PyThreadState_SetAsyncExc failed")

def stop_thread(thread):
    _async_raise(thread.ident, SystemExit)

def cur_pid_is_smallest():
    ppid = os.getppid()
    pid = os.getpid()
    parent = psutil.Process(ppid)
    childrens = parent.children()
    all_pids = [c.pid for c in childrens]
    logger.debug(f"当前进程:{os.getpid()},父进程:{os.getppid()},父进程所有子进程:{all_pids},父进程childrens:{childrens}")
    return pid == min(all_pids)

def get_model_id_switch(model_id):
    """
    根据原始model_id生成发布的model_id_switch
    model_id: 原始model_id
    """
    if model_id.endswith("_switch"):
        raise Exception(f"get_model_id_switch参数有问题:{model_id}")
    model_id = model_id + "_switch"
    return model_id

def get_client_model_id(model_id):
    """
    获取当前服务应该使用的model_id,训练端或者外呼的使用原始model_id,预测端使用model_id_switch
    model_id: 原始model_id
    """
    if setting.CLIENT == "train" or setting.CALLOUT_SERVICE:
        return model_id
    else:
        return get_model_id_switch(model_id)


def remove_smallest_overlapping_list(sentence_index):
    b = []
    for i in range(len(sentence_index)):
        if len(sentence_index[i]) < 2:
            b.append(sentence_index[i])
            continue
        end_index = sentence_index[i][1]
        for j in sentence_index:
            if sentence_index[i][0] == j[0]:
                end_index = max(end_index, j[1])
        b.append([sentence_index[i][0], end_index])
    # 去重
    b = [list(x) for x in set(tuple(x) for x in b)]
    c = []
    for i in range(len(b)):
        if len(b[i]) < 2:
            c.append(b[i])
            continue
        start_index = b[i][0]
        for j in b:
            if b[i][1] == j[1]:
                start_index = min(start_index, j[0])

        c.append([start_index, b[i][1]])
    # 去重
    c = [list(x) for x in set(tuple(x) for x in c)]
    c = sorted(c)
    return c


def get_service_instances():
    """获取指定服务的所有实例信息"""
    try:
        n = NacosHelper("server_info")
        if not hasattr(n, 'nacos_client'):
            print("未连接nacos")
            return False
        # 获取服务信息
        server_info = []
        predict_info = n.nacos_client.list_naming_instance("nlp-nlu-predict")
        train_info = n.nacos_client.list_naming_instance("nlp-nlu-train")
        filechat_info = n.nacos_client.list_naming_instance("nlp-nlu-filechat")
        for predict in predict_info["hosts"]:
            ip = predict["ip"]
            port = predict["port"]
            server_info.append({"ip": ip, "service_port": port, "service": "predict"})

        for filechat in filechat_info["hosts"]:
            ip = filechat["ip"]
            port = filechat["port"]
            server_info.append({"ip": ip, "service_port": port, "service": "filechat"})

        for train in train_info["hosts"]:
            ip = train["ip"]
            port = train["port"]
            server_info.append({"ip": ip, "service_port": port, "service": "train"})
        logger.debug(f"服务器列表：{server_info}")
        return server_info
    except Exception as e:
        print(f"Error fetching service instances: {str(e)}")

def get_server_ip_config():
    config = configparser.ConfigParser()
    config.read(os.path.join(setting.MAIN_DIR, "conf/setting.ini"),encoding='utf-8')
    get_ip_list_cmd = "hostname -I"
    local_ip_list = subprocess.check_output(get_ip_list_cmd.split())
    local_ip_list = local_ip_list.decode()
    local_ip_list = local_ip_list.strip().split(" ")
    service_model_path_dict = eval(config.get("IPS", "service_model_path_dict"))
    # server_ip_list = eval(config.get("IPS", "server_ip_list"))
    server_ip_list = get_service_instances() or redis.get_data(setting.SERVICE_NAME)
    # server_ip_list = redis.get_data(setting.SERVICE_NAME)
    for item in server_ip_list:
        item["ssh_port"] = config.get("IPS", "ssh_port")
        item["user"] = config.get("IPS", "user")
    return {"local_ip_list": local_ip_list, "server_ip_list": server_ip_list, "service_model_path_dict": service_model_path_dict}

class MyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return super(MyEncoder, self).default(obj)

class CountSmooth:
    def __init__(self, max_steps):
        self.q = []
        self.max_steps = max_steps

    def get(self):
        return mean(self.q)

    def add(self, value):
        if len(self.q) > self.max_steps:
            self.q.pop(0)
        self.q.append(value)

@total_ordering
class RankReturnObject:
    def __init__(self, query="", sim_query="", norm_query="", norm_query_id="", score=1, save_bigger=True, match_re=""):
        """
        query: 用户输入的查询句子
        sim_query: 从数据库中检索到的句子
        norm_query: 与 sim_query 等价的 norm_query
        norm_query_id: norm_query 对应的 id
        score: query 和 sim_query 的相似性或距离
        save_bigger: bool, 表示 score 是不是越大越相似
        """
        self.query = query
        self.sim_query = sim_query
        self.norm_query = norm_query
        self.norm_query_id = norm_query_id
        self.score = score
        self.save_bigger = save_bigger
        self.match_re = match_re

    def __eq__(self, other):
        return self.score == other.score

    def __ne__(self, other):
        return self.score != other.score

    def __gt__(self, other):
        if self.save_bigger:
            return self.score > other.score
        else:
            return self.score < other.score

    def __str__(self):
        return f'[sim_query: {self.sim_query}, norm_query: {self.norm_query}, norm_query_id: {self.norm_query_id}, score: {self.score}]'

def lang_symbol_to_lang_string(l_s):
    l_s_dict = {
        "chs": "简体中文",
        "cht": "繁体中文",
        "yue": "粤语",
        "en": "英语"
    }
    return l_s_dict.get(l_s, "简体中文")

def contains_chinese(text):
    res = re.search('[\u4e00-\u9fff]+', text)
    if res is None:
        return False
    else:
        return True

def contains_english(text):
    res = re.search('[a-zA-Z]+', text)
    if res is None:
        return False
    else:
        return True

def convert_en_char_and_digit_to_normal(text_input):
    char_dict = {
        "Ａ": "A",
        "Ｂ": "B",
        "Ｃ": "C",
        "Ｄ": "D",
        "Ｅ": "E",
        "Ｆ": "F",
        "Ｇ": "G",
        "Ｈ": "H",
        "Ｉ": "I",
        "Ｊ": "J",
        "Ｋ": "K",
        "Ｌ": "L",
        "Ｍ": "M",
        "Ｎ": "N",
        "Ｏ": "O",
        "Ｐ": "P",
        "Ｑ": "Q",
        "Ｒ": "R",
        "Ｓ": "S",
        "Ｔ": "T",
        "Ｕ": "U",
        "Ｖ": "V",
        "Ｗ": "W",
        "Ｘ": "X",
        "Ｙ": "Y",
        "Ｚ": "Z",
        "ａ": "a",
        "ｂ": "b",
        "ｃ": "c",
        "ｄ": "d",
        "ｅ": "e",
        "ｆ": "f",
        "ｇ": "g",
        "ｈ": "h",
        "ｉ": "i",
        "ｊ": "j",
        "ｋ": "k",
        "ｌ": "l",
        "ｍ": "m",
        "ｎ": "n",
        "ｏ": "o",
        "ｐ": "p",
        "ｑ": "q",
        "ｒ": "r",
        "ｓ": "s",
        "ｔ": "t",
        "ｕ": "u",
        "ｖ": "v",
        "ｗ": "w",
        "ｘ": "x",
        "ｙ": "y",
        "ｚ": "z",
        "０": "0",
        "１": "1",
        "２": "2",
        "３": "3",
        "４": "4",
        "５": "5",
        "６": "6",
        "７": "7",
        "８": "8",
        "９": "9"
    }

    symbol_dict = {
        "　": " ",
        "．": ".",
        "［": "[",
        "］": "]"
    }
    new_text = text_input
    for k, v in char_dict.items():
        new_text = new_text.replace(k, v)
    if new_text != text_input:
        for k, v in symbol_dict.items():
            new_text = new_text.replace(k, v)
    return new_text

def random_id(use_time_stamp=True):
    if use_time_stamp:
        random_str = "".join([f"{random.randint(0, 9)}" for i in range(2)])
        id_str = f"{int(time.time())}"
        return id_str + random_str
    else:
        return "".join([f"{random.randint(0, 9)}" for i in range(10)])

def get_list_of_string(res_string):
    try:
        res_string = res_string.strip()
        try:
            temp = json.loads(res_string.strip())
            return temp
        except:
            pass

        try:
            temp = eval(res_string.strip())
            return temp
        except:
            pass

        try:
            res_string = res_string[res_string.find("["):res_string.rfind("]")+1]
            temp = json.loads(res_string.strip())
            return temp
        except:
            pass

        try:
            res_string = res_string[res_string.find("["):res_string.rfind("]")+1]
            temp = eval(res_string.strip())
            return temp
        except:
            pass
    except:
        return None

def get_dict_of_string(res_string):
    try:
        res_string = res_string.strip()
        try:
            temp = json.loads(res_string.strip())
            return temp
        except:
            pass

        try:
            temp = eval(res_string.strip())
            return temp
        except:
            pass

        try:
            res_string = res_string[res_string.find("{"):res_string.rfind("}")+1]
            temp = json.loads(res_string.strip())
            return temp
        except:
            pass

        try:
            res_string = res_string[res_string.find("{"):res_string.rfind("}")+1]
            temp = eval(res_string.strip())
            return temp
        except:
            pass
        return None
    except:
        return None


def auto_online(main_fun, load_model_dict, model_id, lastest_switch_time):
    # 第一次检查条件
    if model_id not in load_model_dict:
        logger.debug(f'模型自动上线, 正在加载模型{model_id}, main_name: {main_fun.__class__.__name__}')
        with mutex:
            # 第二次检查条件
            if model_id not in load_model_dict:
                load_model_dict[model_id] = int(time.time())
                lastest_switch_time[model_id] = int(time.time())
                try:
                    load_result = main_fun._load_model(model_id)  # 上线失败的异常
                except Exception as e:
                    logger.error(f'模型{model_id}加载失败: {e}')
                    load_model_dict.pop(model_id)
    else:
        logger.warning(f'模型{model_id}加载加锁')
        with mutex:
            load_model_dict[model_id] = int(time.time())


def get_model_record_time(main_fun,lastest_switch_time):
    logger.debug(f'启动模型上线监听线程:{main_fun.__class__.__name__}')
    while True:
        try:
            if True:
                model_record_redis_key = 'model_record' + f".{main_fun.__class__.__name__.replace('Main', '')}"
                model_record = main_fun.redis.get_data(model_record_redis_key,show_log=False)
                if type(model_record) == str:
                    model_record = json.loads(model_record)
            else:
                model_record_path = os.path.join(setting.MODEL_RECORD_FILE + f".{main_fun.__class__.__name__.replace('Main','')}")
                with open(model_record_path, "r") as f:
                    text = f.read()
                    model_record = json.loads(text)

            for model_id, timestamp in model_record.items():
                if timestamp==1:
                    pass
                elif model_id not in lastest_switch_time:
                    #针对最近一小时内上线的模型自动加载
                    if '_switch' in model_id and time.time()-timestamp<3600:
                        main_fun._load_model(model_id)
                        lastest_switch_time[model_id] = timestamp
                        logger.info(f'最近一小时发布：{model_id}，自动上线，mainname：{main_fun.__class__.__name__}，timestamp：{timestamp}')
                elif lastest_switch_time.get(model_id,time.time()) < timestamp:#最近上线时间小于记录时间，则需要再上线
                    if model_id in main_fun.load_model_dict:
                        main_fun._load_model(model_id)
                        lastest_switch_time[model_id] = timestamp
                        #如果main存在属性train_client_load_model_list，则将模型id加入其中
                        if hasattr(main_fun,'train_client_load_model_list') and setting.CLIENT=='train':
                            if model_id not in main_fun.train_client_load_model_list:
                                main_fun.train_client_load_model_list.append(model_id)
                        logger.info(f'新训练模型：{model_id}，自动上线，mainname：{main_fun.__class__.__name__}，timestamp：{timestamp}')
        except:
            logger.error(f'get_model_record_time错误：{main_fun.__class__.__name__},{lastest_switch_time}',exc_info=True)
        time.sleep(1)


def auto_offline(main_fun, load_model_dict: dict):
    logger.info(f'main_name: {main_fun.__class__.__name__},启用自动下线')
    while True:
        for model_id, timestamp in list(load_model_dict.items()):  # 使用 .items() 方法来遍历字典的键值对
            try:
                if time.time() - timestamp >= setting.MODEL_OFFLINE_TIME*60*60:
                    with mutex:  # 使用 with 语句来管理锁
                        logger.debug(f'模型自动下线：{model_id} main_name: {main_fun.__class__.__name__}')
                        load_result = main_fun._offline_model(model_id)
                        if load_result.get("code", "") == 0:
                            load_model_dict.pop(model_id)
            except Exception as e:
                logger.error(f'模型自动下线失败：{model_id} main_name: {main_fun.__class__.__name__}, {e}')
        time.sleep(60)


def get_port_from_pid():
    port = 'cannot find port'
    # 通过进程号获取0.0.0.0:$port

    # 获取当前进程ID
    pid = os.getpid()

    # 执行ps命令，找到对应pid的启动指令
    try:
        ps_output = subprocess.check_output(['ps', '-ef'], text=True)
    except subprocess.CalledProcessError as e:
        print(f"An error occurred while trying to run ps: {e}")
        ps_output = ""

    # 使用正则表达式匹配包含pid的行
    pattern = re.compile(r'\b{}\b'.format(pid))
    for line in ps_output.strip().split('\n'):
        if pattern.search(line):
            # 找到包含当前进程ID的行
            cmdline = line.split(None, 7)[-1]  # 通常命令行是最后一个字段
            print(f"Command line for PID {pid}: {cmdline}")

            # 匹配命令行中的端口号
            port_match = re.search(r'-b\s*0\.0\.0\.0:(\d+)', cmdline)
            if port_match:
                port = port_match.group(1)
                print(f"Port number for PID {pid}: {port}")
                return port
    assert port != 'cannot find port', f'cannot find port for pid {pid}'


def find_highlight_indices(text, words):
    highlight_indices = []
    for word in words:
        start = 0
        while start < len(text):
            start = text.find(word, start)
            if start == -1:
                break
            end = start + len(word)
            highlight_indices.extend(list(range(start, end)))
            start += len(word)  # Move past the current word to find subsequent occurrences
    return highlight_indices

if __name__ == "__main__":
    cur_pid_is_smallest()
    get_server_ip_config()


    import requests
    import json
    from multiprocessing import Pool
    import time
    def send_request(data):
        url = "http://127.0.0.1:9880/nlp/switch_model"
        headers = {
            "Content-Type": "application/json"
        }
        response = requests.post(url, headers=headers, data=json.dumps(data))
        time.sleep(1)
        return response.text

    def multi_process_request(data_list, num_processes):
        with Pool(num_processes) as p:
            results = p.map(send_request, data_list)
        return results


    data_list = [{"flag": 1, "model_id": "1811324304873254914"}]*100000000
    for i in data_list:
        send_request(i)
        print(i)
