import onnxruntime as ort
# import onnx
import tensorflow as tf
import tf2onnx
# from transformers import (TFBertForQuestionAnswering, BertTokenizer)  #请根据任务导出对应的包
# import time
import numpy
import os
# import psutil   #该库可以查看本地计算机的硬件信息


def convert_tf2onnx(instantiated_tf_model, onnx_model_path, input_signature=None, enable_overwrite=True):
    """
    Args:
    instantiated_tf_model:
                An instantiated tensorflow model,this is the fine-tuned model you want to convert(to onnx).
    onnx_model_path :
                Path where onnx_model will be saved,please be noted that the path's suffix must be ".onnx".
    input_signature :
                Default is None.A tf.TensorSpec or a numpy array defining the shape and dtype of the input.
                This parameter determines the input form of the targeted onnx.
                Please be noted that the "name"(such as name ='input_ids') must be the same with the encoded
                   inputs's name you are about to feed into the onnx model for prediction.
    enable_overwrite :
                Whether allow overwrite existing script or model. Default setting is to overwrite exsiting
                  converted model file or create a new onnx model file if no converted onnx model file exists.
                If there is already a converted onnx model file(or you are not sure), you can set it to False,
                  and the function will not do conversion/overwrite again.

    Returns :   An ONNX model_proto and an external_tensor_storage dict.


    Connversion Settings(most settings are optional):
    opset : the opset to be used for the ONNX model, default is the latest
    custom_ops: if a model contains ops not recognized by onnx runtime,you can tag these ops with a custom op
                domain so that the runtime can still open the model. Type is a dictionary `{op name: domain}`.
    target : list of workarounds applied to help certain platforms
    custom_op_handlers : dictionary of custom ops handlers
    custom_rewriter : list of custom graph rewriters
    extra_opset : list of extra opset's, for example the opset's used by custom ops
    shape_override : dict with inputs that override the shapes given by tensorflow
    inputs_as_nchw : transpose inputs in list from nchw to nhwc
    large_model : use the ONNX external tensor storage format
    output_path : save model to output_path

    """
    # Needed this to export onnx model with multiple inputs with TF 2.2
    # model._saved_model_inputs_spec = None

    use_external_data_format = False

    if input_signature is None:
        input_signature = [tf.TensorSpec(shape=(None, None), dtype=tf.int32, name ='input_ids'),
                           tf.TensorSpec(shape=(None, None), dtype=tf.int32, name ='token_type_ids'),
                           tf.TensorSpec(shape=(None, None), dtype=tf.int32, name ='attention_mask')]

    # 中文提示：如果已有转换好的onnx，设置为False,就不复重转换/覆盖
    if enable_overwrite or not os.path.exists(onnx_model_path):
        try:
            model_proto, external_tensor_storage = tf2onnx.convert.from_keras(
                instantiated_tf_model,
                input_signature = input_signature,
                opset = 13,
                large_model = use_external_data_format,
                output_path = onnx_model_path)
        except Exception as e:
            print(f"转换中的报错信息是:{e}")
            raise Exception("如果模型未实例化，请确保参数'instantiated_tf_model'是实例化的tensorflow模型。如果是实例化的模型，请参考报错信息")
    print("onnx model's saved to {}".format(onnx_model_path))



def create_session(onnx_model_path,inter_op_num_threads=0,intra_op_num_threads=1,execution_mode=ort.ExecutionMode.ORT_SEQUENTIAL):

    """
    Args:
    onnx_model_path : path where onnx_model is located,the path's suffix must be ".onnx"

    Returns : session,an instantiated onnx model, used for prediction/inference


    TensorFlow Runtime Settings/sess_options:
    # 没有明确区分并发情况的，表明暂不需要关注是否并发。
    # ExecutionMode:
            Controls whether the operators in the calculation graph are calculated sequentially or in parallel.
            When a model contains multiple branches, setting it to False/ORT_PARALLEL will have a relatively
            big performance improvement.
            However,we suggest ORT_SEQUENTIAL,since it's usually lead to better performance with speed.
    # inter_op_num_threads:
            It's the number of thread pools to use for a TensorFlow session.
            1.并发情况下：该参数影响较小，默认值0即可.
            2.非并发情况下：Suggested number is 1 or 2 .
    # intra_op_parallelism_threads：
            It's the number of threads in each threadpool to use for a TensorFlow session.
            1.并发情况下：须显式设置为1，默认值为0.
            2.非并发情况下：
            This should be set to the number of physical cores may be different from the number of logical cores
            or CPUs and can be found in Linux with the lscpu command.
            物理内核数=物理cpu数*每个物理CPU中的核/cores数。支持超线程的CPU可根据如下logic core数来确定：
            if logic cores > 24, suggested number is in [12,16,20,24];
            if logic cores <= 4, [2,3];
            if logic cores is in between,then try [4,8,12].
    # providers:
            Execution Providers (EP) framework to optimally execute the ONNX models on different hardware
               platform.
            Default setting is "providers=['CPUExecutionProvider']",meaning using local CPUs to do the inference.
            备注：暂时没用到providers参数，这里默认用CPU进行推理。


    """
    if not os.path.exists(onnx_model_path):
        raise FileNotFoundError("找不到模型路径，请确保模型路径正确，并且路径下的onnx模型后缀为'.onnx'")

    try:
        sess_options = ort.SessionOptions()
        sess_options.execution_mode = execution_mode

        # Set the num_of_threads
        sess_options.intra_op_num_threads = intra_op_num_threads
        sess_options.inter_op_num_threads = inter_op_num_threads

        session = ort.InferenceSession(onnx_model_path, sess_options)

    except TypeError as e:
        raise TypeError("线程数和线程池数必须是整数")
    except Exception as e:
        raise e

    return session


#Inference the Exported onnx Model with ONNX Runtime
def inference_with_onnx(session, inputs):
    """
    Args:
    inputs:  text representation after preprocessing & encoding,it's a dict, in the form like this:\
             {input_name: text_numpy/tf_tensor} or input_feed={input_name:text_numpy/tf_tensor}. \
             it must be in line with  the input_signature you set during the convertion from the tf model to onnx.

    Returns: onnx's predicting results in the form of logits,you may want to do post-processing on this returned
             result according to your downstream tasks.

    """

    inputs_onnx = {k: numpy.array(v) for k, v in inputs.items()}
    result = session.run(None,inputs_onnx)

    return result


if __name__=='__main__':
    from transformers import BertTokenizer, BertConfig
    import setting
    from module.PreRank.ArcCSEPreRank.ArcCSE import ArcCSE

    # input_signature = [tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='input_ids'),
    #                    tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='token_type_ids'),
    #                    tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='attention_mask'),
    #                    tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='length_mask'),
    #                    tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='length_div')]

    input_signature = [{"input_ids": tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='input_ids'),
                       "token_type_ids": tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='token_type_ids'),
                       "attention_mask": tf.TensorSpec(shape=(None, None), dtype=tf.int32, name='attention_mask'),
                       "length_mask": tf.TensorSpec(shape=(None, None,None), dtype=tf.float32, name='length_mask'),
                        "length_div": tf.TensorSpec(shape=(None, None), dtype=tf.float32, name='length_div')}]



    # setting.PRETRAIN_BERT_DIR = r'/data/sammy/BERT/roberta_chinese_clue_tiny'
    setting.PRETRAIN_BERT_DIR = r'/data/sammy/BERT/bert768'
    config = BertConfig.from_pretrained(setting.PRETRAIN_BERT_DIR)
    config.emb_layer = setting.ARCCSE_PRE_RANK_EMB_LAYER
    config.max_len = 512
    tokenizer = BertTokenizer.from_pretrained(setting.PRETRAIN_BERT_DIR)

    model = ArcCSE(config=config, tokenizer=tokenizer, model_path=setting.PRETRAIN_BERT_DIR)

    import os
    onnx_model_path = os.path.join(setting.PRETRAIN_BERT_DIR,'tf_model.onnx')
    if not os.path.exists(onnx_model_path):
        convert_tf2onnx(model, onnx_model_path, input_signature=input_signature, enable_overwrite=True)



    onnx_model = create_session(onnx_model_path=onnx_model_path, intra_op_num_threads=1)

    from collections import defaultdict
    record = defaultdict(list)
    max_lens = [32,64,128]
    import time

    from tqdm import tqdm
    for max_len in max_lens:
        for i in tqdm(range(100)):
            inputs = model.get_data(texts=['你好'*100], max_len=max_len, return_tensor=True)
            start = time.time()
            logits = inference_with_onnx(onnx_model, inputs)[0]
            consume = time.time()-start
            record[max_len].append(consume)
    #mean consume
    for k,v in record.items():
        print(f'max_len:{k},mean consume:{sum(v)/len(v)}')

