import json
import os

src_nlp_model_dir = "/data/python/NERModel"
src_model_record_files = []
src_model_id = []

dst_ip = "*************"
dst_nlp_model_dir = "/data/79/NERModel"

for file in os.listdir(src_nlp_model_dir):
    if file.startswith("model_record"):
        src_model_record_files.append(os.path.join(src_nlp_model_dir, file))
        with open(src_model_record_files[-1], "r", encoding="utf-8") as f:
            data = json.load(f)
            for model, _ in data.items():
                src_model_id.append(model)
src_model_id = set(src_model_id)

for file in src_model_record_files:
    send_times = 1
    scp_command = f"scp -r {file}  root@{dst_ip}:{dst_nlp_model_dir}"
    res = os.system(scp_command)
    if res != 0:
        print(f"{file} 发送失败 - ip: {dst_ip}, scp_cmd: {scp_command}")


for model_id in src_model_id:
    send_times = 1
    scp_command = f"scp -r {src_nlp_model_dir}/{model_id}  root@{dst_ip}:{dst_nlp_model_dir}"
    res = os.system(scp_command)
    if res != 0:
        print(f"{model_id} 发送失败 - ip: {dst_ip}, scp_cmd: {scp_command}")


